plugins {
    id 'java-library'
}

dependencies {
    // jackson
    implementation 'com.fasterxml.jackson.core:jackson-annotations'
    implementation 'com.fasterxml.uuid:java-uuid-generator:5.1.0' // no BOM available, standalone artifact

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'
}