package com.illumio.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FlowInventory {

    @Builder.Default
    private List<InventoryItem> inventoryItems = new ArrayList<>();
    @Builder.Default
    private List<IdentityToDeviceRelationship> identityToDeviceRelationships = new ArrayList<>();
    @Builder.Default
    private List<DeviceToDeviceRelationship> deviceToDeviceRelationships = new ArrayList<>();
}
