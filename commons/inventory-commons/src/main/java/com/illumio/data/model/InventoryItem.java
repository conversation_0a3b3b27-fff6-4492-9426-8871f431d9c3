package com.illumio.data.model;

import com.fasterxml.uuid.Generators;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.time.Instant;
import java.util.UUID;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class InventoryItem extends InventoryObject {

    private UUID id;
    private UUID tenantId;
    private InventoryItemType type;
    private InventoryObjectType superType;
    private String entityId;
    private UUID resourceId;
    private String data;
    private byte[] hash;
    private Instant lastSeenAt;
    private Instant updatedAt;

    public InventoryItem(final String tenantId,
                         final String entityId,
                         final InventoryObjectType superType,
                         final InventoryItemType type,
                         final String data) {
        this.id = Generators.timeBasedEpochGenerator().generate(); // uuidv7
        this.tenantId = UUID.fromString(tenantId);
        this.type = type;
        this.superType = superType;
        this.entityId = entityId;
        this.resourceId = generateResourceId(tenantId, entityId, type.name());
        this.data = data;
        this.hash = generateHash();
        this.lastSeenAt = Instant.now();
        this.updatedAt = null; // populated when persisting to DB
        setCreatedAt(null);  // populated when persisting to DB if not already persisted
    }

    @SneakyThrows
    private byte[] generateHash() {
        final String hashInput = String.join("|",
                String.valueOf(id),
                String.valueOf(tenantId),
                type != null ? type.name() : "",
                superType != null ? superType.name() : "",
                entityId != null ? entityId : "",
                data != null ? data : ""
        );
        final MessageDigest digest = MessageDigest.getInstance("SHA-256");
        return digest.digest(hashInput.getBytes(StandardCharsets.UTF_8));
    }

    private UUID generateResourceId(final String tenantId, final String entityId, final String type) {
        return Generators.nameBasedGenerator(UUID.fromString(tenantId))
                         .generate( entityId + type);
    }

    @Override
    public String toString() {
        return id.toString();
    }

}
