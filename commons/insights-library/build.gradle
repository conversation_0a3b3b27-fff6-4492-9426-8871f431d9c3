plugins {
    id 'java-library'
}

repositories {
    maven {
        url  = uri("https://packages.confluent.io/maven/")
    }
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.boot:spring-boot-starter-cache'

    implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.5' // for JSON parsing

    implementation('com.microsoft.azure.kusto:kusto-ingest:5.1.0') {
        exclude group: 'org.slf4j', module: 'slf4j-simple'
    }

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.boot:spring-boot-starter-web'
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'io.projectreactor:reactor-test'
    testImplementation 'org.apache.commons:commons-csv:1.9.0'
}

// Adds a configuration to expose test classes
configurations {
    testOutput.extendsFrom testImplementation
}

task testJar(type: Jar) {
    archiveClassifier.set("tests")
    from sourceSets.test.output
}

artifacts {
    testOutput testJar
}
