package com.illumio.data.model;

import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.generalFilters.InFilter;
import com.illumio.data.model.generalFilters.LogicFilter;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class SrcDestExtLabelFilter {
    /**
     * while migrating to the general filters, this logic is migrated to the @LogicFilter class
     */
    public static final String CSP_CATEGORY = "CSP";
    public static final String IP_CATEGORY = "IP";

    public static final String MALICIOUS_LABEL = "Malicious";
    public static final String KNOWN_INTERNET_LABEL = "Known Internet";
    public static final String UNKNOWN_INTERNET_LABEL = "Unknown Internet";

    public static String adjustExtLabel(Filters categoryFilter, Filters labelFilter) {
        SrcDestExtLabelUnit unit = new SrcDestExtLabelUnit(categoryFilter, labelFilter);
        if (!unit.isValid()) {
            log.error("Can not get valid KQL based on source/destination external label {}", unit);
            return "";
        }

        String category = categoryFilter.getCategoryValue().get(0).toString();
        String label = labelFilter.getCategoryValue().get(0).toString();

        if (category.equalsIgnoreCase(CSP_CATEGORY)) {
            Filters zone = Filters.builder()
                .categoryName(unit.getZoneField().getFieldKey())
                .categoryType(unit.getZoneField().getFieldType())
                .categoryValue(List.of(label))
                .build();

            return SrcDestZoneFilter.adjustSrcDestZone(zone);
        }

        if (category.equalsIgnoreCase(IP_CATEGORY)) {
            if (label.equalsIgnoreCase(MALICIOUS_LABEL)) {
                return " %s >= 2 ".formatted(unit.getThreadLevelColumnName());
            }

            if (label.equalsIgnoreCase(KNOWN_INTERNET_LABEL)) {
                return " %s == 'true' ".formatted(unit.getWellKnownColumnName());
            }

            if (label.equalsIgnoreCase(UNKNOWN_INTERNET_LABEL)) {
                return " %s != 'true' ".formatted(unit.getWellKnownColumnName());
            }
        }

        return unit.getFallbackNormalKQL();
    }

    protected static class SrcDestExtLabelUnit {

        protected final Boolean isSrc;
        protected Filters categoryFilter;
        protected Filters labelFilter;

        protected SrcDestExtLabelUnit(Filters categoryFilter, Filters labelFilter){
            if (categoryFilter == null || labelFilter == null) {
                this.isSrc = null;
            } else if (categoryFilter.getCategoryName().equals(Fields.SOURCE_EXTERNAL_LABEL_CATEGORY.getFieldKey())
                && labelFilter.getCategoryName().equals(Fields.SOURCE_EXTERNAL_LABEL.getFieldKey())) {
                this.isSrc = true;
            } else if (categoryFilter.getCategoryName().equals(Fields.DESTINATION_EXTERNAL_LABEL_CATEGORY.getFieldKey())
                && labelFilter.getCategoryName().equals(Fields.DESTINATION_EXTERNAL_LABEL.getFieldKey())) {
                this.isSrc = false;
            } else {
                this.isSrc = null;
            }

            this.categoryFilter = categoryFilter;
            this.labelFilter = labelFilter;
        }

        protected boolean isValid(){
            return this.isSrc != null;
        }

        protected Fields getZoneField(){
            if (!isValid()) {
                return null;
            }

            return isSrc ? Fields.SOURCE_ZONE : Fields.DESTINATION_ZONE;
        }

        protected String getThreadLevelColumnName(){
            if (!isValid()) {
                return null;
            }
            return isSrc ? Fields.SOURCE_THREAT_LEVEL.getTableColumnName() : Fields.DESTINATION_THREAT_LEVEL.getTableColumnName();
        }

        protected String getWellKnownColumnName(){
            if (!isValid()) {
                return null;
            }

            return isSrc ? Fields.SOURCE_WELL_KNOWN.getTableColumnName() : Fields.DESTINATION_WELL_KNOWN.getTableColumnName();
        }

        protected String getFallbackNormalKQL(){
            if (!isValid()) {
                return null;
            }

            // Normal case - both values are not null
            InFilter tempCatFilter = new InFilter();
            tempCatFilter.setField(this.categoryFilter.getCategoryName());
            tempCatFilter.setValues(this.categoryFilter.getCategoryValue().stream().map(Object::toString).collect(
                Collectors.toList()));
            tempCatFilter.setIgnoreCase(true);

            InFilter tmpLabelFilter = new InFilter();
            tmpLabelFilter.setField(this.labelFilter.getCategoryName());
            tmpLabelFilter.setValues(this.labelFilter.getCategoryValue().stream().map(Object::toString).collect(
                Collectors.toList()));
            tmpLabelFilter.setIgnoreCase(true);

            LogicFilter logicFilter = new LogicFilter(true);
            logicFilter.setFilters(List.of(tempCatFilter, tmpLabelFilter));
            logicFilter.setAndRelation(true);

            return logicFilter.buildKQL();
        }
    }
}
