package com.illumio.data.model.deepPagination;

import com.illumio.data.model.generalFilters.BaseFilter;

public class FirstPageInforBuilder extends AbstractPageBuilder{

    public FirstPageInforBuilder(DpMetaData metaData) {
        super(metaData);
    }

    @Override
    public DpTargetPageInfor build() {
        return super.toFirstPage();
    }

    @Override
    BaseFilter getAdditionalFilter() {
        return null;
    }

    @Override
    int getRowCountToSkip() {
        return 0;
    }

    @Override
    int getTargetPageNumber() {
        return 1;
    }
}
