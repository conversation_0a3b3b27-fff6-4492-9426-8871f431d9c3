package com.illumio.data.model.generalFilters;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Setter
@Getter
@Slf4j
public class EmptyCheckFilter extends LeafFilter {
    private boolean isEmpty = true;

    protected String getOperator() {
        return isEmpty ? " isempty " : " isnotempty ";
    }

    @Override
    public String buildKQL() {
        return "%s(%s)".formatted(this.getOperator(), this.getDBColumn());
    }

    /**
     * Checks if the EmptyCheckFilter is well-formatted.
     * A well-formatted StartsWithFilter must have a valid field
     *
     * @return a Pair containing a boolean indicating if the filter is well-formatted,
     *         and a String with the error message if not well-formatted (or null if well-formatted)
     *
     * implementation is same as super class
     */

}
