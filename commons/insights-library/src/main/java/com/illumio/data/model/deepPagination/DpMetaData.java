package com.illumio.data.model.deepPagination;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;
import com.illumio.data.model.SortByFields;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@JsonDeserialize(builder = DpMetaData.DpMetaDataBuilder.class)
public class DpMetaData {
    private SortByFields primarySortBy;
    private int totalPages;
    private int currPage;
    private List<AnchorRows> anchorRows;

    @JsonPOJOBuilder(withPrefix = "")
    public static class DpMetaDataBuilder {
    }

    /**
     * Assume the input metadata is this object
     * based on PaginationType to generate target page related information
     *
     *
     * @param type, requested pagination type
     * @return target page related information
     *  1. the additional filter for KQL
     *  2. how many rows to trim after getting the rows from database
     *  3. whether the targeted page is out of range or not
     *  4. target page number
     */
    @JsonIgnore
    public DpTargetPageInfor buildTargetPageInfor(PaginationType type){
        DpTargetPageBuilder builder = TargetBuilderFactory.createBuilder(this, type);

        return builder.build();
    }

    @JsonIgnore
    public boolean inReverseTraverse(){
        if (this.getAnchorRows() == null || this.getAnchorRows().isEmpty()) return false;

        // starting from last page, no matter go prev or next, need to
        // do reverse sorting when query data from DB
        // do reverse sorting after get data from DB
        return this.getAnchorRows().get(0).getPageNumber() < 0;
    }

    // startingPage is inclusive
    private int getAccumulatedDuplicatedCount(String targetValue, int startingPage, boolean useFirstRow){
        int result = 0;
        for(int i= this.anchorRows.size() - 1; i >= 0; i--){
            AnchorRows row = this.anchorRows.get(i);
            if (Math.abs(row.getPageNumber()) > Math.abs(startingPage)) { continue; }

            String value = row.getFirstValue();
            int count = row.getFirstDupCount();
            if (!useFirstRow) {
                value = row.getLastValue();
                count = row.getLastDupCount();
            }

            if (targetValue.equals(value)) {
                result += count;
            }
            // as data is sorted, if can not find value in the page, no need to check previous pages
            else {
                break;
            }
        }
        return result;
    }

    public boolean sanityCheck(){
        if (totalPages < 1) { return false; }

        // currPage can be negative for reverse traverse
        if (Math.abs(currPage) > totalPages) { return false; }

        /**
         * anchorRows should be continuous, first one either start with 1 or -1 (last page)
         * anchorRows should contain the currPage information
         */

        if (this.anchorRows == null || this.anchorRows.isEmpty() || this.anchorRows.size() > this.totalPages) {
            return false;
        }

        // current page should be the last one in the anchorRows
        if (currPage != this.anchorRows.get(this.anchorRows.size()-1).getPageNumber()) { return false; }
        if (Math.abs(currPage) != this.anchorRows.size()) { return false; }

        for(int i=0; i<this.anchorRows.size(); i++) {
            AnchorRows anchorRow = this.anchorRows.get(i);

            if (i==0 && Math.abs(anchorRow.getPageNumber()) != 1) {
                return false;
            }

            // NOT continuous
            if (i>0 &&
                Math.abs(Math.abs(anchorRow.getPageNumber()) - Math.abs(this.anchorRows.get(i-1).getPageNumber())) != 1) {
                return false;
            }
        }

        return true;
    }
}
