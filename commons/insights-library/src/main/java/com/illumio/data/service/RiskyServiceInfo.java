package com.illumio.data.service;

import com.illumio.data.configuration.InsightsServiceConfiguration;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Stream;

@Slf4j
@Getter
@Component
public class RiskyServiceInfo {
    private final Map<PortProtocolPair, ServiceInfo> portProtoToServiceInfoMap = new ConcurrentHashMap<>() {};
    private final Map<String, List<ServiceInfo>> servicetoServiceInfoMap = new ConcurrentHashMap<>();
    private String filePath;

    public RiskyServiceInfo(InsightsServiceConfiguration config) {
        filePath = config.getRiskyServiceConfig().getRiskyServiceFilePath();
        loadRiskyServices();
    }

    private void readContent(BufferedReader reader) throws IOException {
        Stream<String> lines = reader.lines();
        lines.skip(1)
                .forEach(
                        line -> {
                            try {
                                String[] parts = line.split(",");
                                int port = Integer.parseInt(parts[0]);
                                String serviceName = parts[1];
                                String protocol = parts[2].toUpperCase();
                                String severity = parts[3].toUpperCase();
                                String category = parts[4].toUpperCase();

                                portProtoToServiceInfoMap.put(
                                        new PortProtocolPair(port, protocol),
                                        new ServiceInfo(
                                                port, serviceName, protocol, severity, category));

                                servicetoServiceInfoMap.putIfAbsent(serviceName, new ArrayList<>());
                                servicetoServiceInfoMap
                                        .get(serviceName)
                                        .add(
                                                new ServiceInfo(
                                                        port,
                                                        serviceName,
                                                        protocol,
                                                        severity,
                                                        category));

                            } catch (NumberFormatException | NullPointerException e) {
                                log.warn(
                                        "Poorly formatted risky service file. Check line: {}",
                                        line);
                            }
                        });
    }

    private void loadRiskyServices() {
        if (filePath.startsWith("classpath:")) {
            try {
                String fileName = filePath.substring("classpath:".length());
                InputStream inputStream = getClass().getClassLoader().getResourceAsStream(fileName);
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
                readContent(reader);
                reader.close();
                inputStream.close();
                return;
            } catch (IOException e) {
                throw new RuntimeException(
                        "Error in loading the risky ports lookup table. Please verify that the file exists.",
                        e);
            }
        }
        try (BufferedReader reader = Files.newBufferedReader(Paths.get(filePath))) {
            this.readContent(reader);
        } catch (IOException | NullPointerException e) {
            throw new RuntimeException("Error in loading the risky service lookup table", e);
        }
    }

    @Getter
    public static class ServiceInfo {
        private final int port;
        private final String service;
        private final String protocol;
        private final String severity;
        private final String category;

        public ServiceInfo(
                int port, String service, String protocol, String severity, String category) {
            this.port = port;
            this.service = service;
            this.protocol = protocol;
            this.severity = severity;
            this.category = category;
        }
    }

    @Getter
    @AllArgsConstructor
    @EqualsAndHashCode
    public static class PortProtocolPair {
        int port;
        String protocol;
    }
}
