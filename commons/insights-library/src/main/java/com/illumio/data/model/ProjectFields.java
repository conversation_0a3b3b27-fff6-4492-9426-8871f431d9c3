package com.illumio.data.model;

import com.illumio.data.model.constants.Fields;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class ProjectFields extends DefaultFieldTransfer implements KQLProvider, WellFormattedInput {
    private List<String> projectFields;

    @Override
    public String buildKQL() {
        List<String> columns = this.getProjectColumns();
        if (columns == null || columns.isEmpty()) {
            return buildKQLForAllAvailableColumns();
        }

        return "| project " + String.join(", ", columns);
    }

    public static String buildKQLForAllAvailableColumns() {
        List<String> columns =
                Fields.getFieldsForUI().stream()
                        .map(Fields::getTableColumnName)
                        .collect(Collectors.toList());

        return "| project " + String.join(", ", columns);
    }

    // mapping from UI fields to table columns
    protected List<String> getProjectColumns() {
        // need to use the function instead of the variable
        // as that is overwritten in the subclass
        if (this.getProjectFields() == null) {
            return Collections.emptyList();
        }
        return this.getProjectFields().stream()
                .map(this::transformToKQLColumn)
                .filter(e -> e != null && !e.isEmpty()) // filter out invalid columns
                .collect(Collectors.toList());
    }

    /**
     * Validates if the ProjectFields is well-formatted.
     * A well-formatted ProjectFields must have a non-null projectFields list
     * where all fields exist in the Fields enum.
     *
     * @return a Pair containing a boolean indicating if the input is well-formatted,
     *         and a String with the error message if not well-formatted (or null if well-formatted)
     */
    @Override
    public Pair<Boolean, String> isWellFormatted() {
        if (projectFields == null) {
            // This is actually valid - it means we'll use all available columns
            return new ImmutablePair<>(true, "projectFields is null");
        }

        if (projectFields.isEmpty()) {
            // This is also valid - it means we'll use all available columns
            return new ImmutablePair<>(true, "projectFields is empty");
        }

        // Check if all fields are in Fields enum
        for (String field : projectFields) {
            if (field == null || field.isEmpty()) {
                return new ImmutablePair<>(false, "Project field cannot be null or empty");
            }

            if (!Fields.getFieldKeySet().contains(field)) {
                return new ImmutablePair<>(false, "Invalid project field: " + field);
            }
        }

        return new ImmutablePair<>(true, null);
    }
}
