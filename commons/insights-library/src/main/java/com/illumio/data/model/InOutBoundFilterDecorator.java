package com.illumio.data.model;

import static com.illumio.data.model.constants.Fields.TRAFFIC_DIRECTION;

import com.illumio.data.model.constants.Fields;

public class InOutBoundFilterDecorator extends Filters{
    /**
     * while migrating to the general filters, this logic is migrated to the @InFilter class
     */
    public static final String INBOUND = "inbound";
    public static final String OUTBOUND = "outbound";

    private final Filters filters;
    public InOutBoundFilterDecorator(Filters filters) {
        this.filters = filters;
    }

    private String buildKQLForInbound() {
        return "%s == '' and %s != ''".formatted(Fields.SOURCE_RESOURCE_ID.getTableColumnName(),
            Fields.DESTINATION_RESOURCE_ID.getTableColumnName());
    }

    private String buildKQLForOutbound() {
        return "%s != '' and %s == ''".formatted(Fields.SOURCE_RESOURCE_ID.getTableColumnName(),
            Fields.DESTINATION_RESOURCE_ID.getTableColumnName());
    }

    @Override
    public String buildInnerKQL() {
        if (this.filters.getCategoryName().equalsIgnoreCase(TRAFFIC_DIRECTION.getFieldKey()) &&
            this.filters.getCategoryValue() != null &&
            !this.filters.getCategoryValue().isEmpty()
        ) {
            String rawValue = this.filters.getCategoryValue().get(0).toString();
            if (rawValue.equalsIgnoreCase(INBOUND)) {
                return buildKQLForInbound();
            } else if (rawValue.equalsIgnoreCase(OUTBOUND)) {
                return buildKQLForOutbound();
            }
        }

        return filters.buildInnerKQL();
    }
}
