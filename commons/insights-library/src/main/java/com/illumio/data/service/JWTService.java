package com.illumio.data.service;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;

import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.Base64;

@Component
@Slf4j
public class JWTService {
    @Value("${jwt.secret}")
    private String secretKeyString;

    private PublicKey publicKey=null;

    public static final String ISSUED_AT = "iat";
    public static final String EXPIRATION_TIME = "exp";

    private PublicKey loadPublicKey() throws Exception {
        if (this.publicKey != null) {
            return this.publicKey;
        }

        byte[] outerDecoded = Base64.getDecoder().decode(this.secretKeyString);
        String pem = new String(outerDecoded, StandardCharsets.UTF_8);

        // Step 2: Strip headers and footers
        String publicKeyPEM = pem
            .replaceAll("-----BEGIN ([A-Z ]*)-----", "")
            .replaceAll("-----END ([A-Z ]*)-----", "")
            .replaceAll("\\s", "");

        byte[] keyBytes = Base64.getDecoder().decode(publicKeyPEM);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(keyBytes);
        this.publicKey = KeyFactory.getInstance("RSA").generatePublic(spec);
        return this.publicKey;
    }

    public Claims decodePermissionsJwtInAsymmetric(String jwt) throws Exception {
        return Jwts.parser()
            .setSigningKey(this.loadPublicKey())
            .parseClaimsJws(jwt)
            .getBody();
    }

    // decodePermissionsJwt with Symmetric key
    public Claims decodePermissionsJwtInSymmetric(String jwt) throws Exception {
        byte[] decodedBytes = Base64.getDecoder().decode(this.secretKeyString);
        return Jwts.parser()
            .setSigningKey(decodedBytes)
            .parseClaimsJws(jwt)
            .getBody();
    }

    public Pair<Boolean, String> validateJWT(MultiValueMap<String, String> headers, String tenantId)
    {
        return validateJWT(headers, tenantId, true);
    }

    public Pair<Boolean, String> validateJWT(MultiValueMap<String, String> headers, String tenantId, boolean isSymmetricKey) {
        String jwtKey = "jwt";
        String tenantKey = "tenant";

        // no jwt, return false
        if (!headers.containsKey(jwtKey)){
            return Pair.of(false, "No JWT provided");
        }

        try {
            Claims claims;
            if (isSymmetricKey){
                claims = this.decodePermissionsJwtInSymmetric(headers.getFirst(jwtKey));
            } else {
                claims = this.decodePermissionsJwtInAsymmetric(headers.getFirst(jwtKey));
            }

            Object obj = claims.get(tenantKey);
            log.info("JWT claims: {}, tenant ID in JWT: {} tenant ID from input {}", claims, obj, tenantId);
            boolean isValid = obj != null && obj.equals(tenantId);
            if (!isValid) {
                return Pair.of(false, "Tenant ID in JWT does not match the tenant ID in the request");
            }
            return Pair.of(true, null);
        }
        catch(Exception e){
            return Pair.of(false, e.getMessage());
        }
    }
}
