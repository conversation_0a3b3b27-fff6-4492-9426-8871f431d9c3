package com.illumio.data.model.deepPagination;

import com.illumio.data.model.generalFilters.BaseFilter;
import com.illumio.data.model.generalFilters.GreaterFilter;
import com.illumio.data.model.generalFilters.LesserFilter;
import com.illumio.data.model.generalFilters.LogicFilter;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@AllArgsConstructor
@Getter
public abstract class AbstractPageBuilder implements DpTargetPageBuilder{
    private final DpMetaData metaData;

    public DpTargetPageInfor toFirstPage() {
        return DpTargetPageInfor.builder()
            .targetPageNumber(1)
            .build();
    }

    public DpTargetPageInfor toLastPage() {
        return DpTargetPageInfor.builder()
            .targetPageNumber(-1)
            .build();
    }

    @Override
    public DpTargetPageInfor build() {
        // default to first page when have errors
        if (!this.metaData.sanityCheck()) {
            return this.toFirstPage();
        }

        int targetPage = this.getTargetPageNumber();

        // out of boundary
        if (Math.abs(targetPage) > this.metaData.getTotalPages()) { return toFirstPage(); }
        if (Math.abs(targetPage) < 1) { return toFirstPage(); }

        // first/last page
        if (targetPage == 1 || targetPage == -this.metaData.getTotalPages()) { return toFirstPage(); }
        if (targetPage == -1 || targetPage == this.metaData.getTotalPages()) { return toLastPage(); }

        return DpTargetPageInfor.builder()
            .targetPageNumber(targetPage)
            .additionalFilter(this.getAdditionalFilter())
            .rowCountToSkip(this.getRowCountToSkip())
            .outOfRange(this.targetPageOutOfRange())
            .build();
    }

    protected boolean targetPageOutOfRange() {
        return Math.abs(this.getTargetPageNumber()) > this.metaData.getAnchorRows().size();
    }

    protected BaseFilter generateGreaterThanFilter(String anchorValue){
        GreaterFilter greaterFilter = new GreaterFilter();
        greaterFilter.setField(this.metaData.getPrimarySortBy().getField());
        greaterFilter.setValue(anchorValue);
        greaterFilter.setIncludeEqual(true);
        return greaterFilter;
    }

    protected BaseFilter generateLesserThanFilter(String anchorValue) {
        LesserFilter lesserFilter = new LesserFilter();
        lesserFilter.setField(this.metaData.getPrimarySortBy().getField());
        lesserFilter.setValue(anchorValue);
        lesserFilter.setIncludeEqual(true);
        return lesserFilter;
    }

    /**
     * Generate a filter for values between anchorValue and null
     * @param anchorValue
     * @param isMinimal: whether the anchorValue is the minimal value
     * @return the logic filter for the range, with one of the value being null
     *   Example: where ((FlowCount  <=  10 ) or (isnull(FlowCount)))
     */
    protected BaseFilter generateInBetweenWithNullValue(String anchorValue, boolean isMinimal) {
        BaseFilter compareFilter;
        if(isMinimal){
            compareFilter = this.generateGreaterThanFilter(anchorValue);
        } else {
            compareFilter = this.generateLesserThanFilter(anchorValue);
        }

        BaseFilter isNull = this.generateNullFilter();
        LogicFilter logicFilter = new LogicFilter();
        logicFilter.setFilters(List.of(compareFilter, isNull));
        logicFilter.setAndRelation(false);

        return logicFilter;
    }


    protected BaseFilter generateInBetweenFilter(AnchorRows anchorRows) {
        String minimal = anchorRows.getFirstValue();
        String maximal = anchorRows.getLastValue();
        boolean isMinimalNull = anchorRows.isFirstValueNull();
        boolean isMaximalNull = anchorRows.isLastValueNull();

        if (!this.metaData.getPrimarySortBy().isAscending()){
            minimal = anchorRows.getLastValue();
            maximal = anchorRows.getFirstValue();
            isMinimalNull = anchorRows.isLastValueNull();
            isMaximalNull = anchorRows.isFirstValueNull();
        }

        // If both values are null, return a filter for null values
        if (isMinimalNull && isMaximalNull) {
            return generateNullFilter();
        }

        // If only one value is null, handle according to sort order and nulls first/last policy
        // For ascending: nulls first, for descending: nulls last
        if (isMinimalNull || isMaximalNull) {
            if (isMinimalNull) {
                return generateInBetweenWithNullValue(maximal, false);
            } else { // isMaximalNull
                return generateInBetweenWithNullValue(minimal, true);
            }
        }

        // Normal case - both values are not null
        GreaterFilter greaterFilter = new GreaterFilter();
        greaterFilter.setField(this.metaData.getPrimarySortBy().getField());
        greaterFilter.setValue(minimal);
        greaterFilter.setIncludeEqual(true);

        LesserFilter lesserFilter = new LesserFilter();
        lesserFilter.setField(this.metaData.getPrimarySortBy().getField());
        lesserFilter.setValue(maximal);
        lesserFilter.setIncludeEqual(true);

        LogicFilter logicFilter = new LogicFilter();
        logicFilter.setFilters(List.of(greaterFilter, lesserFilter));
        logicFilter.setAndRelation(true);

        return logicFilter;
    }

    protected int getAccumulatedDuplicatedCount(String targetValue, int startingPage, boolean useFirstRow, boolean isTargetValueNull){
        int result = 0;
        for(int i= this.metaData.getAnchorRows().size() - 1; i >= 0; i--){
            AnchorRows row = this.metaData.getAnchorRows().get(i);
            if (Math.abs(row.getPageNumber()) > Math.abs(startingPage)) { continue; }

            String value = row.getFirstValue();
            boolean isValueNull = row.isFirstValueNull();
            int count = row.getFirstDupCount();
            if (!useFirstRow) {
                value = row.getLastValue();
                isValueNull = row.isLastValueNull();
                count = row.getLastDupCount();
            }

            // If both are null or both are not null and equal
            if ((isTargetValueNull && isValueNull) ||
                (!isTargetValueNull && !isValueNull && targetValue.equals(value))) {
                result += count;
            }
            // as data is sorted, if can not find value in the page, no need to check previous pages
            else {
                break;
            }
        }
        return result;
    }

    protected int getAccumulatedDuplicatedCount(String targetValue, int startingPage, boolean useFirstRow){
        return getAccumulatedDuplicatedCount(targetValue, startingPage, useFirstRow, false);
    }

    /**
     * Generate a filter for null values
     * @return a filter that matches null values
     */
    protected BaseFilter generateNullFilter() {
        // In Kusto, use "| where isnull(field)" to filter for null values
        return new NullColumnKQLFilter(this.metaData.getPrimarySortBy().getField(), true);
    }

    /**
     * Generate a filter for non-null values
     * @return a filter that matches non-null values
     */
    protected BaseFilter generateIsNotNullFilter() {
        // In Kusto, use "| where isnotnull(field)" or "| where isnotempty(field)" to filter for non-null values
        return new NullColumnKQLFilter(this.metaData.getPrimarySortBy().getField(), false);
    }

    abstract int getTargetPageNumber();
    abstract BaseFilter getAdditionalFilter();
    abstract int getRowCountToSkip();
}
