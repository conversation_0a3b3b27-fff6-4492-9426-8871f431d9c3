package com.illumio.data.model.generalFilters;

import com.illumio.data.model.Filters;
import com.illumio.data.model.InOutBoundFilterDecorator;
import com.illumio.data.model.constants.Fields;

import lombok.Builder;
import lombok.Getter;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * This class is used to map the legacy filter to the new general filter for IP Overview
 */
public class IpOverviewFilter {
    public static final String GENERAL_IP = "ip";

    public static final Set<String> VALID_ENTRIES = Set.of(GENERAL_IP,
        Fields.SOURCE_IP.getFieldKey(),
        Fields.DESTINATION_IP.getFieldKey(),
        Fields.TRAFFIC_DIRECTION.getFieldKey()
        );

    public static final List<String> SRC_PROJECT_FIELDS = List.of(
        Fields.SOURCE_IP.getField<PERSON>ey(),
        Fields.SOURCE_CLOUD_PROVIDER.getField<PERSON>ey(),
        Fields.SOURCE_WELL_KNOWN.getField<PERSON>ey(),
        Fields.SOURCE_THREAT_LEVEL.getFieldKey(),
        Fields.SOURCE_REGION.getFieldKey(),
        Fields.SOURCE_CITY.getFieldKey(),
        Fields.SOURCE_COUNTRY.getFieldKey()
    );
    public static final List<String> DEST_PROJECT_FIELDS = List.of(
        Fields.DESTINATION_IP.getFieldKey(),
        Fields.DESTINATION_CLOUD_PROVIDER.getFieldKey(),
        Fields.DESTINATION_WELL_KNOWN.getFieldKey(),
        Fields.DESTINATION_THREAT_LEVEL.getFieldKey(),
        Fields.DESTINATION_REGION.getFieldKey(),
        Fields.DESTINATION_CITY.getFieldKey(),
        Fields.DESTINATION_COUNTRY.getFieldKey()
    );

    @Getter
    @Builder
    public static class IpOverviewUnit {
        private Optional<BaseFilter> filter;
        private String error;
        private List<String> projectFields;

        private List<String> originalSearchValues;

        // both could be true as for general IP search, the relationship is OR
        private boolean searchSrcIP;
        private boolean searchDestIP;
    }

    /**
     *
     * @param filters
     * @return a Pair containing mapped BaseFilter and empty string if the filter is well-formatted,
     *          a Pair containing null and error message if the filter is not well-formatted
     */
    public static IpOverviewUnit mapFromLegacyFilter(List<Filters> filters) {
        /**
         * if the filter is source_ip or destination_ip, use it directly
         * else if filter is "ip" + traffic_direction
         *   -> with INBOUND, use source_ip
         *   -> with OUTBOUND, use destination_ip
         * else if filter is "ip"
         *   -> use both source_ip and destination_ip, KQL with or relationship
         */

        if (filters == null || filters.isEmpty()) {
            return IpOverviewUnit.builder().filter(Optional.empty()).error("Empty input").build();
        }

        if (filters.size()>2){
            return IpOverviewUnit.builder().filter(Optional.empty()).error("Too many filters").build();
        }

        for (Filters filter : filters) {
            if (VALID_ENTRIES.stream()
                .noneMatch(s -> s.equalsIgnoreCase(filter.getCategoryName()))){
                return IpOverviewUnit.builder().filter(Optional.empty()).error("Invalid filter name: " + filter.getCategoryName()).build();
            }

            if (!filter.isStringType()) {
                return IpOverviewUnit.builder().filter(Optional.empty()).error("Invalid filter type: " + filter.getCategoryType()).build();
            }
        }

        Optional<Filters> specificIpFilter = filters.stream()
            .filter(f -> f.getCategoryName().equalsIgnoreCase(Fields.SOURCE_IP.getFieldKey()) || f.getCategoryName().equalsIgnoreCase(Fields.DESTINATION_IP.getFieldKey()))
            .findFirst();

        if (specificIpFilter.isPresent()) {
            if (specificIpFilter.get().getCategoryValue().isEmpty() || specificIpFilter.get().getCategoryValue().size() > 1) {
                return IpOverviewUnit.builder().filter(Optional.empty()).error("Invalid filters: can only provide 1 IP address").build();
            }

            InFilter inFilter = buildPartialFilter(specificIpFilter);
            inFilter.setField(specificIpFilter.get().getCategoryName());

            if (specificIpFilter.get().getCategoryName().equalsIgnoreCase(Fields.SOURCE_IP.getFieldKey())){
                return IpOverviewUnit.builder().filter(Optional.of(inFilter))
                    .projectFields(SRC_PROJECT_FIELDS)
                    .originalSearchValues(specificIpFilter.get().getCategoryValue().stream().map(s -> s.toString()).collect(Collectors.toList()))
                    .searchSrcIP(true)
                    .searchDestIP(false)
                    .build();
            } else {
                return IpOverviewUnit.builder().filter(Optional.of(inFilter))
                    .projectFields(DEST_PROJECT_FIELDS)
                    .originalSearchValues(specificIpFilter.get().getCategoryValue().stream().map(s -> s.toString()).collect(Collectors.toList()))
                    .searchSrcIP(false)
                    .searchDestIP(true)
                    .build();
            }
        }

        Optional<Filters> generalIPFilter = filters.stream()
            .filter(f -> f.getCategoryName().equalsIgnoreCase(GENERAL_IP))
            .findFirst();

        if (generalIPFilter.isEmpty()) {
            return IpOverviewUnit.builder().filter(Optional.empty()).error("Invalid filters: Can not find IP related filters").build();
        }

        if (generalIPFilter.get().getCategoryValue().isEmpty() || generalIPFilter.get().getCategoryValue().size() > 1) {
            return IpOverviewUnit.builder().filter(Optional.empty()).error("Invalid filters: can only provide 1 IP address").build();
        }

        Optional<Filters> trafficDirectionFilter = filters.stream()
            .filter(f -> f.getCategoryName().equalsIgnoreCase(Fields.TRAFFIC_DIRECTION.getFieldKey()))
            .findFirst();

        if (trafficDirectionFilter.isPresent()){
            if (trafficDirectionFilter.get().getCategoryValue().size() != 1) {
                return IpOverviewUnit.builder().filter(Optional.empty()).error("Invalid filters: Can not have multiple traffic directions").build();
            }

            String trafficDirValue = trafficDirectionFilter.get().getCategoryValue().get(0).toString();
            if (trafficDirValue.equalsIgnoreCase(InOutBoundFilterDecorator.INBOUND)){
                InFilter inFilter = buildPartialFilter(generalIPFilter);
                inFilter.setField(Fields.SOURCE_IP.getFieldKey());
                return IpOverviewUnit.builder().filter(Optional.of(inFilter))
                    .projectFields(SRC_PROJECT_FIELDS)
                    .originalSearchValues(generalIPFilter.get().getCategoryValue().stream().map(s -> s.toString()).collect(Collectors.toList()))
                    .searchSrcIP(true)
                    .searchDestIP(false)
                    .build();
            } else if (trafficDirValue.equalsIgnoreCase(InOutBoundFilterDecorator.OUTBOUND)){
                InFilter inFilter = buildPartialFilter(generalIPFilter);
                inFilter.setField(Fields.DESTINATION_IP.getFieldKey());
                return IpOverviewUnit.builder().filter(Optional.of(inFilter))
                    .projectFields(DEST_PROJECT_FIELDS)
                    .originalSearchValues(generalIPFilter.get().getCategoryValue().stream().map(s -> s.toString()).collect(Collectors.toList()))
                    .searchSrcIP(false)
                    .searchDestIP(true)
                    .build();
            } else {
                return IpOverviewUnit.builder().filter(Optional.empty()).error("Invalid traffic direction: " + trafficDirValue).build();
            }
        } else {
            InFilter srcFilter = buildPartialFilter(generalIPFilter);
            srcFilter.setField(Fields.SOURCE_IP.getFieldKey());

            InFilter destFilter = buildPartialFilter(generalIPFilter);
            destFilter.setField(Fields.DESTINATION_IP.getFieldKey());

            LogicFilter logic = new LogicFilter();
            logic.setAndRelation( false );
            logic.setFilters(List.of(srcFilter, destFilter));

            return IpOverviewUnit.builder().filter(Optional.of(logic))
                .projectFields(Stream.concat(SRC_PROJECT_FIELDS.stream(), DEST_PROJECT_FIELDS.stream())
                    .collect(Collectors.toList()))
                .originalSearchValues(generalIPFilter.get().getCategoryValue().stream().map(s -> s.toString()).collect(Collectors.toList()))
                .searchSrcIP(true)
                .searchDestIP(true) // ONLY here both are true
                .build();
        }
    }

    private static InFilter buildPartialFilter(Optional<Filters> filter){
        if (filter.isEmpty()) { return null; }
        InFilter result = new InFilter();
        result.setValues(filter.get().getCategoryValue().stream().map(s -> s.toString()).collect(
            Collectors.toList()));
        result.setIgnoreCase(true);
        return result;
    }
}
