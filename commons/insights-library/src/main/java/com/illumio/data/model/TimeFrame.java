package com.illumio.data.model;

import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TimeFrame implements KQLProvider {
    public static final String START_TIME_COLUMN = "StartTime";
    public static final String END_TIME_COLUMN = "EndTime";
    public static final String INGESTION_TIME = "ingestion_time()";

    @Setter @JsonIgnore boolean useIngestionTime;

    String startTime;
    String endTime;

    @Override
    public String buildKQL() {
        if (useIngestionTime) {
            return " | where %s >= datetime('%s')".formatted(INGESTION_TIME, startTime)
                    + "| where %s <= datetime('%s')".formatted(INGESTION_TIME, endTime);
        }

        return " | where todatetime(%s) >= datetime('%s')".formatted(START_TIME_COLUMN, startTime)
                + "| where todatetime(%s) <= datetime('%s')".formatted(END_TIME_COLUMN, endTime);
    }

    @JsonIgnore
    public String getFromToString() {
        return "from datetime('%s') to datetime('%s')".formatted(startTime, endTime);
    }
}
