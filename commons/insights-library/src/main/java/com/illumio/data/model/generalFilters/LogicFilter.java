package com.illumio.data.model.generalFilters;

import com.illumio.data.model.Filters;
import com.illumio.data.model.KQLProvider;
import com.illumio.data.model.SrcDestExtLabelFilter;
import com.illumio.data.model.constants.Fields;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Setter
@Getter
@Slf4j
public class LogicFilter extends BaseFilter {
    private boolean andRelation; // "and" or "or"
    private List<BaseFilter> filters;

    private boolean ignoreSpecialHandling;

    public LogicFilter() {
        this(false);
    }

    public LogicFilter(boolean ignoreSpecialHandling) {
        this.ignoreSpecialHandling = ignoreSpecialHandling;
    }

    protected String getOperator(){
        return andRelation?" and ":" or ";
    }

    @Override
    public String buildKQL() {
        if (!this.ignoreSpecialHandling) {
            Optional<String> extLabel = this.getKQLForExtLabels();
            if (extLabel.isPresent()) {
                return extLabel.get();
            }
        }

        String joined = filters.stream()
            .map(KQLProvider::buildKQL)
            .filter( kql -> kql != null && !kql.isEmpty())
            .map(kql -> "(" + kql + ")")
            .collect(Collectors.joining(this.getOperator()));

        return joined.isEmpty()? "" : "(" + joined + ")";
    }

    private Optional<String> getKQLForExtLabels(){
        if (!andRelation){ return Optional.empty(); }

        if (filters.size() != 2){ return Optional.empty(); }

        AtomicReference<Filters> destExtLabelCat = new AtomicReference<>();
        AtomicReference<Filters> destExtLabel = new AtomicReference<>();

        AtomicReference<Filters> srcExtLabelCat = new AtomicReference<>();
        AtomicReference<Filters> srcExtLabel = new AtomicReference<>();
        this.getFilters()
                .forEach(
                        f -> {
                            if (f instanceof InFilter) {
                                InFilter inFilter0 = (InFilter) f;
                                if (inFilter0
                                        .getField()
                                        .equalsIgnoreCase(
                                                Fields.DESTINATION_EXTERNAL_LABEL_CATEGORY
                                                        .getFieldKey())) {
                                    destExtLabelCat.set(InFilter.convertToFilters(inFilter0));
                                } else if (inFilter0
                                        .getField()
                                        .equalsIgnoreCase(
                                                Fields.DESTINATION_EXTERNAL_LABEL.getFieldKey())) {
                                    destExtLabel.set(InFilter.convertToFilters(inFilter0));
                                } else if (inFilter0
                                    .getField()
                                    .equalsIgnoreCase(
                                        Fields.SOURCE_EXTERNAL_LABEL_CATEGORY.getFieldKey())) {
                                    srcExtLabelCat.set(InFilter.convertToFilters(inFilter0));
                                } else if (inFilter0
                                    .getField()
                                    .equalsIgnoreCase(
                                        Fields.SOURCE_EXTERNAL_LABEL.getFieldKey())) {
                                    srcExtLabel.set(InFilter.convertToFilters(inFilter0));
                                }
                            }
                        });

        if (destExtLabelCat.get() != null && destExtLabel.get() != null) {
            String kql = SrcDestExtLabelFilter.adjustExtLabel(destExtLabelCat.get(), destExtLabel.get());
            if (kql == null || kql.isEmpty()) {
                log.error("Can not build KQL for invalid destination external label filter");
                return Optional.empty();
            }
            return Optional.of("( %s )".formatted(kql));
        }

        if (srcExtLabelCat.get() != null && srcExtLabel.get() != null) {
            String kql = SrcDestExtLabelFilter.adjustExtLabel(srcExtLabelCat.get(), srcExtLabel.get());
            if (kql == null || kql.isEmpty()) {
                log.error("Can not build KQL for invalid source external label filter");
                return Optional.empty();
            }
            return Optional.of("( %s )".formatted(kql));
        }

        return Optional.empty();
    }

    /**
     * Checks if the LogicFilter is well-formatted.
     * A well-formatted LogicFilter must have a non-null, non-empty filters list,
     * and all child filters must be well-formatted.
     *
     * @return a Pair containing a boolean indicating if the filter is well-formatted,
     *         and a String with the error message if not well-formatted (or null if well-formatted)
     */
    @Override
    public Pair<Boolean, String> isWellFormatted() {
        if (filters == null || filters.isEmpty()) {
            return new ImmutablePair<>(false, "Filters list is null or empty");
        }

        // Check that all child filters are well-formatted
        for (int i = 0; i < filters.size(); i++) {
            BaseFilter filter = filters.get(i);
            Pair<Boolean, String> result = filter.isWellFormatted();
            if (!result.getLeft()) {
                return new ImmutablePair<>(false, "Child filter at index " + i + " is not well-formatted: " + result.getRight());
            }
        }

        return new ImmutablePair<>(true, null);
    }
}
