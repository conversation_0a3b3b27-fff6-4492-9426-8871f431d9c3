package com.illumio.data.service;

import static com.illumio.data.model.constants.Fields.BYTES;
import static com.illumio.data.model.constants.Fields.FLOWS;
import static com.illumio.data.model.constants.Fields.PREVIOUS_BYTES;
import static com.illumio.data.model.constants.Fields.PREVIOUS_FLOWS;
import static com.illumio.data.model.constants.Fields.SERVICE;

import com.illumio.data.model.Filters;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.constants.Fields;

import lombok.AllArgsConstructor;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
@AllArgsConstructor
public class RequestPayloadValidation {
    private final RiskyServiceInfo riskyServiceInfo;

    public String validateRequest(RequestPayload payload) {
        // Validate Filter Fields
        if (payload.getFilters() != null && !payload.getFilters().isEmpty()) {
            for (Filters filter : payload.getFilters()) {
                // Validate filter name
                if (!Fields.getFieldKeySet().contains(filter.getCategoryName())) {
                    return String.format(
                            "Invalid filter value. Category name '%s' is not a valid field."
                                    + "Valid field types are: %s",
                            filter.getCategoryName(),
                            Arrays.toString(Fields.getFieldKeySet().toArray()));
                }
                // Validate filter values
                if (filter.getCategoryValue() == null || filter.getCategoryValue().isEmpty()) {
                    return "Invalid filter value. Category value must have values.";
                }
                // Validate service if exists
                if (filter.getCategoryName().equals(SERVICE.getFieldKey())) {
                    if (filter.getCategoryValue().size() != 1) {
                        return "Invalid Service Filter. There can only be one service per request";
                    }
                    if (!riskyServiceInfo
                            .getServicetoServiceInfoMap()
                            .containsKey(filter.getCategoryValue().get(0))) {
                        return String.format(
                                "Invalid Service Filter. Service value '%s' is invalid",
                                filter.getCategoryValue().get(0));
                    }
                }
            }
        }

        // Validate Sort By Fields
        List<String> validSortByFields = new ArrayList<>();
        validSortByFields.addAll(
                Arrays.asList(
                        BYTES.getFieldKey(),
                        FLOWS.getFieldKey(),
                        PREVIOUS_BYTES.getFieldKey(),
                        PREVIOUS_FLOWS.getFieldKey()));

        if (payload.getSortByFields() != null && !payload.getSortByFields().isEmpty()) {
            for (SortByFields sortByField : payload.getSortByFields()) {
                if (!validSortByFields.contains(sortByField.getField())) {
                    return String.format(
                            "Invalid sort value. Category name '%s' is not a valid field."
                                    + "Valid field types are: %s",
                            sortByField.getField(), Arrays.toString(validSortByFields.toArray()));
                }
            }
        }

        return "";
    }
}
