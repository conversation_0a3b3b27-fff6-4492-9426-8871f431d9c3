package com.illumio.data.model;

import com.fasterxml.jackson.annotation.JsonIgnore;

import org.apache.commons.lang3.tuple.Pair;

/**
 * Interface for classes that need to validate if they are well-formatted.
 * Implementing classes should provide validation logic to check if all required fields are properly set.
 */
public interface WellFormattedInput {
    /**
     * Validates if the input is well-formatted.
     *
     * @return a Pair containing a boolean indicating if the input is well-formatted,
     *         and a String with the error message if not well-formatted (or null if well-formatted)
     */
    @JsonIgnore
    Pair<Boolean, String> isWellFormatted();
}
