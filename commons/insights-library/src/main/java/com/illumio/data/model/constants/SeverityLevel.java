package com.illumio.data.model.constants;

import lombok.Getter;

@Getter
public enum SeverityLevel {
    LOW(0),
    MEDIUM(1),
    HIGH(2),
    CRITICAL(3);

    private final int value;

    SeverityLevel(int value) {
        this.value = value;
    }

    public static int fromString(String severity) {
        if (severity == null) {
            return -1; // Default value for unknown severity
        }
        try {
            return SeverityLevel.valueOf(severity.toUpperCase()).getValue();
        } catch (IllegalArgumentException e) {
            return -1; // Unknown severity
        }
    }
}
