package com.illumio.data.model.constants;

import java.util.Map;
import java.util.stream.Collectors;

public class KustoInternal {

    /**
     * For Kusto internal function, the project name is different than the return name,
     *
     *  DecoratedFlows1
     * | order by ingestion_time() desc
     * | project Port, SrcIP, FlowCount, Proto, ingestion_time()
     *  | take 1
     *
     *  the project name is "ingestion_time()"
     *  the return name is "$ingestionTime"
     */
    public static final Map<String, String> PROJECT_TO_RETURN_COLUMN =
            Map.of("ingestion_time()", "$IngestionTime");

    public static final Map<String, String> RETURN_TO_PROJECT_COLUMN = PROJECT_TO_RETURN_COLUMN.entrySet()
        .stream()
        .collect(Collectors.toMap(Map.Entry::getValue, Map.Entry::getKey));

}
