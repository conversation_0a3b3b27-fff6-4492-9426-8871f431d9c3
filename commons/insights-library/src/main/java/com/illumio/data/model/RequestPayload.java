package com.illumio.data.model;

import static com.illumio.data.model.constants.Fields.PORT;
import static com.illumio.data.model.constants.Fields.PROTOCOL;
import static com.illumio.data.model.constants.Fields.SERVICE;

import com.illumio.data.model.generalFilters.BaseFilter;
import com.illumio.data.service.RiskyServiceInfo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class RequestPayload {
    private TimeFrame currentTimeFrame;
    private TimeFrame comparisonTimeFrame;
    private List<Filters> filters;
    private List<SortByFields> sortByFields;
    private Pagination pagination;

    private List<FilterGroup> filterGroups;
    private BaseFilter baseFilter;

    // Currently, we have a static file which has a mapping on Service to Port-Proto
    // If we get service as a filter, we need to translate it to db readable filters
    // There will always be one service selection in the ui
    public void replaceServiceFilters(
            Map<String, List<RiskyServiceInfo.ServiceInfo>> serviceToServiceInfoMap) {
        List<Filters> newFilters = new ArrayList<>();
        List<FilterGroup> newFilterGroups = new ArrayList<>();

        if (filters != null) {
            for (Filters filter : filters) {
                if (filter.getCategoryName().equals(SERVICE.getFieldKey())) {
                    // valid Service entry
                    if (!filter.getCategoryValue().isEmpty()
                            && (filter.getCategoryValue().get(0) instanceof String)
                            && (serviceToServiceInfoMap.containsKey(
                                    filter.getCategoryValue().get(0)))) {
                        List<InnerKQLProvider> filters = new ArrayList<>();
                        for (RiskyServiceInfo.ServiceInfo riskyServiceInfo :
                                serviceToServiceInfoMap.get(filter.getCategoryValue().get(0))) {
                            /**
                             * andFilter is the Port and Protocol filters for one entry for the
                             * service
                             */
                            InnerKQLProvider andFilter =
                                    FilterGroup.builder()
                                            .filtersList(
                                                    List.of(
                                                            Filters.builder()
                                                                    .categoryType(
                                                                            PORT.getFieldType())
                                                                    .categoryName(
                                                                            PORT.getFieldKey())
                                                                    .categoryValue(
                                                                            List.of(
                                                                                    riskyServiceInfo
                                                                                            .getPort()))
                                                                    .build(),
                                                            Filters.builder()
                                                                    .categoryType(
                                                                            PROTOCOL.getFieldType())
                                                                    .categoryName(
                                                                            PROTOCOL.getFieldKey())
                                                                    .categoryValue(
                                                                            List.of(
                                                                                    riskyServiceInfo
                                                                                            .getProtocol()))
                                                                    .build()))
                                            .orRelation(false)
                                            .build();

                            filters.add(andFilter);
                        }

                        /** orFilter for all the andFilters */
                        FilterGroup filterGroup =
                                FilterGroup.builder().orRelation(true).filtersList(filters).build();

                        newFilterGroups.add(filterGroup);
                    }
                    // invalid search
                    else {
                        log.error("Invalid service filter {}", filter);
                    }
                }
                else {
                    newFilters.add(filter);
                }
            }
        }
        this.filters = newFilters;
        this.filterGroups = newFilterGroups;
    }
}
