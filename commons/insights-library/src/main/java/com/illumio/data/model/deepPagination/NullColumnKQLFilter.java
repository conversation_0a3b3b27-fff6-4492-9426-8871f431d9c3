package com.illumio.data.model.deepPagination;

import com.illumio.data.model.generalFilters.LeafFilter;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * A filter that generates custom KQL for special cases like null values
 */
@Setter
@Getter
@Slf4j
public class NullColumnKQLFilter extends LeafFilter {
    public static final String IS_NULL = "isnull";
    public static final String IS_NOT_NULL = "isnotnull";
    private String operation;

    /**
     * Constructor for CustomKQLFilter
     * @param field The field to apply the operation to
     * @param isNull True if the operation is isnull, false if it is isnotnull
     */
    public NullColumnKQLFilter(String field, boolean isNull) {
        this.setField(field);
        if (isNull) {
            this.operation = IS_NULL;
        } else {
            this.operation = IS_NOT_NULL;
        }
    }

    @Override
    public String buildKQL() {
        if (this.getDBColumn() == null) {
            log.error("Can not build KQL for invalid field {}", this.getField());
            return "";
        }

        // For null checks in Kusto, we use the isnull/isnotnull functions
        return "%s(%s)".formatted(this.operation, this.getDBColumn());
    }
}
