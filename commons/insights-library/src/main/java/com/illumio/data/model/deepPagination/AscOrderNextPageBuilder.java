package com.illumio.data.model.deepPagination;

import com.illumio.data.model.generalFilters.BaseFilter;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class AscOrderNextPageBuilder extends NextPageBuilder {

    public AscOrderNextPageBuilder(DpMetaData metaData) {
        super(metaData);
    }

    @Override
    BaseFilter getAdditionalFilter() {
        List<AnchorRows> anchorRows = this.getMetaData().getAnchorRows();
        if (this.targetPageOutOfRange()){
            if (anchorRows.isEmpty()){
                log.error("Invalid data {}", this.getMetaData());
            }
            AnchorRows lastAnchorRow = anchorRows.get(anchorRows.size() - 1);
            String anchorValue = lastAnchorRow.getLastValue();

            // last value is null, for out of range, do NOT have additional filter
            if (lastAnchorRow.isLastValueNull()){
                return null;
            }
            return super.generateGreaterThanFilter(anchorValue);
        } else {
            if (anchorRows.size() < 2){
                log.error("Invalid data {}", this.getMetaData());
            }
            return super.generateInBetweenFilter(anchorRows.get(anchorRows.size()-2));
        }
    }

    @Override
    int getRowCountToSkip() {
        String targetValue;
        int startingPage;
        boolean useFirstRow;
        boolean isTargetValueNull;

        List<AnchorRows> anchorRows = this.getMetaData().getAnchorRows();
        // start from First page -> current page, go next
        if (this.targetPageOutOfRange()){
            if (anchorRows.isEmpty()){
                log.error("Invalid data {}", this.getMetaData());
            }
            AnchorRows anchorRow = anchorRows.get(anchorRows.size()-1);
            targetValue = anchorRow.getLastValue();
            isTargetValueNull = anchorRow.isLastValueNull();
            startingPage = anchorRow.getPageNumber();
            useFirstRow = false;
        }
        // start from last page -> current page, go next
        else {
            if (anchorRows.size() < 3){
                log.error("Invalid data {}", this.getMetaData());
            }
            AnchorRows anchorRow = anchorRows.get(anchorRows.size()-2);
            targetValue = anchorRow.getLastValue();
            isTargetValueNull = anchorRow.isLastValueNull();
            startingPage = anchorRows.get(anchorRows.size()-3).getPageNumber();
            useFirstRow = true;
        }
        return super.getAccumulatedDuplicatedCount(targetValue, startingPage, useFirstRow, isTargetValueNull);
    }
}
