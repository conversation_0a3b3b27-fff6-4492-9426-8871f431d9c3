package com.illumio.data.model.generalFilters;

import com.illumio.data.model.Filters;
import com.illumio.data.model.InOutBoundFilterDecorator;
import com.illumio.data.model.SrcDestZoneFilter;
import com.illumio.data.model.constants.Fields;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.stream.Collectors;

@Setter
@Getter
@Slf4j
public class InFilter extends LeafFilter {
    private List<String> values;
    private boolean ignoreCase=false;


    protected String getOperator(){
        return ignoreCase?" in~ ":" in ";
    }

    public static Filters convertToFilters(InFilter inFilter){
        return Filters.builder()
            .categoryName(inFilter.getField())
            .categoryType( Fields.getFieldByFieldKey(inFilter.getField()).getFieldType() )
            .categoryValue(inFilter.getValues().stream().map(s -> (Object)s).collect(Collectors.toList()))
            .build();
    }

    @Override
    public String buildKQL() {
        if (this.getDBColumn() == null) {
            log.error("Can not build KQL for invalid field {}", this.getField());
            return "";
        }

        if ( Fields.SERVICE.getFieldKey().equals(this.getField())) {
            return new ServiceFilter(this).buildKQL();
        }

        if ( Fields.TRAFFIC_DIRECTION.getFieldKey().equals(this.getField())) {
            Filters old = InFilter.convertToFilters(this);
            return new InOutBoundFilterDecorator(old)
                .buildInnerKQL();
        }

        if (this.getField().equals(Fields.SOURCE_ZONE.getFieldKey())
            || this.getField().equals(Fields.DESTINATION_ZONE.getFieldKey())) {
            String result = SrcDestZoneFilter.adjustSrcDestZone( convertToFilters(this));
            if (result.isEmpty()) {
                log.error("Can not get valid KQL based on source/destination zone {}", this);
            }
            return result;
        }

        String inClause = values.stream()
            .map(v -> this.isNumericType()? v: "'" + v + "'")
            .collect(Collectors.joining(", "));

        return "%s %s ( %s )".formatted(this.getDBColumn(), this.getOperator(), inClause);
    }

    /**
     * Checks if the InFilter is well-formatted.
     * A well-formatted InFilter must have a valid field and a non-null, non-empty values list.
     *
     * @return a Pair containing a boolean indicating if the filter is well-formatted,
     *         and a String with the error message if not well-formatted (or null if well-formatted)
     */
    @Override
    public Pair<Boolean, String> isWellFormatted() {
        Pair<Boolean, String> baseResult = super.isWellFormatted();
        if (!baseResult.getLeft()) {
            return baseResult;
        }

        if (values == null || values.isEmpty()) {
            return new ImmutablePair<>(false, "Values list is null or empty for field " + field);
        }

        return new ImmutablePair<>(true, null);
    }
}