package com.illumio.data.model;

import com.illumio.data.model.constants.Fields;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Slf4j
public class Filters extends Default<PERSON>ieldTransfer implements KQLProvider, InnerKQLProvider {
    public static final String STRING_CATEGORY = "string";

    private String categoryType;
    private String categoryName;
    private List<Object> categoryValue;

    private boolean notIgnoreCase;

    @Override
    public String buildKQL() {
        String innerKQL = this.buildInnerKQL();
        if (innerKQL == null || innerKQL.isEmpty()) {
            return "";
        }

        return "| where " + this.buildInnerKQL();
    }

    @Override
    public String buildInnerKQL() {
        // special handling for source/destination zone
        if (getCategoryName().equals(Fields.SOURCE_ZONE.getFieldKey())
                || getCategoryName().equals(Fields.DESTINATION_ZONE.getFieldKey())) {
            String result = SrcDestZoneFilter.adjustSrcDestZone(this);
            if (result.isEmpty()) {
                log.error("Can not get valid KQL based on source/destination zone {}", this);
            }
            return result;
        }

        String dbColumn = this.transformToKQLColumn(this.categoryName);
        if (dbColumn.isEmpty()) {
            return "";
        }

        String operator = notIgnoreCase ? " in " : " in~ ";

        return "%s %s (%s)".formatted(dbColumn, operator, this.getMatchValues());
    }

    public boolean isStringType() {
        return categoryType.equalsIgnoreCase(STRING_CATEGORY);
    }

    private String getMatchValues() {
        return categoryValue.stream()
                .map(
                        value -> {
                            if (!this.isStringType()) {
                                return value.toString();
                            } else {
                                return "'" + value + "'";
                            }
                        })
                .collect(Collectors.joining(", "));
    }
}
