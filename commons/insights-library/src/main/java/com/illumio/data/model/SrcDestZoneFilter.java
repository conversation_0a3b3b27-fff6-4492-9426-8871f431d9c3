package com.illumio.data.model;

import com.illumio.data.model.constants.Fields;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.tuple.Pair;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class SrcDestZoneFilter {
    /**
     * while migrating to the general filters, this logic is migrated to the @InFilter class
     */
    public static final String INTERNAL_ZONE_PREFIX = "Internal ";
    public static final String INTERNAL_AZURE_ZONE_POSTFIX = "Azure";
    public static final String INTERNAL_AZURE_ZONE = "%s%s".formatted(INTERNAL_ZONE_PREFIX, INTERNAL_AZURE_ZONE_POSTFIX);
    public static final String UNKNOWN_ZONE = "Unknown";

    // Cloud Provider mapping, key is the database column srcCloudProvider/destCloudProvider
    // value is the sourceZone/destinationZone
    public static final Map<String, String> CLOUD_PROVIDER_MAP =
            Map.of(
                    "aws", "AWS",
                    "microsoft", "Azure",
                    "azure", "Azure",
                    "oracle", "OCI",
                    "oci", "OCI",
                    "google", "GCP",
                    "gcp", "GCP",
                    "linode", "Linode",
                    "digitalocean", "DigitalOcean",
                    "cloudflare", "Cloudflare");

    private static final Map<String, List<String>> CLOUD_PROVIDER_REVSERVE_MAP =
            CLOUD_PROVIDER_MAP.entrySet().stream()
                    .collect(
                            Collectors.groupingBy(
                                    Map.Entry::getValue, // Group by value
                                    Collectors.mapping(
                                            Map.Entry::getKey,
                                            Collectors.collectingAndThen(
                                                    Collectors.toList(),
                                                    list -> { // Sort each list
                                                        Collections.sort(list);
                                                        return list;
                                                    }))));

    public static String adjustSrcDestZone(Filters filters) {
        if (filters == null) {
            return null;
        }

        if (filters.getCategoryName().equals(Fields.SOURCE_ZONE.getFieldKey())) {
            return adjustZone(filters, true);
        }

        if (filters.getCategoryName().equals(Fields.DESTINATION_ZONE.getFieldKey())) {
            return adjustZone(filters, false);
        }

        return "";
    }

    // format is "Internal XXX"
    private static Pair<Boolean, String> getInternalZone(String rawValue){
        // "Internal",  "Internal " , "internal"
        if (rawValue.toLowerCase().trim().equals(INTERNAL_ZONE_PREFIX.toLowerCase().trim())) {
            return Pair.of(true, "");
        }

        if (!rawValue.toLowerCase().startsWith(INTERNAL_ZONE_PREFIX.toLowerCase())) {
            return Pair.of(false, rawValue);
        }

        return Pair.of(true, rawValue.substring(INTERNAL_ZONE_PREFIX.length()).trim());
    }

    private static String getProviderIgnoreCase(String provider) {
        return CLOUD_PROVIDER_REVSERVE_MAP.entrySet().stream()
            .filter(entry -> entry.getKey().equalsIgnoreCase(provider))
            .findFirst()
            .map(entry -> entry.getValue().stream()
                .map(v -> "'" + v + "'")
                .collect(Collectors.joining(",")))
            .orElse(null);
    }

    private static String adjustZone(Filters filters, boolean isSource) {
        Fields resourceID = Fields.SOURCE_RESOURCE_ID;
        Fields cloudProvider = Fields.SOURCE_CLOUD_PROVIDER;
        if (!isSource) {
            resourceID = Fields.DESTINATION_RESOURCE_ID;
            cloudProvider = Fields.DESTINATION_CLOUD_PROVIDER;
        }

        String rawValue = filters.getCategoryValue().get(0).toString();

        Pair<Boolean, String> internalZone = getInternalZone(rawValue);
        if (internalZone.getLeft()) {
            return " %s != '' and %s =~ '%s'".formatted(resourceID.getTableColumnName(),
                cloudProvider.getTableColumnName(), internalZone.getRight());
        }

        String provider = getProviderIgnoreCase(rawValue);
        if (provider != null) {
            return " %s == '' and %s in~ (%s)"
                    .formatted(resourceID.getTableColumnName(), cloudProvider.getTableColumnName(), provider);
        }

        if (rawValue.equals(UNKNOWN_ZONE) || rawValue.trim().isEmpty()) {
            return " %s == '' and %s ==''"
                    .formatted(resourceID.getTableColumnName(), cloudProvider.getTableColumnName());
        }

        return "";
    }
}
