package com.illumio.data.model.generalFilters;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.illumio.data.model.KQLProvider;
import com.illumio.data.model.WellFormattedInput;

import org.apache.commons.lang3.tuple.Pair;

/**
 * Supported filter types:
 *      IN / NOT_IN: Can be case-sensitive or ignore case
 *      GREATER (greater than), LESSER (less than): Support includeEqual flag.
 *      LOGIC: Combines filters with AND or OR logic via andRelation flag.
 *
 * Notes:
 *      NOT_EQUAL: Use NOT_IN with single value
 *      IN_BETWEEN: Combine GR(min) and LE(max).
 *      Fields can be nested via recursive LogicFilter.
 *
 *
 *  Json Example
 * {
 *    "operand":"logic",
 *    "andRelation":false,
 *    "filters":[
 *       {
 *          "operand":"logic",
 *          "andRelation":true,
 *          "filters":[
 *             {
 *                "operand":"in",
 *                "field":"flows",
 *                "values":[
 *                   "60",
 *                   "520"
 *                ],
 *                "ignoreCase":true
 *             },
 *             {
 *                "operand":"not_in",
 *                "field":"protocol",
 *                "values":[
 *                   "tcp",
 *                   "udp"
 *                ],
 *                "ignoreCase":false
 *             }
 *          ]
 *       },
 *       {
 *          "operand":"logic",
 *          "andRelation":true,
 *          "filters":[
 *             {
 *                "operand":"greater",
 *                "field":"port",
 *                "value":"21000",
 *                "includeEqual":false
 *             },
 *             {
 *                "operand":"lesser",
 *                "field":"port",
 *                "value":"42000",
 *                "includeEqual":true
 *             }
 *          ]
 *       }
 *    ]
 * }
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "operand")
@JsonSubTypes({
    @JsonSubTypes.Type(value = InFilter.class, name = "in"),
    @JsonSubTypes.Type(value = StartsWithFilter.class, name = "starts_with"),
    @JsonSubTypes.Type(value = EmptyCheckFilter.class, name = "empty_check"),
    @JsonSubTypes.Type(value = NotInFilter.class, name = "not_in"),
    @JsonSubTypes.Type(value = GreaterFilter.class, name = "greater"),
    @JsonSubTypes.Type(value = LesserFilter.class, name = "lesser"),
    @JsonSubTypes.Type(value = LogicFilter.class, name = "logic")
})
public abstract class BaseFilter implements KQLProvider, WellFormattedInput {
    /**
     * Validates if the filter is well-formatted.
     * Each filter implementation should check if all required fields are properly set.
     * For example, GreaterFilter should check if field and value are not null.
     *
     * @return a Pair containing a boolean indicating if the filter is well-formatted,
     *         and a String with the error message if not well-formatted (or null if well-formatted)
     */
    @JsonIgnore
    public abstract Pair<Boolean, String> isWellFormatted();
}
