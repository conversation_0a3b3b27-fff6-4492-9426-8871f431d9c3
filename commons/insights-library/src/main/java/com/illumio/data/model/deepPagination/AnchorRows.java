package com.illumio.data.model.deepPagination;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonPOJOBuilder;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonDeserialize(builder = AnchorRows.AnchorRowsBuilder.class)
public class AnchorRows {
    @JsonPOJOBuilder(withPrefix = "")
    public static class AnchorRowsBuilder {
    }

    // 1 for first page, 2 for 2nd
    // -1 for last page, -2 for (last-1 page)
    private int pageNumber;

    private String firstValue;
    private String lastValue;
    private int firstDupCount; // first row duplicated count in that page
    private int lastDupCount;  // last row duplicated count in that page

    /**
     * Check if the first value is null
     * @return true if the first value is null
     */
    @JsonIgnore
    public boolean isFirstValueNull() {
        return this.firstValue == null;
    }

    /**
     * Check if the last value is null
     * @return true if the last value is null
     */
    @JsonIgnore
    public boolean isLastValueNull() {
        return this.lastValue == null;
    }
}
