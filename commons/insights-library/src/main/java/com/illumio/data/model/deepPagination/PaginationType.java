package com.illumio.data.model.deepPagination;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Only support following Type of pagination without page jump
 */
public enum PaginationType {
    FIRST,
    LAST,
    NEXT,
    PREV;

    @JsonCreator
    public static PaginationType fromString(String key) {
        try {
            return PaginationType.valueOf(key.toUpperCase());
        } catch (Exception e) {
            return FIRST;
        }
    }

    @JsonValue
    public String toValue() {
        return this.name();
    }

//    @JsonIgnore
//    public boolean isReverseOrder() {
//        return this == PREV || this == LAST;
//    }
}