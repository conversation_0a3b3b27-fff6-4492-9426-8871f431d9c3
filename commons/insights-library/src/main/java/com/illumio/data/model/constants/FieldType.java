package com.illumio.data.model.constants;

/**
 * Defined the FieldType Enum instead of hardcode based on string
 */
public enum FieldType {
    STRING, NUMBER, TIMESTAMP;

    public static FieldType fromString(String s) {
        for (FieldType status : values()) {
            if (status.name().equalsIgnoreCase(s)) {
                return status;
            }
        }
        return STRING;
    }

    public String toString() {
        return name().toLowerCase();
    }
}
