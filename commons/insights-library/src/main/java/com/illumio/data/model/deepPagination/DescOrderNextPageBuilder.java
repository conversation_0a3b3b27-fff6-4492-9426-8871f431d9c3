package com.illumio.data.model.deepPagination;

import com.illumio.data.model.generalFilters.BaseFilter;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class DescOrderNextPageBuilder extends Asc<PERSON>rderNextPageBuilder {

    public DescOrderNextPageBuilder(DpMetaData metaData) {
        super(metaData);
    }

    @Override
    BaseFilter getAdditionalFilter() {
        List<AnchorRows> anchorRows = this.getMetaData().getAnchorRows();
        if (this.targetPageOutOfRange()){
            if (anchorRows.isEmpty()){
                log.error("Invalid data {}", this.getMetaData());
            }
            AnchorRows lastAnchorRow = anchorRows.get(anchorRows.size() - 1);
            String anchorValue = lastAnchorRow.getLastValue();
            // last value is null, for out of range, do NOT have additional filter
            if (lastAnchorRow.isLastValueNull()){
                return null;
            }
            return super.generateLesserThanFilter(anchorValue);
        } else {
            if (anchorRows.size() < 2){
                log.error("Invalid data {}", this.getMetaData());
            }
            return super.generateInBetweenFilter(anchorRows.get(anchorRows.size()-2));
        }
    }
}

