package com.illumio.data.configuration;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Primary
@ConfigurationProperties(prefix = "insights-config")
@Configuration
@Data
public class InsightsServiceConfiguration {
    private final KustoConfiguration kustoConfig = new KustoConfiguration();
    private final RiskyServiceConfig riskyServiceConfig = new RiskyServiceConfig();
    private final LlmServiceConfig llmServiceConfig = new LlmServiceConfig();
    private final PaginationConfig paginationConfig = new PaginationConfig();
    private final RedisConfig redisConfig = new RedisConfig();
    private final JwtConfig jwtConfig = new JwtConfig();

    @Configuration
    @Getter
    @Setter
    public static class KustoConfiguration {
        private String azureClientId;
        private String azureClientSecret;
        private String azureTenantId;
        private Boolean isManagedIdentity;
        private String managedIdentityClientId;
        private String clusterUri;
        private String database;
        private String rawTable;
        private String aggregatedTable;
        private Boolean useIngestionTime;
    }

    @Configuration
    @Getter
    @Setter
    public static class RiskyServiceConfig {
        private String riskyServiceFilePath;
    }

    @Configuration
    @Getter
    @Setter
    public static class LlmServiceConfig {
        private String llmServiceFilePath;
    }

    @Configuration
    @Getter
    @Setter
    public static class JwtConfig {
        private Boolean enableJwt;
        private Boolean isSymmetricKey;
    }

    @Configuration
    @Getter
    @Setter
    public static class PaginationConfig {
        private int maxPageSize;
        private int defaultPageSize;
        private int defaultPageNumber;
        private int topResultHardLimit;
        private Boolean enableScatterGather;
    }

    @Configuration
    @Getter
    @Setter
    public static class RedisConfig {
        private String host;
        private Integer port;
        private String password;
        private Boolean useSsl;
        private Long commandTimeoutMs;
        private Boolean enableLandingPageCache;
        private Long ttlInMinutes;
        private Boolean inClusterMode;
        private String clusterHosts;
    }
}
