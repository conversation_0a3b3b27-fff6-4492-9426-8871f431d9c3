package com.illumio.data.model.deepPagination;

import com.illumio.data.model.generalFilters.BaseFilter;

public class LastPageInforBuilder extends AbstractPageBuilder{

    public LastPageInforBuilder(DpMetaData metaData) {
        super(metaData);
    }

    @Override
    public DpTargetPageInfor build() {
        return super.toLastPage();
    }

    @Override
    BaseFilter getAdditionalFilter() {
        return null;
    }

    @Override
    int getRowCountToSkip() {
        return 0;
    }

    @Override
    int getTargetPageNumber() {
        return this.getMetaData().getTotalPages();
    }
}
