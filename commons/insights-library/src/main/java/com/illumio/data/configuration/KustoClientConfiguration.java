package com.illumio.data.configuration;

import com.microsoft.azure.kusto.data.Client;
import com.microsoft.azure.kusto.data.ClientFactory;
import com.microsoft.azure.kusto.data.StringUtils;
import com.microsoft.azure.kusto.data.auth.ConnectionStringBuilder;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
@Slf4j
public class KustoClientConfiguration {
    private final InsightsServiceConfiguration inventoryConfig;

    @Bean
    @SneakyThrows
    public Client ingestClient() {
        ConnectionStringBuilder connectionStringBuilder;
        if (Optional.ofNullable(inventoryConfig.getKustoConfig())
                .map(InsightsServiceConfiguration.KustoConfiguration::getIsManagedIdentity)
                .orElse(false)) {
            if (StringUtils.isBlank(
                    inventoryConfig.getKustoConfig().getManagedIdentityClientId())) {
                log.info("Use cluster uri {} to create kusto client with Null managed identity client id.",
                        inventoryConfig.getKustoConfig().getClusterUri());
                connectionStringBuilder =
                        ConnectionStringBuilder.createWithAadManagedIdentity(
                                inventoryConfig.getKustoConfig().getClusterUri());
            } else {
                log.info("Use cluster uri {} to create kusto client with managed identity client id {}",
                    inventoryConfig.getKustoConfig().getClusterUri(),
                    inventoryConfig.getKustoConfig().getManagedIdentityClientId());
                connectionStringBuilder =
                        ConnectionStringBuilder.createWithAadManagedIdentity(
                                inventoryConfig.getKustoConfig().getClusterUri(),
                                inventoryConfig.getKustoConfig().getManagedIdentityClientId());
            }
        } else {
            log.info("Use cluster uri {} to create kusto client with application credentials.",
                    inventoryConfig.getKustoConfig().getClusterUri());
            connectionStringBuilder =
                    ConnectionStringBuilder.createWithAadApplicationCredentials(
                            inventoryConfig.getKustoConfig().getClusterUri(),
                            inventoryConfig.getKustoConfig().getAzureClientId(),
                            inventoryConfig.getKustoConfig().getAzureClientSecret(),
                            inventoryConfig.getKustoConfig().getAzureTenantId());
        }
        return ClientFactory.createClient(connectionStringBuilder);
    }
}
