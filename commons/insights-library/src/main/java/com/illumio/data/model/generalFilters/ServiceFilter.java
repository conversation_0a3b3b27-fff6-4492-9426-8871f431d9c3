package com.illumio.data.model.generalFilters;

import static com.illumio.data.model.constants.Fields.PORT;
import static com.illumio.data.model.constants.Fields.PROTOCOL;

import com.illumio.data.model.KQLProvider;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.service.RiskyServiceInfo;
import com.illumio.data.service.RiskyServiceInfo.ServiceInfo;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class ServiceFilter implements KQLProvider {
    private final InFilter inFilter;

    public static RiskyServiceInfo riskyServiceInfo;

    public ServiceFilter(InFilter inFilter) {
        this.inFilter = inFilter;
    }

    @Override
    public String buildKQL() {
        if (inFilter == null || inFilter.getField() == null ||
            !inFilter.getField().equals(Fields.SERVICE.getFieldKey())) { return ""; }

        // service name should be only have single value
        if (this.inFilter.getValues() == null || this.inFilter.getValues().size() != 1) {
            log.error("Can ONLY generate service filters with single values {}, but with ",
                this.inFilter.getValues());
            return "";
        }

        if (riskyServiceInfo == null) {
            log.error("riskyServiceInfo is null, can not generate service filters");
            return "";
        }

        Map<String, List<ServiceInfo>> serviceToServiceInfoMap = riskyServiceInfo.getServicetoServiceInfoMap();
        List<BaseFilter> filters = new ArrayList<>();
        for (RiskyServiceInfo.ServiceInfo riskyServiceInfo :
            serviceToServiceInfoMap.get(this.inFilter.getValues().get(0))) {
            /**
             * And relationship for the Port and Protocol filters for one entry for the
             * service
             */

            InFilter port = new InFilter();
            port.setField(PORT.getFieldKey());
            port.setValues(List.of(riskyServiceInfo.getPort() + ""));

            InFilter proto = new InFilter();
            proto.setField(PROTOCOL.getFieldKey());
            proto.setIgnoreCase(true);
            proto.setValues(List.of(riskyServiceInfo.getProtocol()));

            LogicFilter logicFilter = new LogicFilter();
            logicFilter.setAndRelation(true);
            logicFilter.setFilters(List.of(port, proto));

            filters.add(logicFilter);
        }

        /**
         * Or relationship for all entries
         */
        LogicFilter root = new LogicFilter();
        root.setAndRelation(false);
        root.setFilters(filters);

        return root.buildKQL();
    }
}
