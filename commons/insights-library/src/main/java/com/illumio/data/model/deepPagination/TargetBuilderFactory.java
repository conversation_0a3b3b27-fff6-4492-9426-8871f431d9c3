package com.illumio.data.model.deepPagination;

public class TargetBuilderFactory {
    public static DpTargetPageBuilder createBuilder(DpMetaData metaData, PaginationType type) {
        switch (type) {
            case FIRST:
                return new FirstPageInforBuilder(metaData);
            case LAST:
                return new LastPageInforBuilder(metaData);
            case PREV:
                if (metaData.getPrimarySortBy().isAscending()) {
                    return new AscOrderPrevPageBuilder(metaData);
                }
                else {
                    return new DescOrderPrevPageBuilder(metaData);
                }
            case NEXT:
                if (metaData.getPrimarySortBy().isAscending()) {
                    return new AscOrderNextPageBuilder(metaData);
                }
                else {
                    return new DescOrderNextPageBuilder(metaData);
                }
        }

        // should NOT come here
        return null;
    }

}
