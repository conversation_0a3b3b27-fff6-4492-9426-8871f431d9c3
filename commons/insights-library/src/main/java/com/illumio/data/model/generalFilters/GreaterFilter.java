package com.illumio.data.model.generalFilters;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

@Setter
@Getter
@Slf4j
public class GreaterFilter extends LeafFilter {
    private String value;
    private boolean includeEqual=false;

    protected String getOperator(){
        return includeEqual?" >= ":" > ";
    }

    @Override
    public String buildKQL() {
        if (this.getDBColumn() == null) {
            log.error("Can not build KQL for invalid field {}", this.getField());
            return "";
        }

        if (this.isNumericType()){
            return "%s %s %s ".formatted(this.getDBColumn(), this.getOperator(), value);
        }

        if (this.isTimestampType()){
            return " %s %s datetime('%s') ".formatted(this.getDBColumn(), this.getOperator(), value);
        }

        // strcmp (Proto, "tcp") > 0
        return " strcmp (%s, '%s') %s 0 ".formatted(this.getDBColumn(), value, this.getOperator());
    }

    /**
     * Checks if the GreaterFilter is well-formatted.
     * A well-formatted GreaterFilter must have a valid field and a non-null value.
     *
     * @return a Pair containing a boolean indicating if the filter is well-formatted,
     *         and a String with the error message if not well-formatted (or null if well-formatted)
     */
    @Override
    public Pair<Boolean, String> isWellFormatted() {
        Pair<Boolean, String> baseResult = super.isWellFormatted();
        if (!baseResult.getLeft()) {
            return baseResult;
        }

        if (value == null || value.isEmpty()) {
            return new ImmutablePair<>(false, "Value is null or empty for field " + field);
        }

        return new ImmutablePair<>(true, null);
    }
}
