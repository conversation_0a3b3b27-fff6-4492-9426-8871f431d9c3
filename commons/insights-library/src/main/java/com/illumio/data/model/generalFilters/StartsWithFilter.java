package com.illumio.data.model.generalFilters;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;

@Setter
@Getter
@Slf4j
public class StartsWithFilter extends LeafFilter {
    private String value;
    private boolean ignoreCase = true;

    protected String getOperator() {
        return ignoreCase ? " startswith " : " startswith_cs ";
    }

    @Override
    public String buildKQL() {
        return "%s %s \"%s\"".formatted(this.getDBColumn(), this.getOperator(), this.value);
    }

    /**
     * Checks if the StartsWithFilter is well-formatted.
     * A well-formatted StartsWithFilter must have a valid field and a non-null, non-empty values.
     *
     * @return a Pair containing a boolean indicating if the filter is well-formatted,
     *         and a String with the error message if not well-formatted (or null if well-formatted)
     */
    @Override
    public Pair<Boolean, String> isWellFormatted() {
        Pair<Boolean, String> baseResult = super.isWellFormatted();
        if (!baseResult.getLeft()) {
            return baseResult;
        }

        if (value == null || value.isEmpty()) {
            return new ImmutablePair<>(false, "Value is null or empty for field " + field);
        }

        if (!this.isStringType()) {
            return new ImmutablePair<>(false, "Field " + field + " is not a string type");
        }

        return new ImmutablePair<>(true, null);
    }
}
