package com.illumio.data.model;

import static com.illumio.data.model.constants.Fields.TOTAL_ROWS;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.illumio.data.model.deepPagination.DpMetaData;
import com.illumio.data.model.deepPagination.DpTargetPageInfor;
import com.illumio.data.model.deepPagination.FirstPageInforBuilder;
import com.illumio.data.model.deepPagination.PaginationType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Optional;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Pagination implements KQLProvider {

    public static final String TOTAL_COUNT = "totalCountFromPagination";

    /**
     * Example of Pagination result which return only Top N rows
     *     "pagination": {
     *         "pageNumber": 1,
     *         "rowLimit": 5,
     *         "totalPages": 1,
     *         "topResultHardLimit": 100,
     *         "topResultRows": 38,
     *         "enableScatterGather": false
     *     }
     *
     * pageNumber: always 1
     * rowLimit: ignored
     * totalPages: always 1
     * topResultHardLimit: hard limit of top N rows (100)
     * topResultRows: actual results in response (38)
     *
     * enableScatterGather: whether enable the scatter and gather
     */
    private int pageNumber;

    private int rowLimit;
    private int totalPages;
    private int totalRows;

    private Integer topResultHardLimit;
    private int topResultRows;
    private Boolean enableScatterGather;

    private DpMetaData dpMetaData;
    private PaginationType paginationType;

    @ToString.Exclude // Exclude from toString()
    @EqualsAndHashCode.Exclude // Exclude from equals() and hashCode()
    @JsonIgnore // Prevents field from appearing in JSON
    @Builder.Default
    private Optional<String> overallKQLWithoutPagination = Optional.empty();

    @ToString.Exclude // Exclude from toString()
    @EqualsAndHashCode.Exclude // Exclude from equals() and hashCode()
    @JsonIgnore // Prevents field from appearing in JSON
    @Builder.Default
    private DpTargetPageInfor targetPage = null;

    @JsonIgnore
    public void buildTargetPage(){
        if (this.getTargetPage() != null) return;
        if (this.getDpMetaData() == null) {
            targetPage = new FirstPageInforBuilder(null).build();
            return;
        }

        targetPage = this.getDpMetaData().buildTargetPageInfor(this.getPaginationType());
    }


    @Override
    public String buildKQL() {
        if (overallKQLWithoutPagination.isEmpty()) {
            return "";
        }

        if (this.topResultHardLimit > 0) {
            return this.buildKQLForLargeDataset();
        }

        return this.buildLegacyKQL();
    }

    @JsonIgnore
    public boolean inReverseTraverse(){
        if (this.paginationType == PaginationType.LAST) return true;

        if (this.dpMetaData == null) return false;
        return this.dpMetaData.inReverseTraverse();
    }

    /**
     * Example
     *
     *  """
     *  DecoratedFlows
     * | where IllumioTenantId == 'f6684fc1-3d15-479f-becd-76a927cb4811'
     *  | where ingestion_time() >= datetime('2025-03-01T08:00:00')
     *  | where ingestion_time() <= datetime('2025-03-07T08:00:00')
     *  |  where SourceZone  in~  ('')
     * | order by FlowCount desc
     * | project SrcIP
     * | take 100
     *  """
     *
     * @return the KQL with pagination, with the total count as well
     * This will NOT have memory issue for big dataset
     */
    private String buildKQLForLargeDataset(){
        String actualKQL = this.overallKQLWithoutPagination.get();
        StringBuilder sb = new StringBuilder();

        // First query, need to get the total count from Kusto
        if (this.getDpMetaData() == null || this.getDpMetaData().getTotalPages() ==0) {
            sb.append(" let query = %s ;\n" .formatted(actualKQL));
            sb.append(" let %s = toscalar(query | count); \n".formatted(TOTAL_COUNT));
            sb.append("query | extend %s \n" .formatted(TOTAL_COUNT));
            sb.append("| take %s ".formatted(this.topResultHardLimit));
            return sb.toString();
        }

        sb.append(actualKQL);
        int additionalRows = 0;
        if (this.targetPage != null) {
            additionalRows = this.targetPage.getRowCountToSkip();
        }
        sb.append("\n | take %s ".formatted(this.topResultHardLimit + additionalRows));

        return sb.toString();
    }


    /**
     * Example
     *
     * <p>let temp= DecoratedFlows1 | where StartTime >= datetime(' 2025-02-08T08:00:00Z ')| where
     * EndTime <= datetime(' 2025-02-25T07:59:59Z ') | where Proto in~ ('udp') | order by
     * ReceivedBytes desc | project Port, Proto, SrcIP, DestIP ;
     *
     * <p>let c = toscalar(temp | count);
     *
     * <p>temp | extend TotalCount = c| serialize rn = row_number() | where rn between (1 .. 15 ) |
     * project-away rn
     *
     * @return the KQL with pagination
     *
     * Suitable for small dataset as the KQL, will have memory issue for big dataset
     */
    private String buildLegacyKQL(){
        String pageKQL = this.buildPageKQL();
        StringBuilder kql = new StringBuilder();
        kql.append("let temp= %s ; \n".formatted(overallKQLWithoutPagination.get()));
        kql.append("let c = toscalar(temp | count); \n");
        kql.append("temp | extend %s = c".formatted(TOTAL_ROWS.getTableColumnName()));
        kql.append(pageKQL);

        return kql.toString();
    }

    private String buildPageKQL() {
        int start = (pageNumber - 1) * rowLimit + 1; // inclusive
        int end = start + rowLimit - 1;
        return "| serialize rn = row_number() | where rn between (%s .. %s ) | project-away rn "
                .formatted(start, end);
    }
}
