package com.illumio.data.model;

import com.fasterxml.jackson.databind.ObjectMapper;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class AggregationFieldTest {

    @Test
    void testJson() throws Exception{
        AggregationField af = AggregationField.builder()
            .aggregatedBy(List.of("sent_bytes", "flows"))
            .groupBy(List.of("source_ip", "destination_ip"))
            .stepNumber(1)
            .stepUnit("hours")
            .limit(10)
            .build();

        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(af);

    String expected = """
{
   "aggregatedBy":[
      "sent_bytes",
      "flows"
   ],
   "groupBy":[
      "source_ip",
      "destination_ip"
   ],
   "stepNumber":1,
   "stepUnit":"hours",
   "limit":10
}
""";

        System.out.println(json);

        Assertions.assertEquals(expected.replaceAll("\\s+", ""),
            json.replaceAll("\\s+", ""));

        // test deserialization
        AggregationField deserialized = mapper.readValue(json, AggregationField.class);
        Assertions.assertEquals(af, deserialized);
    }
}
