package com.illumio.data.generalFilters;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.model.generalFilters.BaseFilter;
import com.illumio.data.model.generalFilters.EmptyCheckFilter;
import com.illumio.data.model.generalFilters.GreaterFilter;
import com.illumio.data.model.generalFilters.InFilter;
import com.illumio.data.model.generalFilters.LesserFilter;
import com.illumio.data.model.generalFilters.LogicFilter;
import com.illumio.data.model.generalFilters.NotInFilter;

import com.illumio.data.model.generalFilters.StartsWithFilter;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class GeneralFiltersTest {

    @Test
    public void validateEmtpyCheck() throws Exception {
        EmptyCheckFilter filter = new EmptyCheckFilter();
        filter.setField("destination_country");

        Pair<Boolean, String> wellFormatted = filter.isWellFormatted();
        String kql3 = filter.buildKQL();
        System.out.println(kql3);
        Assertions.assertEquals(" isempty (DestCountry)", kql3);
        Assertions.assertTrue(wellFormatted.getLeft());
        Assertions.assertNull(wellFormatted.getRight());

        filter.setEmpty(false);
        kql3 = filter.buildKQL();
        System.out.println(kql3);
        Assertions.assertEquals(" isnotempty (DestCountry)", kql3);
    }


    @Test
    public void validateStartsWith() throws Exception {
        StartsWithFilter startsWithFilter = new StartsWithFilter();
        startsWithFilter.setField("prev_bytes");
        startsWithFilter.setValue("u");

        Pair<Boolean, String> wellFormatted = startsWithFilter.isWellFormatted();
        Assertions.assertFalse(wellFormatted.getLeft());
        Assertions.assertNotNull(wellFormatted.getRight());
        Assertions.assertTrue(wellFormatted.getRight().contains("Field prev_bytes is not a string type"));
    }

    @Test
    public void testStartsWith() throws Exception {
        StartsWithFilter startsWithFilter = new StartsWithFilter();
        startsWithFilter.setField("destination_country");
        startsWithFilter.setValue("u");

        String kql3 = startsWithFilter.buildKQL();
        System.out.println(kql3);
        Assertions.assertEquals("DestCountry  startswith  \"u\"", kql3);

        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(startsWithFilter);

        System.out.println(json);
        /**
         * {"operand":"starts_with","field":"destination_country","value":"u","ignoreCase":false}
         */
        BaseFilter deserialized = mapper.readValue(json, BaseFilter.class);
        Assertions.assertInstanceOf(StartsWithFilter.class, deserialized);

        startsWithFilter = new StartsWithFilter();
        startsWithFilter.setField("destination_country");
        startsWithFilter.setValue("u");
        startsWithFilter.setIgnoreCase(true);

        kql3 = startsWithFilter.buildKQL();
        System.out.println(kql3);
        Assertions.assertEquals("DestCountry  startswith  \"u\"", kql3);
    }

    @Test
    public void testStringCompare() throws Exception {
        GreaterFilter protocol = new GreaterFilter();
        protocol.setField("protocol");
        protocol.setValue("tcp");

        String kql3 = protocol.buildKQL();
        System.out.println(kql3);
        Assertions.assertEquals(" strcmp (Proto, 'tcp')  >  0 ", kql3);

        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(protocol);

        System.out.println(json);
        /**
         * {
         *    "operand":"greater",
         *    "field":"protocol",
         *    "value":"tcp",
         *    "includeEqual":false
         * }
         */
        BaseFilter deserialized = mapper.readValue(json, BaseFilter.class);
        Assertions.assertInstanceOf(GreaterFilter.class, deserialized);
    }

    @Test
    public void testIngestTimeCompare() throws Exception {
        GreaterFilter protocol = new GreaterFilter();
        protocol.setField("ingestion_time");
        protocol.setValue("2025-03-27T08:00:00");

        String kql3 = protocol.buildKQL();
        System.out.println(kql3);
        Assertions.assertEquals(" ingestion_time()  >  datetime('2025-03-27T08:00:00') ", kql3);

        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(protocol);

        System.out.println(json);
        /**
         {
         "operand":"greater",
         "field":"ingestion_time",
         "value":"2025-03-27T08:00:00",
         "includeEqual":false
         }
         */
        BaseFilter deserialized = mapper.readValue(json, BaseFilter.class);
        Assertions.assertInstanceOf(GreaterFilter.class, deserialized);
    }

    @Test
    public void testSrcZone() throws Exception {
        InFilter srcZone = new InFilter();
        srcZone.setField( "source_zone");
        srcZone.setValues(List.of("GCP"));
        srcZone.setIgnoreCase( true );

        String kql3 = srcZone.buildKQL();
        System.out.println(kql3);
        Assertions.assertEquals(" SrcResId == '' and SrcCloudProvider in~ ('gcp','google')", kql3);
    }

    @Test
    public void testWrongField() throws Exception {
        GreaterFilter protocol = new GreaterFilter();
        protocol.setField("protocolXX");
        protocol.setValue("tcp");

        String kql3 = protocol.buildKQL();
        Assertions.assertEquals("", kql3);
    }

    @Test
    public void testWrongField2() throws Exception {
        GreaterFilter protocol = new GreaterFilter();
        protocol.setField("protocolXX");
        protocol.setValue("tcp");

        InFilter flows = new InFilter();
        flows.setField( "flows");
        flows.setValues(List.of("60", "520"));
        flows.setIgnoreCase( true );

        LogicFilter logic = new LogicFilter();
        logic.setAndRelation( true );
        logic.setFilters(List.of(flows, protocol));

        String kql3 = logic.buildKQL();
        Assertions.assertEquals("((FlowCount  in~  ( 60, 520 )))", kql3);
    }

    @Test
    public void testNestedCase() throws Exception{

        InFilter flows = new InFilter();
        flows.setField( "flows");
        flows.setValues(List.of("60", "520"));
        flows.setIgnoreCase( true );

        NotInFilter notProto = new NotInFilter();
        notProto.setField( "protocol");
        notProto.setValues( List.of("tcp", "udp"));
        notProto.setIgnoreCase( false );

        LogicFilter logic = new LogicFilter();
        logic.setAndRelation( true );
        logic.setFilters(List.of(flows, notProto));

        String kql = logic.buildKQL();
        System.out.println(kql);

        GreaterFilter ports = new GreaterFilter();
        ports.setField( "port");
        ports.setValue( "21000");

        GreaterFilter ports2 = new LesserFilter();
        ports2.setField( "port");
        ports2.setValue( "42000");
        ports2.setIncludeEqual( true);

        LogicFilter l2 = new LogicFilter();
        l2.setAndRelation( true );
        l2.setFilters(List.of(ports, ports2));

        String kql2 = l2.buildKQL();
        System.out.println(kql2);

        Assertions.assertEquals("((FlowCount  in~  ( 60, 520 )) and (Proto  !in  ( 'tcp', 'udp' )))", kql);
        Assertions.assertEquals("((Port  >  21000 ) and (Port  <=  42000 ))", kql2);

        LogicFilter root = new LogicFilter();
        root.setAndRelation( false );
        root.setFilters(List.of(logic, l2));
        String kql3 = root.buildKQL();
        System.out.println(kql3);
        Assertions.assertEquals("((((FlowCount  in~  ( 60, 520 )) and (Proto  !in  ( 'tcp', 'udp' )))) or (((Port  >  21000 ) and (Port  <=  42000 ))))", kql3);

        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(root);

        // Test the new toString() method
        System.out.println("=== Testing toString() method ===");
        System.out.println(root.toString());

        System.out.println(json);
        /**
         *
         * {
         *    "operand":"logic",
         *    "andRelation":false,
         *    "filters":[
         *       {
         *          "operand":"logic",
         *          "andRelation":true,
         *          "filters":[
         *             {
         *                "operand":"in",
         *                "field":"flows",
         *                "values":[
         *                   "60",
         *                   "520"
         *                ],
         *                "ignoreCase":true
         *             },
         *             {
         *                "operand":"not_in",
         *                "field":"protocol",
         *                "values":[
         *                   "tcp",
         *                   "udp"
         *                ],
         *                "ignoreCase":false
         *             }
         *          ]
         *       },
         *       {
         *          "operand":"logic",
         *          "andRelation":true,
         *          "filters":[
         *             {
         *                "operand":"greater",
         *                "field":"port",
         *                "value":"21000",
         *                "includeEqual":false
         *             },
         *             {
         *                "operand":"lesser",
         *                "field":"port",
         *                "value":"42000",
         *                "includeEqual":true
         *             }
         *          ]
         *       }
         *    ]
         * }
         *
         */
        BaseFilter deserialized = mapper.readValue(json, BaseFilter.class);
        Assertions.assertInstanceOf(LogicFilter.class, deserialized);
        LogicFilter d = (LogicFilter) deserialized;
        Assertions.assertFalse(d.isAndRelation());
        Assertions.assertEquals(2, d.getFilters().size());
    }

    @Test
    public void testNestedCaseForWellFormatted() throws Exception {

        InFilter flows = new InFilter();
        flows.setField("flows");
        flows.setValues(List.of("60", "520"));
        flows.setIgnoreCase(true);

        NotInFilter notProto = new NotInFilter();
        notProto.setField("protocol");
        notProto.setValues(null);
        notProto.setIgnoreCase(false);

        LogicFilter logic = new LogicFilter();
        logic.setAndRelation(true);
        logic.setFilters(List.of(flows, notProto));

        Pair<Boolean, String> result = logic.isWellFormatted();
        Assertions.assertFalse(result.getLeft());
        Assertions.assertNotNull(result.getRight());
        Assertions.assertTrue(result.getRight().contains("Values list"));
    }

    @Test
    public void testIsWellFormatted() {
        // Test GreaterFilter
        GreaterFilter validGreaterFilter = new GreaterFilter();
        validGreaterFilter.setField("port");
        validGreaterFilter.setValue("8080");
        Pair<Boolean, String> validGreaterResult = validGreaterFilter.isWellFormatted();
        Assertions.assertTrue(validGreaterResult.getLeft());
        Assertions.assertNull(validGreaterResult.getRight());

        GreaterFilter invalidGreaterFilter1 = new GreaterFilter();
        invalidGreaterFilter1.setField("port");
        // Missing value
        Pair<Boolean, String> invalidGreater1Result = invalidGreaterFilter1.isWellFormatted();
        Assertions.assertFalse(invalidGreater1Result.getLeft());
        Assertions.assertNotNull(invalidGreater1Result.getRight());
        Assertions.assertTrue(invalidGreater1Result.getRight().contains("Value"));

        GreaterFilter invalidGreaterFilter2 = new GreaterFilter();
        // Missing field
        invalidGreaterFilter2.setValue("8080");
        Pair<Boolean, String> invalidGreater2Result = invalidGreaterFilter2.isWellFormatted();
        Assertions.assertFalse(invalidGreater2Result.getLeft());
        Assertions.assertNotNull(invalidGreater2Result.getRight());
        Assertions.assertTrue(invalidGreater2Result.getRight().contains("Field"));

        GreaterFilter invalidGreaterFilter3 = new GreaterFilter();
        invalidGreaterFilter3.setField("invalidField"); // Invalid field
        invalidGreaterFilter3.setValue("8080");
        Pair<Boolean, String> invalidGreater3Result = invalidGreaterFilter3.isWellFormatted();
        Assertions.assertFalse(invalidGreater3Result.getLeft());
        Assertions.assertNotNull(invalidGreater3Result.getRight());
        Assertions.assertTrue(invalidGreater3Result.getRight().contains("Invalid field"));

        // Test InFilter
        InFilter validInFilter = new InFilter();
        validInFilter.setField("protocol");
        validInFilter.setValues(List.of("tcp", "udp"));
        Pair<Boolean, String> validInResult = validInFilter.isWellFormatted();
        Assertions.assertTrue(validInResult.getLeft());
        Assertions.assertNull(validInResult.getRight());

        InFilter invalidInFilter1 = new InFilter();
        invalidInFilter1.setField("protocol");
        // Missing values
        Pair<Boolean, String> invalidIn1Result = invalidInFilter1.isWellFormatted();
        Assertions.assertFalse(invalidIn1Result.getLeft());
        Assertions.assertNotNull(invalidIn1Result.getRight());
        Assertions.assertTrue(invalidIn1Result.getRight().contains("Values"));

        InFilter invalidInFilter2 = new InFilter();
        // Missing field
        invalidInFilter2.setValues(List.of("tcp", "udp"));
        Pair<Boolean, String> invalidIn2Result = invalidInFilter2.isWellFormatted();
        Assertions.assertFalse(invalidIn2Result.getLeft());
        Assertions.assertNotNull(invalidIn2Result.getRight());
        Assertions.assertTrue(invalidIn2Result.getRight().contains("Field"));

        // Test LogicFilter
        LogicFilter validLogicFilter = new LogicFilter();
        validLogicFilter.setFilters(List.of(validGreaterFilter, validInFilter));
        Pair<Boolean, String> validLogicResult = validLogicFilter.isWellFormatted();
        Assertions.assertTrue(validLogicResult.getLeft());
        Assertions.assertNull(validLogicResult.getRight());

        LogicFilter invalidLogicFilter1 = new LogicFilter();
        // Missing filters
        Pair<Boolean, String> invalidLogic1Result = invalidLogicFilter1.isWellFormatted();
        Assertions.assertFalse(invalidLogic1Result.getLeft());
        Assertions.assertNotNull(invalidLogic1Result.getRight());
        Assertions.assertTrue(invalidLogic1Result.getRight().contains("Filters list"));

        LogicFilter invalidLogicFilter2 = new LogicFilter();
        invalidLogicFilter2.setFilters(List.of(invalidGreaterFilter1, validInFilter));
        // Contains invalid filter
        Pair<Boolean, String> invalidLogic2Result = invalidLogicFilter2.isWellFormatted();
        Assertions.assertFalse(invalidLogic2Result.getLeft());
        Assertions.assertNotNull(invalidLogic2Result.getRight());
        Assertions.assertTrue(invalidLogic2Result.getRight().contains("Child filter"));
    }

    @Test
    public void testToStringMethod() {
        // Create a simple InFilter
        InFilter simpleFilter = new InFilter();
        simpleFilter.setField("protocol");
        simpleFilter.setValues(List.of("tcp", "udp"));
        simpleFilter.setIgnoreCase(true);

        System.out.println("Simple InFilter:");
        System.out.println(simpleFilter.toString());

        // Create a GreaterFilter
        GreaterFilter greaterFilter = new GreaterFilter();
        greaterFilter.setField("port");
        greaterFilter.setValue("8080");
        greaterFilter.setIncludeEqual(true);

        System.out.println("\nGreaterFilter:");
        System.out.println(greaterFilter.toString());

        // Create a complex nested LogicFilter
        LogicFilter complexFilter = new LogicFilter();
        complexFilter.setAndRelation(false); // OR relation
        complexFilter.setFilters(List.of(simpleFilter, greaterFilter));

        System.out.println("\nComplex nested LogicFilter:");
        System.out.println(complexFilter.toString());

        // Verify the output contains expected information
        String output = complexFilter.toString();
        Assertions.assertTrue(output.contains("LogicFilter"));
        Assertions.assertTrue(output.contains("relation=OR"));
        Assertions.assertTrue(output.contains("InFilter"));
        Assertions.assertTrue(output.contains("GreaterFilter"));
        Assertions.assertTrue(output.contains("field='protocol'"));
        Assertions.assertTrue(output.contains("field='port'"));
    }
}
