package com.illumio.data.generalFilters;

import static org.mockito.Mockito.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.when;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.generalFilters.InFilter;
import com.illumio.data.model.generalFilters.ServiceFilter;
import com.illumio.data.service.RiskyServiceInfo;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;

public class ServiceFilterTest {
    @Test
    void testServiceFilter() {
        InsightsServiceConfiguration config = Mockito.mock(InsightsServiceConfiguration.class, RETURNS_DEEP_STUBS);
        when(config.getRiskyServiceConfig().getRiskyServiceFilePath()).thenReturn("classpath:risky_services.csv");

        RiskyServiceInfo serviceInfo = new RiskyServiceInfo(config);

        ServiceFilter.riskyServiceInfo = serviceInfo;

        InFilter inFilter = new InFilter();
        inFilter.setField(Fields.SERVICE.getFieldKey());
        inFilter.setValues(List.of("RustDesk"));

        ServiceFilter serviceFilter = new ServiceFilter(inFilter);
        String kql = serviceFilter.buildKQL();
        System.out.println(kql);
        Assertions.assertEquals(
                "((((Port  in  ( 21114 )) and (Proto  in~  ( 'TCP' )))) or (((Port  in  ( 21114 )) and (Proto  in~  ( 'UDP' )))) or (((Port  in  ( 21115 )) and (Proto  in~  ( 'TCP' )))) or (((Port  in  ( 21115 )) and (Proto  in~  ( 'UDP' )))) or (((Port  in  ( 21116 )) and (Proto  in~  ( 'TCP' )))) or (((Port  in  ( 21116 )) and (Proto  in~  ( 'UDP' )))) or (((Port  in  ( 21117 )) and (Proto  in~  ( 'TCP' )))) or (((Port  in  ( 21117 )) and (Proto  in~  ( 'UDP' )))) or (((Port  in  ( 21118 )) and (Proto  in~  ( 'TCP' )))) or (((Port  in  ( 21118 )) and (Proto  in~  ( 'UDP' )))) or (((Port  in  ( 21119 )) and (Proto  in~  ( 'UDP' )))) or (((Port  in  ( 21119 )) and (Proto  in~  ( 'TCP' )))))",
                kql);
    }
}
