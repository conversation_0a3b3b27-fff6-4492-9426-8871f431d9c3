package com.illumio.data.mockKusto;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;

import java.io.*;
import java.nio.file.*;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * An in-memory Kusto database simulator that supports basic Kusto query operations.
 */
public class MockKustoDatabase {
    private final Map<String, MockKustoTable> tables;

    public MockKustoDatabase() {
        this.tables = new HashMap<>();
    }

    /**
     * Creates a new table with the specified schema.
     */
    public void createTable(String tableName, Map<String, Class<?>> schema) {
        tables.put(tableName, new MockKustoTable(tableName, schema));
    }

    /**
     * Ingests data from a CSV resource file into a table.
     */
    public void ingestFromCsv(String tableName, String resourcePath) throws IOException {
        MockKustoTable table = getTable(tableName);

        try (InputStream is = getClass().getResourceAsStream(resourcePath);
             BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {

            String headerLine = reader.readLine();
            String[] headers = parseCsvLine(headerLine);
            String line;
            while ((line = reader.readLine()) != null) {
                String[] values = parseCsvLine(line);
                if (values.length != headers.length) {
                    System.err.println("Invalid CSV line: " + line);
                    continue;
                }
                Map<String, Object> row = new HashMap<>();

                for (int i = 0; i < headers.length; i++) {
                    String header = headers[i].trim().replace("\"", "").replace("\uFEFF", "");
                    String value = values[i].trim().replace("\"", "");
                    Class<?> type = table.getSchema().get(header);
                    row.put(header, convertToType(value, type));
                }

                table.addRow(row);
            }
        }
    }

    private String[] parseCsvLine(String csvLine) {
        try {
            List<String> values = new ArrayList<>();
            CSVParser parser = CSVFormat.DEFAULT.parse(new StringReader(csvLine));
            for (CSVRecord record : parser) {
                for (String value : record) {
                    values.add(value);
                }
            }
            return values.toArray(new String[0]);
        }
        catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * Executes a Kusto query on the specified table.
     */
    public List<Map<String, Object>> executeQuery(String query) {
        KustoQueryParser parser = new KustoQueryParser(query);
        MockKustoTable table = getTable(parser.getTableName());

        List<Map<String, Object>> result = new ArrayList<>(table.getRows());

        // Apply filters
        if (parser.hasFilter()) {
            result = result.stream()
                .filter(row -> evaluateFilterConditions(row, parser.getFilterConditions()))
                .collect(Collectors.toList());
        }

        // Apply column selection
        if (parser.hasSelectedColumns()) {
            result = result.stream()
                .map(row -> selectColumns(row, parser.getSelectedColumns()))
                .collect(Collectors.toList());
        }

        // Apply sorting
        if (parser.hasSortColumn()) {
            String sortColumn = parser.getSortColumn();
            boolean ascending = parser.isSortAscending();
            result.sort((a, b) -> {
                Object valueA = a.get(sortColumn);
                Object valueB = b.get(sortColumn);

                // Handle null values in sorting
                if (valueA == null && valueB == null) {
                    return 0;
                } else if (valueA == null) {
                    return ascending ? -1 : 1; // Nulls first for ascending, last for descending
                } else if (valueB == null) {
                    return ascending ? 1 : -1; // Nulls first for ascending, last for descending
                }

                if (valueA instanceof Comparable && valueB instanceof Comparable) {
                    return ascending ?
                        ((Comparable) valueA).compareTo(valueB) :
                        ((Comparable) valueB).compareTo(valueA);
                }

                // If not comparable, use string representation
                return ascending ?
                    valueA.toString().compareTo(valueB.toString()) :
                    valueB.toString().compareTo(valueA.toString());
            });
        }

        return result;
    }

    /**
     * Gets a table by name. Throws IllegalArgumentException if the table doesn't exist.
     */
    public MockKustoTable getTable(String tableName) {
        MockKustoTable table = tables.get(tableName);
        if (table == null) {
            throw new IllegalArgumentException("Table not found: " + tableName);
        }
        return table;
    }

    private Object convertToType(String value, Class<?> type) {
        if (value == null || value.isEmpty()) {
            return null;
        }

        if (type == String.class) {
            return value;
        } else if (type == Integer.class) {
            return parseWithUSFormat(value, type);
        } else if (type == Long.class) {
            return parseWithUSFormat(value, type);
        } else if (type == Double.class) {
            return parseWithUSFormat(value, type);
        } else if (type == Boolean.class) {
            return Boolean.parseBoolean(value);
        } else if (type == LocalDateTime.class) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSS");
            return LocalDateTime.parse(value, formatter);
        }

        throw new IllegalArgumentException("Unsupported type: " + type.getSimpleName());
    }

    private Object parseWithUSFormat(String input, Class<?> type){
        try {
            NumberFormat format = NumberFormat.getInstance(Locale.US);
            Number number = format.parse(input);
            if (type == Integer.class) {
                return number.intValue();
            } else if (type == Long.class) {
                return number.longValue();
            } else if (type == Double.class) {
                return number.doubleValue();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        throw new IllegalArgumentException("Unsupported type: " + type.getSimpleName());
    }

    private boolean evaluateFilter(Map<String, Object> row, Map<String, Object> filter) {
        return filter.entrySet().stream()
            .allMatch(entry -> Objects.equals(row.get(entry.getKey()), entry.getValue()));
    }

    private boolean evaluateFilterConditions(Map<String, Object> row, List<KustoQueryParser.FilterCondition> conditions) {
        return conditions.stream()
            .allMatch(condition -> condition.evaluate(row));
    }

    private Map<String, Object> selectColumns(Map<String, Object> row, List<String> columns) {
        return columns.stream()
            .collect(Collectors.toMap(
                col -> col,
                col -> row.get(col),
                (v1, v2) -> v1,
                LinkedHashMap::new
            ));
    }
}