package com.illumio.data.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class FilterGroupTest {

    private List<InnerKQLProvider> getInnerKQLProviders() {
        Filters protocol =
                Filters.builder()
                        .categoryType("string")
                        .categoryName("protocol")
                        .categoryValue(List.of("tcp", "udp"))
                        .build();

        Filters port =
                Filters.builder()
                        .categoryType("integer")
                        .categoryName("port")
                        .categoryValue(List.of(21117, 21118))
                        .build();

        return List.of(protocol, port);
    }

    @Test
    void testKQLWithOr() {
        FilterGroup filterGroup =
                FilterGroup.builder()
                        .filtersList(this.getInnerKQLProviders())
                        .orRelation(true)
                        .build();

        String kql = filterGroup.buildKQL();

        Assertions.assertEquals(
                "| where (Proto  in~  ('tcp', 'udp')) or (Port  in~  (21117, 21118))", kql);
        Assertions.assertEquals(
                "(Proto  in~  ('tcp', 'udp')) or (Port  in~  (21117, 21118))",
                filterGroup.buildInnerKQL());
    }

    @Test
    void testKQLWithAnd() {
        FilterGroup filterGroup =
                FilterGroup.builder()
                        .filtersList(this.getInnerKQLProviders())
                        .orRelation(false)
                        .build();

        String kql = filterGroup.buildKQL();

        Assertions.assertEquals(
                "| where (Proto  in~  ('tcp', 'udp')) and (Port  in~  (21117, 21118))", kql);
        Assertions.assertEquals(
                "(Proto  in~  ('tcp', 'udp')) and (Port  in~  (21117, 21118))",
                filterGroup.buildInnerKQL());
    }
}
