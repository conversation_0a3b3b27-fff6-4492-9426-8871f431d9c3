package com.illumio.data.model;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

public class TimeFrameTest {

    @Test
    void testKQLNotUseIngestTime() {
        TimeFrame tf =
                TimeFrame.builder()
                        .startTime("2025-02-15T08:00:00Z")
                        .endTime("2025-02-23T07:59:59Z")
                        .build();

        String kql = tf.buildKQL();
        assertEquals(
                " | where todatetime(StartTime) >= datetime('2025-02-15T08:00:00Z')| where todatetime(EndTime) <= datetime('2025-02-23T07:59:59Z')",
                kql);
    }

    @Test
    void testKQLUseIngestTime() {
        TimeFrame tf =
                TimeFrame.builder()
                        .startTime("2025-02-15T08:00:00Z")
                        .endTime("2025-02-23T07:59:59Z")
                        .useIngestionTime(true)
                        .build();

        String kql = tf.buildKQL();
        assertEquals(
                " | where ingestion_time() >= datetime('2025-02-15T08:00:00Z')| where ingestion_time() <= datetime('2025-02-23T07:59:59Z')",
                kql);
    }
}
