package com.illumio.data.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class SortByFieldsTest {
    @Test
    void testKQL() {
        SortByFields sb = SortByFields.builder().field("received_bytes").order("desc").build();

        String kql = sb.buildKQL();
        Assertions.assertEquals(" ReceivedBytes desc ", kql);
    }

    @Test
    void testBytesSort() {
        SortByFields sb = SortByFields.builder().field("bytes").order("desc").build();

        String kql = sb.buildKQL();
        Assertions.assertEquals(" SentBytes desc ", kql);
    }

    @Test
    void testAdjustedFields() {
        SortByFields sb = SortByFields.builder().field("bytes").order("desc").build();

        String adjusted = sb.getAdjustedField();
        Assertions.assertEquals("sent_bytes", adjusted);
    }

    @Test
    void testMultipleFields() {
        SortByFields sb = SortByFields.builder().field("bytes").order("desc").build();
        SortByFields sb2 = SortByFields.builder().field("flows").order("asc").build();

        String combined = SortByFields.buildKQLForMultiples(List.of(sb, sb2));
        Assertions.assertEquals("| sort by  SentBytes desc , FlowCount asc ", combined);
    }
}
