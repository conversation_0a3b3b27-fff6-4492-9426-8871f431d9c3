package com.illumio.data.db;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.illumio.data.model.constants.Fields;

import lombok.Getter;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Used to test the column name in Fields matches the column name in DB
 *
 * Database table schema is defined in the DecoratedFlows_Schema.csv file, exported from Kusto cmd:
 *
 * DecoratedFlows
 * | getschema
 *
 */
public class FieldsColumnTest {
    @Test
    void testFieldsColumnNames() {
        InputStream inputStream = getClass().getClassLoader().getResourceAsStream("DecoratedFlows_Schema.csv");
        assertNotNull(inputStream, "Resource file not found");

        Map<String, DBSchema> schemaMap = new BufferedReader(new InputStreamReader(inputStream))
            .lines()
            .skip(1) // skip header
            .map(DBSchema::from)
            .collect(Collectors.toMap(DBSchema::getColumnName, e -> e));

        for(Fields field : Fields.values()) {
            if (!schemaMap.containsKey(field.getTableColumnName())) {
                continue;
            }

            DBSchema dbSchema = schemaMap.get(field.getTableColumnName());
            if (!dbSchema.getColumnType().equalsIgnoreCase(field.getFieldType())) {
                if (field.isNumericField() && dbSchema.isNumeric()) {
                    continue;
                }

                // bool can be treated as string
                // source_well_known and destination_well_known
                if (field.isStringField() && dbSchema.isBool()) {
                    continue;
                }

                Assertions.fail("Data type mismatch for field " + field.getFieldKey() + " expected " + field.getFieldType() + " but found " + dbSchema.getColumnType());
            }

        }
    }
}

// "ColumnName","ColumnOrdinal","DataType","ColumnType"
class DBSchema{
    @Getter
    protected final String ColumnName;
    protected final int ColumnOrdinal;

    @Getter
    protected final String DataType;
    @Getter
    protected final String ColumnType;

    public DBSchema(String ColumnName, int ColumnOrdinal, String DataType, String ColumnType) {
        this.ColumnName = ColumnName;
        this.ColumnOrdinal = ColumnOrdinal;
        this.DataType = DataType;
        this.ColumnType = ColumnType;
    }

    public boolean isNumeric() {
        return ColumnType.equalsIgnoreCase("int") || ColumnType.equalsIgnoreCase("long");
    }

    public boolean isBool() {
        return ColumnType.equalsIgnoreCase("bool");
    }

    public static DBSchema from(String line) {
        line = line.replace("\"", "");
        String[] parts = line.split(",");
        return new DBSchema(parts[0], Integer.parseInt(parts[1]), parts[2], parts[3]);
    }

}
