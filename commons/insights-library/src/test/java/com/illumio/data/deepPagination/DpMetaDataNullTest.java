package com.illumio.data.deepPagination;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.deepPagination.AnchorRows;
import com.illumio.data.model.deepPagination.DpMetaData;
import com.illumio.data.model.deepPagination.DpTargetPageInfor;
import com.illumio.data.model.deepPagination.PaginationType;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class DpMetaDataNullTest {

    @Test
    void testAscFromFirstPageWithNulls() throws Exception {
        /**
         * Page 1: value null, null, null
         * Page 2: value null, null, 3
         * Page 3: value 3, 4, 5
         * Page 4: value 5, 6, 7
         * Page 5: ...
         */

        AnchorRows p1 = AnchorRows.builder()
            .pageNumber(1)
            .firstValue(null)
            .lastValue(null)
            .firstDupCount(3)
            .lastDupCount(3)
            .build();

        AnchorRows p2 = AnchorRows.builder()
            .pageNumber(2)
            .firstValue(null)
            .lastValue("3")
            .firstDupCount(2)
            .lastDupCount(1)
            .build();

        AnchorRows p3 = AnchorRows.builder()
            .pageNumber(3)
            .firstValue("3")
            .lastValue("5")
            .firstDupCount(1)
            .lastDupCount(1)
            .build();

        SortByFields sb = SortByFields.builder().field("flows").order("asc").build();

        DpMetaData metaData = DpMetaData.builder()
            .totalPages(5)
            .currPage(3)
            .primarySortBy(sb)
            .anchorRows(List.of(p1, p2, p3))
            .build();

        Assertions.assertTrue(metaData.sanityCheck());

        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(metaData);

        System.out.println(json);
        DpMetaData deserialized = mapper.readValue(json, DpMetaData.class);
        Assertions.assertTrue(deserialized.sanityCheck());

        // on 3rd page, go next
        // out of range
        DpTargetPageInfor infor = metaData.buildTargetPageInfor(PaginationType.NEXT);
        Assertions.assertNotNull(infor);
        Assertions.assertEquals(4, infor.getTargetPageNumber());

        String filterKQL = infor.getAdditionalFilter().buildKQL();
        System.out.println(filterKQL);
        // For values greater than the last value on the current page
        Assertions.assertEquals("FlowCount  >=  5 ", filterKQL);
        Assertions.assertEquals(1, infor.getRowCountToSkip());
        Assertions.assertEquals(4, infor.getTargetPageNumber());
        Assertions.assertTrue(infor.isOutOfRange());

        // on 3rd page, go prev
        // in range
        infor = metaData.buildTargetPageInfor(PaginationType.PREV);
        Assertions.assertNotNull(infor);
        filterKQL = infor.getAdditionalFilter().buildKQL();
        System.out.println(filterKQL);
        Assertions.assertEquals("((FlowCount  <=  3 ) or (isnull(FlowCount)))", filterKQL);
        Assertions.assertEquals(3, infor.getRowCountToSkip());
        Assertions.assertEquals(2, infor.getTargetPageNumber());
        Assertions.assertFalse(infor.isOutOfRange());
    }

    @Test
    void testDescFromLastPageWithAllNullsInCurrentPage() throws Exception {
        /**
         * Page 1: value 11, 10, 9
         * Page 2: value 9, 8, 7
         * Page 3: value null, null, null
         * Page 4: value null, null, null
         * Page 5: value null, null, null
         */
        AnchorRows p1 =
                AnchorRows.builder()
                        .pageNumber(-1)
                        .firstValue(null)
                        .lastValue(null)
                        .firstDupCount(3)
                        .lastDupCount(3)
                        .build();

        AnchorRows p2 =
            AnchorRows.builder()
                .pageNumber(-2)
                .firstValue(null)
                .lastValue(null)
                .firstDupCount(3)
                .lastDupCount(3)
                .build();

        AnchorRows p3 =
            AnchorRows.builder()
                .pageNumber(-3)
                .firstValue(null)
                .lastValue(null)
                .firstDupCount(3)
                .lastDupCount(3)
                .build();

        SortByFields sb = SortByFields.builder().field("flows").order("desc").build();

        DpMetaData metaData = DpMetaData.builder()
            .totalPages(5)
            .currPage(-3)
            .primarySortBy(sb)
            .anchorRows(List.of(p1, p2, p3))
            .build();

        Assertions.assertTrue(metaData.sanityCheck());

        // From last page -> 3rd page, then next
        // in range
        DpTargetPageInfor infor = metaData.buildTargetPageInfor(PaginationType.NEXT);
        Assertions.assertNotNull(infor);
        String filterKQL = infor.getAdditionalFilter().buildKQL();
        System.out.println(filterKQL);
        Assertions.assertEquals("isnull(FlowCount)", filterKQL);
        Assertions.assertEquals(3, infor.getRowCountToSkip());
        Assertions.assertEquals(-2, infor.getTargetPageNumber());
        Assertions.assertFalse(infor.isOutOfRange());

        // From last page -> 3rd page, then go prev
        // out of range
        infor = metaData.buildTargetPageInfor(PaginationType.PREV);
        Assertions.assertNotNull(infor);

        // no additional filter
        Assertions.assertNull(infor.getAdditionalFilter());
        Assertions.assertEquals(9, infor.getRowCountToSkip());
        Assertions.assertEquals(-4, infor.getTargetPageNumber());
        Assertions.assertTrue(infor.isOutOfRange());
    }

    @Test
    void testDescFromLastPageWithNulls() throws Exception {
        /**
         * Page 1: value 11, 10, 9
         * Page 2: value 9, 8, 7
         * Page 3: value 7, 6, 5
         * Page 4: value 5, 4, null
         * Page 5: value null, null, null
         */

        AnchorRows p1 = AnchorRows.builder()
            .pageNumber(-1)
            .firstValue(null)
            .lastValue(null)
            .firstDupCount(3)
            .lastDupCount(3)
            .build();

        AnchorRows p2 = AnchorRows.builder()
            .pageNumber(-2)
            .firstValue("5")
            .lastValue(null)
            .firstDupCount(1)
            .lastDupCount(1)
            .build();

        AnchorRows p3 = AnchorRows.builder()
            .pageNumber(-3)
            .firstValue("7")
            .lastValue("5")
            .firstDupCount(1)
            .lastDupCount(1)
            .build();

        SortByFields sb = SortByFields.builder().field("flows").order("desc").build();

        DpMetaData metaData = DpMetaData.builder()
            .totalPages(5)
            .currPage(-3)
            .primarySortBy(sb)
            .anchorRows(List.of(p1, p2, p3))
            .build();

        Assertions.assertTrue(metaData.sanityCheck());

        // From last page -> 3nd page, go next
        // in range
        DpTargetPageInfor infor = metaData.buildTargetPageInfor(PaginationType.NEXT);
        Assertions.assertNotNull(infor);
        String filterKQL = infor.getAdditionalFilter().buildKQL();
        System.out.println(filterKQL);
        Assertions.assertEquals("((FlowCount  <=  5 ) or (isnull(FlowCount)))", filterKQL);
        Assertions.assertEquals(3, infor.getRowCountToSkip());
        Assertions.assertEquals(-2, infor.getTargetPageNumber());
        Assertions.assertFalse(infor.isOutOfRange());

        // From last page -> 2nd page, go prev
        // out of range
        infor = metaData.buildTargetPageInfor(PaginationType.PREV);
        Assertions.assertNotNull(infor);
        filterKQL = infor.getAdditionalFilter().buildKQL();
        System.out.println(filterKQL);
        Assertions.assertEquals("FlowCount  >=  7 ", filterKQL);
        Assertions.assertEquals(1, infor.getRowCountToSkip());
        Assertions.assertEquals(-4, infor.getTargetPageNumber());
        Assertions.assertTrue(infor.isOutOfRange());
    }
}
