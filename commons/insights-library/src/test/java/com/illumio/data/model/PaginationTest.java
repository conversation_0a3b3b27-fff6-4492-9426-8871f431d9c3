package com.illumio.data.model;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

import java.util.Optional;

public class PaginationTest {
    @Test
    void testKQLWithOverallKQL() {
        Pagination p =
                Pagination.builder()
                        .rowLimit(10)
                        .pageNumber(3)
                        .topResultHardLimit(-1)
                        .overallKQLWithoutPagination(
                                Optional.of("DecoratedFlows1| where Proto  in~  ('tcp') "))
                        .build();

        assertEquals(
                "let temp= DecoratedFlows1| where Proto  in~  ('tcp')  ; \n"
                        + "let c = toscalar(temp | count); \n"
                        + "temp | extend totalRows = c| serialize rn = row_number() | where rn between (21 .. 30 ) | project-away rn ",
                p.buildKQL());
    }

    @Test
    void testKQLWithoutOverallKQL() {
        Pagination p = Pagination.builder().rowLimit(10).pageNumber(3).topResultHardLimit(-1).build();

        assertEquals("", p.buildKQL());
    }

    @Test
    void testKQLWithLargeDataSet() {
        Pagination p =
            Pagination.builder()
                .rowLimit(10)
                .pageNumber(3)
                .topResultHardLimit(100)
                .overallKQLWithoutPagination(
                    Optional.of("DecoratedFlows1| where Proto  in~  ('tcp') "))
                .build();

        String kql = p.buildKQL();

        String expectedKQL =
                """
 let query = DecoratedFlows1| where Proto  in~  ('tcp')  ;
 let totalCountFromPagination = toscalar(query | count);\s
query | extend totalCountFromPagination\s
| take 100\s
""";

        assertEquals(kql.replaceAll("\\s+", ""),
            expectedKQL.replaceAll("\\s+", ""));
    }
}
