package com.illumio.data.mockKusto;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.*;

import java.io.IOException;
import java.util.*;

public class MockKustoFlowTableTest {
    private MockKustoDatabase db;
    private static final String FLOW_TABLE = "DecoratedFlows";

    @BeforeEach
    void setUp() {
        db = MockKustoFlowTable.createKustoDatabase();

    }

    @Test
    void testDataIngestion() throws IOException {
        String query = FLOW_TABLE;
        List<Map<String, Object>> result = db.executeQuery(query);
        assertFalse(result.isEmpty());
        assertEquals(200, result.size());
    }

    @Test
    void testProjection() throws IOException {
        String query2 =
            FLOW_TABLE
                + " | where Port >= 135"
                + " | project SrcIP, SrcId, SentBytes "
                + " | sort by  SentBytes desc";

        List<Map<String, Object>> result2 = db.executeQuery(query2);
        assertEquals(200, result2.size());

        query2 =
            FLOW_TABLE
                + " | where Port > 135"
                + " | project SrcIP, SrcId, SentBytes "
                + " | sort by  SentBytes desc";

        result2 = db.executeQuery(query2);
        assertEquals(0, result2.size());

        String query1 =
            FLOW_TABLE
                /**
                 * TODO syntax error in query need to be fixed, actual query is
                 * where ingestion_time() > datetime('2025-05-19T18:15:25.3140')
                 * but the "()" mess up with the regex
                 */
                + " | where ingestion_time() > datetime('2025-05-19T18:15:25.3140')"
                + " | project SrcIP, SrcId, SentBytes "
                + " | sort by  SentBytes desc";

        List<Map<String, Object>> result1 = db.executeQuery(query1);
        assertEquals(170, result1.size());



        String query = FLOW_TABLE + " | where Port in (135) ";
        List<Map<String, Object>> result = db.executeQuery(query);
        assertEquals(200, result.size());

        query = FLOW_TABLE + " | where Port !in (135) ";
        result = db.executeQuery(query);
        assertEquals(0, result.size());

        query = FLOW_TABLE + " | where SrcIP in (\"***********\") | project SrcIP, DestIP, SentBytes | sort by  SentBytes asc";
        result = db.executeQuery(query);
        assertEquals(29, result.size());
        for(int i=0; i<result.size()-1; i++){
            assertTrue((Long) result.get(i).get("SentBytes") <= (Long) result.get(i+1).get("SentBytes"));
            assertEquals("***********", result.get(i).get("SrcIP"));
        }

        query = FLOW_TABLE + " | where (SrcIP in (\"***********\") and SrcId in (\"D27CE85C472B\")) | project SrcIP, DestIP, SentBytes | sort by  SentBytes asc";
        result = db.executeQuery(query);
        assertEquals(2, result.size());

        query =
                FLOW_TABLE
                        + " | where (SrcIP in (\"***********\") and SrcId in (\"D27CE85C472B\")) or (SrcIP in (\"************\")) "
                        + " | project SrcIP, SrcId, SentBytes "
                        + " | sort by  SentBytes desc";

        result = db.executeQuery(query);
        for(int i=0; i<result.size()-1; i++){
            assertTrue((Long) result.get(i).get("SentBytes") >= (Long) result.get(i+1).get("SentBytes"));
            String srcIP = (String) result.get(i).get("SrcIP");
            String srcId = (String) result.get(i).get("SrcId");
            assertTrue(srcIP.equals("***********") && srcId.equals("D27CE85C472B") || srcIP.equals("************"));
        }

        assertEquals(32, result.size());
    }

}
