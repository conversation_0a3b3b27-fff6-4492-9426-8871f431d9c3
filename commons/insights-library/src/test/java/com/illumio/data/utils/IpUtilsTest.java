package com.illumio.data.utils;

import static com.illumio.data.utils.IpType.*;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

public class IpUtilsTest {

    // Tests for the new getIpType method
    @Test
    void testPrivateIPv4Addresses_ShouldReturnPrivate() {
        // Site-local addresses (RFC 1918)
        assertEquals(PRIVATE, IpUtils.getIpType("********"), "******** should be private");
        assertEquals(PRIVATE, IpUtils.getIpType("**************"), "************** should be private");
        assertEquals(PRIVATE, IpUtils.getIpType("**********"), "********** should be private");
        assertEquals(PRIVATE, IpUtils.getIpType("**************"), "************** should be private");
        assertEquals(PRIVATE, IpUtils.getIpType("***********"), "*********** should be private");
        assertEquals(PRIVATE, IpUtils.getIpType("***************"), "*************** should be private");

        // Loopback addresses
        assertEquals(PRIVATE, IpUtils.getIpType("127.0.0.1"), "127.0.0.1 should be private (loopback)");
        assertEquals(PRIVATE, IpUtils.getIpType("***************"), "*************** should be private (loopback)");

        // Link-local addresses (RFC 3927)
        assertEquals(PRIVATE, IpUtils.getIpType("***********"), "*********** should be private (link-local)");
        assertEquals(PRIVATE, IpUtils.getIpType("***************"), "*************** should be private (link-local)");
    }

    @Test
    void testPublicIPv4Addresses_ShouldReturnPublic() {
        assertEquals(PUBLIC, IpUtils.getIpType("*******"), "******* should be public");
        assertEquals(PUBLIC, IpUtils.getIpType("*******"), "******* should be public");
        assertEquals(PUBLIC, IpUtils.getIpType("**************"), "************** should be public");
        assertEquals(PUBLIC, IpUtils.getIpType("*************"), "************* should be public");
        assertEquals(PUBLIC, IpUtils.getIpType("***************"), "*************** should be public");

        // Edge cases around private ranges
        assertEquals(PUBLIC, IpUtils.getIpType("*************"), "************* should be public (just before 10.x.x.x)");
        assertEquals(PUBLIC, IpUtils.getIpType("********"), "******** should be public (just after 10.x.x.x)");
        assertEquals(PUBLIC, IpUtils.getIpType("**************"), "************** should be public (just before 172.16.x.x)");
        assertEquals(PUBLIC, IpUtils.getIpType("**********"), "********** should be public (just after 172.31.x.x)");
        assertEquals(PUBLIC, IpUtils.getIpType("***************"), "*************** should be public (just before 192.168.x.x)");
        assertEquals(PUBLIC, IpUtils.getIpType("***********"), "*********** should be public (just after 192.168.x.x)");
    }

    @Test
    void testPrivateIPv6Addresses_ShouldReturnPrivate() {
        // IPv6 loopback
        assertEquals(PRIVATE, IpUtils.getIpType("::1"), "::1 should be private (IPv6 loopback)");
        assertEquals(PRIVATE, IpUtils.getIpType("0:0:0:0:0:0:0:1"), "0:0:0:0:0:0:0:1 should be private (IPv6 loopback)");

        // IPv6 link-local addresses (fe80::/10)
        assertEquals(PRIVATE, IpUtils.getIpType("fe80::1"), "fe80::1 should be private (IPv6 link-local)");
        assertEquals(PRIVATE, IpUtils.getIpType("fe80:0000:0000:0000:0000:0000:0000:0001"),
                  "fe80:0000:0000:0000:0000:0000:0000:0001 should be private (IPv6 link-local)");

        // Note: IPv6 unique local addresses (fc00::/7) are NOT considered site-local by Java's InetAddress
        // They would need to be handled separately if required
    }

    @Test
    void testPublicIPv6Addresses_ShouldReturnPublic() {
        // Public IPv6 addresses
        assertEquals(PUBLIC, IpUtils.getIpType("2001:4860:4860::8888"), "2001:4860:4860::8888 should be public (Google DNS)");
        assertEquals(PUBLIC, IpUtils.getIpType("2606:4700:4700::1111"), "2606:4700:4700::1111 should be public (Cloudflare DNS)");
        assertEquals(PUBLIC, IpUtils.getIpType("2001:db8::1"), "2001:db8::1 should be public (documentation range but not private)");

        // IPv6 unique local addresses (fc00::/7) - Java's InetAddress doesn't consider these site-local
        assertEquals(PUBLIC, IpUtils.getIpType("fc00::1"), "fc00::1 is not considered private by Java's InetAddress");
        assertEquals(PUBLIC, IpUtils.getIpType("fd00::1"), "fd00::1 is not considered private by Java's InetAddress");
    }

    @ParameterizedTest
    @ValueSource(strings = {
        "invalid.ip.address",  // Invalid format
        "256.256.256.256",     // Out of range IPv4
        "***********.1",       // Too many octets
        "192.168.1.256",       // Invalid octet value
        "not.an.ip",           // Clearly not an IP
        "192.168.1.-1",        // Negative value
        "192.168.1.abc",       // Non-numeric
        ":::",                 // Invalid IPv6
        "gggg::1",             // Invalid IPv6 hex
        "2001:db8::1::2"       // Invalid IPv6 format
    })
    void testInvalidIPAddresses_ShouldReturnUnknown(String invalidIP) {
        assertEquals(UNKNOWN, IpUtils.getIpType(invalidIP),
                   String.format("'%s' should return UNKNOWN (invalid IP)", invalidIP));
    }

    @Test
    void testSpecialInputs() {
        // Note: InetAddress.getByName(null) resolves to localhost, so it returns PRIVATE
        assertEquals(PRIVATE, IpUtils.getIpType(null), "null input resolves to localhost");

        // Note: InetAddress.getByName("") resolves to localhost, so it returns PRIVATE
        assertEquals(PRIVATE, IpUtils.getIpType(""), "empty string resolves to localhost");

        // Note: InetAddress.getByName("192.168.1") resolves to a valid address
        assertEquals(PRIVATE, IpUtils.getIpType("192.168.1"), "incomplete IP may resolve to valid private address");
    }

    @Test
    void testSpecialIPv4Addresses() {
        // Test broadcast address
        assertEquals(PUBLIC, IpUtils.getIpType("***************"), "*************** should be public (broadcast)");

        // Test network addresses
        assertEquals(PRIVATE, IpUtils.getIpType("10.0.0.0"), "10.0.0.0 should be private (network address)");
        assertEquals(PRIVATE, IpUtils.getIpType("***********"), "*********** should be private (network address)");
        assertEquals(PRIVATE, IpUtils.getIpType("**********"), "********** should be private (network address)");

        // Test zero address
        assertEquals(PUBLIC, IpUtils.getIpType("0.0.0.0"), "0.0.0.0 should be public (any address)");
    }

    @Test
    void testSpecialIPv6Addresses() {
        // Test IPv6 any address
        assertEquals(PUBLIC, IpUtils.getIpType("::"), ":: should be public (IPv6 any address)");
        assertEquals(PUBLIC, IpUtils.getIpType("0:0:0:0:0:0:0:0"), "0:0:0:0:0:0:0:0 should be public (IPv6 any address)");
    }

    @Test
    void testEdgeCasesWithWhitespace() {
        // Test that the method handles different formats correctly
        assertEquals(PRIVATE, IpUtils.getIpType("***********"), "Standard format should work");

        // These should fail because InetAddress.getByName() cannot parse them
        assertEquals(UNKNOWN, IpUtils.getIpType(" *********** "), "IP with spaces should return UNKNOWN");
        assertEquals(UNKNOWN, IpUtils.getIpType("*********** extra"), "IP with extra text should return UNKNOWN");
    }

    // Tests for backward compatibility with the deprecated isPrivateIP method
    @Test
    void testDeprecatedIsPrivateIPMethod() {
        // Test that the deprecated method still works correctly
        assertTrue(IpUtils.isPrivateIP("***********"), "Deprecated method should still work for private IPs");
        assertFalse(IpUtils.isPrivateIP("*******"), "Deprecated method should still work for public IPs");
        assertFalse(IpUtils.isPrivateIP("invalid.ip"), "Deprecated method should return false for invalid IPs");
    }

    // Additional tests specifically for the UNKNOWN enum value
    @Test
    void testUnknownIPTypes() {
        assertEquals(UNKNOWN, IpUtils.getIpType("definitely.not.an.ip"), "Clearly invalid IP should return UNKNOWN");
        assertEquals(UNKNOWN, IpUtils.getIpType("999.999.999.999"), "Out of range IP should return UNKNOWN");
        assertEquals(UNKNOWN, IpUtils.getIpType("***********.1.1"), "Too many octets should return UNKNOWN");
    }
}
