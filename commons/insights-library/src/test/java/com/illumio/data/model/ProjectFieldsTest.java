package com.illumio.data.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class ProjectFieldsTest {
    @Test
    void testKQL() {
        ProjectFields pf =
                ProjectFields.builder()
                        .projectFields(List.of("port", "protocol", "source_ip", "destination_ip"))
                        .build();

        String kql = pf.buildKQL();

        // the field name transfer to the column names based on Fields
        Assertions.assertEquals("| project Port, Proto, SrcIP, DestIP", kql);
    }
}
