package com.illumio.data.mockKusto;

import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.*;

/**
 * Parses Kusto query language into components for filtering, projection, and sorting.
 */
public class KustoQueryParser {
    private final String tableName;
    private final List<String> selectedColumns;
    private final List<FilterCondition> filterConditions;
    private String sortColumn;
    private boolean sortAscending;

    private static final Pattern TABLE_PATTERN = Pattern.compile("^([\\w]+)");
    private static final Pattern PROJECT_PATTERN = Pattern.compile("\\| project ([\\w\\s,]+)");
    private static final Pattern WHERE_PATTERN = Pattern.compile("\\| where ([^|]+)");
    private static final Pattern SORT_PATTERN = Pattern.compile("\\| sort by\\s+([\\w]+)(\\s+asc|\\s+desc)?");
    private static final Pattern CONDITION_PATTERN =
    Pattern.compile(
        "([\\w]+(?:\\(\\))?)\\s*(>=|<=|!=|=|>|<|in~|!in~|in|!in)\\s*\\(([^\\)]+)\\)"
            + "|"
            + "([\\w]+(?:\\(\\))?)\\s*(>=|<=|!=|=|>|<)\\s*([^\\s\\|]+)"
    );

    private static final Pattern NESTED_CONDITION_PATTERN = Pattern.compile("\\(([^\\(\\)]+)\\)\\s*(and|or)\\s*\\(([^\\(\\)]+)\\)", Pattern.CASE_INSENSITIVE);

    public static abstract class FilterCondition {
        public abstract boolean evaluate(Map<String, Object> row);

        // For backward compatibility with tests
        public String getColumn() {
            if (this instanceof SimpleFilterCondition) {
                return ((SimpleFilterCondition) this).column;
            }
            return null;
        }

        public String getOperator() {
            if (this instanceof SimpleFilterCondition) {
                return ((SimpleFilterCondition) this).operator;
            }
            return null;
        }

        public Object getValue() {
            if (this instanceof SimpleFilterCondition) {
                return ((SimpleFilterCondition) this).value;
            }
            return null;
        }

        // For backward compatibility with tests that call evaluate with a single value
        public boolean evaluate(Object columnValue) {
            if (this instanceof SimpleFilterCondition) {
                SimpleFilterCondition simple = (SimpleFilterCondition) this;
                Map<String, Object> row = new HashMap<>();
                row.put(simple.column, columnValue);
                return evaluate(row);
            }
            return false;
        }
    }

    public static class SimpleFilterCondition extends FilterCondition {
        private final String column;
        private final String operator;
        private final Object value;

        public SimpleFilterCondition(String column, String operator, Object value) {
            this.column = column;
            this.operator = operator;

            if (column.equals("ingestion_time()")) {
                // Regex to extract content inside single quotes after "datetime("
                Pattern pattern = Pattern.compile("datetime\\('([^']+)'\\)");
                Matcher matcher = pattern.matcher(value.toString());

                if (matcher.find()) {
                    this.value = LocalDateTime.parse(matcher.group(1));
                } else {
                    this.value = value;
                }
            } else {
                this.value = value;
            }
        }

        public String getColumn() {
            return column;
        }

        public String getOperator() {
            return operator;
        }

        public Object getValue() {
            return value;
        }

        @Override
        public boolean evaluate(Map<String, Object> row) {
            Object columnValue = row.get(column);

            if (columnValue == null) {
                return "isnull".equals(operator);
            }

            if ("=".equals(operator)) {
                return Objects.equals(columnValue, value);
            } else if ("!=".equals(operator)) {
                return !Objects.equals(columnValue, value);
            } else if (">".equals(operator)) {
                if (columnValue instanceof Comparable && value instanceof Comparable) {
                    return ((Comparable) columnValue).compareTo(value) > 0;
                }
            } else if (">=".equals(operator)) {
                if (columnValue instanceof Comparable && value instanceof Comparable) {
                    return ((Comparable) columnValue).compareTo(value) >= 0;
                }
            } else if ("<".equals(operator)) {
                if (columnValue instanceof Comparable && value instanceof Comparable) {
                    return ((Comparable) columnValue).compareTo(value) < 0;
                }
            } else if ("<=".equals(operator)) {
                if (columnValue instanceof Comparable && value instanceof Comparable) {
                    return ((Comparable) columnValue).compareTo(value) <= 0;
                }
            } else if ("isnull".equals(operator)) {
                return columnValue == null;
            } else if ("isnotnull".equals(operator)) {
                return columnValue != null;
            } else if ("in".equals(operator) || "in~".equals(operator)) {
                if (value instanceof List<?> values) {
                    if ("in".equals(operator)) {
                        return values.contains(columnValue);
                    } else { // in~ (case insensitive)
                        if (columnValue instanceof String && values.stream().allMatch(v -> v instanceof String)) {
                            String strValue = (String) columnValue;
                            return values.stream()
                                    .map(v -> ((String) v).toLowerCase())
                                    .anyMatch(v -> v.equals(strValue.toLowerCase()));
                        }
                        return values.contains(columnValue);
                    }
                }
            } else if ("!in".equals(operator) || "!in~".equals(operator)) {
                if (value instanceof List<?> values) {
                    if ("!in".equals(operator)) {
                        return !values.contains(columnValue);
                    } else { // !in~ (case insensitive)
                        if (columnValue instanceof String && values.stream().allMatch(v -> v instanceof String)) {
                            String strValue = (String) columnValue;
                            return values.stream()
                                    .map(v -> ((String) v).toLowerCase())
                                    .noneMatch(v -> v.equals(strValue.toLowerCase()));
                        }
                        return !values.contains(columnValue);
                    }
                }
            }

            return false;
        }
    }

    public static class CompositeFilterCondition extends FilterCondition {
        public final List<FilterCondition> conditions;
        public final String operator; // "and" or "or"

        public CompositeFilterCondition(List<FilterCondition> conditions, String operator) {
            this.conditions = conditions;
            this.operator = operator;
        }

        @Override
        public boolean evaluate(Map<String, Object> row) {
            if ("and".equals(operator)) {
                return conditions.stream().allMatch(condition -> condition.evaluate(row));
            } else if ("or".equals(operator)) {
                return conditions.stream().anyMatch(condition -> condition.evaluate(row));
            }
            return false;
        }
    }

    public  KustoQueryParser(String query) {
        this.selectedColumns = new ArrayList<>();
        this.filterConditions = new ArrayList<>();

        // Extract table name
        Matcher tableMatcher = TABLE_PATTERN.matcher(query);
        if (!tableMatcher.find()) {
            throw new IllegalArgumentException("Invalid query format: table name not found");
        }
        this.tableName = tableMatcher.group(1);

        // Parse project clause
        Matcher projectMatcher = PROJECT_PATTERN.matcher(query);
        if (projectMatcher.find()) {
            String[] columns = projectMatcher.group(1).split(",");
            for (String column : columns) {
                selectedColumns.add(column.trim());
            }
        }

        // Parse where clause
        Matcher whereMatcher = WHERE_PATTERN.matcher(query);
        if (whereMatcher.find()) {
            String whereClause = whereMatcher.group(1).trim();

            // Check for nested conditions with OR/AND
            if (whereClause.contains(" or ") || whereClause.contains(" OR ")) {
                // Special handling for the specific pattern: (A and B) or (C)
                Matcher nestedMatcher = NESTED_CONDITION_PATTERN.matcher(whereClause);
                if (nestedMatcher.find()) {
                    String leftCondition = nestedMatcher.group(1).trim();
                    String logicalOperator = nestedMatcher.group(2).trim().toLowerCase(); // "and" or "or"
                    String rightCondition = nestedMatcher.group(3).trim();

                    List<FilterCondition> compositeConditions = new ArrayList<>();

                    // Parse left condition group
                    List<FilterCondition> leftConditions = parseConditionGroup(leftCondition);
                    if (leftConditions.size() > 1) {
                        compositeConditions.add(new CompositeFilterCondition(leftConditions, "and"));
                    } else if (leftConditions.size() == 1) {
                        compositeConditions.add(leftConditions.get(0));
                    }

                    // Parse right condition group
                    List<FilterCondition> rightConditions = parseConditionGroup(rightCondition);
                    if (rightConditions.size() > 1) {
                        compositeConditions.add(new CompositeFilterCondition(rightConditions, "and"));
                    } else if (rightConditions.size() == 1) {
                        compositeConditions.add(rightConditions.get(0));
                    }

                    // Add the composite condition
                    if (!compositeConditions.isEmpty()) {
                        // Clear any existing conditions that might have been added
                        filterConditions.clear();
                        // Add the composite OR condition
                        filterConditions.add(new CompositeFilterCondition(compositeConditions, logicalOperator));
                    }
                } else {
                    // Handle other OR patterns
                    String[] orParts = whereClause.split("(?i)\\s+or\\s+");
                    List<FilterCondition> orConditions = new ArrayList<>();

                    for (String orPart : orParts) {
                        List<FilterCondition> andConditions = parseConditionGroup(orPart.trim());
                        if (andConditions.size() > 1) {
                            orConditions.add(new CompositeFilterCondition(andConditions, "and"));
                        } else if (andConditions.size() == 1) {
                            orConditions.add(andConditions.get(0));
                        }
                    }

                    if (!orConditions.isEmpty()) {
                        // Clear any existing conditions that might have been added
                        filterConditions.clear();
                        // Add the composite OR condition
                        filterConditions.add(new CompositeFilterCondition(orConditions, "or"));
                    }
                }
            } else {
                // Handle simple conditions (no nesting)
                String[] conditions = whereClause.split("and");
                for (String condition : conditions) {
                    parseSimpleCondition(condition.trim());
                }
            }
        }

        // Parse sort clause
        Matcher sortMatcher = SORT_PATTERN.matcher(query);
        if (sortMatcher.find()) {
            sortColumn = sortMatcher.group(1).trim();
            String direction = sortMatcher.group(2);
            sortAscending = direction == null || direction.trim().equals("asc");
        }
    }

    private List<FilterCondition> parseConditionGroup(String conditionGroup) {
        List<FilterCondition> conditions = new ArrayList<>();
        String[] parts = conditionGroup.split("and");

        for (String part : parts) {
            parseSimpleCondition(part.trim(), conditions);
        }

        return conditions;
    }

    private void parseSimpleCondition(String condition) {
        parseSimpleCondition(condition, this.filterConditions);
    }

    private void parseSimpleCondition(String condition, List<FilterCondition> targetList) {
        Matcher conditionMatcher = CONDITION_PATTERN.matcher(condition);

        if (conditionMatcher.find()) {
            // Check which pattern matched
            if (conditionMatcher.group(3) != null) {
                // This is an 'in' type operator with parentheses
                String column = conditionMatcher.group(1).trim();
                String operator = conditionMatcher.group(2).trim();
                String valueStr = conditionMatcher.group(3).trim();

                // Parse the list of values
                String[] items = valueStr.split(",");
                List<Object> values = new ArrayList<>();
                for (String item : items) {
                    String trimmedItem = item.trim();
                    // Remove quotes if present
                    if ((trimmedItem.startsWith("'") && trimmedItem.endsWith("'")) ||
                        (trimmedItem.startsWith("\"") && trimmedItem.endsWith("\""))) {
                        trimmedItem = trimmedItem.substring(1, trimmedItem.length() - 1);
                    }
                    values.add(parseValue(trimmedItem));
                }
                targetList.add(new SimpleFilterCondition(column, operator, values));
            } else {
                // This is a regular operator
                String column = conditionMatcher.group(4).trim();
                String operator = conditionMatcher.group(5).trim();
                String valueStr = conditionMatcher.group(6).trim();

                // Handle regular operators
                Object value = parseValue(valueStr);
                targetList.add(new SimpleFilterCondition(column, operator, value));
            }
        } else if (condition.contains("isnull")) {
            // Handle isnull operator
            String[] parts = condition.split("isnull");
            if (parts.length > 0) {
                String column = parts[0].trim();
                targetList.add(new SimpleFilterCondition(column, "isnull", null));
            }
        } else if (condition.contains("isnotnull")) {
            // Handle isnotnull operator
            String[] parts = condition.split("isnotnull");
            if (parts.length > 0) {
                String column = parts[0].trim();
                targetList.add(new SimpleFilterCondition(column, "isnotnull", null));
            }
        }
    }

    private Object parseValue(String value) {
        // Try to parse as different types
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e1) {
            try {
                return Double.parseDouble(value);
            } catch (NumberFormatException e2) {
                if (value.equalsIgnoreCase("true") || value.equalsIgnoreCase("false")) {
                    return Boolean.parseBoolean(value);
                }
                // Remove quotes if present
                return value.replaceAll("^[\"']|[\"']$", "");
            }
        }
    }

    public String getTableName() {
        return tableName;
    }

    public boolean hasSelectedColumns() {
        return !selectedColumns.isEmpty();
    }

    public List<String> getSelectedColumns() {
        return Collections.unmodifiableList(selectedColumns);
    }

    public boolean hasFilter() {
        return !filterConditions.isEmpty();
    }

    public List<FilterCondition> getFilterConditions() {
        return Collections.unmodifiableList(filterConditions);
    }

    /**
     * @deprecated Use getFilterConditions() instead
     */
    @Deprecated
    public Map<String, Object> getFilter() {
        // For backward compatibility
        Map<String, Object> filter = new HashMap<>();
        for (FilterCondition condition : filterConditions) {
            if (condition instanceof SimpleFilterCondition && "=".equals(condition.getOperator())) {
                filter.put(condition.getColumn(), condition.getValue());
            }
        }
        return Collections.unmodifiableMap(filter);
    }

    public boolean hasSortColumn() {
        return sortColumn != null;
    }

    public String getSortColumn() {
        return sortColumn;
    }

    public boolean isSortAscending() {
        return sortAscending;
    }
}