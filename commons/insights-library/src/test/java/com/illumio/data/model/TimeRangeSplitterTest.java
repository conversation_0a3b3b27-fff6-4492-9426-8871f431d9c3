package com.illumio.data.model;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.time.temporal.ChronoUnit;
import java.util.List;

public class TimeRangeSplitterTest {

    @Test
    void testSplitByHour() {
        String start = "2025-02-15T08:00:00Z";
        String end = "2025-02-15T12:50:59";

        List<Pair<String, String>> pairs = TimeRangeSplitter.splitIntoBuckets(start, end,
            ChronoUnit.HOURS);

        Assertions.assertEquals(5, pairs.size());

        Assertions.assertEquals("2025-02-15T08:00:00", pairs.get(0).getLeft());
        Assertions.assertEquals("2025-02-15T09:00:00", pairs.get(0).getRight());

        Assertions.assertEquals("2025-02-15T09:00:00", pairs.get(1).getLeft());
        Assertions.assertEquals("2025-02-15T10:00:00", pairs.get(1).getRight());

        Assertions.assertEquals("2025-02-15T10:00:00", pairs.get(2).getLeft());
        Assertions.assertEquals("2025-02-15T11:00:00", pairs.get(2).getRight());

        Assertions.assertEquals("2025-02-15T11:00:00", pairs.get(3).getLeft());
        Assertions.assertEquals("2025-02-15T12:00:00", pairs.get(3).getRight());

        Assertions.assertEquals("2025-02-15T12:00:00", pairs.get(4).getLeft());
        Assertions.assertEquals("2025-02-15T12:50:59", pairs.get(4).getRight());
    }

    @Test
    void testSplitByDay() {
        String start = "2025-02-15T08:00:00Z";
        String end = "2025-02-18T12:50:59";

        List<Pair<String, String>> pairs =
                TimeRangeSplitter.splitIntoBuckets(start, end, ChronoUnit.DAYS);

        Assertions.assertEquals(4, pairs.size());

        Assertions.assertEquals("2025-02-15T08:00:00", pairs.get(0).getLeft());
        Assertions.assertEquals("2025-02-16T08:00:00", pairs.get(0).getRight());

        Assertions.assertEquals("2025-02-16T08:00:00", pairs.get(1).getLeft());
        Assertions.assertEquals("2025-02-17T08:00:00", pairs.get(1).getRight());

        Assertions.assertEquals("2025-02-17T08:00:00", pairs.get(2).getLeft());
        Assertions.assertEquals("2025-02-18T08:00:00", pairs.get(2).getRight());

        Assertions.assertEquals("2025-02-18T08:00:00", pairs.get(3).getLeft());
        Assertions.assertEquals("2025-02-18T12:50:59", pairs.get(3).getRight());
    }

    @Test
    void testRoundToHour() {
        String end = "2025-02-18T12:50:59";
        String roundToHour = TimeRangeSplitter.roundToHour(end);

        Assertions.assertEquals("2025-02-18T12:00:00", roundToHour);
    }

    @Test
    void testHourDiff() {
        String start = "2025-02-15T08:00:00Z";
        String end = "2025-02-15T12:50:59";

        Assertions.assertEquals(4L, TimeRangeSplitter.hourDifference(start, end));

    }
}
