package com.illumio.data.deepPagination;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.deepPagination.AnchorRows;
import com.illumio.data.model.deepPagination.DpMetaData;
import com.illumio.data.model.deepPagination.DpTargetPageInfor;
import com.illumio.data.model.deepPagination.PaginationType;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class DpMetaDataTest {

    @Test
    void testAscFromFirstPage() throws Exception {
        /**
         * Page 1: value 1, 2, 3
         * Page 2: value 3, 3, 3,
         * Page 3: value 3, 3, 4
         * Page 4: value 4, 5, 6
         * Page 5: ...
         */

        AnchorRows p1 = AnchorRows.builder()
            .pageNumber(1)
            .firstValue("1")
            .lastValue("3")
            .firstDupCount(1)
            .lastDupCount(1)
            .build();

        AnchorRows p2 = AnchorRows.builder()
            .pageNumber(2)
            .firstValue("3")
            .lastValue("3")
            .firstDupCount(3)
            .lastDupCount(3)
            .build();

        AnchorRows p3 = AnchorRows.builder()
            .pageNumber(3)
            .firstValue("3")
            .lastValue("4")
            .firstDupCount(2)
            .lastDupCount(1)
            .build();

        SortByFields sb = SortByFields.builder().field("flows").order("asc").build();

        DpMetaData metaData = DpMetaData.builder()
            .totalPages(5)
            .currPage(3)
            .primarySortBy(sb)
            .anchorRows(List.of(p1, p2, p3))
            .build();

        Assertions.assertTrue(metaData.sanityCheck());

        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writeValueAsString(metaData);

        System.out.println(json);
        DpMetaData deserialized = mapper.readValue(json, DpMetaData.class);
        Assertions.assertTrue(deserialized.sanityCheck());

        // on 3rd page, go next
        // out of range
        DpTargetPageInfor infor = metaData.buildTargetPageInfor(PaginationType.NEXT);
        Assertions.assertNotNull(infor);
        Assertions.assertEquals(4, infor.getTargetPageNumber());


        String filterKQL = infor.getAdditionalFilter().buildKQL();
        System.out.println(filterKQL);
        Assertions.assertEquals("FlowCount  >=  4 ", filterKQL);
        Assertions.assertEquals(1, infor.getRowCountToSkip());
        Assertions.assertEquals(4, infor.getTargetPageNumber());
        Assertions.assertTrue(infor.isOutOfRange());


        // on 3rd page, go prev
        // in range
        infor = metaData.buildTargetPageInfor(PaginationType.PREV);
        Assertions.assertNotNull(infor);
        filterKQL = infor.getAdditionalFilter().buildKQL();
        System.out.println(filterKQL);
        Assertions.assertEquals("((FlowCount  >=  3 ) and (FlowCount  <=  3 ))", filterKQL);
        Assertions.assertEquals(1, infor.getRowCountToSkip());
        Assertions.assertEquals(2, infor.getTargetPageNumber());
        Assertions.assertFalse(infor.isOutOfRange());

    }

    @Test
    void testAscFromLastPage() throws Exception {

        /**
         * Page 1: ...
         * Page 2: value 1, 2, 3
         * Page 3: value 3, 3, 3,
         * Page 4: value 3, 3, 4
         * Page 5: value 4, 5, 6
         */

        AnchorRows p1 =
                AnchorRows.builder()
                        .pageNumber(-1)
                        .firstValue("4")
                        .lastValue("6")
                        .firstDupCount(1)
                        .lastDupCount(1)
                        .build();

        AnchorRows p2 =
                AnchorRows.builder()
                        .pageNumber(-2)
                        .firstValue("3")
                        .lastValue("4")
                        .firstDupCount(2)
                        .lastDupCount(1)
                        .build();

        AnchorRows p3 =
                AnchorRows.builder()
                        .pageNumber(-3)
                        .firstValue("3")
                        .lastValue("3")
                        .firstDupCount(3)
                        .lastDupCount(3)
                        .build();

        SortByFields sb = SortByFields.builder().field("flows").order("asc").build();

        DpMetaData metaData = DpMetaData.builder()
            .totalPages(5)
            .currPage(-3)
            .primarySortBy(sb)
            .anchorRows(List.of(p1, p2, p3))
            .build();

        Assertions.assertTrue(metaData.sanityCheck());

        // From last page -> 3nd page, go next
        // in range
        DpTargetPageInfor infor = metaData.buildTargetPageInfor(PaginationType.NEXT);
        Assertions.assertNotNull(infor);
        String filterKQL = infor.getAdditionalFilter().buildKQL();
        System.out.println(filterKQL);
        Assertions.assertEquals("((FlowCount  >=  3 ) and (FlowCount  <=  4 ))", filterKQL);
        Assertions.assertEquals(1, infor.getRowCountToSkip());
        Assertions.assertEquals(-2, infor.getTargetPageNumber());
        Assertions.assertFalse(infor.isOutOfRange());


        // From last page -> 2nd page, go prev
        // out of range
        infor = metaData.buildTargetPageInfor(PaginationType.PREV);
        Assertions.assertNotNull(infor);
        filterKQL = infor.getAdditionalFilter().buildKQL();
        System.out.println(filterKQL);
        Assertions.assertEquals("FlowCount  <=  3 ", filterKQL);
        Assertions.assertEquals(5, infor.getRowCountToSkip());
        Assertions.assertEquals(-4, infor.getTargetPageNumber());
        Assertions.assertTrue(infor.isOutOfRange());

    }

    @Test
    void testDescFromFirstPage() throws Exception {
        /**
         * Page 1: value 11, 10, 9
         * Page 2: value 9, 9, 9,
         * Page 3: value 9, 8, 8
         * Page 4: value 7, 6, 4
         * Page 5: value 3, 2, 1
         */

        AnchorRows p1 = AnchorRows.builder()
            .pageNumber(1)
            .firstValue("11")
            .lastValue("9")
            .firstDupCount(1)
            .lastDupCount(1)
            .build();

        AnchorRows p2 = AnchorRows.builder()
            .pageNumber(2)
            .firstValue("9")
            .lastValue("9")
            .firstDupCount(3)
            .lastDupCount(3)
            .build();

        AnchorRows p3 = AnchorRows.builder()
            .pageNumber(3)
            .firstValue("9")
            .lastValue("8")
            .firstDupCount(1)
            .lastDupCount(2)
            .build();

        SortByFields sb = SortByFields.builder().field("flows").order("desc").build();

        DpMetaData metaData = DpMetaData.builder()
            .totalPages(5)
            .currPage(3)
            .primarySortBy(sb)
            .anchorRows(List.of(p1, p2, p3))
            .build();

        Assertions.assertTrue(metaData.sanityCheck());

        // on 3rd page, go next
        // out of range
        DpTargetPageInfor infor = metaData.buildTargetPageInfor(PaginationType.NEXT);
        Assertions.assertNotNull(infor);
        Assertions.assertEquals(4, infor.getTargetPageNumber());


        String filterKQL = infor.getAdditionalFilter().buildKQL();
        System.out.println(filterKQL);
        Assertions.assertEquals("FlowCount  <=  8 ", filterKQL);
        Assertions.assertEquals(2, infor.getRowCountToSkip());
        Assertions.assertEquals(4, infor.getTargetPageNumber());
        Assertions.assertTrue(infor.isOutOfRange());


        // on 3rd page, go prev
        // in range
        infor = metaData.buildTargetPageInfor(PaginationType.PREV);
        Assertions.assertNotNull(infor);
        filterKQL = infor.getAdditionalFilter().buildKQL();
        System.out.println(filterKQL);
        Assertions.assertEquals("((FlowCount  >=  9 ) and (FlowCount  <=  9 ))", filterKQL);
        Assertions.assertEquals(1, infor.getRowCountToSkip());
        Assertions.assertEquals(2, infor.getTargetPageNumber());
        Assertions.assertFalse(infor.isOutOfRange());

    }

    @Test
    void testDescFromLastPage() throws Exception {

        /**
         * Page 1: value 11, 10, 9
         * Page 2: value 9, 9, 9,
         * Page 3: value 9, 8, 8
         * Page 4: value 7, 6, 4
         * Page 5: value 3, 2, 1
         */

        AnchorRows p1 =
            AnchorRows.builder()
                .pageNumber(-1)
                .firstValue("3")
                .lastValue("1")
                .firstDupCount(1)
                .lastDupCount(1)
                .build();

        AnchorRows p2 =
            AnchorRows.builder()
                .pageNumber(-2)
                .firstValue("7")
                .lastValue("4")
                .firstDupCount(1)
                .lastDupCount(1)
                .build();

        AnchorRows p3 =
            AnchorRows.builder()
                .pageNumber(-3)
                .firstValue("9")
                .lastValue("8")
                .firstDupCount(1)
                .lastDupCount(2)
                .build();

        SortByFields sb = SortByFields.builder().field("flows").order("desc").build();

        DpMetaData metaData = DpMetaData.builder()
            .totalPages(5)
            .currPage(-3)
            .primarySortBy(sb)
            .anchorRows(List.of(p1, p2, p3))
            .build();

        Assertions.assertTrue(metaData.sanityCheck());

        // From last page -> 3nd page, go next
        // in range
        DpTargetPageInfor infor = metaData.buildTargetPageInfor(PaginationType.NEXT);
        Assertions.assertNotNull(infor);
        String filterKQL = infor.getAdditionalFilter().buildKQL();
        System.out.println(filterKQL);
        Assertions.assertEquals("((FlowCount  >=  4 ) and (FlowCount  <=  7 ))", filterKQL);
        Assertions.assertEquals(0, infor.getRowCountToSkip());
        Assertions.assertEquals(-2, infor.getTargetPageNumber());
        Assertions.assertFalse(infor.isOutOfRange());

        // From last page -> 2nd page, go prev
        // out of range
        infor = metaData.buildTargetPageInfor(PaginationType.PREV);
        Assertions.assertNotNull(infor);
        filterKQL = infor.getAdditionalFilter().buildKQL();
        System.out.println(filterKQL);
        Assertions.assertEquals("FlowCount  >=  9 ", filterKQL);
        Assertions.assertEquals(1, infor.getRowCountToSkip());
        Assertions.assertEquals(-4, infor.getTargetPageNumber());
        Assertions.assertTrue(infor.isOutOfRange());
    }
}
