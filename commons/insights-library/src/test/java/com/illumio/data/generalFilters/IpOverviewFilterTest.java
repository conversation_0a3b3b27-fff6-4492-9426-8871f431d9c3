package com.illumio.data.generalFilters;


import com.illumio.data.model.Filters;
import com.illumio.data.model.generalFilters.IpOverviewFilter;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class IpOverviewFilterTest {

    @Test
    public void testMapFromGeneralIPFilter() {
        Filters f =
            Filters.builder()
                .categoryType("string")
                .categoryName("ip")
                .categoryValue(List.of("*******"))
                .build();

        List<Filters> filters = List.of(f);
        IpOverviewFilter.IpOverviewUnit result = IpOverviewFilter.mapFromLegacyFilter(filters);
        Assertions.assertTrue(result.getFilter().isPresent());
        Assertions.assertNull(result.getError());
        Assertions.assertEquals("((SrcIP  in~  ( '*******' )) or (DestIP  in~  ( '*******' )))", result.getFilter().get().buildKQL());
        Assertions.assertTrue(result.getProjectFields().containsAll(IpOverviewFilter.SRC_PROJECT_FIELDS));
        Assertions.assertTrue(result.getProjectFields().containsAll(IpOverviewFilter.DEST_PROJECT_FIELDS));

        // only here both are true
        Assertions.assertTrue(result.isSearchSrcIP());
        Assertions.assertTrue(result.isSearchDestIP());
        Assertions.assertEquals(List.of("*******"), result.getOriginalSearchValues());
    }

    @Test
    public void testInBound() {
        Filters f =
            Filters.builder()
                .categoryType("string")
                .categoryName("ip")
                .categoryValue(List.of("*******"))
                .build();

        Filters f2 =
            Filters.builder()
                .categoryType("string")
                .categoryName("traffic_direction")
                .categoryValue(List.of("inbound"))
                .build();

        List<Filters> filters = List.of(f, f2);
        IpOverviewFilter.IpOverviewUnit result = IpOverviewFilter.mapFromLegacyFilter(filters);
        Assertions.assertTrue(result.getFilter().isPresent());
        Assertions.assertNull(result.getError());
        Assertions.assertEquals("SrcIP  in~  ( '*******' )", result.getFilter().get().buildKQL());
        Assertions.assertEquals(IpOverviewFilter.SRC_PROJECT_FIELDS, result.getProjectFields());
        Assertions.assertTrue(result.isSearchSrcIP());
        Assertions.assertFalse(result.isSearchDestIP());
        Assertions.assertEquals(List.of("*******"), result.getOriginalSearchValues());
    }

    @Test
    public void testOutBound() {
        Filters f =
            Filters.builder()
                .categoryType("string")
                .categoryName("ip")
                .categoryValue(List.of("*******"))
                .build();

        Filters f2 =
            Filters.builder()
                .categoryType("string")
                .categoryName("traffic_direction")
                .categoryValue(List.of("OUTbound"))
                .build();

        List<Filters> filters = List.of(f, f2);
        IpOverviewFilter.IpOverviewUnit result = IpOverviewFilter.mapFromLegacyFilter(filters);
        Assertions.assertTrue(result.getFilter().isPresent());
        Assertions.assertNull(result.getError());
        Assertions.assertEquals("DestIP  in~  ( '*******' )", result.getFilter().get().buildKQL());
        Assertions.assertEquals(IpOverviewFilter.DEST_PROJECT_FIELDS, result.getProjectFields());

        Assertions.assertFalse(result.isSearchSrcIP());
        Assertions.assertTrue(result.isSearchDestIP());
        Assertions.assertEquals(List.of("*******"), result.getOriginalSearchValues());
    }

    @Test
    public void testSrcIP() {
        Filters f =
            Filters.builder()
                .categoryType("string")
                .categoryName("source_ip")
                .categoryValue(List.of("*******"))
                .build();

        List<Filters> filters = List.of(f);
        IpOverviewFilter.IpOverviewUnit result = IpOverviewFilter.mapFromLegacyFilter(filters);
        Assertions.assertTrue(result.getFilter().isPresent());
        Assertions.assertNull(result.getError());
        Assertions.assertEquals("SrcIP  in~  ( '*******' )", result.getFilter().get().buildKQL());
        Assertions.assertEquals(IpOverviewFilter.SRC_PROJECT_FIELDS, result.getProjectFields());

        Assertions.assertTrue(result.isSearchSrcIP());
        Assertions.assertFalse(result.isSearchDestIP());
        Assertions.assertEquals(List.of("*******"), result.getOriginalSearchValues());
    }

    @Test
    public void testDestIP() {
        Filters f =
            Filters.builder()
                .categoryType("string")
                .categoryName("destination_ip")
                .categoryValue(List.of("*******"))
                .build();

        List<Filters> filters = List.of(f);
        IpOverviewFilter.IpOverviewUnit result = IpOverviewFilter.mapFromLegacyFilter(filters);
        Assertions.assertTrue(result.getFilter().isPresent());
        Assertions.assertNull(result.getError());
        Assertions.assertEquals("DestIP  in~  ( '*******' )", result.getFilter().get().buildKQL());
        Assertions.assertEquals(IpOverviewFilter.DEST_PROJECT_FIELDS, result.getProjectFields());

        Assertions.assertFalse(result.isSearchSrcIP());
        Assertions.assertTrue(result.isSearchDestIP());
        Assertions.assertEquals(List.of("*******"), result.getOriginalSearchValues());
    }

    @Test
    public void testWrongInput() {
        Filters f =
            Filters.builder()
                .categoryType("string")
                .categoryName("wrong")
                .categoryValue(List.of("*******"))
                .build();

        List<Filters> filters = List.of(f);
        IpOverviewFilter.IpOverviewUnit result = IpOverviewFilter.mapFromLegacyFilter(filters);
        Assertions.assertTrue(result.getFilter().isEmpty());
        Assertions.assertTrue(result.getError().contains("Invalid filter name"));


        Filters f1 =
            Filters.builder()
                .categoryType("string")
                .categoryName("ip")
                .categoryValue(List.of("*******"))
                .build();

        Filters f2 =
            Filters.builder()
                .categoryType("string")
                .categoryName("traffic_direction")
                .categoryValue(List.of("wrong"))
                .build();
        filters = List.of(f1, f2);
        result = IpOverviewFilter.mapFromLegacyFilter(filters);
        Assertions.assertTrue(result.getFilter().isEmpty());
        Assertions.assertTrue(result.getError().contains("Invalid traffic direction"));
    }

    @Test
    public void testMultipleIPsError(){
        Filters f =
            Filters.builder()
                .categoryType("string")
                .categoryName("ip")
                .categoryValue(List.of("*******", "*******"))
                .build();

        List<Filters> filters = List.of(f);
        IpOverviewFilter.IpOverviewUnit result = IpOverviewFilter.mapFromLegacyFilter(filters);
        Assertions.assertFalse(result.getFilter().isPresent());
        Assertions.assertTrue(result.getError().contains("can only provide 1 IP"));
    }
}
