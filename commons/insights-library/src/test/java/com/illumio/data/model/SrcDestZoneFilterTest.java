package com.illumio.data.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class SrcDestZoneFilterTest {
    @Test
    void testKQLSrcZoneFilter() {
        Filters f =
            Filters.builder()
                .categoryType("string")
                .categoryName("source_zone")
                .categoryValue(List.of("GCP"))
                .build();

        String kql = f.buildKQL();
        Assertions.assertEquals("| where  SrcResId == '' and SrcCloudProvider in~ ('gcp','google')", kql);

        f =
            Filters.builder()
                .categoryType("string")
                .categoryName("source_zone")
                .categoryValue(List.of("Azure"))
                .build();

        kql = f.buildKQL();
        Assertions.assertEquals(
            "| where  SrcResId == '' and SrcCloudProvider in~ ('azure','microsoft')", kql);

        f =
            Filters.builder()
                .categoryType("string")
                .categoryName("source_zone")
                .categoryValue(List.of(SrcDestZoneFilter.UNKNOWN_ZONE))
                .build();

        kql = f.buildKQL();
        Assertions.assertEquals("| where  SrcResId == '' and SrcCloudProvider ==''", kql);

        f =
            Filters.builder()
                .categoryType("string")
                .categoryName("source_zone")
                .categoryValue(List.of(SrcDestZoneFilter.INTERNAL_AZURE_ZONE))
                .build();

        kql = f.buildKQL();
        Assertions.assertEquals("| where  SrcResId != '' and SrcCloudProvider =~ 'Azure'", kql);
    }

    @Test
    void testKQLDestZoneFilter() {
        Filters f =
            Filters.builder()
                .categoryType("string")
                .categoryName("destination_zone")
                .categoryValue(List.of("GCP"))
                .build();

        String kql = f.buildKQL();
        Assertions.assertEquals(
            "| where  DestResId == '' and DestCloudProvider in~ ('gcp','google')", kql);

        f =
            Filters.builder()
                .categoryType("string")
                .categoryName("destination_zone")
                .categoryValue(List.of("Azure"))
                .build();

        kql = f.buildKQL();
        Assertions.assertEquals(
            "| where  DestResId == '' and DestCloudProvider in~ ('azure','microsoft')", kql);

        f =
            Filters.builder()
                .categoryType("string")
                .categoryName("destination_zone")
                .categoryValue(List.of(SrcDestZoneFilter.UNKNOWN_ZONE))
                .build();

        kql = f.buildKQL();
        Assertions.assertEquals("| where  DestResId == '' and DestCloudProvider ==''", kql);

        f =
            Filters.builder()
                .categoryType("string")
                .categoryName("destination_zone")
                .categoryValue(List.of(SrcDestZoneFilter.INTERNAL_AZURE_ZONE))
                .build();

        kql = f.buildKQL();
        Assertions.assertEquals("| where  DestResId != '' and DestCloudProvider =~ 'Azure'", kql);

        f =
            Filters.builder()
                .categoryType("string")
                .categoryName("destination_zone")
                .categoryValue(List.of("internal gcp"))
                .build();

        kql = f.buildKQL();
        Assertions.assertEquals("| where  DestResId != '' and DestCloudProvider =~ 'gcp'", kql);

        f =
            Filters.builder()
                .categoryType("string")
                .categoryName("destination_zone")
                .categoryValue(List.of("internal "))
                .build();

        kql = f.buildKQL();
        Assertions.assertEquals("| where  DestResId != '' and DestCloudProvider =~ ''", kql);

        f =
            Filters.builder()
                .categoryType("string")
                .categoryName("destination_zone")
                .categoryValue(List.of("internal"))
                .build();

        kql = f.buildKQL();
        Assertions.assertEquals("| where  DestResId != '' and DestCloudProvider =~ ''", kql);
    }

    @Test
    void testEmptyZoneFilter() {
        Filters f =
            Filters.builder()
                .categoryType("string")
                .categoryName("source_zone")
                .categoryValue(List.of(""))
                .build();

        String kql = f.buildKQL();
        Assertions.assertEquals("| where  SrcResId == '' and SrcCloudProvider ==''", kql);
    }

}
