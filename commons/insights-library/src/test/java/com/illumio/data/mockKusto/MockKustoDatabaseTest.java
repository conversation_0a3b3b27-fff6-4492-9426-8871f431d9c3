package com.illumio.data.mockKusto;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.*;

import java.io.IOException;
import java.util.*;

public class MockKustoDatabaseTest {
    private MockKustoDatabase db;
    private static final String TEST_TABLE = "TestTable";

    @BeforeEach
    void setUp() {
        db = new MockKustoDatabase();
        Map<String, Class<?>> schema = new LinkedHashMap<>();
        schema.put("id", Integer.class);
        schema.put("name", String.class);
        schema.put("active", Boolean.class);
        schema.put("score", Double.class);
        db.createTable(TEST_TABLE, schema);
    }

    @Test
    void testTableCreation() {
        String query = TEST_TABLE + " | project id, name";
        List<Map<String, Object>> result = db.executeQuery(query);
        assertTrue(result.isEmpty());
    }

    @Test
    void testDataIngestion() throws IOException {
        // Create test data file in resources
        db.ingestFromCsv(TEST_TABLE, "/test-data.csv");
        
        String query = TEST_TABLE;
        List<Map<String, Object>> result = db.executeQuery(query);
        assertFalse(result.isEmpty());
    }

    @Test
    void testProjection() {
        addTestData();
        String query = TEST_TABLE + " | project name, score";
        
        List<Map<String, Object>> result = db.executeQuery(query);
        assertEquals(2, result.size());
        assertTrue(result.get(0).containsKey("name"));
        assertTrue(result.get(0).containsKey("score"));
        assertFalse(result.get(0).containsKey("id"));
    }

    @Test
    void testFiltering() {
        addTestData();
        String query = TEST_TABLE + " | where active = true";
        
        List<Map<String, Object>> result = db.executeQuery(query);
        assertEquals(1, result.size());
        assertTrue((Boolean) result.get(0).get("active"));
    }

    @Test
    void testSorting() {
        addTestData();
        String query = TEST_TABLE + " | sort by score desc";
        
        List<Map<String, Object>> result = db.executeQuery(query);
        assertEquals(2, result.size());
        assertTrue((Double) result.get(0).get("score") > (Double) result.get(1).get("score"));
    }

    @Test
    void testComplexQuery() {
        addTestData();
        String query = TEST_TABLE + " | where active = true | project name, score | sort by score asc";
        
        List<Map<String, Object>> result = db.executeQuery(query);
        assertEquals(1, result.size());
        assertEquals(2, result.get(0).size());
        assertTrue(result.get(0).containsKey("name"));
        assertTrue(result.get(0).containsKey("score"));
    }

    @Test
    void testInvalidQuery() {
        assertThrows(IllegalArgumentException.class, () -> {
            db.executeQuery("InvalidTable | project name");
        });
    }

    private void addTestData() {
        Map<String, Object> row1 = new HashMap<>();
        row1.put("id", 1);
        row1.put("name", "Test1");
        row1.put("active", true);
        row1.put("score", 85.5);

        Map<String, Object> row2 = new HashMap<>();
        row2.put("id", 2);
        row2.put("name", "Test2");
        row2.put("active", false);
        row2.put("score", 75.0);

        MockKustoTable table = db.getTable(TEST_TABLE);
        table.addRow(row1);
        table.addRow(row2);
    }
} 