package com.illumio.data.mockKusto;

import java.util.*;

/**
 * Represents a table in the mock Kusto database with schema and data storage capabilities.
 */
public class MockKustoTable {
    private final String tableName;
    private final Map<String, Class<?>> schema;
    private final List<Map<String, Object>> rows;

    public MockKustoTable(String tableName, Map<String, Class<?>> schema) {
        this.tableName = tableName;
        this.schema = new LinkedHashMap<>(schema);
        this.rows = new ArrayList<>();
    }

    public String getTableName() {
        return tableName;
    }

    public Map<String, Class<?>> getSchema() {
        return Collections.unmodifiableMap(schema);
    }

    /**
     * Validates and adds a row to the table.
     * @param row The row data as key-value pairs
     * @throws IllegalArgumentException if the row doesn't match the schema
     */
    public void addRow(Map<String, Object> row) {
        validateRow(row);
        rows.add(new HashMap<>(row));
    }

    /**
     * Validates that a row matches the table schema.
     */
    private void validateRow(Map<String, Object> row) {
        if (!row.keySet().equals(schema.keySet())) {
            throw new IllegalArgumentException("Row columns don't match schema columns");
        }

        for (Map.Entry<String, Object> entry : row.entrySet()) {
            String column = entry.getKey();
            Object value = entry.getValue();
            if (value != null && !schema.get(column).isInstance(value)) {
                throw new IllegalArgumentException(
                    String.format("Invalid type for column %s. Expected %s, got %s",
                        column, schema.get(column).getSimpleName(), value.getClass().getSimpleName())
                );
            }
        }
    }

    /**
     * Returns all rows in the table.
     */
    public List<Map<String, Object>> getRows() {
        return Collections.unmodifiableList(rows);
    }

    /**
     * Clears all data from the table.
     */
    public void clear() {
        rows.clear();
    }
} 