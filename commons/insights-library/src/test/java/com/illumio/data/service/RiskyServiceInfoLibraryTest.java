package com.illumio.data.service;

import static org.mockito.Mockito.RETURNS_DEEP_STUBS;
import static org.mockito.Mockito.when;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.service.RiskyServiceInfo.ServiceInfo;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public class RiskyServiceInfoLibraryTest {

    @Test
    void testServiceMap() {
        InsightsServiceConfiguration config = Mockito.mock(InsightsServiceConfiguration.class, RETURNS_DEEP_STUBS);
        when(config.getRiskyServiceConfig().getRiskyServiceFilePath()).thenReturn("classpath:risky_services.csv");

        RiskyServiceInfo serviceInfo = new RiskyServiceInfo(config);

        Map<String, List<ServiceInfo>> servicetoServiceInfoMap = serviceInfo.getServicetoServiceInfoMap();

        Assertions.assertEquals(22, servicetoServiceInfoMap.size());
        Assertions.assertEquals(12, servicetoServiceInfoMap.get("RustDesk").size());

        for(int port = 21114; port <=21119; port++) {
            final int fPort = port;
            Optional<ServiceInfo> first = servicetoServiceInfoMap.get("RustDesk")
                .stream()
                .filter(si -> si.getService().equals("RustDesk") && si.getPort() == fPort)
                .findFirst();

            Assertions.assertTrue(first.isPresent());
        }
    }
}
