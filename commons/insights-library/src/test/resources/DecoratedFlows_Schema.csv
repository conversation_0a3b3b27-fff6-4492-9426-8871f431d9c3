"ColumnName","ColumnOrdinal","DataType","ColumnType"
"SrcIP","0","System.String","string"
"SrcId","1","System.String","string"
"CSSrcId","2","System.String","string"
"DestIP","3","System.String","string"
"DestId","4","System.String","string"
"CSDestId","5","System.String","string"
"Port","6","System.Int32","int"
"Proto","7","System.String","string"
"SentBytes","8","System.Int64","long"
"ReceivedBytes","9","System.Int64","long"
"IllumioTenantId","10","System.String","string"
"SrcTenantId","11","System.String","string"
"SrcSubId","12","System.String","string"
"SrcRegion","13","System.String","string"
"SrcResId","14","System.String","string"
"SrcVnetId","15","System.String","string"
"SrcUserName","16","System.String","string"
"DestTenantId","17","System.String","string"
"DestSubId","18","System.String","string"
"DestRegion","19","System.String","string"
"DestResId","20","System.String","string"
"DestVnetId","21","System.String","string"
"DestUserName","22","System.String","string"
"SrcFlowType","23","System.String","string"
"DestFlowType","24","System.String","string"
"SrcDeviceId","25","System.String","string"
"SrcFirewallId","26","System.String","string"
"SrcUserId","27","System.String","string"
"DestDeviceId","28","System.String","string"
"DestFirewallId","29","System.String","string"
"DestUserId","30","System.String","string"
"SrcResourceType","31","System.String","string"
"DestResourceType","32","System.String","string"
"SrcThreatLevel","33","System.Int32","int"
"DestThreatLevel","34","System.Int32","int"
"SrcIsWellknown","35","System.SByte","bool"
"DestIsWellknown","36","System.SByte","bool"
"SrcDomain","37","System.String","string"
"DestDomain","38","System.String","string"
"SrcCountry","39","System.String","string"
"DestCountry","40","System.String","string"
"SrcCity","41","System.String","string"
"DestCity","42","System.String","string"
"SrcCloudProvider","43","System.String","string"
"DestCloudProvider","44","System.String","string"
"SourceHostName","45","System.String","string"
"SourceMACAddress","46","System.String","string"
"SourceNTDomain","47","System.String","string"
"SourceProcessId","48","System.String","string"
"SourceProcessName","49","System.Int32","int"
"SourceUserName","50","System.String","string"
"SourceUserPrivileges","51","System.String","string"
"DeviceAction","52","System.String","string"
"DeviceAddress","53","System.String","string"
"DestinationDnsDomain","54","System.String","string"
"DestinationHostName","55","System.String","string"
"DestinationMACAddress","56","System.String","string"
"DestinationNTDomain","57","System.String","string"
"DestinationProcessId","58","System.Int32","int"
"DestinationProcessName","59","System.String","string"
"DestinationServiceName","60","System.String","string"
"DestinationTranslatedAddress","61","System.String","string"
"DestinationUserName","62","System.String","string"
"LogSeverity","63","System.String","string"
"MaliciousIP","64","System.String","string"
"MaliciousIPCountry","65","System.String","string"
"MaliciousIPLatitude","66","System.Double","real"
"MaliciousIPLongitude","67","System.Double","real"
"LAWTenantId","68","System.String","string"
"ThreatConfidence","69","System.String","string"
"ThreatDescription","70","System.String","string"
"ThreatSeverity","71","System.Int32","int"
"StartTime","72","System.String","string"
"EndTime","73","System.String","string"
"SourceDnsDomain","74","System.String","string"
"SourceServiceName","75","System.String","string"
"SourceSystem","76","System.String","string"
"DeviceMacAddress","77","System.String","string"
"DeviceName","78","System.String","string"
"DeviceOutboundInterface","79","System.String","string"
"DeviceProduct","80","System.String","string"
"DeviceTranslatedAddress","81","System.String","string"
"DeviceVersion","82","System.String","string"
"DeviceTimeZone","83","System.String","string"
"DeviceExternalID","84","System.String","string"
"DeviceCustomNumber3","85","System.Int32","int"
"ReceiptTime","86","System.String","string"
"Activity","87","System.String","string"
"AdditionalExtensions","88","System.String","string"
"SourceZone","89","System.String","string"
"DestinationZone","90","System.String","string"
"RequestURL","91","System.String","string"
"Computer","92","System.String","string"
"SourceLabel","93","System.String","string"
"DestinationLabel","94","System.String","string"
"SrcCloudTags","95","System.String","string"
"DestCloudTags","96","System.String","string"
"FlowCount","97","System.Int32","int"
"PacketsReceived","98","System.Int32","int"
"PacketsSent","99","System.Int32","int"
"TrafficStatus","100","System.String","string"
"SrcAccountName","101","System.String","string"
"DestAccountName","102","System.String","string"
"SrcResourceCategory","103","System.String","string"
"DestResourceCategory","104","System.String","string"
"SrcResourceName","105","System.String","string"
"DestResourceName","106","System.String","string"
"SrcResourceGroup","107","System.String","string"
"DestResourceGroup","108","System.String","string"
"SrcSubnetId","109","System.String","string"
"DestSubnetId","110","System.String","string"
"SrcCSLabel","111","System.String","string"
"DestCSLabel","112","System.String","string"
"DeviceExternalId","113","System.String","string"
"SrcGeoRegion","114","System.String","string"
"DestGeoRegion","115","System.String","string"
"DestinationExternalLabel","116","System.String","string"
"DestinationExternalLabelCategory","117","System.String","string"
"SrcCSLabels","118","System.String","string"
"DestCSLabels","119","System.String","string"
"SourceExternalLabel","120","System.String","string"
"SourceExternalLabelCategory","121","System.String","string"
"Hops","122","System.String","string"
"DeniedAt","123","System.String","string"