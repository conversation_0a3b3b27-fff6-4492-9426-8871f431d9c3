{"records": [{"Activity": "Activity", "AdditionalExtensions": "", "ApplicationProtocol": "", "_BilledSize": "", "CollectorHostName": "", "CommunicationDirection": "", "Computer": "", "DestinationDnsDomain": "1", "DestinationHostName": "", "DestinationIP": "", "DestinationMACAddress": "", "DestinationNTDomain": "", "DestinationPort": "", "DestinationProcessId": "", "DestinationProcessName": "", "DestinationServiceName": "", "DestinationTranslatedAddress": "", "DestinationTranslatedPort": "", "DestinationUserID": "", "DestinationUserName": "", "DestinationUserPrivileges": "", "DeviceAction": "", "DeviceAddress": "", "DeviceCustomDate1": "", "DeviceCustomDate1Label": "", "DeviceCustomDate2": "", "DeviceCustomDate2Label": "", "DeviceCustomFloatingPoint1": "", "DeviceCustomFloatingPoint1Label": "", "DeviceCustomFloatingPoint2": "", "DeviceCustomFloatingPoint2Label": "", "DeviceCustomFloatingPoint3": "", "DeviceCustomFloatingPoint3Label": "", "DeviceCustomFloatingPoint4": "", "DeviceCustomFloatingPoint4Label": "", "DeviceCustomIPv6Address1": "", "DeviceCustomIPv6Address1Label": "", "DeviceCustomIPv6Address2": "", "DeviceCustomIPv6Address2Label": "", "DeviceCustomIPv6Address3": "", "DeviceCustomIPv6Address3Label": "", "DeviceCustomIPv6Address4": "", "DeviceCustomIPv6Address4Label": "", "DeviceCustomNumber1": "", "DeviceCustomNumber1Label": "", "DeviceCustomNumber2": "", "DeviceCustomNumber2Label": "", "DeviceCustomNumber3": "", "DeviceCustomNumber3Label": "", "DeviceCustomString1": "", "DeviceCustomString1Label": "", "DeviceCustomString2": "", "DeviceCustomString2Label": "", "DeviceCustomString3": "", "DeviceCustomString3Label": "", "DeviceCustomString4": "", "DeviceCustomString4Label": "", "DeviceCustomString5": "", "DeviceCustomString5Label": "", "DeviceCustomString6": "", "DeviceCustomString6Label": "", "DeviceDnsDomain": "", "DeviceEventCategory": "", "DeviceEventClassID": "", "DeviceExternalId": "", "DeviceFacility": "", "DeviceInboundInterface": "", "DeviceMacAddress": "", "DeviceName": "", "DeviceNtDomain": "", "DeviceOutboundInterface": "", "DevicePayloadId": "", "DeviceProduct": "", "DeviceTimeZone": "", "DeviceTranslatedAddress": "", "DeviceVendor": "", "DeviceVersion": "", "EndTime": "", "EventCount": "", "EventOutcome": "", "EventType": "", "ExternalID": "", "ExtID": "", "FieldDeviceCustomNumber1": "", "FieldDeviceCustomNumber2": "", "FieldDeviceCustomNumber3": "", "FileCreateTime": "", "FileHash": "", "FileID": "", "FileModificationTime": "", "FileName": "", "FilePath": "", "FilePermission": "", "FileSize": "", "FileType": "", "FlexDate1": "", "FlexDate1Label": "", "FlexNumber1": "", "FlexNumber1Label": "", "FlexNumber2": "", "FlexNumber2Label": "", "FlexString1": "", "FlexString1Label": "", "FlexString2": "", "FlexString2Label": "", "IndicatorThreatType": "", "_IsBillable": "", "LogSeverity": "", "MaliciousIP": "", "MaliciousIPCountry": "", "MaliciousIPLatitude": "", "MaliciousIPLongitude": "", "Message": "", "OldFileCreateTime": "", "OldFileHash": "", "OldFileID": "", "OldFileModificationTime": "", "OldFileName": "", "OldFilePath": "", "OldFilePermission": "", "OldFileSize": "", "OldFileType": "", "OriginalLogSeverity": "", "ProcessID": "", "ProcessName": "", "Protocol": "", "Reason": "", "ReceiptTime": "2024/04/30 10:42:36", "ReceivedBytes": "", "RemoteIP": "", "RemotePort": "", "ReportReferenceLink": "", "RequestClientApplication": "", "RequestContext": "", "RequestCookies": "", "RequestMethod": "", "RequestURL": "", "_ResourceId": "", "SentBytes": "", "SimplifiedDeviceAction": "", "SourceDnsDomain": "", "SourceHostName": "", "SourceIP": "", "SourceMACAddress": "", "SourceNTDomain": "", "SourcePort": "", "SourceProcessId": "", "SourceProcessName": "", "SourceServiceName": "", "SourceSystem": "", "SourceTranslatedAddress": "", "SourceTranslatedPort": "", "SourceUserID": "", "SourceUserName": "", "SourceUserPrivileges": "", "StartTime": "", "_SubscriptionId": "", "TenantId": "", "ThreatConfidence": "", "ThreatDescription": "", "ThreatSeverity": "", "TimeGenerated": "2024-05-10T19:24:28.9754943Z", "Type": ""}]}