package com.illumio.data;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class CommonSecurityLogRecordsTest {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    @Test
    @SneakyThrows
    void testDeserializes() {
        CommonSecurityLogRecords commonSecurityLogRecords =
                OBJECT_MAPPER.readValue(
                        this.getClass().getClassLoader().getResourceAsStream("CommonSecurityLog.json"),
                        CommonSecurityLogRecords.class);
        Assertions.assertEquals(1, commonSecurityLogRecords.getRecords().size());
        CommonSecurityLog cslRecord = commonSecurityLogRecords.getRecords().get(0);
        Assertions.assertEquals("Activity", cslRecord.getActivity());
    }
}
