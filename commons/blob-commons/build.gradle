plugins {
    id 'java-library'
}

dependencies {
    // logging
    implementation 'org.slf4j:slf4j-api'

    // implementation
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'io.projectreactor:reactor-core'
    implementation 'com.azure:azure-storage-blob'
    implementation 'com.azure:azure-identity'
    implementation 'org.springframework.boot:spring-boot-starter-validation'

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
}