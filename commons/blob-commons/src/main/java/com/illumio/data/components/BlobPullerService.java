package com.illumio.data.components;

import com.azure.core.credential.TokenCredential;
import com.azure.storage.blob.BlobContainerAsyncClient;
import com.azure.storage.blob.models.BlobItem;
import com.illumio.data.config.BlobServiceClientFactory;
import com.illumio.data.config.BlobStorageConfig;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.function.Supplier;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class BlobPullerService {

    private final BlobServiceClientFactory blobServiceClientFactory;
    private final BlobStorageConfig blobStorageConfig;

    public Flux<BlobItemWithClient> getBlobs(String storageAccountName) {
        return getBlobs(storageAccountName, blobItem -> true, null);
    }

    public Flux<BlobItemWithClient> getBlobs(String storageAccountName, Predicate<BlobItem> blobFilter) {
        return getBlobs(storageAccountName, blobFilter, null);
    }

    public Flux<BlobItemWithClient> getBlobs(String storageAccountName, Supplier<Mono<TokenCredential>> credentialSupplier) {
        return getBlobs(storageAccountName, blobItem -> true, null);
    }

    public Flux<BlobItemWithClient> getBlobs(String storageAccountName, Predicate<BlobItem> blobFilter, Supplier<Mono<TokenCredential>> credentialSupplier) {
        Optional<BlobStorageConfig.StorageAccountConfig> storageAccountConfig = Optional.ofNullable(blobStorageConfig.getAccounts().get(storageAccountName));

        if (storageAccountConfig.isEmpty()) {
            throw new IllegalArgumentException("No configuration found for the storage account: " + storageAccountName);
        }

        return blobServiceClientFactory.getBlobContainerClient(storageAccountName, Optional.ofNullable(credentialSupplier))
                .flatMapMany(blobContainerAsyncClient ->
                        blobContainerAsyncClient.listBlobs()
                                .filter(blobFilter)
                                .map(blobItem -> new BlobItemWithClient(blobItem, blobContainerAsyncClient))
                );
    }

    @Value
    @Getter
    public static class BlobItemWithClient {
        BlobItem blobItem;
        BlobContainerAsyncClient blobContainerClient;
    }
}
