package com.illumio.data.config;

import static com.illumio.data.util.Constants.STORAGE_ACCOUNTS_PREFIX;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@ConfigurationProperties(prefix = STORAGE_ACCOUNTS_PREFIX)
@Validated
@Getter
@Setter
public class BlobStorageConfig {

    @NotEmpty(message = "Mandatory property 'storage-accounts.accounts' is missing or empty")
    private final Map<String, StorageAccountConfig> accounts = new HashMap<>();

    @Getter
    @Setter
    public static class StorageAccountConfig {
        @NotBlank(message = "Mandatory property 'storageAccountEndpoint' is missing in BlobStorageConfig")
        private String storageAccountEndpoint;

        @NotBlank(message = "Mandatory property 'container' is missing in BlobStorageConfig")
        private String container;

        private StorageAccountAuthConfig authConfig;
    }

    @Getter
    @Setter
    public static class StorageAccountAuthConfig {
        private String azureClientId;
        private String azureClientSecret;
        private String azureTenantId;
        private Boolean isConnectionString;
        private String connectionString;
    }
}