package com.illumio.data.config;

import com.azure.core.credential.TokenCredential;
import com.azure.identity.DefaultAzureCredentialBuilder;
import com.azure.storage.blob.BlobContainerAsyncClient;
import com.azure.storage.blob.BlobContainerClientBuilder;
import java.util.Optional;
import java.util.function.Supplier;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class BlobServiceClientFactory {

    private final BlobStorageConfig blobStorageConfig;

    @SneakyThrows
    public Mono<BlobContainerAsyncClient> getBlobContainerClient(final String storageAccount) {
        return getBlobContainerClient(storageAccount, Optional.empty());
    }

    @SneakyThrows
    public Mono<BlobContainerAsyncClient> getBlobContainerClient(final String storageAccount, Optional<Supplier<Mono<TokenCredential>>> credentialSupplier) {
        BlobStorageConfig.StorageAccountConfig saConfig = getStorageAccountConfig(storageAccount);

        BlobContainerClientBuilder blobContainerClientBuilder = createBlobContainerClientBuilder(saConfig);

        if (credentialSupplier.isPresent()) {
            return buildWithCredentialSupplier(blobContainerClientBuilder, credentialSupplier);
        }

        Optional<BlobStorageConfig.StorageAccountAuthConfig> authConfig = Optional.ofNullable(saConfig.getAuthConfig());

        if (authConfig.isPresent() && Boolean.TRUE.equals(authConfig.get().getIsConnectionString())) {
            return buildWithConnectionString(authConfig.get(), blobContainerClientBuilder, storageAccount);
        }

        blobContainerClientBuilder.credential(new DefaultAzureCredentialBuilder().build());
        return Mono.just(blobContainerClientBuilder.buildAsyncClient());
    }

    private BlobStorageConfig.StorageAccountConfig getStorageAccountConfig(String storageAccount) {
        return Optional.ofNullable(blobStorageConfig.getAccounts().get(storageAccount))
                .orElseThrow(() ->
                        new IllegalArgumentException(String.format("Storage account '%s' is not configured.", storageAccount))
                );
    }

    private BlobContainerClientBuilder createBlobContainerClientBuilder(
            BlobStorageConfig.StorageAccountConfig saConfig) {

        return new BlobContainerClientBuilder()
                .endpoint(saConfig.getStorageAccountEndpoint())
                .containerName(saConfig.getContainer());
    }

    private Mono<BlobContainerAsyncClient> buildWithConnectionString(
            BlobStorageConfig.StorageAccountAuthConfig authConfig,
            BlobContainerClientBuilder blobContainerClientBuilder,
            String storageAccount) {

        String connectionString = authConfig.getConnectionString();

        if (!StringUtils.hasText(connectionString)) {
            throw new IllegalArgumentException(String.format("Connection string not found for storage account '%s'.", storageAccount));
        }

        blobContainerClientBuilder.connectionString(connectionString);
        return Mono.just(blobContainerClientBuilder.buildAsyncClient());
    }

    private Mono<BlobContainerAsyncClient> buildWithCredentialSupplier(
            BlobContainerClientBuilder blobContainerClientBuilder,
            Optional<Supplier<Mono<TokenCredential>>> credentialSupplier) {

        return credentialSupplier.get().get()
                .map(credential -> {
                    blobContainerClientBuilder.credential(credential);
                    return blobContainerClientBuilder.buildAsyncClient();
                });
    }
}
