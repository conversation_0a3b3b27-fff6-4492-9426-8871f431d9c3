package com.illumio.data.components;

import com.azure.core.credential.TokenCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Service;
import org.springframework.vault.core.ReactiveVaultTemplate;
import org.springframework.vault.support.VaultResponse;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

import static com.illumio.data.constants.Constants.CS_VAULT_AZURE_CLIENT_ID;
import static com.illumio.data.constants.Constants.CS_VAULT_AZURE_CLIENT_SECRET;
import static com.illumio.data.constants.Constants.CS_VAULT_AZURE_TENANT_ID;
import static com.illumio.data.constants.Constants.SECRET_CREDENTIALS_KEY;
import static com.illumio.data.constants.Constants.SECRET_DATA;

@Service
@Configuration
@Slf4j
@RequiredArgsConstructor
@ConditionalOnBean(ReactiveVaultTemplate.class)
public class VaultService {

    @Value("${vault.cache-time-mins:60}")
    private Integer vaultCacheTimeMins;

    private AsyncLoadingCache<String, Map<String, Object>> secretsCache;

    private final ReactiveVaultTemplate reactiveVaultTemplate;
    private final ObjectMapper objectMapper;

    @PostConstruct
    public void setUp() {
        secretsCache = Caffeine.newBuilder()
                .expireAfterWrite(vaultCacheTimeMins, TimeUnit.MINUTES)
                .buildAsync((String key, Executor executor) ->
                        getSecret(key)
                                .subscribeOn(Schedulers.fromExecutor(executor))
                                .toFuture()
                );
    }

    @SneakyThrows
    public Mono<Map<String, Object>> getSecretWithCaching(final String secretPath) {
        return Mono.fromFuture(secretsCache.get(secretPath));
    }

    @SneakyThrows
    public Mono<Map<String, Object>> getSecret(final String secretPath) {
        log.info("Retrieving Vault secret from path={}", secretPath);
        if (reactiveVaultTemplate == null) {
            throw new IllegalStateException("VaultTemplate is not configured. Please ensure a valid vault.uri and vault.token are configured.");
        }
        return reactiveVaultTemplate.read(secretPath)
                .map(VaultResponse::getData)
                .switchIfEmpty(Mono.error(new RuntimeException(String.format("Failed to get secret at secretPath=%s from Vault. Please double-check authentication and path.", secretPath))));
    }

    @SneakyThrows
    public Mono<TokenCredential> getAzureTokenCredentialFromCSVault(final String vaultPath) {
        return getSecretWithCaching(vaultPath).map(secret -> {
            final String invalidFormatError = String.format("Secret at vaultPath %s does not conform to expected format.", vaultPath);
            if (null == secret.get(SECRET_DATA) || !(secret.get(SECRET_DATA) instanceof Map<?,?>)) {
                throw new RuntimeException(invalidFormatError);
            }
            final Map<String, Object> secretData = (Map<String, Object>) secret.get(SECRET_DATA);
            if (!secretData.containsKey(SECRET_CREDENTIALS_KEY) || !(secretData.get(SECRET_CREDENTIALS_KEY) instanceof String)) {
                throw new RuntimeException(invalidFormatError);
            }
            final String credentialsJson = (String) secretData.get(SECRET_CREDENTIALS_KEY);
            try {
                final Map<String, String> credentialsMap = (Map<String, String>) objectMapper.readValue(credentialsJson, Map.class);
                if (!credentialsMap.containsKey(CS_VAULT_AZURE_CLIENT_ID)
                        || !credentialsMap.containsKey(CS_VAULT_AZURE_TENANT_ID)
                        || !credentialsMap.containsKey(CS_VAULT_AZURE_CLIENT_SECRET)) {
                    throw new RuntimeException(invalidFormatError);
                }
                return new ClientSecretCredentialBuilder()
                        .clientId(credentialsMap.get(CS_VAULT_AZURE_CLIENT_ID))
                        .tenantId(credentialsMap.get(CS_VAULT_AZURE_TENANT_ID))
                        .clientSecret(credentialsMap.get(CS_VAULT_AZURE_CLIENT_SECRET))
                        .build();
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        });
    }
}
