package com.illumio.data.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.vault.authentication.ClientAuthentication;
import org.springframework.vault.authentication.TokenAuthentication;
import org.springframework.vault.client.VaultEndpoint;
import org.springframework.vault.config.AbstractReactiveVaultConfiguration;
import org.springframework.vault.core.ReactiveVaultTemplate;

import java.net.URI;

@Configuration
@ConditionalOnProperty(name = {"vault.uri", "vault.token"})
public class VaultConfig extends AbstractReactiveVaultConfiguration {

    @Value("${vault.uri}")
    private String vaultUri;

    @Value("${vault.token}")
    private String vaultToken;

    @Override
    public VaultEndpoint vaultEndpoint() {
        return VaultEndpoint.from(URI.create(vaultUri));
    }

    @Override
    public ClientAuthentication clientAuthentication() {
        return new TokenAuthentication(vaultToken);
    }

    @Bean
    public ReactiveVaultTemplate reactiveVaultTemplate() {
        return new ReactiveVaultTemplate(vaultEndpoint(), new ReactorClientHttpConnector(), vaultTokenSupplier());
    }
}