plugins {
    id 'com.google.protobuf'
}

def grpcVersion = '1.69.0'
def protobufVersion = '3.25.5'
def reactiveGrpcVersion = '1.2.4'

dependencies {
    implementation 'io.grpc:grpc-netty-shaded'
    implementation 'io.grpc:grpc-protobuf'
    implementation 'io.grpc:grpc-stub'
    implementation 'io.grpc:grpc-api'
    implementation 'com.google.protobuf:protobuf-java'
    implementation 'javax.annotation:javax.annotation-api'
    implementation 'com.salesforce.servicelibs:reactor-grpc-stub'
    implementation 'io.projectreactor:reactor-core'
}

sourceSets {
    main {
        proto {
            srcDir 'services'
            srcDir 'vendor'
        }
        java {
            srcDirs = ['src/main/java',
                       'build/generated/source/proto/main/java',
                       'build/generated/source/proto/main/grpc',
                       'build/generated/source/proto/main/reactor',
                       'build/generated/source/proto/main']
        }
    }
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:${protobufVersion}"
    }
    plugins {
        grpc {
            artifact = "io.grpc:protoc-gen-grpc-java:${grpcVersion}"
        }
        reactor {
            artifact = "com.salesforce.servicelibs:reactor-grpc:${reactiveGrpcVersion}"
        }
    }
    generateProtoTasks {
        all().forEach { task ->
            task.plugins {
                grpc {}
                reactor {}
            }
        }
    }
}

tasks.compileJava {
    dependsOn tasks.named('generateProto')
}

sourceSets.main.java.srcDirs 'build/generated/source/proto/main/java'