/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	usagepb "illum.io/cloud/api/generated/usage"
	"illum.io/cloud/api/services/usage/pkg/usage"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type UsageClient struct {
	usagepb.UsageServiceClient
	con *grpc.ClientConn
}

func (c *UsageClient) Close() {
	_ = c.con.Close()
}

// NewUsageClient creates a new gRPC client for service running on url endpoint
func NewUsageClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*UsageClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := usagepb.NewUsageServiceClient(con)

	return &UsageClient{
		con:                con,
		UsageServiceClient: client,
	}, nil
}

// NewUsageClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewUsageClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*UsageClient, error) {
	url, ok := config.LookupGRPCService(usage.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", usage.ServiceName)
	}
	return NewUsageClient(ctx, url, opts...)
}
