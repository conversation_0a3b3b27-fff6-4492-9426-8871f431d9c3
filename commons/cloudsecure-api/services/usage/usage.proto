// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package usage;

import "common/inventorydata/inventorydata.proto";

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/api/generated/usage";

message FetchTenantUsagesRequest {
  string tenant_id = 1 [(validate.rules).string.uuid = true];
}

message FetchTenantUsagesResponse {
  map<string, uint32> count_map = 1;
  uint64 bytes_used = 2;
}

message Tenant {
  string id = 1;
  string preferred_region = 2;
  string country_code = 3;
  optional bool disabled = 4;
  optional bool trial = 5;
  optional google.protobuf.Timestamp trial_end_date = 6;
  string name = 7;
}

message GetTenantListResponse {
  repeated Tenant tenants = 1;
}

message ProcessTenantDailyUsageRequest {
  string tenant_id = 1 [(validate.rules).string.uuid = true];
  optional google.protobuf.Timestamp date = 2;
}

message TenantUsage {
  string tenant_id = 1;
  string tenant_name = 2;
  string cloud = 3;
  google.protobuf.Timestamp created_at = 4;
  uint32 vms = 5;
  uint32 serverless_fns = 6;
  uint32 cntnr_hosts = 7;
  uint32 serverless_cntnr = 8;
  uint32 cloud_dbs = 9;
  uint64 bytes_used = 10;
}

message ProcessTenantDailyUsageResponse {
  repeated TenantUsage tenantusages = 1;
}

message TenantUsageIWLH {
  string tenant_id = 1;
  string tenant_name = 2;
  string cloud = 3;
  google.protobuf.Timestamp created_at = 4;
  uint32 vms = 5;
  uint32 serverless_fns = 6;
  uint32 cntnr_hosts = 7;
  uint32 serverless_cntnr = 8;
  uint32 cloud_dbs = 9;
  double total_iwlh = 10;
  uint64 bytes_used = 11;
}

message GetTenantUsageRequest {
  google.protobuf.Timestamp from_time = 1;
  google.protobuf.Timestamp to_time = 2;
  bool day_zero_only = 3;
}

message GetTenantUsageResponse {
  repeated TenantUsageIWLH tenant_usages_iwlh = 1;
}

message DayMonthYear {
  uint32 day = 1;
  uint32 month = 2;
  uint32 year = 3;
}

message UsageSyncWithFlowRequest {
  DayMonthYear fromDayMonthYear = 1;
  DayMonthYear toDayMonthYear = 2;
  repeated string tenantIdList = 3;
}

message UsageSyncWithFlowResponse {

}

message DeleteTenantDataRequest {}

message DeleteTenantDataResponse {}

service UsageService {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
        get: "/api/v1/version"
      };
    }

    rpc FetchTenantUsages(FetchTenantUsagesRequest) returns (FetchTenantUsagesResponse) {
    }

    rpc GetTenantList(google.protobuf.Empty) returns (GetTenantListResponse) {
    }

    rpc ProcessTenantDailyUsage(ProcessTenantDailyUsageRequest) returns (ProcessTenantDailyUsageResponse) {
    }

    rpc GetTenantUsage(GetTenantUsageRequest) returns (GetTenantUsageResponse) {
        option (google.api.http) = {
        get: "/api/v1/usage"
      };
    }

    rpc UsageSyncWithFlow(UsageSyncWithFlowRequest) returns (UsageSyncWithFlowResponse) {
      // REST API isn't configured as we don't intent to use this from UI.
    }

    rpc DeleteTenantData(DeleteTenantDataRequest) returns (DeleteTenantDataResponse) {
      // REST API isn't configured for this API as we don't intend to use it via REST.
      // We will use this only internally.
    }
}



