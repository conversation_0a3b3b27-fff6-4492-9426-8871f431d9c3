syntax = "proto3";

package iplists;

import "google/api/annotations.proto";
import "google/api/httpbody.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/type/interval.proto";
import "validate/validate.proto";
import "google/protobuf/any.proto";
import "common/wipeout/wipeout.proto";

option go_package = "illum.io/cloud/api/generated/bridge";

message IPListAttribute {
  string id = 1;
  string name = 2;
}

message User {
  string id = 1;
  string email = 2;
}

message IPList {
  string id = 1;
  string name = 2;
  string description = 3;
  IPListAttribute attribute = 4;
  User modified_by = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  repeated string addrs = 8;

}

message CreateIPListRequest {
  string name = 1;
  string description = 2;
  string attribute = 3;
  repeated string addrs = 4;
}

message CreateIPListResponse {
  IPList item = 1;
}

message ListIPListsRequest {
  int32 limit = 1;
  string reserved = 2 [deprecated = true];
  string prevToken = 3 [deprecated = true];
  optional string attribute_id = 4;
  string pageToken = 5;
}

message ListIPListsResponse {
  repeated IPList items = 1;
  string nextToken = 2;
  string prevToken = 3;
}

message GetIPListRequest {
  string id = 1;
}

message GetIPListResponse {
  IPList item = 1;
}

message UpdateIPListRequest {
  string id = 1;
  optional string name = 2;
  optional string description = 3;
  optional string attribute = 4;
  repeated string addrs = 5;
}

message UpdateIPListResponse {
  IPList item = 1;
}

message DeleteIPListRequest {
  string id = 1;
}

message DeleteIPListResponse {
}


message CreateIPListAttributeRequest {
  string name = 1;
}

message CreateIPListAttributeResponse {
  IPListAttribute item = 1;
}

message ListIPListAttributesRequest {

}

message ListIPListAttributesResponse {
  repeated IPListAttribute items = 1;
}

message DeleteIPListAttributeRequest {
  string id = 1;
}

message DeleteIPListAttributeResponse {

}

service IPListsService {
  rpc CreateIPListV1(CreateIPListRequest) returns (CreateIPListResponse) {
    option (google.api.http) = {
      post: "/api/v1/iplists"
      body: "*"
    };
  }

  rpc ListIPListsV1(ListIPListsRequest) returns (ListIPListsResponse) {
    option (google.api.http) = {
      get: "/api/v1/iplists"
    };
  }

  rpc GetIPListV1(GetIPListRequest) returns (GetIPListResponse) {
    option (google.api.http) = {
      get: "/api/v1/iplists/{id}"
    };
  }

  rpc UpdateIPListV1(UpdateIPListRequest) returns (UpdateIPListResponse) {
    option (google.api.http) = {
      put: "/api/v1/iplists/{id}"
      body: "*"
    };
  }

  rpc DeleteIPListV1(DeleteIPListRequest) returns (DeleteIPListResponse) {
    option (google.api.http) = {
      delete: "/api/v1/iplists/{id}"
    };
  }

  rpc CreateIPListAttributesV1(CreateIPListAttributeRequest) returns (CreateIPListAttributeResponse) {
    option (google.api.http) = {
      post: "/api/v1/iplist_attributes"
      body: "*"
    };
  }

  rpc ListIPListAttributesV1(ListIPListAttributesRequest) returns (ListIPListAttributesResponse) {
    option (google.api.http) = {
      get: "/api/v1/iplist_attributes"
    };
  }

  rpc DeleteIPListAttributeV1(DeleteIPListAttributeRequest) returns (DeleteIPListAttributeResponse) {
    option (google.api.http) = {
      delete: "/api/v1/iplist_attributes/{id}"
    };
  }

  rpc DeleteTenantData(wipeout.DeleteTenantDataRequest) returns (wipeout.DeleteTenantDataResponse) {
  }

  // Uploading file is not supported by transcoding HTTP to GRPC
  // https://grpc-ecosystem.github.io/grpc-gateway/docs/mapping/binary_file_uploads/
//  rpc UploadIPListsV1(google.api.HttpBody) returns (google.protobuf.Empty) {
//    option (google.api.http) = {
//      post: "/api/v1/iplists/upload"
//      body: "*"
//    };
//  }
}
