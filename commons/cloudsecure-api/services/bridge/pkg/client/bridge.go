package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	bridgepb "illum.io/cloud/api/generated/bridge"
	"illum.io/cloud/api/services/bridge/pkg/bridge"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type BridgeClient struct {
	bridgepb.BridgeServiceClient
	con *grpc.ClientConn
}

func (c *BridgeClient) Close() {
	_ = c.con.Close()
}

// NewBridgeClient creates a new gRPC client for service running on url endpoint
func NewBridgeClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*BridgeClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := bridgepb.NewBridgeServiceClient(con)

	return &BridgeClient{
		con:                 con,
		BridgeServiceClient: client,
	}, nil
}

// NewBridgeClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewBridgeClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*BridgeClient, error) {
	url, ok := config.LookupGRPCService(bridge.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", bridge.ServiceName)
	}
	return NewBridgeClient(ctx, url, opts...)
}
