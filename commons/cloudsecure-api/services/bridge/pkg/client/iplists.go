package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	bridgepb "illum.io/cloud/api/generated/bridge"
	"illum.io/cloud/api/services/bridge/pkg/bridge"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type IPListsClient struct {
	bridgepb.IPListsServiceClient
	conn *grpc.ClientConn
}

func (c *IPListsClient) Connection() {
	_ = c.conn.Close()
}

func NewIPListsClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*IPListsClient, error) {
	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := bridgepb.NewIPListsServiceClient(con)

	return &IPListsClient{
		conn:                 con,
		IPListsServiceClient: client,
	}, nil
}

// NewIPListsClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewIPListsClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*IPListsClient, error) {
	url, ok := config.LookupGRPCService(bridge.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", bridge.ServiceName)
	}
	return NewIPListsClient(ctx, url, opts...)
}
