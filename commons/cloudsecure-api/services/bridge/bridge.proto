// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package bridge;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "validate/validate.proto";
import "google/protobuf/any.proto";
import "google/type/interval.proto";

import "common/version/version.proto";
import "common/inventorydata/inventorydata.proto";
import "common/inventorydata/external.proto";
import "common/flow/flow.proto";
import "common/flow/query.proto";
import "bridge/iplists.proto";
import "common/label/label.proto";
import "common/wipeout/wipeout.proto";

option go_package = "illum.io/cloud/api/generated/bridge";

message VersionsResponse {
  repeated version.VersionResponse versions= 1;
}

enum GroupRelationFilterType {
  RELATION_TYPE_UNSPECIFIED = 0;
  EKS = 1;
  AKS = 2;
  SUBNET = 3;
  NETWORK = 4;
}

message GroupRelationFilter {
  GroupRelationFilterType filter_type = 1;
  repeated string resource_ids = 2;
  repeated string resource_csp_ids = 3;
}

message ListResourcesRequest {
  int32 max_results = 1;
  repeated string clouds = 2;
  repeated string object_types = 3;
  repeated string categories = 4;
  repeated string regions = 5;
  repeated string account_ids = 6;
  repeated string network_ids = 7;
  repeated string subnet_ids = 8;
  repeated string resource_ids = 9;
  repeated string csp_ids = 10;
  map<string, inventorydata.stringList> tags = 11;
  bool exclude_references = 12;
  optional string page_token = 13;
  optional inventorydata.SortBy sortBy = 14;
  repeated string subcategories = 15;
  // Support for VPC/Vnet and Subnet CSP_IDs
  repeated string network_csp_ids = 16;
  repeated string subnet_csp_ids = 17;

  optional bool with_total_count = 18;
  repeated RelationsFilter relation_filters = 19;

  repeated string resource_names = 20;

  // Partial Match Filter
  repeated inventorydata.PartialMatchFilter partial_match_filters = 21;

  repeated string label_ids = 22;
  repeated label.ApplicationFilter application_filters = 23;

  // Property Filter
  repeated inventorydata.PropertyFilter property_filters = 24;
  repeated string states = 25;

  optional bool json_view = 26;

  bool include_enforcement_status = 27;
  repeated string ip_addresses = 28;

  optional GroupRelationFilter group_relation_filter = 29;
}

message ListResourcesResponse {
  repeated inventorydata.Resource items = 1;
  uint32 total_size = 2;
  optional string next_page_token = 3;
  optional string prev_page_token = 4;
  uint32 page = 5;
}

message InventoryResourcesRequest {
  int32 max_results = 1;
  repeated string clouds = 2;
  repeated string object_types = 3;
  repeated string categories = 4;
  repeated string regions = 5;
  repeated string account_ids = 6;
  repeated string network_ids = 7;
  repeated string subnet_ids = 8;
  repeated string resource_ids = 9;
  repeated string csp_ids = 10;
  map<string, inventorydata.stringList> tags = 11;
  optional bool exclude_references = 12; // boolean to excluded referenced resources from resource map. Returned resources will still have relationships.
  repeated string ip_addresses = 13;
}

message InventoryResourcesResponse {
  map<string, inventorydata.Resource> resource_map = 1;

  // truncated_results is an indicator that shows whether an inventory API response includes all resources that match
  // request parameters.
  // For example, if the query was for all vms & the resource map only contains 50 out of 100 total, this would be set to true
  optional bool truncated_results = 2;

  // primary_resource_count is the count of resources that match the request parameters.
  // For example, if you query for a VM with a resource id, that VM is the primary resource. The referenced resources
  // like the VM's nic, sg, and etc won't count towards the primary_resource_count
  uint32 primary_resource_count = 3;
}

message InventoryCountsRequest {
  map<string, inventorydata.stringList> tags = 1;
  repeated string clouds = 2;
  repeated string object_types = 3;
  repeated string categories = 4;
  repeated string regions = 5;
  repeated string account_ids = 6;
  repeated string network_ids = 7;
  repeated string subnet_ids = 8;
  repeated string resource_ids = 9;
  repeated string csp_ids = 10;
}

message InventoryCountsResponse {
  map<string, uint32> count_map = 1;
}

message InventoryRegionsRequest {}

message InventoryRegionsResponse {
  repeated inventorydata.DecoratedRegion regions = 1;
}


message FlowLogConfigsRequest {
  string cloud = 1 [(validate.rules).string.min_len = 3];
  repeated string regions = 2;
  repeated string account_ids = 3;
  repeated string resource_ids = 4;
  repeated string csp_ids = 5;
}

message ListFlowLogConfigsResponse {
  repeated inventorydata.FlowLogSummary summaries = 1;
}

message FlowLogConfigDetailRequest {
  string cloud = 1 [(validate.rules).string.min_len = 3];
  string account_csp_id = 2 [(validate.rules).string.min_len = 1];
}

message FlowLogConfigDetailResponse {
  string account_csp_id = 1;
  string account_name = 2;
  string access = 3;
  string cloud = 4;
  repeated inventorydata.FlowLogConfig configs = 5;
}

message InventoryMetadataRequest {
  repeated string clouds = 1;
  inventorydata.MetadataType metadata_type = 2;
  repeated string regions = 3;
  repeated string account_ids = 4;
  repeated string object_types = 5;
  repeated string subcategories = 6;
  string partial_filter = 7;

  optional uint32 max_results = 8;
  repeated string ip_addresses = 9;
}

message InventoryMetadataResponse {
  repeated string values = 1;
  // want to eventually move to new_values since metadata values like account & tags can't just be a list of strings
  google.protobuf.Struct new_values = 2;
}

message InventoryTagsRequest {
  repeated string clouds = 1;
  repeated string regions = 2;
  repeated string account_ids = 3;
  repeated string object_types = 5;
}

message InventoryTagsResponse {
  repeated inventorydata.Tag Tags = 1;
}

message GetNetworkInterfacesByResourceIdsRequest {
  repeated string resource_ids = 1 [(validate.rules).repeated.items.string.uuid = true];
}

message GetNetworkInterfacesByResourceIdsResponse {
  google.protobuf.Struct resource_id_to_nic_ids = 1;
}

message GetNetworkInterfacesByIPsRequest {
  repeated string ips = 1;

  // a list of urls of the inventory that need not be queried
  repeated string skip_addrs = 2;
}

message GetNetworkInterfacesByIPsResponse {
  map<string, string> ip_to_nic_id = 1;
}

message SyncStatusRequest {}

message SyncStatusResponse {
  repeated SyncStatusDetails dp_sync_status_details = 1;
}

message SyncStatusDetails {
  string dataplane_name = 1;
  string account_id = 2;
  SyncStatus sync_status = 3;
  google.protobuf.Timestamp finished_at = 4;
}

enum SyncStatus {
  SYNCSTATUS_UNSPECIFIED = 0;
  FINISHED = 1;
  IN_PROGRESS = 2;
}

message ListExternalResourcesRequest {
  int32 max_results = 1;
  repeated string types = 2;
  repeated string resource_ids = 3;
  repeated string ips = 4;
  bool exclude_references = 5;
  optional string page_token = 6;
  // withTotal says if response should provide total size of resources
  optional bool withTotalCount = 7;

  inventorydata.ExternalSortBy sortBy = 8;

  // Relationship filters
  repeated ExtRelationsFilter ext_relation_filters = 9;
}

message ExtRelationsFilter {
  string resource_type = 1;
  repeated string relation_types = 2;
  bool exclude = 3;
}

message ListExternalResourcesResponse {
  repeated inventorydata.ExternalResource items = 1;
  uint32 total_size = 2;
  optional string next_page_token = 3;
  optional string prev_page_token = 4;
  uint32 page = 5;
}

message ExtResourcesByResourceIDRequest {
  repeated string resource_id = 1;
}

message ExtResourcesByIPRequest {
  repeated string ip = 1;
}

message ExternalResourceResponse {
  inventorydata.ExternalResource ext_resource = 1;
}


// ------------------------
// TODO: Definitions from here to 'message Flow' should be moved to a common shared (with flowsearch) proto file.
// Until then these need to be kept in sync (if needed) manually with flowsearch definitions

enum SrcDst {
  SRCDST_UNSPECIFIED = 0;
  SRC = 1;
  DST = 2;
}

enum FlowStatus {
  FLOWSTATUS_UNSPECIFIED = 0;
  ALLOWED = 1;
  DENIED = 2;
}

// message IpAddress { // TODO: need global message for this, including separate ipv4 and ipv6
//   string ip = 1;
// }


// message ResourceID {
//   string id = 1 [(validate.rules).string.uuid = true];
// }

message DirectionEntity {
  SrcDst src_dst = 1;
  oneof type {
    string ip_address = 2;      // NOTE: This is different from what flow does (and not in sync...) TODO: fix
    string resource_id = 3;      // NOTE: This is different from what flow does (and not in sync...) TODO: fix
  }
}

message EndPoint {
  string ip_address = 1;
  string resource_id = 2;
  optional iplists.IPList ip_list = 3;
}

message Service {
  uint32 protocol = 1 [(validate.rules).uint32.lte = 255];
  uint32 port = 2 [(validate.rules).uint32.lte = 65535];
}

message Flow {
  FlowStatus status = 1;
  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
  EndPoint src = 4;
  EndPoint dst = 5;
  uint32 dst_port = 6;
  uint32 protocol = 7;
  uint64 bytes = 8;
}

message FlowsRequest {
  google.type.Interval period = 1;
  repeated DirectionEntity entities = 2;
  optional FlowStatus status = 3;
  optional uint32 dst_port = 4;
  optional uint32 protocol = 5;
  optional int32 max_results = 6;
  repeated Service services = 7;
  optional flow.Expr query = 8; // query can only be used instead of all other filter params
  repeated flow.FlowPart group_bys = 9;
  optional bool sort_asc_end_time = 1001; // Preliminary, do not use
}

message FlowsResponse {
  repeated Flow flows = 1;
  map<string, inventorydata.Resource> resource_map = 2;
  bool truncated_results = 3;
  map<string, inventorydata.ExternalResource> ext_resource_map = 4;
}

message Label {
  string id = 1;
  string key = 2;
  string href = 3;
  string value = 4;
}

message Decoration {
  inventorydata.Resource resource = 1;
  repeated Label labels = 2;
}

message DecorationsRequest {
  repeated string resource_ids = 1;
}

message DecorationsResponse {
  map<string, Decoration> decorations = 1;
}

message FlowsWithDecorationsRequest {
  google.type.Interval period = 1;
  optional int32 max_results = 2;
  optional flow.Expr query = 3;
  // group_bys are no longer supported by the new api, since flows are streamed
  optional bool sort_asc_end_time = 4;  // not properly implemented yet
}

message FlowsWithDecorationsResponse {
  repeated Flow flows = 1;
  // map key: uuid of the resource
  map<string, Decoration> decorations = 2;
}

message FlowsIndexSizeRequest {
  google.protobuf.Timestamp time = 1;     // Hour for which to get size
}

message FlowsIndexSizeResponse {
  uint64 size_bytes = 1; // Size of index as stored
}

message GetHistoryRequest {
  google.protobuf.Timestamp start_time = 1;
  google.protobuf.Timestamp end_time = 2;
  ListResourcesRequest filters = 3;
}

message GetHistoryResponse {
  repeated inventorydata.Resource items = 1;
}

enum ResourceDistributionField {
  UNSPECIFIED = 0;
  SUBCATEGORY = 1;
  RESOURCE_TYPE = 2;
  CATEGORY = 3;
  CLOUD = 4;
}

message ResourceDistribution {
  string field_name = 1;
  float percentage = 2;
  uint32 count = 3;
}

message ResourceDistributionRequest {
  optional ResourceDistributionField field = 1;
}

message ResourceDistributionResponse {
  repeated ResourceDistribution resource_distribution = 1;
}

enum Operator {
  OR = 0;
  //AND = 1; we only currently support OR operator for relation filters
}

message SignalLoginRequest {
}

message SignalLoginResponse {
}

// RelationsFilter specifies resources of resource_type with any relation specified in relation_types
// resource_type specifies the type that the relation filters applies
// relation_types specifies the types that will be included or excluded
// exclude is a boolean that specifies whether we should include or exclude the relation_types
// operator specifies whether we AND/OR the relations_types (e.g. exclude resource if ALL relation_types exists or exclude resource if ANY relation_type exists)
// example request (fetches all nsgs without flow logs):
// {
// "with_total_count": false,
// "object_types": ["Microsoft.Network/networkSecurityGroups"],
// "relation_filters": [{"resource_type": "Microsoft.Network/networkSecurityGroups", "exclude": true, "relation_types": ["Microsoft.Network/networkWatchers/FlowLogs"]}],
// "max_results": 50
// }
message RelationsFilter {
  string resource_type = 1;
  repeated string relation_types = 2;
  bool exclude = 3;
  Operator operator = 4;
}

message GetEndpointIDsByResourceIDsRequest {
  repeated string resource_ids = 1 [(validate.rules).repeated.items.string.uuid = true];
}

message GetEndpointIDsByResourceIDsResponse {
  repeated inventorydata.EndpointIDs resource_id_to_endpoint_ids = 1;
}

message GetEndpointIDsByIPsRequest {
  repeated string ips = 1;
}

message GetEndpointIDsByIPsResponse {
  map<string, string> ip_to_endpoint_id_map = 1;
}

service BridgeService {
  rpc GetHistory(GetHistoryRequest) returns (GetHistoryResponse) {
    option (google.api.http) = {
      post: "/api/v1/inventory/history"
      body: "*"
    };
  }

  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
    option (google.api.http) = {
      get: "/api/v1/version"
    };
  }

  rpc InventoryVersions(google.protobuf.Empty) returns (VersionsResponse) {
  }

  rpc InventoryResources(InventoryResourcesRequest) returns (InventoryResourcesResponse) {
    option (google.api.http) = {
      post: "/api/v1/inventory/resources"
        body: "*"
    };
  }

  rpc ListResources(ListResourcesRequest) returns (ListResourcesResponse) {
    option (google.api.http) = {
      post: "/api/v1/bridge/resources"
      body: "*"
    };
  }

  rpc InventoryCounts(InventoryCountsRequest) returns (InventoryCountsResponse) {
    option (google.api.http) = {
      post: "/api/v1/inventory/counts"
      body: "*"
    };
  }

  rpc InventoryMetadata(InventoryMetadataRequest) returns (InventoryMetadataResponse) {
    option (google.api.http) = {
      get: "/api/v1/inventory/metadata"
    };
  }

  // Note: InventoryRegions returns a list of regions decorated with accountId & cloud
  // InventoryMetadata returns regions as a list of strings
  // This API may return duplicate regions since the same region can be in different accounts & cloud
  rpc InventoryRegions(InventoryRegionsRequest) returns (InventoryRegionsResponse) {
    option (google.api.http) = {
      get: "/api/v1/inventory/regions"
    };
  }

  rpc GetNetworkInterfacesByResourceIds(GetNetworkInterfacesByResourceIdsRequest) returns (GetNetworkInterfacesByResourceIdsResponse) {
    option (google.api.http) = {
      get: "/api/v1/inventory/nicids"
    };

  }

  rpc InventoryTags(InventoryTagsRequest) returns (InventoryTagsResponse) {
    option (google.api.http) = {
      get: "/api/v1/inventory/tags"
    };
  }

  rpc FlowLogConfigSummary(FlowLogConfigsRequest) returns (ListFlowLogConfigsResponse) {
    option (google.api.http) = {
      post: "/api/v1/inventory/flowlogconfigs"
      body: "*"
    };
  }

  rpc FlowLogConfigDetail(FlowLogConfigDetailRequest) returns (FlowLogConfigDetailResponse) {
    option (google.api.http) = {
      post: "/api/v1/inventory/flowlogconfigs/detail"
      body: "*"
    };
  }

  rpc Decorations(DecorationsRequest) returns (DecorationsResponse) {

  }

  rpc Flows(FlowsRequest) returns (FlowsResponse) {
    option (google.api.http) = {
      post: "/api/v1/flows"
      body: "*"
    };
  }

  rpc FlowsWithDecorations(FlowsWithDecorationsRequest) returns (stream
  FlowsWithDecorationsResponse) {

  }

  rpc FlowsIndexSize(FlowsIndexSizeRequest) returns (FlowsIndexSizeResponse) {
  }

  rpc DeleteTenantDataForFlows(wipeout.DeleteTenantDataRequest) returns (wipeout.DeleteTenantDataResponse) {
  }

  rpc SignalLogin(SignalLoginRequest) returns (SignalLoginResponse) {
    option (google.api.http) = {
        post: "/api/v1/signallogin"
      };
  }

  rpc GetNetworkInterfacesByIPs(GetNetworkInterfacesByIPsRequest) returns
  (GetNetworkInterfacesByIPsResponse) {

  }

  rpc ResourceDistribution (ResourceDistributionRequest) returns (ResourceDistributionResponse) {
    option (google.api.http) = {
      get: "/api/v1/insights/inventory/rsrcdistribution"
    };
  }

  rpc GetEndpointIDsByResourceIDs(GetEndpointIDsByResourceIDsRequest) returns (GetEndpointIDsByResourceIDsResponse) {
    option (google.api.http) = {
      get: "/api/v1/inventory/endpointids"
    };
  }


  rpc GetEndpointIDsByIPsStream(stream GetEndpointIDsByIPsRequest) returns (stream GetEndpointIDsByIPsResponse) {}

  rpc SyncStatus (SyncStatusRequest) returns (SyncStatusResponse) {
    option (google.api.http) = {
      get: "/api/v1/bridge/syncstatus"
    };
  }

  rpc ListExternalResources(ListExternalResourcesRequest) returns (ListExternalResourcesResponse) {
    option (google.api.http) = {
      post: "/api/v1/inventory/external/resources"
      body: "*"
    };

  }
  rpc GetExternalResourceByResourceID(ExtResourcesByResourceIDRequest) returns (ExternalResourceResponse) {
    option (google.api.http) = {
      get: "/api/v1/inventory/external/resource/id/{resource_id}"
    };
  }

  rpc GetExternalResourceByIP(ExtResourcesByIPRequest) returns (ExternalResourceResponse) {
    option (google.api.http) = {
      get: "/api/v1/inventory/external/resource/ip/{ip}"
    };
  }

}
