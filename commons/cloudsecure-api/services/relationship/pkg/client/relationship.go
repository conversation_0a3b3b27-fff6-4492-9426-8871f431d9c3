package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	relationshippb "illum.io/cloud/api/generated/relationship"
	"illum.io/cloud/api/services/relationship/pkg/relationship"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type RelationshipClient struct {
	relationshippb.RelationshipServiceClient
	con *grpc.ClientConn
}

func (c *RelationshipClient) Close() {
	_ = c.con.Close()
}

// NewRelationshipClient creates a new gRPC client for service running on url endpoint
func NewRelationshipClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*RelationshipClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := relationshippb.NewRelationshipServiceClient(con)

	return &RelationshipClient{
		con:                       con,
		RelationshipServiceClient: client,
	}, nil
}

// NewRelationshipClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewRelationshipClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*RelationshipClient, error) {
	url, ok := config.LookupGRPCService(relationship.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", relationship.ServiceName)
	}
	return NewRelationshipClient(ctx, url, opts...)
}
