// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package relationship;

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/api/generated/services/relationship/relationship";

enum CtrlPlaneConsumer {
  CTRL_PLANE_UNSPECIFIED = 0;
  CTRL_PLANE_LABELING = 1;
}

message Resource {
  string type = 1;

  string csp_id = 2;

  bool deleted = 3;

  bool updated = 4;

  string resource_id = 5;

  string region = 6;
}

// Sync result is a message sent via message queue to notify that syncing is finished
message SyncResult {
  string tenant_id = 1;
  string account_id = 2;
  google.protobuf.Timestamp timestamp = 3;
  repeated Resource resources = 4;
  string cloud = 5;
  string dataplane = 6;
}

message SyncRequest {
  string cloud = 1;
  string tenantId = 2;
  string accountId = 3;
}


service RelationshipService {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
        get: "/api/v1/version"
      };
    }
//    // Add more API methods here
//    rpc ListResources(ListResourcesRequest) returns (ListResourcesResponse) {
//      option (google.api.http) = {
//        post: "/api/v1/inventory/resources"
//        body: "*"
//      };
//    }

    rpc Sync(SyncRequest) returns (google.protobuf.Empty); //TODO: change response to have meaning
}
