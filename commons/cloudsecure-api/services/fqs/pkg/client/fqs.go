/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	fqspb "illum.io/cloud/api/generated/fqs"
	"illum.io/cloud/api/services/fqs/pkg/fqs"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type FQSClient struct {
	fqspb.FQSServiceClient
	con *grpc.ClientConn
}

func (c *FQSClient) Close() {
	_ = c.con.Close()
}

// NewFQSClient creates a new gRPC client for service running on url endpoint
func NewFQSClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*FQSClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := fqspb.NewFQSServiceClient(con)

	return &FQSClient{
		con:              con,
		FQSServiceClient: client,
	}, nil
}

// NewFQSClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewFQSClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*FQSClient, error) {
	url, ok := config.LookupGRPCService(fqs.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", fqs.ServiceName)
	}
	return NewFQSClient(ctx, url, opts...)
}
