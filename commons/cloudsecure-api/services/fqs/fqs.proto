// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package fqs;


import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/type/interval.proto";
import "validate/validate.proto";

import "common/version/version.proto";
import "common/inventorydata/inventorydata.proto";
import "common/flow/flow.proto";
import "common/flow/query.proto";
import "bridge/iplists.proto";

option go_package = "illum.io/cloud/api/generated/fqs";

enum FlowStatus {
  FLOWSTATUS_UNSPECIFIED = 0;
  ALLOWED = 1;
  DENIED = 2;
}

// message ResourceID {
//   string id = 1 [(validate.rules).string.uuid = true];
// }

message CloudResource {
  string resource_uuid = 1;
  repeated Label labels = 2;
  string type = 3;
  // tbd - at some point put CloudResourceInfo here rather than in resource_map (however UI prefers it in resource_map for now)
}

message Network {
  string href = 1;
  string name = 2;
}

message CoreService {
  uint32 port = 1 [(validate.rules).uint32.lte = 255];
  uint32 proto = 2 [(validate.rules).uint32.lte = 65535];
  string process_name = 3; //tbd - optional
  string user_name = 4;
  string windows_service_name = 5;
  string os_type = 6;
}

message Workload {
  bool endpoint = 1;
  string enforcement_mode = 2; // tbd - optional enum EnforcementMode?
  string hostname = 3;
  string href = 4;
  repeated Label labels = 5;
  bool managed = 6;
  string name = 7; // tbd - optional?
  string os_type = 8; // tbd - optional enum OsType?
  string id = 9; // tbd - move out 'id'? Have 'href'?
}

enum OsType {
  OS_TYPE_UNSPECIFIED = 0;
  OS_TYPE_WINDOWS = 1;
  OS_TYPE_LINUX = 2;
}

enum EnforcementMode {
  EM_IDLE = 0;
  EM_VISIBILITY_ONLY = 1;
  EM_FULL = 2;
  EM_SELECTIVE = 3;
}

message Rule {
  oneof type {
    string href = 1;
    string essential_service_rule = 2;
  }
}

message EnforcementBoundaries {
  string href = 1;
}

message Label {
  string href = 1;
  string key = 2;
  string value = 3;
}

message IPList { // tbd
  optional string name = 1;
  string href = 2;
  optional int64 size = 3;
  repeated EnforcementBoundaries rules = 4;
  repeated EnforcementBoundaries deny_rules = 5;
  repeated EnforcementBoundaries override_deny_rules = 6;
}

message VirtualServer {
  optional string name = 1;
  string href = 2;
  optional string enforcement_mode = 3;
  repeated Label labels = 4;
}

message VirtualService {
  optional string name = 1;
  string href = 2;
  repeated Label labels = 3;
  optional string workload_enforcement_mode = 4;
}

message Endpoint {
  string ip = 1; // tbd- optional?
  optional Workload workload = 2;
  optional CloudResource cloud_resource = 3;
  optional VirtualServer virtual_server = 4;
  optional VirtualService virtual_service = 5;
  optional string fqdn = 6;
  repeated IPList ip_lists = 7;
  string href = 8;
}

message CloudResourceInfo {
  string csp_id = 1;
  string account_id = 2;
  string tenant_id = 3;
  string cloud = 4;
  string name = 5;
  string object_type = 6;
  string region = 7;
  string category = 8;
  string subcategory = 9;
  string state = 10;
  google.protobuf.Timestamp created_at = 11;
  string account_name = 12;
  google.protobuf.Struct properties = 13;
  map<string, string> tags = 14;
}

enum FlowDir {
  FLOW_DIR_UNSPECIFIED = 0;
  FLOW_DIR_INBOUND = 1;
  FLOW_DIR_OUTBOUND = 2;
}

message TimestampRange {
  google.protobuf.Timestamp first_detected = 1;
  google.protobuf.Timestamp last_detected = 2;
}

enum PolicyDecision {
  POLICY_DECISION_UNSPECIFIED = 0;
  POLICY_DECISION_ALLOWED = 1;
  POLICY_DECISION_BLOCKED = 2;
  POLICY_DECISION_POTENTIALLY_BLOCKED = 3;
}

enum BoundaryDecision {
  BOUNDARY_DECISION_UNSPECIFIED = 0;
  BOUNDARY_DECISION_BLOCKED = 1;
  BOUNDARY_DECISION_BLOCKED_BY_OVERRIDE_DENY = 2;
  BOUNDARY_DECISION_BLOCKED_NON_ILLUMIO_RULE = 3;
}

enum DraftPolicyDecision {
  DRAFT_POLICY_DECISION_UNSPECIFIED = 0;
  DRAFT_POLICY_DECISION_ALLOWED = 1;
  DRAFT_POLICY_DECISION_BLOCKED = 2;
  DRAFT_POLICY_DECISION_POTENTIALLY_BLOCKED = 3;
  DRAFT_POLICY_DECISION_ALLOWED_ACROSS_BOUNDARY = 4;
  DRAFT_POLICY_DECISION_BLOCKED_BY_BOUNDARY = 5;
  DRAFT_POLICY_DECISION_POTENTIALLY_BLOCKED_BY_BOUNDARY = 6;
  DRAFT_POLICY_DECISION_BLOCKED_BY_OVERRIDE_DENY = 7;
}

enum Transmission {
  TRANSMISSION_UNSPECIFIED = 0;
  TRANSMISSION_BROADCAST = 1;
  TRANSMISSION_MULTICAST = 2;
}

enum State {
  STATE_UNSPECIFIED = 0;
  STATE_ACTIVE = 1;
  STATE_CLOSED = 2;
  STATE_TIMED_OUT = 3;
  STATE_SNAPSHOT = 4;
  STATE_NEW = 5;
  STATE_INCOMPLETE = 6;
}

message FederatedFlow {
  //FlowStatus status = 1;
  google.protobuf.Timestamp start_time = 2;
  google.protobuf.Timestamp end_time = 3;
  Endpoint src = 4;
  Endpoint dst = 5;
  uint32 dst_port = 6;
  uint32 protocol = 7;
  uint64 bytes = 8;

  repeated string caps = 9; // "write"
  string client_type = 10; // "server","endpoint","protobuf","nen","bulk","unknown". tbd - optional?
  int32 dst_bi = 11; //tbd - optional?
  int32 dst_bo = 12; //tbd - optional?
  string flow_direction = 13; // tbd - use FlowDir?
  Network network = 14; // tbd - optional?
  uint32 num_connections = 15;
  string policy_decision = 16; // tbd - use enum PolicyDecision?
  int32 seq_id = 17;
  CoreService service = 18;
  string state = 19; // tbd - use optional enum State?
  string origin = 20;
  string boundary_decision = 21; // tbd- use enum BoundaryDecision
  optional string draft_policy_decision = 22; // tbd - use enum DraftPolicyDecision
  optional string transmission = 23; // tbd - use enum Transmission
  TimestampRange timestamp_range = 24;
  optional int32 icmp_type = 25;
  optional int32 icmp_code = 26;
  repeated EnforcementBoundaries rules = 27;
  repeated EnforcementBoundaries deny_rules = 28;
  repeated EnforcementBoundaries override_deny_rules = 29;
}

message EndPoint {
  string ip_address = 1;
  string resource_id = 2;
  optional iplists.IPList ip_list = 3;
}

message PortProto {
  uint32 port = 1 [(validate.rules).uint32.lte = 65535];
  uint32 proto = 2 [(validate.rules).uint32.lte = 255];
  string process_name = 3;
  string windows_service_name = 4;
  uint32 to_port = 5 [(validate.rules).uint32.lte = 65535];
}

message Service {
  //uint32 protocol = 1 [(validate.rules).uint32.lte = 255];
  //uint32 port = 2 [(validate.rules).uint32.lte = 65535];
  repeated PortProto include = 1;
  repeated PortProto exclude = 2;
}

message region {
  string pce_fqdn = 1;
  bool responded = 2;
  optional int32 matches_count = 3;
  optional int32 flows_count = 4;
}

enum QueryStatus {
  QUERY_STATUS_UNSPECIFIED = 0;
  QUERY_STATUS_QUEUED = 1;
  QUERY_STATUS_WORKING = 2;
  QUERY_STATUS_COMPLETED = 3;
  QUERY_STATUS_FAILED = 4;
  QUERY_STATUS_KILLED = 5;
  QUERY_STATUS_REQUESTED = 6;
}

message FlowInfo {
 string query_id = 1;
 string status = 2;  // tbd - use QueryStatus enum?
 string rules = 3;
 string created_by = 4; // href?
 InitiateFlowRequest query_parameters = 5;
 google.protobuf.Timestamp created_at = 6;
 optional google.protobuf.Timestamp updated_at = 7;
 optional int32 matches_count = 8;
 optional int32 flows_count = 9;
 repeated region regions = 10;
 string result = 11;
}

message Source {
  repeated google.protobuf.ListValue include = 1;
  repeated google.protobuf.Struct exclude = 2;
}

message Destination {
  repeated google.protobuf.ListValue include = 1;
  repeated google.protobuf.Struct exclude = 2;
}

message DataSources {
  repeated google.protobuf.ListValue include = 1;
  repeated google.protobuf.Struct exclude = 2;
}

message Attr_ids {
  string type = 1;
  string name = 2;
}

message Ids {
  Attr_ids attr_ids = 1;
}

message Op_endpoint_id {
  repeated Ids ids = 1;
  string src_dst = 2;
  string op = 3; // EQUAL, NOT EQUAL
}

message Args {
  Op_endpoint_id op_endpoint_id = 1;
}

message CloudQuery {
  string        op = 1; // AND, OR
  repeated Args args = 2;
}

message InitiateFlowRequest {
  optional FlowStatus status = 1;  // tbd - why needed?
  optional int32 max_results = 2;
  Service services = 3;
  Source sources = 4;
  Destination destinations = 5;
  repeated string policy_decisions = 6;
  repeated string boundary_decisions = 7;
  string query_name = 8;
  bool exclude_workloads_from_ip_list_query = 9;
  bool aggregate_flows_across_days = 10;
  string sources_destinations_query_op = 11;
  google.protobuf.Timestamp start_date = 12;
  google.protobuf.Timestamp end_date = 13;
  optional bool include_rule_coverage = 14;
  DataSources datasources = 15;
  //CloudQuery query = 16;
  optional flow.Expr query = 16;
}

message InitiateFlowResponse {
  FlowInfo info = 1;
}

message GetFlowStatusRequest {
  string id = 1;
  string query_id = 2; // TBD - what's this for??
}

message GetFlowStatusResponse {
  FlowInfo status = 1;
}

message ListFlowsRequest {
}

message ListFlowsResponse {
  repeated FlowInfo info = 1;
}

message RetrieveFlowRequest {
  string id = 1 [(validate.rules).string.uuid = true];
  int32 limit = 2;
  int32 offset = 3;
  optional string origin = 4;
  repeated string reported_policy_decision = 5;
  repeated string draft_policy_decision = 6;
  repeated string ip_property = 7;
  repeated string transmission = 8;
}

message RetrieveFlowResponse {
  repeated FederatedFlow flows = 1;
  map<string, CloudResourceInfo> resource_map = 2;
  bool truncated_results = 3;
  string query_id = 4;
  string status = 5;
  optional int32 matches_count = 6;
  optional int32 flows_count = 7;
}

message GetSessionByCSIRequest {
    string csi = 1;
}

message AuthenticatedPCEGetRequest {
  string request_url = 1;
  map<string, string> params = 2;
  string csi = 3;
  repeated string headers = 4;
}

message AuthenticatedPCEResponse {
  string response = 1;
}

message AuthenticatedPCEPostRequest {
  string request_url = 1;
  string params = 2;
  string csi = 3;
  repeated string headers = 4;
}

message RuleCoverageRequest {
  string id = 1;
  bool include_deny_rules = 2;
  int32 limit = 3;
  int32 offset = 4;
  bool label_based = 5;
}

message RuleCoverageResponse {
  int32 limit = 1;
  int32 offset = 2;
}

message CoreIPList {
  message IPRanges {
    string description = 1;
    string from_ip = 2;
    string to_ip = 3;
    bool exclusion = 4;
  }
  string href = 1;
  string name = 2;
  string description = 3;
  repeated IPRanges ip_ranges = 4;
  repeated string fqdns = 5;
  //message User {
  //  string href = 1;
  //}
  //google.protobuf.Timestamp created_at = 5;
  //optional google.protobuf.Timestamp deleted_at = 6;
  //optional User created_by = 7;
  //optional User deleted_by = 8;
  //optional string update_type = 10;
  //optional google.protobuf.Timestamp updated_at = 11;
  //optional User updated_by = 12;
}

message CleanupCacheRequest {
  optional string tenant_id = 1;
  optional uint32 older_than_hours = 2;
  optional uint32 queries_more_than = 3;
}

service FQSService {
	/*
		APIs supported:

		1) POST InitiateFlow allows to communicate with PCE and create 1 Query request
		2) GET  GetFlowStatus allows to check PCE request status for 1 query
		3) GET  ListFlows     allows to check metadata for all cached queries
		4) GET  RetrieveFlow  allows to retrieve Flow results for 1 query
	*/
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
        get: "/api/v1/version"
      };
    }

    rpc InitiateFlow(InitiateFlowRequest) returns (InitiateFlowResponse) {
      option (google.api.http) = {
        post: "/api/v1/fqs/async_queries/flows"
        body: "*"
      };
    }

    rpc GetFlowStatus(GetFlowStatusRequest) returns (GetFlowStatusResponse) {
      option (google.api.http) = {
        get: "/api/v1/fqs/async_queries/flows/{id}"
      };
    }

    rpc ListFlows(ListFlowsRequest) returns (ListFlowsResponse) {
      option (google.api.http) = {
        get: "/api/v1/fqs/async_queries/flows"
      };
    }

    rpc RetrieveFlow(RetrieveFlowRequest) returns (RetrieveFlowResponse) {
      option (google.api.http) = {
        get: "/api/v1/fqs/async_queries/flows/download/{id}"
      };
    }

    rpc RuleCoverage(RuleCoverageRequest) returns (RuleCoverageResponse) {
      option (google.api.http) = {
        put: "/api/v1/fqs/async_queries/flows/update_rule_coverage/{id}"
		body: "*"
      };
    }

	rpc CleanupCache(CleanupCacheRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			post: "/api/v1/fqs/async_queries/flows/cleanup_cache"
			body: "*"
		};
	}

}
