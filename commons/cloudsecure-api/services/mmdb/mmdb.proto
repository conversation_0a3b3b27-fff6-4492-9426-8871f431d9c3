// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package mmdb;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/api/generated/mmdb";

message Record {
  string city = 1;
  string cloud_provider = 2;
  string country = 3;
  string domain = 4;
  string region = 5;
  optional int32 threat_level = 6;
  optional bool is_wellknown = 7;
  string rir_asn = 8;
}

message IPClassification {
  string ip = 1;
  Record record = 2;
}

message IPCRequest {
  repeated string ips = 1;
}

message IPCResponse {
  repeated IPClassification items = 1;
}

service MmdbService {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
        get: "/api/v1/version"
      };
    }

    rpc GetIPClassification(IPCRequest) returns (IPCResponse) {}
}
