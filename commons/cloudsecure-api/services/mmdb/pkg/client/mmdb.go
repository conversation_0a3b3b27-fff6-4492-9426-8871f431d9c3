/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	mmdbpb "illum.io/cloud/api/generated/mmdb"
	"illum.io/cloud/api/services/mmdb/pkg/mmdb"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type MmdbClient struct {
	mmdbpb.MmdbServiceClient
	con *grpc.ClientConn
}

func (c *MmdbClient) Close() {
	_ = c.con.Close()
}

// NewMmdbClient creates a new gRPC client for service running on url endpoint
func NewMmdbClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*MmdbClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := mmdbpb.NewMmdbServiceClient(con)

	return &MmdbClient{
		con:               con,
		MmdbServiceClient: client,
	}, nil
}

// NewMmdbClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewMmdbClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*MmdbClient, error) {
	url, ok := config.LookupGRPCService(mmdb.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", mmdb.ServiceName)
	}
	return NewMmdbClient(ctx, url, opts...)
}
