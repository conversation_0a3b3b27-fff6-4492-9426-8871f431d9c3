package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	cloudsyncpb "illum.io/cloud/api/generated/cloudsync"
	"illum.io/cloud/api/services/cloudsync/pkg/cloudsync"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type CloudsyncClient struct {
	cloudsyncpb.CloudSyncClient
	con *grpc.ClientConn
}

func (c *CloudsyncClient) Close() {
	_ = c.con.Close()
}

// NewCloudsyncClient creates a new gRPC client for service running on url endpoint
func NewCloudsyncClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*CloudsyncClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := cloudsyncpb.NewCloudSyncClient(con)

	return &CloudsyncClient{
		con:             con,
		CloudSyncClient: client,
	}, nil
}

// NewCloudsyncClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewCloudsyncClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*CloudsyncClient, error) {
	url, ok := config.LookupGRPCService(cloudsync.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", cloudsync.ServiceName)
	}
	return NewCloudsyncClient(ctx, url, opts...)
}
