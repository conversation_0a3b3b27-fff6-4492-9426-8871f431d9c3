// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package cloudsync;

option go_package = "illum.io/cloud/sync-api/pkg/generated/cloudsync";

import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/version/version.proto";

message Resource {
    // type is type of resource
    string type = 1;

    // id is unique identification of the resource. It's internal id in database
    string id = 2;

    // csp_id represents id from aws or azure side
    string csp_id = 3;

    // deleted shows if the resource deleted. It deleted=false it means resource was updated or it's new resource
    bool deleted = 4 [deprecated=true];

    string resource_id = 5;

    Status status = 6;
    enum Status {
        UNSPECIFIED = 0;
        DELETED = 1;
        UPDATED = 2;
        NEW = 3;
    }
}

// Result contains notification of updates for one region/location
message Result {
    // region is a region/location of updates
    string region = 1;

    // resources is a list of updated resources
    repeated Resource resources = 2;
}

enum CtrlPlaneConsumer {
    CTRL_PLANE_UNSPECIFIED = 0;
    CTRL_PLANE_INTEGRATIONS = 1;
}

enum DataPlaneConsumer {
    DATA_PLANE_UNSPECIFIED = 0;
    DATA_PLANE_RELATIONSHIP = 2;
}

// Sync result is a message sent via message queue to notify that syncing is finished
message SyncResult {
    // cloud secure tenant_id
    string tenant_id = 1;
    // account_id for aws and subscription_id for azure
    string account_id = 2;
    // the timestamp of the message
    google.protobuf.Timestamp timestamp = 3;
    // sync_results contains list of regions/locations where resources where updated
    repeated Result sync_results = 4;
    // cloud is cloud service provider name. aws or azure
    string cloud = 5;
    // data plane is the name of the data plane
    string dataplane = 6;
}

service Inventory {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
            get: "/api/v1/cloudsync/version"
        };
    }
}

service Relationship {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
            get: "/api/v1/relationship/version"
        };
    }
}