/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	reportingpb "illum.io/cloud/api/generated/reporting"
	"illum.io/cloud/api/services/reporting/pkg/reporting"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type ReportingClient struct {
	reportingpb.ReportingServiceClient
	con *grpc.ClientConn
}

func (c *ReportingClient) Close() {
	_ = c.con.Close()
}

// NewReportingClient creates a new gRPC client for service running on url endpoint
func NewReportingClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*ReportingClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := reportingpb.NewReportingServiceClient(con)

	return &ReportingClient{
		con:                    con,
		ReportingServiceClient: client,
	}, nil
}

// NewReportingClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewReportingClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*ReportingClient, error) {
	url, ok := config.LookupGRPCService(reporting.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", reporting.ServiceName)
	}
	return NewReportingClient(ctx, url, opts...)
}
