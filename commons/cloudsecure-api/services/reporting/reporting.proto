// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package reporting;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "common/version/version.proto";
import "audit/audit.proto";
import "bridge/bridge.proto";
import "flowstats/flowstats.proto";
import "labeling/applications.proto";
import "labeling/deployments.proto";
import "usage/usage.proto";
import "common/flow/flow.proto";
import "common/flow/query.proto";
import "common/insights/insights.proto";

option go_package = "illum.io/cloud/api/generated/reporting";

enum ReportStatus {
  REPORT_STATUS_UNSPECIFIED = 0;
  REPORT_STATUS_NEW = 1;
  REPORT_STATUS_IN_PROGRESS = 2;
  REPORT_STATUS_SUCCESS=3;
  REPORT_STATUS_FAILED=4;
  REPORT_STATUS_CANCELLED=5;
  // Special status for entries in the reports table which are scheduled in the future (have a future created_at timestamp)
  // Required for newly created schedules to be shown in the UI.
  REPORT_STATUS_SCHEDULED=6;
  REPORT_STATUS_DISABLED=7;
}

enum ExportStatus {
  EXPORT_STATUS_ENABLE=0;
  EXPORT_STATUS_DISABLE=1;
}

/*
FileFormat is the allowed formats.
If support for more formats is required, update this.
*/
enum FileFormat {
  FILE_FORMAT_UNSPECIFIED = 0;
  FILE_FORMAT_CSV = 1;
  FILE_FORMAT_JSON = 2;
  FILE_FORMAT_PDF = 3;
}

enum ReportType {
  REPORT_TYPE_UNSPECIFIED = 0;
  REPORT_TYPE_AUDIT_REPORT = 1;
  REPORT_TYPE_RISK_REPORT = 2;
  REPORT_TYPE_TRAFFIC_REPORT = 3;
  REPORT_TYPE_INSIGHTS_REPORT = 4;
  REPORT_TYPE_INVENTORY_REPORT = 5;
  REPORT_TYPE_SYSTEM_EVENTS_REPORT = 6;
  REPORT_TYPE_DEPLOYMENTS_REPORT = 7;
  REPORT_TYPE_TAGS_TO_LABEL_REPORT = 8;
  REPORT_TYPE_USAGE_REPORT = 9;
  REPORT_TYPE_APPLICATIONS_REPORT = 10;
  REPORT_TYPE_DISCOVERY_RULES_REPORT = 11;
  REPORT_TYPE_APP_DEFINITIONS_REPORT = 12;
}

message CreateReportRequest {
  FileFormat file_format = 1;
  optional string file_name = 2 [(validate.rules).string.pattern = "^[a-zA-Z0-9_]*$", (validate.rules).string.max_len=100];
  oneof report_parameters {
    audit.ListSortAuditRecordsRequest audit_report_request = 3;
    flowstats.RiskReportRequest risk_report_request = 4;
    bridge.FlowsRequest traffic_request = 5;
    insights.GetInsightRequest insights_request = 6;
    bridge.ListResourcesRequest inventory_report_request = 8;
    audit.ListSortSysEventsRequest system_events_report_request = 9;
    labeling.ListDeploymentsRequest deployments_request = 11;
    labeling.ListTagsToLabelRequest tags_to_label_request = 12;
    usage.GetTenantUsageRequest usage_request = 13;
    labeling.GetApplicationsRequest applications_request = 14;
    labeling.ListDiscoveryRulesRequest discovery_rules_request = 15;
    labeling.GetApplicationDefinitionsRequest application_definitions_request = 16;
  }
  // We are using seconds as it is more granular. We compute expiry time based on this.
  uint64 seconds_to_expire = 7;
  // Display filters is a field used to help UI display selected filters on the client side
  // Instead of trying to re-create the filers on the client side from the report_params, we just store them
  // for the UI to display, in a format understood by the UI.
  // Eventually these display filters will also be used to export filter information to exported reports.
  google.protobuf.Struct display_filters = 10;
}

message CreateQueryExportStatsRequest {
  google.protobuf.Timestamp start_time = 1;
  google.protobuf.Timestamp end_time = 2;
  string query_export_job_id = 3;
  ReportStatus status = 4;
}

message CreateQueryExportStatsResponse {
  string id = 1;
  ReportStatus status = 3;
}

message CreateReportResponse {
  // id is the report id
  string id = 1;
  // This is named report_status to not to be confused for HTTP status.
  ReportStatus report_status = 2;
}

// Add the sort column for every sort order here
// Whenever there is a new sort order needed, add it here.
message SortBy {
  enum Field {
    CREATED_AT = 0;
    FREQUENCY = 1;
    REPORT_TYPE = 2;
    NAME = 3;
    CREATED_BY = 4;
    FILE_FORMAT = 5;
  }
  Field field = 1;
  bool asc = 2;
}

message ListReportsRequest {
  // The below are the filters for reports[fields 1 through 6].
  // The naming is based on UI.
  // ignore_empty ensures that the validate rules are checked only if the parameter is set/non-default
  repeated ReportType report_type = 1[(validate.rules).repeated = {unique: true, min_items:1, ignore_empty:true}];
  // Since UI deals primarily with user-emails, we will let the user pass emails.
  // In the backend, we fetch the user-id to use in db queries.
  repeated string generated_by = 2[(validate.rules).repeated = {unique: true, min_items:1, ignore_empty:true}];
  repeated ReportStatus status = 3[(validate.rules).repeated = {unique: true, min_items:1, ignore_empty:true}];
  repeated FileFormat file_type = 4[(validate.rules).repeated = {unique: true, min_items:1, ignore_empty:true}];
  repeated string file_name = 5[(validate.rules).repeated = {unique: true, min_items:1, ignore_empty:true}];
  optional bool recurring = 6;

  // The below fields are used for pagination[fields 7 through 10].
  // sort by encompasses sort field and order
  SortBy sort_by = 7;
  string page_token = 8;
  optional uint32 max_results = 9 [(validate.rules).uint32.lte = 1000];
  // with_total_count is a boolean flag to indicate whether to send total count and matching count of results
  // if this is set, we return total_count and matched_count.
  bool with_total_count = 10;
}

// ExposedReport is a data-structure that holds information about a report.
// We only expose some columns from Reports to the User(UI).
// Columns like file path, retries, params don't make much sense for Users.
message ExposedReport{
  string id = 1;
  string created_by = 2;
  // status can be used to show/hide the Download button in UI.
  ReportStatus status = 3;
  google.protobuf.Timestamp created_at = 4;
  ReportType report_type = 5;
  string report_name = 6;
  google.protobuf.Timestamp expire_time = 7;
  FileFormat file_format = 8;
  google.protobuf.Struct display_filters = 9;
  Schedule schedule = 10;
  google.protobuf.Timestamp next_scheduled = 11;
  string definition_id = 12;
  string definition_name = 13;
}

message ListReportsResponse {
  repeated ExposedReport reports = 1;

  // The below fields[fields 2 through 5] are for pagination
  // total_count gives the number of total records, without applying filters
  uint32  total_count = 2[json_name="total_count"];
  // matched_count is the count of entries that match the filters
  uint32 matched_count = 3[json_name="matched_count"];
  string next_token = 4[json_name="next_token"];
  string prev_token = 5[json_name="prev_token"];
}

message GetReportRequest {
  string id = 1[(validate.rules).string.uuid = true];
}

message GetReportResponse {
  // This is a pre-signed URL with an expiry.
  string url = 1;
}

message GetAvailableFormatsRequest {
  ReportType report_type = 1;
}

message GetAvailableFormatsResponse {
  repeated FileFormat file_formats = 1;
}

message DeleteReportRequest {
  string id = 1[(validate.rules).string.uuid = true];
}

message DeleteReportResponse {}

message BulkDeleteReportsRequest {
  repeated string ids = 1[(validate.rules).repeated.items.string.uuid = true];
}

message BulkDeleteReportsResponse {
  // This map identifies which reports are deleted and which aren't
  map<string, bool> report_status = 1;
}

// Specify the fields that we let the user(UI) update.
message UpdateReportRequest {
  string id = 1[(validate.rules).string.uuid = true];
  uint64 seconds_to_expire = 2;
  string file_name = 3[(validate.rules).string.pattern = "^[a-zA-Z0-9_]*$", (validate.rules).string.max_len=100];
}

message UpdateReportResponse {}

message FrequencyWeeklyParams {
  // Array of days in a week when the report needs to be generated
  // 0 - Monday, 1 - Tuesday, 2 - Wednesday, 3 - Thursday, 4 - Friday, 5 - Saturday, 6 - Sunday
  repeated uint32 days = 2 [(validate.rules).repeated.unique = true, (validate.rules).repeated.items.uint32.lt = 7];
}

message FrequencyMonthlyParams {
  // Both day and day_of_month cannot be set at the same time.
  // If neither is set, the report will be generated on the first day of the month.

  // Which day of the month does the report need to be generated
  optional uint32 day = 2 [(validate.rules).uint32.lte = 31, (validate.rules).uint32.gt = 0];
  // first or last day of the month
  optional string day_of_month = 3 [(validate.rules).string = {in: ["first", "last"]}];
}

enum Frequency {
  FREQUENCY_NONE = 0;
  FREQUENCY_DAILY = 1;
  FREQUENCY_WEEKLY = 2;
  FREQUENCY_MONTHLY = 3;
  FREQUENCY_QUARTERLY = 4;
  FREQUENCY_HOURLY = 5;
}

message Schedule {
  // This shows how frequent the report is generated: Daily, Weekly or Monthly
  Frequency frequency = 1;
  // Time of the day when the report should be generated.
  // This is a 24hr format string
  optional string time = 2 [(validate.rules).string.pattern = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$"];
  oneof frequency_params {
    FrequencyWeeklyParams weekly_params = 3;
    FrequencyMonthlyParams monthly_params = 4;
  }
}

message CreateReportDefinitionRequest {
  // The schedule that the report generation follows
  Schedule schedule = 1 [(validate.rules).message.required = true];
  // Since we will need all the fields of a CreateReportRequest, we are using a nested field.
  // The file name field in the create report request will be used to determine file prefixes instead.
  CreateReportRequest create_report_request = 2 [(validate.rules).message.required = true];
  // When a report expires. This will also be used to make a schedule inactive.
  google.protobuf.Timestamp expire_time = 3;
  TimeRange time_range = 4;
}

message CreateReportDefinitionResponse {
  string id = 1;
}


message TimeRange {
  uint32 hours = 1 [(validate.rules).uint32 = {gte: 0, lte: 23}];
  uint32 days = 2 [(validate.rules).uint32 = {gte: 0, lte: 365}];
  uint32 weeks = 3 [(validate.rules).uint32 = {gte: 0, lte: 52}];
  uint32 months = 4 [(validate.rules).uint32 = {gte: 0, lte: 11}];
  uint32 years = 5 [(validate.rules).uint32 = {gte: 0, lte: 1}];
}

// ExposedReportDefinition is a data-structure that holds customer-relevant information of ReportDefinition.
message ExposedReportDefinition {
  string id = 1;
  string created_by = 2;
  Schedule schedule = 3;
  CreateReportRequest create_report_request = 4;
  google.protobuf.Timestamp last_processed = 5;
  google.protobuf.Timestamp next_scheduled = 6;
  // expire_time can be used to show whether a schedule is active or inactive
  google.protobuf.Timestamp expire_time = 7;
  google.protobuf.Timestamp created_at = 8;
  string filename = 9;
  FileFormat file_format = 10;
  ReportType report_type = 11;
  bool enabled = 12;
  TimeRange time_range = 13;
}
message ListReportDefinitionsRequest {}

message ListReportDefinitionsResponse {
  repeated ExposedReportDefinition report_definitions = 1;
}

message ListSortReportDefinitionsRequest {
  // The below are for filtering.
  // Constraints of max_items are taken with consideration of future expandability.
  // For example, the number of frequencies is highly unlikely to cross 20, similarly for other filters
  repeated Frequency frequencies = 1[(validate.rules).repeated = {unique:true, max_items:20}];
  repeated ReportType report_types = 2[(validate.rules).repeated = {unique:true, max_items:100}];
  repeated string names = 3[(validate.rules).repeated = {unique:true, max_items:500}];
  repeated string generated_by = 4[(validate.rules).repeated = {unique:true, max_items:500}];
  repeated FileFormat file_formats = 5[(validate.rules).repeated = {unique:true, max_items:10}];

  // sort_by is used to accomplish the sort order
  SortBy sort_by = 6;

  // The below are for pagination
  optional uint32 max_results = 7[(validate.rules).uint32.lte = 1000];
  bool with_total_count = 8;
  string page_token = 9;
}

message ListSortReportDefinitionsResponse {
  repeated ExposedReportDefinition report_definitions = 1;
  uint32 total_count = 2;
  uint32 matched_count = 3;
  string next_token = 4;
  string prev_token = 5;
}

message GetReportDefinitionRequest {
  string id = 1 [(validate.rules).string.uuid = true];
}

message GetReportDefinitionResponse {
  ExposedReportDefinition report_definition = 1;
}

message DeleteReportDefinitionsRequest {
  repeated string ids = 1 [(validate.rules).repeated.items.string.uuid = true, (validate.rules).repeated.unique = true];
}

message DeleteReportDefinitionsResponse {
  map<string, bool> status = 1;
}

// UpdateScheduleRequest contains fields that can updated by the User.
message UpdateReportDefinitionRequest {
  string id = 1[(validate.rules).string.uuid = true];
  // ExposedReportDefinition reportDefinition = 2;
  reserved 2;
  optional CreateReportRequest create_report_request = 3;
  optional google.protobuf.Timestamp expire_time = 4;
  optional Schedule schedule = 5;
  optional TimeRange time_range = 6;
}

message UpdateReportDefinitionResponse {
  ExposedReportDefinition schedule = 1;
}

message EnableSchedulesRequest {
  repeated string ids = 1[(validate.rules).repeated.items.string.uuid = true, (validate.rules).repeated.unique = true, (validate.rules).repeated.min_items = 1];
  // Enable directs whether to enable or disable the report schedules indicated by the ids above.
  bool enable = 2;
}

message EnableSchedulesResponse {
  // failed contains the ids of the schedules that failed to enable/disable
  repeated string failed = 1;
}

message GenerateReportForScheduleRequest {
  string id = 1[(validate.rules).string.uuid = true];
}

message GenerateReportForScheduleResponse {
}

message ReportFilterMetadataParams {
  enum FilterMetadataType {
    UNKNOWN = 0;
    NAME = 1;
    TYPE = 2;
    STATUS = 3;
    FILE_FORMAT = 4;
    // One interesting fact to note is: entries in generated_by are sent in the form of emails.
    // We have to do a lookup to get the user-id from the email(should be a `contains` lookup).
    // For example: If the user enters "test", we find all the user-ids whose emails have "test" in them.
    GENERATED_BY = 5;
  }
  FilterMetadataType type = 1;
  ReportFilters filters = 2;
}

message ReportDefinitionFilterMetadataParams {
  enum FilterMetadataType {
    UNKNOWN = 0;
    NAME = 1;
    TYPE = 2;
    FREQUENCY = 3;
    FILE_FORMAT = 4;
    GENERATED_BY = 5;
  }
  FilterMetadataType type = 1;
  ReportDefinitionFilters filters = 2;
}

message ReportFilters {
  repeated string names = 1;
  repeated ReportType types = 2;
  repeated ReportStatus statuses = 3;
  repeated FileFormat file_formats = 4;
  repeated string generated_by = 5;
}

message ReportDefinitionFilters {
  repeated string names = 1;
  repeated ReportType types = 2;
  repeated Frequency frequencies = 3;
  repeated FileFormat file_formats = 4;
  repeated string generated_by = 5;
}
message ListFilterMetadataRequest {
  uint32 max_results = 1[(validate.rules).uint32.lte = 100];

  // partial filter is used to filter the metadata based on the partial string match
  // for example if the user searches for "test" in the filename field, all the filenames that have "test" in them will be returned
  // This is used to help the user search for metadata based on partial strings.
  // How do we know which field this partial filter is for? We get it from the params.Type.
  optional string partial_filter = 2;

  oneof params {
    ReportFilterMetadataParams report_params = 3;
    ReportDefinitionFilterMetadataParams report_definition_params = 4;
  }
}

message ListFilterMetadataResponse {
  repeated string items = 1;
}

message DeleteTenantDataRequest {}

message DeleteTenantDataResponse {}

message CreateQueryExportRequest {
  string connectorId = 1;
  optional string query = 2;
  optional string file_prefix = 3 [(validate.rules).string.pattern = "^[a-zA-Z0-9_]*$", (validate.rules).string.max_len=50];
  FileFormat export_format = 4;
  Schedule schedule = 5;
  optional google.protobuf.Timestamp expires_at = 6;
  optional string queryFilterUI = 7;
}

message CreateQueryExportResponse {
  string id = 1;
}

message ListQueryExportsRequest {}

message ExposedQueryExport{
  string id = 1;
  string connectorId = 2;
  string query = 3;
  string file_prefix = 4;
  FileFormat export_format = 5;
  string destination = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
  Schedule schedule = 9;
  google.protobuf.Timestamp expires_at = 10;
  uint64 exported_data = 11;
  ReportStatus status = 12;
  google.protobuf.Timestamp schedule_start = 13;
  google.protobuf.Timestamp schedule_end = 14;
  google.protobuf.Timestamp last_success_at = 15;
  string createdBy = 16;
  string queryFilterUI = 17;
}

message ListQueryExportsResponse {
  repeated ExposedQueryExport query_exports = 1;
}

message GetQueryExportRequest {
  string id = 1[(validate.rules).string.uuid = true];
}

message GetQueryExportResponse {
  ExposedQueryExport query_exports = 1;
}

message BulkQueryExportDeleteRequest {
  repeated string ids = 1[(validate.rules).repeated.items.string.uuid = true];
}

message BulkQueryExportDeleteResponse {
  // This map identifies which query exports are deleted and which aren't
  map<string, bool> status = 1;
}

// UpdateQueryExportRequest contains fields that can updated by the User.
message UpdateQueryExportRequest {
  string id = 1[(validate.rules).string.uuid = true];
  optional string connectorId = 2;
  optional string query = 3;
  optional Schedule schedule = 4;
  optional google.protobuf.Timestamp expires_at = 5;
  optional ExportStatus status = 6;
}

message UpdateQueryExportResponse {
  google.protobuf.Timestamp expires_at = 1;
}

service ReportingService {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
        get: "/api/v1/version"
      };
    }

    rpc ListReports(ListReportsRequest) returns (ListReportsResponse) {
      option (google.api.http) = {
        get: "/api/v1/reports"
      };
    }

    rpc ListSortReports(ListReportsRequest) returns (ListReportsResponse) {
      option (google.api.http) = {
        post: "/api/v1/reports"
        body: "*"
      };
    }

    rpc GetReport(GetReportRequest) returns (GetReportResponse) {
      option (google.api.http) = {
        get: "/api/v1/reports/{id}"
      };
    }

    rpc UpdateReport(UpdateReportRequest) returns (UpdateReportResponse) {
      option (google.api.http) = {
        patch: "/api/v1/reports/{id}"
        body: "*"
      };
    }

    rpc GetAvailableFormats(GetAvailableFormatsRequest) returns (GetAvailableFormatsResponse) {
      option (google.api.http) = {
        get: "/api/v1/reports/formats"
        additional_bindings {
          get: "/api/v1/reports/formats/{report_type}"
        }
      };
    }

    rpc DeleteReport(DeleteReportRequest) returns (DeleteReportResponse) {
      option(google.api.http) = {
        delete: "/api/v1/reports/{id}"
      };
    }

    rpc CreateReportDefinition(CreateReportDefinitionRequest) returns (CreateReportDefinitionResponse) {
      option (google.api.http) = {
        post: "/api/v1/reports/definition"
        body: "*"
      };
    }

    rpc ListReportDefinitions(ListReportDefinitionsRequest) returns (ListReportDefinitionsResponse) {
      option (google.api.http) = {
        get: "/api/v1/reports/definitions"
      };
    }

    // ListSortReportDefinitions Lists, Sorts, and Paginates ReportDefinitions
    rpc ListSortReportDefinitions(ListSortReportDefinitionsRequest) returns (ListSortReportDefinitionsResponse) {
      option (google.api.http) = {
        post: "/api/v1/reports/definitions"
        body: "*"
      };
    }

    rpc GetReportDefinition(GetReportDefinitionRequest) returns (GetReportDefinitionResponse) {
      option (google.api.http) = {
        get: "/api/v1/reports/definitions/{id}"
      };
    }

    rpc DeleteReportDefinitions(DeleteReportDefinitionsRequest) returns (DeleteReportDefinitionsResponse) {
      option (google.api.http) = {
        post: "/api/v1/reports/definitions/bulk_delete"
        body: "*"
      };
    }

    rpc UpdateReportDefinition(UpdateReportDefinitionRequest) returns (UpdateReportDefinitionResponse) {
      option (google.api.http) = {
        patch: "/api/v1/reports/definitions/{id}"
        body: "*"
      };
    }

    rpc EnableSchedules(EnableSchedulesRequest) returns (EnableSchedulesResponse) {
      option (google.api.http) = {
        post: "/api/v1/reports/definitions/enable"
        body: "*"
      };
    }

    rpc GenerateReportForSchedule(GenerateReportForScheduleRequest) returns (GenerateReportForScheduleResponse) {
      option (google.api.http) = {
        post: "/api/v1/reports/definitions/generate"
        body: "*"
      };
    }

    rpc BulkDeleteReports(BulkDeleteReportsRequest) returns (BulkDeleteReportsResponse) {
      option(google.api.http) = {
        post: "/api/v1/reports/bulk_delete"
        body: "*"
      };
    }

    rpc ListFilterMetadata(ListFilterMetadataRequest) returns (ListFilterMetadataResponse) {
      option (google.api.http) = {
        post: "/api/v1/reports/filter_metadata"
        body: "*"
      };
    }

    // This will be used internally, so we aren't specifying any REST endpoint
    rpc DeleteTenantData(DeleteTenantDataRequest) returns (DeleteTenantDataResponse) {}

    // Cancelling a Report is not addressed here.
    // It will be a part of another PR later
    /* It will look something like this.
    message CancelReportRequest{
      string report_id = 1;
    }
    message CancelReportResponse{}
    rpc CancelReport(CancelReportRequest) returns (CancelReportResponse){}
    */

    rpc CreateQueryExport(CreateQueryExportRequest) returns (CreateQueryExportResponse) {
      option (google.api.http) = {
        post: "/api/v1/queryexport"
        body: "*"
      };
    }

    rpc ListQueryExports(ListQueryExportsRequest) returns (ListQueryExportsResponse) {
      option (google.api.http) = {
        get: "/api/v1/queryexports"
      };
    }

    rpc GetQueryExport(GetQueryExportRequest) returns (GetQueryExportResponse) {
      option (google.api.http) = {
        get: "/api/v1/queryexport/{id}"
      };
    }

    rpc BulkQueryExportsDelete(BulkQueryExportDeleteRequest) returns (BulkQueryExportDeleteResponse) {
      option(google.api.http) = {
        post: "/api/v1/queryexport/bulk_delete"
        body: "*"
      };
    }

    rpc UpdateQueryExport(UpdateQueryExportRequest) returns (UpdateQueryExportResponse) {
      option (google.api.http) = {
        patch: "/api/v1/queryexport/{id}"
        body: "*"
      };
    }

    // SyncTimeRange is used to sync the time range for reportdefinitions
    // TODO: After this is run in prod, remove this RPC
    rpc SyncTimeRange(google.protobuf.Empty) returns (google.protobuf.Empty) {
    }
}
