package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	eventpb "illum.io/cloud/api/generated/event"
	"illum.io/cloud/api/services/event/pkg/event"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type EventClient struct {
	eventpb.EventServiceClient
	con *grpc.ClientConn
}

func (c *EventClient) Close() {
	_ = c.con.Close()
}

// NewEventClient creates a new gRPC client for service running on url endpoint
func NewEventClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*EventClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := eventpb.NewEventServiceClient(con)

	return &EventClient{
		con:                con,
		EventServiceClient: client,
	}, nil
}

// NewEventClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewEventClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*EventClient, error) {
	url, ok := config.LookupGRPCService(event.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", event.ServiceName)
	}
	return NewEventClient(ctx, url, opts...)
}
