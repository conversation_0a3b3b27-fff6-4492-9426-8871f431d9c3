// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package event;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "common/version/version.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/api/generated/event";

message Event {
  string id = 1;
  string account_id = 2;
  string cloud = 3;
  google.protobuf.Struct data = 4;
  optional string integration_id = 5;
  google.protobuf.Timestamp occurred_at = 6;
  string service = 7;
  optional string status = 8;
}

message CreateEventRequest{
  string account_id = 1;
  string cloud = 2;
  google.protobuf.Struct data = 3;
  optional string integration_id = 4;
  google.protobuf.Timestamp occurred_at = 5;
  string service = 6;
  optional string status = 7;
}

message CreateEventResponse {
  string event_id = 1;
}

message GetEventRequest {
  string event_id = 1 [(validate.rules).string.uuid = true];
}

message GetEventResponse {
  Event event = 1;
}

message ListEventsRequest {
  int32 max_results = 1;
}

message ListEventsResponse {
  repeated Event events = 1;
}

service EventService {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
        get: "/api/v1/version"
      };
    }
    // Add more API methods here
  rpc CreateEvent(CreateEventRequest) returns (CreateEventResponse) {}
  rpc GetEvent(GetEventRequest) returns (GetEventResponse) {
    option (google.api.http) = {
      get: "/api/v1/events/{event_id}"
    };
  }
  rpc ListEvents(ListEventsRequest) returns (ListEventsResponse) {
    option (google.api.http) = {
      get: "/api/v1/events/list/{max_results}"
    };
  }
  rpc DeleteEvents(google.protobuf.Empty) returns (google.protobuf.Empty) {}
}
