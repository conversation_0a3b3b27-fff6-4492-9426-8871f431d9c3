/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	labelmapperpb "illum.io/cloud/api/generated/labelmapper"
	"illum.io/cloud/api/services/labelmapper/pkg/labelmapper"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type LabelmapperClient struct {
	labelmapperpb.LabelmapperServiceClient
	con *grpc.ClientConn
}

func (c *LabelmapperClient) Close() {
	_ = c.con.Close()
}

// NewLabelmapperClient creates a new gRPC client for service running on url endpoint
func NewLabelmapperClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*LabelmapperClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := labelmapperpb.NewLabelmapperServiceClient(con)

	return &LabelmapperClient{
		con:                      con,
		LabelmapperServiceClient: client,
	}, nil
}

// NewLabelmapperClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewLabelmapperClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*LabelmapperClient, error) {
	url, ok := config.LookupGRPCService(labelmapper.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", labelmapper.ServiceName)
	}
	return NewLabelmapperClient(ctx, url, opts...)
}
