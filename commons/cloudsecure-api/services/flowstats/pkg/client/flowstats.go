package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	flowstatspb "illum.io/cloud/api/generated/flowstats"
	"illum.io/cloud/api/services/flowstats/pkg/flowstats"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type FlowstatsClient struct {
	flowstatspb.FlowstatsServiceClient
	con *grpc.ClientConn
}

func (c *FlowstatsClient) Close() {
	_ = c.con.Close()
}

// NewFlowstatsClient creates a new gRPC client for service running on url endpoint
func NewFlowstatsClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*FlowstatsClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := flowstatspb.NewFlowstatsServiceClient(con)

	return &FlowstatsClient{
		con:                    con,
		FlowstatsServiceClient: client,
	}, nil
}

// NewFlowstatsClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewFlowstatsClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*FlowstatsClient, error) {
	url, ok := config.LookupGRPCService(flowstats.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", flowstats.ServiceName)
	}
	return NewFlowstatsClient(ctx, url, opts...)
}
