// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package flowstats;

import "google/api/annotations.proto";
import "google/type/interval.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common/version/version.proto";
import "common/insights/insights.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/api/generated/flowstats";

// Used to send stats updates from flowget to flowstats via MQ
message UpdateFlowStatsTask {
  optional GlobalStatsData global_stats_data = 1;
  optional CrossTalkerStatsData cross_talker_stats_data = 2;
}

// flowget sends flowstats this message (via MQ) containing per cloud global/summary stats
// when it finalizes stats (currently once per hour). If flowget is processing flows from
// 3-4pm, currently the timestamp of those stats will be set to 4pm in this message.
message GlobalStatsData {
  string tenant_id = 1 [(validate.rules).string.uuid = true];
  google.protobuf.Timestamp stats_time = 2;
  repeated GlobalStat global_stats = 3;
}

message GlobalStat {
  string cloud = 1;
  uint64 denied = 2;
  uint64 allowed = 3;
  uint64 bytes = 4;
  uint64 raw_obj_bytes = 5;
}

// flowget sends flowstats this message (via MQ) containing cross talker stats when it
// finalizes stats (currently once per hour). If flowget is processing flows from
// 3-4pm, currently the timestamp of those stats will be set to 4pm in this message.
message CrossTalkerStatsData {
  string tenant_id = 1 [(validate.rules).string.uuid = true];
  google.protobuf.Timestamp stats_time = 2;
  repeated CrossTalkerStat cross_talker_stats = 3;
}

// flowget will send us stats via this message. Not all fields will
// be present in all cases.
message CrossTalkerStat {
  uint64 bytes = 1;
  uint64 risky_bytes = 2;
  uint64 flows = 3;
  uint64 risky_flows = 4;
  bool allowed = 5;
  string src_cloud = 6;
  string dst_cloud = 7;
  string src_region = 8;
  string dst_region = 9;
  string src_account = 10;
  string dst_account = 11;
  string src_org = 12;
  string dst_org = 13;
  string src_account_name = 14;
  string dst_account_name = 15;
  string dst_ip_address = 16;
  string dst_domain = 17;
  optional int32 dst_threat_level = 18;
  optional bool dst_is_well_known = 19;
  string dst_country = 20;
  string dst_asn = 21;
}

message GlobalStats {
  string cloud = 1;
  int64 denied = 2;
  int64 allowed = 3;
  int64 bytes = 4;
  uint64 raw_obj_bytes = 5; // uint64 since the raw_bytes can be quite large
}

message GlobalStatsRequest {
  google.protobuf.Timestamp from_time = 1;
  google.protobuf.Timestamp to_time = 2;
  string cloud = 3;
}

message RiskReportRequest {
  google.protobuf.Timestamp from_time = 1;
  google.protobuf.Timestamp to_time = 2;
  optional uint32 num_endpoints_sorted_by_flow_count = 3;
  optional uint32 num_endpoints_sorted_by_byte_count = 4;
  optional uint32 num_endpoint_pairs_sorted_by_flow_count = 5;
  optional uint32 num_endpoint_pairs_sorted_by_byte_count = 6;
}

message RiskReportResponse {
  bytes report_pdf = 1;
}

message GlobalStatsResponse {
  repeated GlobalStats cloud_stats = 1;
}

message RawObjBytesRequest {
  google.protobuf.Timestamp time = 1 [(validate.rules).timestamp.required = true];     // Hour for which to get size
}

message RawObjBytesResponse {
  uint64 raw_obj_bytes = 1; // The number of raw bytes of flow logs
}

message DeleteTenantRequest {
  string tenant_id = 1 [(validate.rules).string.uuid = true];;
}

message DeleteTenantResponse {
}

service FlowstatsService {
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
    option (google.api.http) = {
      get: "/api/v1/flowstats/version"
    };
  }

  rpc GetGlobalStats(GlobalStatsRequest) returns (GlobalStatsResponse) {
    option (google.api.http) = {
      get: "/api/v1/flowstats/global_stats"
    };
  }

  rpc GetRawObjBytes(RawObjBytesRequest) returns (RawObjBytesResponse) {}

  rpc GetRiskReport(RiskReportRequest) returns (RiskReportResponse) {
    option (google.api.http) = {
      get: "/api/v1/flowstats/risk_report"
    };
  }

  rpc DeleteTenant(DeleteTenantRequest) returns (DeleteTenantResponse) {}
  rpc GetInsight(insights.GetInsightRequest) returns (insights.GetInsightResponse) {}
  rpc GetInsightsList(insights.InsightsListRequest) returns (insights.InsightsListResponse) {}
}
