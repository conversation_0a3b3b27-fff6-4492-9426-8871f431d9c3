// Copyright 2024 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package integrations;

import "integrations/integration_common.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/api/generated/integrations";

message CreateOciIntegrationReqeust {
    string name = 1;
    string home_region = 2;
    string oci_tenant_csp_id = 3 [(validate.rules).string = {
        min_len: 20,
        pattern: "ocid1.tenancy.*"
    }];
    string access_mode = 4 [(validate.rules).string = {in: ["Read", "ReadWrite"]}];
    string oci_user_csp_id = 5 [(validate.rules).string = {
        min_len: 20,
        pattern: "ocid1.user.*"
    }];
    string fingerprint = 7;
    optional bool draft = 8;
    string oci_group_name = 9;
    reserved 6;
}

message OciIntegration {
    string id = 1;
    string name = 2;
    string tenant_id = 3;
    string home_region = 4;
    string root_compartment_csp_id = 5;
    string access_mode = 6;
    string oci_user_csp_id = 7;
    string compartment_csp_id = 9;
    string parent_compartment_id = 10;
    bool root_compartment = 11;
    google.protobuf.Timestamp created_at = 12;
    google.protobuf.Timestamp updated_at = 13;
    bool secret_available = 14;
    string oci_group_name = 15;
    reserved 8;
    bool enabled = 16;
    repeated string enabled_compartments = 17;
}

message ListOciIntegrationsRequest {
    // for future filters
}
message ListOciIntegrationsResponse {
    repeated OciIntegration oci = 1;
}

message UpdateOciIntegrationRequest {
    string id = 1 [(validate.rules).string.uuid = true];
    optional string name = 2;
}

message DeleteOciIntegrationRequest {
    string id = 1 [(validate.rules).string.uuid = true];
}

message GetOciIntegrationRequest {
    oneof identifier {
        string id = 1 [(validate.rules).string.uuid = true];
        string compartment_csp_id = 2[(validate.rules).string = {
            min_len: 20,
            pattern: "ocid1.tenancy.*|ocid1.compartment.*"
        }];
    }
}

message GetOciIntegrationResponse {
    OciIntegration oci = 1;
}

// Temporary fix to have multiple proto files.
//TODO: remove after its fixed.
service MockOci {}