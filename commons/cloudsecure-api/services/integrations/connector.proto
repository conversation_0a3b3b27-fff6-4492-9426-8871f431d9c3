
// Copyright 2024 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package integrations;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/api/generated/integrations";

message S3{
  string account_id = 2 [(validate.rules).string.pattern = "^[0-9]{12}$"];
  string destination = 3 [(validate.rules).string.min_len = 1];
  string region = 4 [(validate.rules).string.min_len = 1];
}

enum ConnectorType{
  UNSPECIFIED_CONNECTOR = 0;
  S3_CONNECTOR = 1;
}

message Connector{
  optional string id = 1; //will be auto-generated when creating
  ConnectorType type = 2;
  oneof c{
    S3 s3_connector = 3;
  }
  bool secret_available = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  google.protobuf.Timestamp last_sync_at = 7;
  bool status = 8;
  uint64 data_transferred = 9; // data transferred over last 24 hours
  string created_by = 10;
}

message CreateConnectorRequest{
  Connector connector = 1;
}

message CreateConnectorResponse{
  Connector connector = 1;
}

message UpdateConnectorRequest{
  string id = 1[(validate.rules).string.uuid = true];
  ConnectorType type = 2;
  optional google.protobuf.Timestamp last_sync_at = 3;
  optional uint64 data_transferred = 4; // data transferred over last 24 hours
  optional bool status = 5;
}

message UpdateConnectorResponse{
  Connector connector = 1;
}

message GetConnectorRequest{
  string id = 1[(validate.rules).string.uuid = true];
}

message GetConnectorResponse{
  Connector connector = 1;
}

message ListConnectorRequest{
  optional ConnectorType type = 1;
}

message ListConnectorResponse{
  repeated Connector connectors = 1;
}

message DeleteConnectorRequest{
  string id = 1[(validate.rules).string.uuid = true];
}

message DeleteConnectorResponse{
}

message TestConnectorRequest {
  ConnectorType type = 1;
  repeated string ids = 2 [(validate.rules).repeated = {
    unique: true
    min_items: 1
    items:{
      string: {
        uuid: true
      }
    }
  }];
}

message ConnectorAccess {
  string id = 1;
  bool access = 2;
  string error = 3;
}

message TestConnectorResponse {
  ConnectorType type = 1;
  repeated ConnectorAccess access = 2;
}

// Temporary fix to have multiple proto files.
//TODO: remove after its fixed.
service MockConnector {}