## SERVICE: Integrations

### Introduction

**Integrations** service provides the basic CRUD operation to help users access the UI and add CSP integrations.
A CloudSecure user should be able to add/update and delete an CSP integration with CloudSecure.

### Starting the service.
#### Database Creation and Migration.
To start a local postgres container locally(docker desktop must be present), run the command

```shell
make run-local-db DB=integrations
```
This creates a container with integrations db. 

If docker desktop is not present locally, then download postgres application and run it in port 5431. 

Create a database named **integrations**.

#### Migration
To Create tables in the database, run the migration for the service.
```shell
make migrate SVC=integrations
```

### Running the system locally
Once the DB is set up and the migrations are run, we can start the Integrations service locally,
```shell
make run SVC=integrations
```

### Updating Cloudformation Templates and Powerhshell Script
For customers to onboard their AWS and Azure Accounts, we provide aws cloudformation stack and powerhshell script
respectively. The template and scripts are hosted in Illumio's aws s3 buckets and enabled with public access.
#### AWS Cloudformation Template
The aws cloudformation template varies for each cluster and is hosted in different locations for each cluster. The
reason for having different templates for different cluster is that, we create assume roles in customers account when 
they onboard their accounts. We use different aws accounts as base account for different clusters. We have 3 different 
template files for each cluster.
1. aws-flow-integration-\<cluster\>.yaml
2. aws-integration-\<cluster\>-readonly.yaml
3. aws-integration-\<cluster\>-readwrite.yaml

Below is the mapping of base accounts and locations of templates for different cluster.
1. Dev Cluster: We use AWS Mr. Pink account as base account for assume role. The files are hosted in Mr. Pink Account 
under the `cloudsecure-integration-templates` bucket.  The location is `s3://cloudsecure-integration-templates/dev/`.
2. Stage Cluster: We use AWS Mr. Orange account as base account for assume role. The files are hosted in Mr. Pink Account
   under the `cloudsecure-integration-templates` bucket.  The location is `s3://cloudsecure-integration-templates/stage/`.
3. Production Cluster: We use AWS Lumos account as base account for assume role. The files are hosted in Lumos Account
   under the `cloudsecure-onboarding-templates` bucket.  The location is `s3://cloudsecure-onboarding-templates/cloudsecure/`.

##### Updating permissions for newly added Resources
As CloudSecure keeps updating the list of resources being synced, permission to read those resources are already present.
But the permission to write security groups and adding tags might be missing for the resources. Hence, with new resources 
being added constantly, we need to add permissions to the existing role. In order to provide customer with the option to 
grant CloudSecure with the new permissions, we provide an update template, which will have the updated permissions and when
run, will grant the existing role with the new updated permissions. This does not need to handle the base account 
conditions. Hence, we can use the same update template for all the clusters. We need to make sure to upload this file 
in both Mr. Pink and Lumos buckets. File name is `aws-policy-update.yaml`

Currently, this template is run manually by the customers and there is no UI flow for it. When a UI flow is added
we need to make sure to update the helm values and provide this script url to UI.

#### Azure Powershell script
We use the same Azure powershell script for all the clusters. It creates the Azure AD Application and grants the required
permissions to the application. The file is hosted in the following location for different clusters.
1. Dev and Stage Cluster: It is hosted in the Mr. Pink Account under the `cloudsecure-integration-templates` bucket. 
The location is `s3://cloudsecure-integration-templates/illumio-init.ps1`
2. Production Cluster: It is hosted in Lumos Account under the `cloudsecure-onboarding-templates` bucket. The location is
`s3://cloudsecure-onboarding-templates/cloudsecure/illumio-init.ps1`;.

#### AWS Flow Grant Access CFT Template Generation
For aws flow Grant Access, the cloud formation template will be generated and stored in s3 buckets as specified below:
1. Dev: arn:aws:s3:::cs-integrations-dev-generated-cft - Mr.Pink(************)
2. Stage: arn:aws:s3:::cs-integrations-stage-generated-cft - Mr. Orange(************)
3. Prod: arn:aws:s3:::cs-integrations-prod-generated-cft - Lumos(************)

The template is generated using go templates, and is accessible to the customer using a pre-signed url (with a expiry of 15 minutes) generated as response to the api call `/api/v1/integrations/script`.
For the three buckets specified above, there is a lifecycle management policy used to delete objects after 7 days of creation.

#### Important Note
Whenever there is a change in the template or powershell script, it is not automatically updated during a build update. 
It is our responsibility to update these files in the respective locations. If not, then things will start to fail.
### Misc
Currently, the service is not running via gateway, hence use the proxy url while running the service.
The reason for having separate CFT templates in AWS for readwrite and read is to avoid yet another param to be in customer control. We can have another param as onboarding mode Read/Readwrite. Having that would require us to get the mode back via callback
and check what was selected input. To avoid such checks and granting user more control over things, we maintain separate scripts.

