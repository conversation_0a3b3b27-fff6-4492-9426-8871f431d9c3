package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	integrationspb "illum.io/cloud/api/generated/integrations"
	"illum.io/cloud/api/services/integrations/pkg/integrations"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type IntegrationsClient struct {
	integrationspb.IntegrationsServiceClient
	con *grpc.ClientConn
}

func (c *IntegrationsClient) Close() {
	_ = c.con.Close()
}

// NewIntegrationsClient creates a new gRPC client for service running on url endpoint
func NewIntegrationsClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*IntegrationsClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := integrationspb.NewIntegrationsServiceClient(con)

	return &IntegrationsClient{
		con:                       con,
		IntegrationsServiceClient: client,
	}, nil
}

// NewIntegrationsClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewIntegrationsClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*IntegrationsClient, error) {
	url, ok := config.LookupGRPCService(integrations.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", integrations.ServiceName)
	}
	return NewIntegrationsClient(ctx, url, opts...)
}
