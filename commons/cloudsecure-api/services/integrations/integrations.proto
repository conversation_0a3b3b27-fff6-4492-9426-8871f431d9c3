// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package integrations;

import "integrations/azure.proto";
import "integrations/oci.proto";
import "integrations/connector.proto";
import "integrations/integration_common.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common/protoc-gen-openapiv2/options/annotations.proto";
import "validate/validate.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/api/generated/integrations";

enum Cloud {
    aws = 0;
    azure = 1;
    oci = 2;
    gcp = 3;
    linode = 4;
    digitalocean = 5;
    cloudflare = 6;
}

enum Event {
    Role = 0 [deprecated = true];
    Flow = 1 [deprecated = true];
    AzureRole = 2;
    AzureFlow = 3;
    AWSRole = 4;
    AWSFlow = 5;
    OCIRole = 6;
    OCIFlow = 7;
    AwsConnector = 8;
}

enum CtrlPlaneConsumers {
    CTRL_PLANE_UNSPECIFIED = 0;
    CTRL_PLANE_LABELING = 1;
}

enum DataPlaneConsumers {
    DATA_PLANE_UNSPECIFIED = 0;
    DATA_PLANE_SYNC_SCHEDULER = 1;
    DATA_PLANE_CLOUD_SYNC = 2;
    DATA_PLANE_RELATIONSHIP = 3;
    DATA_PLANE_INVENTORY = 4;
}

enum IntegrationsActions {
    CREATED = 0;
    DELETED = 1;
    UPDATED = 2;
    FAILED_TO_DELETE = 3;
    FAILED_TO_UPDATE = 4;
}

enum SortOrder{
    ASC = 0;
    DESC = 1;
}

message SortBy {
    enum Field{
        UNSPECIFIED_SORT_BY_FIELD = 0;
        ACCOUNT_CSP_ID = 1;
        ENABLED = 2;
        NAME = 3;
    }
    Field field = 1;
    SortOrder sort_order = 2;
}

message IntegrationActionMessage {
    //cs tenant id
    string tenant_id = 1;
    //csp provider
    Cloud cloud = 2;
    // cloudservice provider account id, will contain only child level id (aws: account id, azure: subscription id)
    string account_csp_id = 3;
    //Integration action
    IntegrationsActions action = 4;
}

message CloudCallBackRequest {
    optional string account_id = 1 [(validate.rules).string.pattern = "^[0-9]{12}$|^ocid1.tenancy.*"];
    optional string role_arn = 2 [(validate.rules).string.pattern = "^arn:aws:iam::[0-9]{12}:role/"];
    optional string external_id = 3 [(validate.rules).string.min_len = 1];
    Event type = 4;
    repeated string destinations = 5;
    optional string org_id = 6;
    optional string management_account_id = 7[(validate.rules).string.len = 12];
    optional string client_id = 8 [(validate.rules).string.uuid = true];
    optional string client_secret = 9;
    optional string subscription_id = 10 [(validate.rules).string.uuid = true];
    optional string azure_tenant_id = 11 [(validate.rules).string.uuid = true];
}

message AWSIntegrationObject {
    string id = 1;
    string name = 2;
    string aws_account_cspid = 3;
    string tenant_id = 4;
    string account_type = 5;
    string access_mode = 6;
    string service_account_id = 7;
    repeated string exclude_regions = 8;
    repeated string exclude_subnet_ids = 9;
    repeated string exclude_vpc_ids = 10;
    bool draft = 11;
    google.protobuf.Timestamp created_at = 12;
    google.protobuf.Timestamp updated_at = 13;
    string role_arn = 14;
    bool enable_accounts = 15;
    bool enable_code_deploy = 16;

}

message CreateAWSIntegrationRequest {
    string name = 1;
    string aws_account_cspid = 2 [(validate.rules).string.len = 12];
    string account_type = 4 [(validate.rules).string = {in: ["Account", "Organization"]}];
    string access_mode = 5 [(validate.rules).string = {in: ["Read", "ReadWrite"]}];
    string service_account_id = 6[(validate.rules).string.uuid = true];
    repeated string exclude_regions = 8;
    repeated string exclude_subnet_ids = 9;
    repeated string exclude_vpc_ids = 10;
    optional bool draft = 11;
    optional bool enable_accounts = 12;
    optional bool enable_code_deploy = 13;
}


message GetAWSIntegrationRequest {
    oneof identifier {
        string id = 1 [(validate.rules).string.uuid = true];
        string aws_account_cspid = 2 [(validate.rules).string.len = 12];
    }
}

message GetAWSIntegrationResponse {
    AWSIntegrationObject aws = 1;
}

message ListAWSIntegrationsResponse {
    repeated AWSIntegrationObject aws = 1;
}

message UpdateAWSIntegrationRequest {
    string id = 1 [(validate.rules).string.uuid = true];
    optional string name = 2;
    repeated string exclude_regions = 3;
    repeated string exclude_subnet_ids = 4;
    repeated string exclude_vpc_ids = 5;
    optional bool enable_accounts = 6;
    optional bool enable_code_deploy = 7;
}

message DeleteAWSIntegrationsRequest {
    string id = 1;
}

message GetAccountsListRequest {
    repeated Cloud cloud = 2 [(validate.rules).repeated = {
        items: {
            enum: {
                defined_only: true
            }
        }
    }];
    reserved 1;
}

message AccountsList {
    repeated AzureObject azure = 1;
    repeated AWSObject aws = 2;
    repeated OciIntegration oci = 3;
}

message GetAccountRequest {
    Cloud cloud = 1 [(validate.rules).enum.defined_only = true];
    string account_id = 2;
}

message GetAccountResponse {
    oneof account {
        AzureObject azure = 2;
        OciIntegration oci = 3;
        AWSAccountObject aws = 4;
    }
    reserved 1;
}

message AWSAccountObject {
    string id = 1;
    string name = 2;
    string aws_account_cspid = 3;
    string tenant_id = 4;
    string org_id = 5;
    string management_account_cspid = 6;
    google.protobuf.Timestamp created_at = 12;
    google.protobuf.Timestamp updated_at = 13;
}

message AWSObject {
    string aws_account_cspid = 1;
    string account_type = 2;
    repeated string exclude_regions = 3;
    repeated string exclude_subnet_ids = 4;
    repeated string exclude_vpc_ids = 5;
    bool secret_available = 6;
    string name = 7;
    string access_mode = 8;
    string account_region = 9;
    string management_account_cspid = 10;
    IntegrationType integration_type = 11;
    string org_id = 12;
    bool enable_code_deploy = 13;
}

message BulkDeleteStatus {
    string cloud = 1;
    string id = 2;
    string status = 3;
    optional string message = 4;
}

message StorageConfig {
    string flow_log_config_id = 1 [(validate.rules).string.uuid = true];
    string destination = 2;
    optional bool access = 3;
}

message CreateFlowConfigAccessRequest {
    Cloud cloud = 1;
    string account_csp_id = 2;
    repeated StorageConfig storage_config = 3;
}

message ListFlowConfigRequest {
    Cloud cloud = 1;
    optional string account_csp_id = 2;
}

message FlowConfigObject {
    Cloud cloud = 1;
    string account_csp_id = 2;
    string tenant_id = 3;
    repeated StorageConfig storage_config = 4;
    string account_region = 5;
}

message ListFlowConfigResponse {
    repeated FlowConfigObject flow_config_access = 1;
}

message GetFlowConfigRequest {
    Cloud cloud = 1;
    string account_csp_id = 2;
    string flow_log_config_id = 4;
    reserved 3;
}

message GetFlowConfigResponse {
    bool access = 1;
}

message FlowLogConfigDetailsRequest {
    Cloud cloud = 1;
    string account_id = 2;
}

message FlowLogConfigDetail {
    string id = 1;
    string source = 2;
    string destination = 3;
    string dst_region = 4;
    string src_region = 5;
    string src_account_csp_id = 6;
    bool access = 7;
    string tenant_id = 8;
    Cloud cloud = 9;
}

message FlowLogConfigDetailsResponse {
    string account_csp_id = 1;
    Cloud cloud = 2;
    string access = 3;
    repeated FlowLogConfigDetail configs = 4;
}


message BulkDeleteRequest {
    repeated string aws = 1 [(validate.rules).repeated = {
        unique: true
        items: {
            string: {
                uuid: true
            }
        }
    }];
    repeated string azure = 2 [(validate.rules).repeated = {
        unique: true
        items: {
            string: {
                uuid: true
            }
        }
    }];
    repeated string oci = 3 [(validate.rules).repeated = {
        unique: true
        items: {
            string: {
                uuid: true
            }
        }
    }];
}

message BulkDeleteResponse {
    repeated BulkDeleteStatus responses = 1;
}

message IntegrationsScriptRequest {
    Cloud cloud = 1 ;
    string mode = 2 [(validate.rules).string = {in: ["Read", "Write", "ReadWrite", "Flow", "Connector"]}];
    optional string account_id = 3 [(validate.rules).string.pattern = "^[0-9]{12}$|^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$|^ocid1.tenancy.*"];
    repeated string destinations = 4;
    optional string region = 5; //only required in case of mode Connector
}

message IntegrationsScriptResponse {
    string url = 1;
    optional bytes file = 2;
}

message GetCloudCredentialRequest {
    Cloud cloud = 1;
    string account_csp_id = 2;
}

message GetConnectorCredentialRequest {
    Cloud cloud = 1;
    string account_csp_id = 2[(validate.rules).string.pattern = "^[0-9]{12}$"];
    string destination = 3[(validate.rules).string.min_len = 1];
}

message GetConnectorCredentialResponse {
    oneof credential {
        string role_arn = 1;
        string client_id = 2;
    }
}

message GetCloudCredentialResponse {
    oneof credential {
        string role_arn = 1;
        string client_id = 2;
    }
}

message GetDataPlaneRequest {
    string name = 1;
    optional string csp = 2;
}

message DataPlanes {
    map<string, DataPlane> items = 1;
}

message DataPlane {
    string name = 1;
    repeated Region regions = 2;
}

message Region {
    string name = 1;
    string cloud = 2;
    string description = 3;
}

message GetRegionsRequest {
    Cloud cloud = 1;
}

message GetRegionsResponse {
    repeated Region regions = 1;
}

message Account {
    string id = 1;
    Cloud cloud = 2;
    string account_csp_id = 3;
    string tenant_id = 4;
    string org_id = 5; // aws org id / azure tenant id
    string name = 6;
    bool enabled = 7;
    string sync_status = 8;
    google.protobuf.Timestamp last_synced_at = 9;
    optional string management_account_csp_id = 10; // this is specific to aws only.
    repeated string children = 11;
}

message ChildAccountFilter{
    repeated string account_ids = 1;
    repeated string names = 2;
    optional bool status = 3;
}

message ListAccountsForOrgRequest {
    Cloud cloud = 1;
    string management_account_id = 2 [(validate.rules).string.uuid = true];
    uint32 max_results = 3;
    optional string page_token = 4;
    optional SortBy sort_by = 5;
    optional ChildAccountFilter filter = 6;
}

// Enum for the metadata type
enum MetadataType {
    UNSPECIFIED_METADATA_TYPE = 0;
    ACCOUNT_IDS = 1;
    NAMES = 2;
    ENABLED = 3;
}

message ChildAccountMetadataRequest {
    MetadataType metadata_type = 1;
    Cloud cloud = 2;
    string parent_id = 3[(validate.rules).string.uuid = true]; // this is the internal unique id for the parent
}

message ChildAccountMetadataResponse {
    repeated string values = 1;
}

message UpdateAccountsForOrgRequest {
    Cloud cloud = 1;
    string management_account_id = 2 [(validate.rules).string.uuid = true];
    repeated string account_ids = 3 [(validate.rules).repeated = {
        unique: true,
        items: {
            string: {
                uuid: true
            }
        }

    }];
    optional bool enabled = 4;
}

message UpdateAccountsForOrgResponse {
    optional string message = 1;
}

message DeleteAccountsForOrgRequest {
    Cloud cloud = 1;
    string management_account_id = 2 [(validate.rules).string.uuid = true];
    repeated string account_ids = 3 [(validate.rules).repeated = {
        unique: true,
        items: {
            string: {
                uuid: true
            }
        }
    }];
}

message ListAccountsForOrgResponse {
    repeated Account accounts = 1;
    uint32 total_size = 2;
    optional string next_page_token = 3;
    optional string prev_page_token = 4;
    uint32 page = 5;
}

message SourceDestination{
    string source = 1;
    string destination = 2;
}

message TestFlowLogConfigRequest {
    Cloud cloud = 1;
    string account_id = 2;
    repeated SourceDestination configs = 3;
}

//if the access is false, the error contains message for what access check failed
message FlowLogDestinationAccess {
    string account_id = 1;
    string destination = 2;
    bool access = 3;
    string error = 4;
}

message TestFlowLogConfigResponse {
    repeated FlowLogDestinationAccess access = 1;
}

message ListIntegrationsRequest{
    repeated Cloud cloud = 1;
    repeated IntegrationType integration_type = 2;
}

message ListIntegrationsResponse{
    repeated AWSIntegrationObject aws = 1;
    repeated AzureIntegrationObject azure = 2;
    repeated OciIntegration oci = 3;
}

message FlowLogConfigsDataMigrationRequest{
    optional string tenant_id = 1;
}

message AwsAccountNameMigrationRequest{
    optional string tenant_id = 1;
}

message SyncFlowLogConfigsRequest{
    optional string tenant_id = 1;
}

message DisableTenantRequest {
    string tenant_id = 1;
}

message DisableTenantResponse {
}

message GetFlowLogDestinationRequest {
    Cloud cloud = 1;
    oneof identifier {
        string id = 2 [(validate.rules).string.uuid = true];
        string destination = 3;
    }
}

message FlowLogDestination{
    string id = 1;
    string destination = 2;
    Cloud cloud = 3;
    bool access = 4;
}

message GetFlowLogDestinationResponse {
    FlowLogDestination destination = 1;
}

message BulkUpdateIntegrationsRequest {
    // TODO: add support for other clouds when needed
    repeated string aws = 1 [(validate.rules).repeated = {
        unique: true
        items: {
            string: {
                uuid: true
            }
        }
    }];
    optional bool enableCD = 2;
}

service IntegrationsService {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse);

    rpc CloudCallBack(CloudCallBackRequest) returns (google.protobuf.Empty) {
        option (google.api.http) = {
            post: "/api/v1/integrations/cloud_credentials",
            body: "*"
        };
    }

    rpc CreateAWSIntegration(CreateAWSIntegrationRequest) returns (GetAWSIntegrationResponse) {
        option (google.api.http) = {
            post: "/api/v1/integrations/aws",
            body: "*"
        };
    }

    rpc ListAWSIntegrations(google.protobuf.Empty) returns (ListAWSIntegrationsResponse) {
        option (google.api.http) = {
            get: "/api/v1/integrations/aws"
        };
    }

    rpc GetAWSIntegration(GetAWSIntegrationRequest) returns (GetAWSIntegrationResponse) {
        option(google.api.http) = {
            get: "/api/v1/integrations/aws/{id}"
            additional_bindings: {
                get: "/api/v1/integrations/aws/accounts/{aws_account_cspid}"
            }
        };
    }
    rpc UpdateAWSIntegration(UpdateAWSIntegrationRequest) returns (GetAWSIntegrationResponse) {
        option(google.api.http) = {
            put: "/api/v1/integrations/aws/{id}",
            body: "*"
        };
    }
    rpc DeleteAWSIntegration(DeleteAWSIntegrationsRequest) returns (google.protobuf.Empty) {
        option(google.api.http) = {
            delete: "/api/v1/integrations/aws/{id}"
        };
    }

    rpc CreateAzureIntegration(CreateAzureIntegrationRequest) returns (GetAzureIntegrationResponse) {
        option (google.api.http) = {
            post: "/api/v1/integrations/azure",
            body: "*"
        };
    }

    rpc ListAzureIntegrations(google.protobuf.Empty) returns (ListAzureIntegrationsResponse) {
        option (google.api.http) = {
            get: "/api/v1/integrations/azure"
        };
    }

    rpc ListIntegrations(ListIntegrationsRequest) returns (ListIntegrationsResponse) {
        option (google.api.http) = {
            get: "/api/v1/integrations"
        };
    }

    rpc GetAzureIntegration(GetAzureIntegrationRequest) returns (GetAzureIntegrationResponse) {
        option(google.api.http) = {
            get: "/api/v1/integrations/azure/{id}"
            additional_bindings: {
                get: "/api/v1/integrations/azure/subscriptions/{subscription_cspid}"
            }
        };
    }
    rpc UpdateAzureIntegration(UpdateAzureIntegrationRequest) returns (GetAzureIntegrationResponse) {
        option(google.api.http) = {
            put: "/api/v1/integrations/azure/{id}",
            body: "*"
        };
    }
    rpc DeleteAzureIntegration(DeleteAzureIntegrationsRequest) returns (google.protobuf.Empty) {
        option(google.api.http) = {
            delete: "/api/v1/integrations/azure/{id}"
        };
    }
    rpc GetAccountsList(GetAccountsListRequest) returns (AccountsList) {
        option(google.api.http) = {
            get: "/api/v1/integrations/accounts"
        };
    }
    rpc GetIntegrationsScript(IntegrationsScriptRequest) returns (IntegrationsScriptResponse) {
        option(google.api.http) = {
            get: "/api/v1/integrations/script"
        };
    }
    rpc BulkDeleteIntegrations(BulkDeleteRequest) returns (BulkDeleteResponse) {
        option(google.api.http) = {
            put: "/api/v1/integrations/delete",
            body: "*"
        };
    }
    rpc AddFlowConfigAccess(CreateFlowConfigAccessRequest) returns (google.protobuf.Empty) {
        option(google.api.http) = {
            post: "/api/v1/integrations/flowconfigaccess",
            body: "*"
        };
    }
    rpc ListFlowConfigAccess(ListFlowConfigRequest) returns (ListFlowConfigResponse);
    rpc GetFlowConfigAccess(GetFlowConfigRequest) returns (GetFlowConfigResponse);
    rpc GetCloudCredential(GetCloudCredentialRequest) returns (GetCloudCredentialResponse) {
        option(google.api.http) = {
            get: "/api/v1/integrations/credential"
        };
    }

    rpc GetConnectorCredential(GetConnectorCredentialRequest) returns (GetConnectorCredentialResponse) {
        option(google.api.http) = {
            get: "/api/v1/integrations/credential/connector"
        };
    }

    rpc GetDataPlanes(google.protobuf.Empty) returns (DataPlanes);

    rpc GetDataPlane(GetDataPlaneRequest) returns (DataPlane);

    rpc GetRegions(GetRegionsRequest) returns (GetRegionsResponse) {
        option(google.api.http) = {
            get: "/api/v1/cloud_regions"
        };
    }
    rpc CheckSecretAvailability(google.protobuf.Empty) returns (google.protobuf.Empty);

    rpc ListAccountsForOrg(ListAccountsForOrgRequest) returns (ListAccountsForOrgResponse) {
        option(google.api.http) = {
            get: "/api/v1/accounts/{cloud}/{management_account_id}/child-accounts"
        };
    }

    rpc ChildAccountMetadata(ChildAccountMetadataRequest) returns (ChildAccountMetadataResponse) {
        option(google.api.http) = {
            get: "/api/v1/accounts/child-accounts/metadata"
        };
    }

    rpc UpdateAccountsForOrg(UpdateAccountsForOrgRequest) returns (UpdateAccountsForOrgResponse) {
        option(google.api.http) = {
            put: "/api/v1/accounts/{cloud}/{management_account_id}/child-accounts",
            body: "*"
        };
    }
    rpc DeleteAccountsForOrg(DeleteAccountsForOrgRequest) returns (BulkDeleteResponse) {
        option(google.api.http) = {
            delete: "/api/v1/accounts/{cloud}/{management_account_id}/child-accounts",
            body: "*"
        };
    }
    rpc GetAccount(GetAccountRequest) returns (GetAccountResponse);
    rpc TestFlowLogConfig(TestFlowLogConfigRequest) returns (TestFlowLogConfigResponse) {
        option(google.api.http) = {
            post: "/api/v1/flowlogconfig/test-connection/{cloud}/{account_id}",
            body: "*"
        };
    }

    rpc FlowLogConfigDetails(FlowLogConfigDetailsRequest) returns (FlowLogConfigDetailsResponse) {
        option(google.api.http) = {
            get: "/api/v1/flowlogconfigs"
        };
    }

    rpc CreateOciIntegration(CreateOciIntegrationReqeust) returns (GetOciIntegrationResponse) {
        option(google.api.http) = {
            post: "/api/v1/integrations/oci",
            body: "*"
        };
    }

    rpc ListOciIntegrations(ListOciIntegrationsRequest) returns (ListOciIntegrationsResponse) {
        option(google.api.http) = {
            get: "/api/v1/integrations/oci"
        };
    }

    rpc GetOciIntegrations(GetOciIntegrationRequest) returns (GetOciIntegrationResponse) {
        option(google.api.http) = {
            get: "/api/v1/integrations/oci/{id}"
            additional_bindings: {
                get: "/api/v1/integrations/oci/compartments/{compartment_csp_id}"
            }
        };
    }

    rpc UpdateOciIntegration(UpdateOciIntegrationRequest) returns (GetOciIntegrationResponse) {
        option(google.api.http) = {
            put: "/api/v1/integrations/oci/{id}",
            body: "*"
        };
    }

    rpc DeleteOciIntegration(DeleteOciIntegrationRequest) returns (google.protobuf.Empty) {
        option(google.api.http) = {
            delete: "/api/v1/integrations/oci/{id}"
        };
    }

    rpc CreateConnector(CreateConnectorRequest) returns (CreateConnectorResponse) {
        option(google.api.http) = {
            post: "/api/v1/integrations/connectors",
            body: "*"
        };
    }

    rpc ListConnector(ListConnectorRequest) returns (ListConnectorResponse) {
        option(google.api.http) = {
            get: "/api/v1/integrations/connectors"
        };
    }

    rpc GetConnector(GetConnectorRequest) returns (GetConnectorResponse) {
        option(google.api.http) = {
            get: "/api/v1/integrations/connectors/{id}"
        };
    }

    rpc UpdateConnector(UpdateConnectorRequest) returns (UpdateConnectorResponse) {
    }

    rpc DeleteConnector(DeleteConnectorRequest) returns (DeleteConnectorResponse) {
        option(google.api.http) = {
            delete: "/api/v1/integrations/connectors/{id}"
        };
    }

    rpc TestConnector(TestConnectorRequest) returns (TestConnectorResponse) {
        option(google.api.http) = {
            post: "/api/v1/integrations/connectors/test-connection",
            body: "*"
        };
    }

    rpc GetFlowLogDestination(GetFlowLogDestinationRequest) returns (GetFlowLogDestinationResponse) {
    }

    rpc BulkUpdateIntegrations(BulkUpdateIntegrationsRequest) returns (google.protobuf.Empty) {
        option(google.api.http) = {
            put: "/api/v1/integrations",
            body: "*"
        };
    }

    rpc FlowLogConfigsDataMigration(FlowLogConfigsDataMigrationRequest) returns (google.protobuf.Empty);
    rpc AwsAccountNameMigration(AwsAccountNameMigrationRequest) returns (google.protobuf.Empty);
    rpc SyncFlowLogConfigs(SyncFlowLogConfigsRequest) returns (google.protobuf.Empty);
    rpc DisableTenant(DisableTenantRequest) returns (DisableTenantResponse);
}
