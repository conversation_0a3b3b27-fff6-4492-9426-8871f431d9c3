// Copyright 2024 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package integrations;

import "integrations/integration_common.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/api/generated/integrations";

message AzureIntegrationObject {
  string id = 1;
  string name = 2;
  string account_type = 4;
  string azure_tenant_cspid = 5;
  string azure_subscription_cspid = 6;
  string access_mode = 7;
  repeated string exclude_regions = 8;
  repeated string exclude_subnet_ids = 9;
  repeated string exclude_vnet_ids = 10;
  bool draft = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
  string azure_client_id = 14;
  bool enable_subscriptions = 15;
}

message CreateAzureIntegrationRequest {
  string name = 1;
  string account_type = 2[(validate.rules).string = {in: ["Tenant", "Subscription"]}];
  string azure_tenant_cspid = 3;
  string azure_subscription_cspid = 4;
  string access_mode = 5 [(validate.rules).string = {in: ["Read", "ReadWrite"]}];
  repeated string exclude_regions = 8;
  repeated string exclude_subnet_ids = 9;
  repeated string exclude_vnet_ids = 10;
  optional bool draft = 11;
  optional bool enable_subscriptions = 12;
  reserved 6,7;
}

message GetAzureIntegrationRequest {
  oneof identifier {
    string id = 1 [(validate.rules).string.uuid = true];
    string subscription_cspid = 2 ;
  }
}

message GetAzureIntegrationResponse {
  AzureIntegrationObject azure = 1;
}

message ListAzureIntegrationsResponse {
  repeated AzureIntegrationObject azure = 1;
}

message UpdateAzureIntegrationRequest {
  string id = 1 [(validate.rules).string.uuid = true];
  optional string name = 2;
  repeated string exclude_regions = 3;
  repeated string exclude_subnet_ids = 4;
  repeated string exclude_vnet_ids = 5;
  optional bool enable_subscriptions = 6;
}

message DeleteAzureIntegrationsRequest {
  string id = 1;
}

message AzureObject {
  string azure_tenant_cspid = 1;
  string azure_subscription_cspid = 2;
  string account_type = 3;
  repeated string exclude_regions = 4;
  repeated string exclude_subnet_ids = 5;
  repeated string exclude_vnet_ids = 6;
  bool secret_available = 7;
  string name = 8;
  string access_mode = 9;
  IntegrationType integration_type = 10;
}

// Temporary fix to have multiple proto files.
//TODO: remove after its fixed.
service MockAzure {}