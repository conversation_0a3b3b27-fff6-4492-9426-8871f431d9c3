/**
 * Copyright 2024 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"
	"time"

	"google.golang.org/grpc"

	insightspb "illum.io/cloud/api/generated/insights"
	"illum.io/cloud/api/services/insights/pkg/insights"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

const InsightRegisterInterval = 5 * time.Minute
const InsightGroomOlderThanInterval = 21 * time.Minute

type InsightsClient struct {
	insightspb.InsightsServiceClient
	con *grpc.ClientConn
}

func (c *InsightsClient) Close() {
	_ = c.con.Close()
}

// NewInsightsClient creates a new gRPC client for service running on url endpoint
func NewInsightsClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*InsightsClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := insightspb.NewInsightsServiceClient(con)

	return &InsightsClient{
		con:                   con,
		InsightsServiceClient: client,
	}, nil
}

// NewInsightsClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewInsightsClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*InsightsClient, error) {
	url, ok := config.LookupGRPCService(insights.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", insights.ServiceName)
	}
	return NewInsightsClient(ctx, url, opts...)
}
