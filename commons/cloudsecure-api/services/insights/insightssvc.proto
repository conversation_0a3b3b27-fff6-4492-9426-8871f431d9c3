// Copyright 2024 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package insights;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common/version/version.proto";
import "common/insights/insights.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/api/generated/insights";

service InsightsService {
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
    option (google.api.http) = {
      get: "/api/v1/version"
    };
  }

  rpc RegisterInsights(insights.RegisterInsightsRequest) returns (insights.RegisterInsightsResponse) {}
  rpc DeRegisterInsights(insights.DeRegisterInsightsRequest) returns (insights.DeRegisterInsightsResponse) {}

  rpc GetInsightsList(insights.InsightsListRequest) returns (insights.InsightsListResponse) {
    option (google.api.http) = {
      get: "/api/v1/insights/definitions"
    };
  }

  rpc GetInsight(insights.GetInsightRequest) returns (insights.GetInsightResponse) {
    option (google.api.http) = {
      post: "/api/v1/insights/data/{id}"
      body: "*"
    };
  }

}
