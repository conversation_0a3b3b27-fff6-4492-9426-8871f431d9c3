package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	authenticationpb "illum.io/cloud/api/generated/authentication"
	"illum.io/cloud/api/services/authentication/pkg/authentication"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type AuthenticationClient struct {
	authenticationpb.AuthenticationServiceClient
	con *grpc.ClientConn
}

func (c *AuthenticationClient) Close() {
	_ = c.con.Close()
}

// NewAuthenticationClient creates a new gRPC client for service running on url endpoint
func NewAuthenticationClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*AuthenticationClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := authenticationpb.NewAuthenticationServiceClient(con)

	return &AuthenticationClient{
		con:                         con,
		AuthenticationServiceClient: client,
	}, nil
}

// NewAuthenticationClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewAuthenticationClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*AuthenticationClient, error) {
	url, ok := config.LookupGRPCService(authentication.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", authentication.ServiceName)
	}
	return NewAuthenticationClient(ctx, url, opts...)
}
