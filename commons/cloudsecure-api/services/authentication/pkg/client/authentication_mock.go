package client

import (
	"go.uber.org/mock/gomock"

	"illum.io/cloud/api/generated/authentication/mock"
)

// NewMockAuthenticationClient creates a new mock client
func NewMockAuthenticationClient(ctrl *gomock.Controller) (*AuthenticationClient, *mock.MockAuthenticationServiceClient) {
	m := mock.NewMockAuthenticationServiceClient(ctrl)
	return &AuthenticationClient{
		AuthenticationServiceClient: m,
	}, m
}
