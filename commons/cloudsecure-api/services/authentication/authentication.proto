// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package authentication;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/authentication-api/pkg/generated/authentication";

message AuthnConfigRequest {
  string username = 1;
}

message AuthnConfigResponse {
  string user_type = 1;
  string issuer_url = 2;
  string client_id = 3;
}

message AuthenticateRequest {
  string key_id = 1;
  string secret = 2;
}

message AuthenticateResponse {
  bool status = 1;
  string jwt = 2;
}

service AuthenticationService {
  rpc AuthnConfig(AuthnConfigRequest) returns (AuthnConfigResponse) {
    option (google.api.http) = {
      post: "/api/v1/authn_config"
      body: "*"
    };
  }

  rpc Authenticate(AuthenticateRequest) returns (AuthenticateResponse) {
    option (google.api.http) = {
      post: "/api/v1/authenticate"
      body: "*"
    };
  }

  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
    option (google.api.http) = {
      get: "/api/v1/version"
    };
  }
}

