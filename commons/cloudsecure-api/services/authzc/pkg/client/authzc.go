package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	authzcpb "illum.io/cloud/api/generated/authzc"
	"illum.io/cloud/api/services/authzc/pkg/authzc"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type AuthzcClient struct {
	authzcpb.AuthzcServiceClient
	con *grpc.ClientConn
}

func (c *AuthzcClient) Close() {
	_ = c.con.Close()
}

// NewAuthzcClient creates a new gRPC client for service running on url endpoint
func NewAuthzcClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*AuthzcClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := authzcpb.NewAuthzcServiceClient(con)

	return &AuthzcClient{
		con:                 con,
		AuthzcServiceClient: client,
	}, nil
}

// NewAuthzcClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewAuthzcClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*AuthzcClient, error) {
	url, ok := config.LookupGRPCService(authzc.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", authzc.ServiceName)
	}
	return NewAuthzcClient(ctx, url, opts...)
}

// inventory.stage.data.aws.us-east-1.cloud.ilabs.io
