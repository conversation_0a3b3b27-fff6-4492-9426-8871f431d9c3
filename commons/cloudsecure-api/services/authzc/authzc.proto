syntax = "proto3";

package authzc;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/version/version.proto";
import "google/protobuf/timestamp.proto";


option go_package = "illum.io/cloud/authzc-api/pkg/generated/authzc";

message Principal {
  enum KIND {
    UNKNOWN = 0;
    JWT = 1;
    UC_TOKEN = 2;
    REFRESH = 3;
  }
  KIND kind = 1;
  string id = 2;
}


message TokenPair {
  string access = 1;
  string refresh = 2;
}

message AccessInfo {
  repeated string permissions = 1;
  repeated string scopes = 2;
}

message ProductAuth {
  TokenPair tokens = 1;
  map<string, string> data = 2;
  AccessInfo access_info = 3;
}

//
message UserInfo {
  string id = 1;
  string name = 2;
  string email = 3;
  string tenant_id = 4;
  uint64 uc_id = 5;
  string first_name = 6;
  string last_name = 7;
  google.protobuf.Timestamp created_at = 8;
}

message AuthorizeRequest {
  Principal principal = 1;
}

message CoreAuth {
  map<string, string> data = 1;
}

message Products {
  ProductAuth cloud = 1;
  repeated CoreAuth core = 2;
}

message AuthorizeResponse {
  TokenPair unified_console_token = 1;
  Products products = 2;
  UserInfo user_info = 3;
}

message ValidateRequest {
  string token = 1;
}

message ValidateResponse {
  reserved 1, 2;
  string user_id = 3;
  string tenant_id = 4;
  string user_email = 5;
  google.protobuf.Timestamp created = 6;
  string service_account_id = 7;
}

message InvalidateRequest {
  string token = 1;
}
message InvalidateResponse {
}

message GetValidationKeysRequest {
}

message GetValidationKeysResponse {
  repeated bytes keys = 1;
}

message GetServiceAccountAccessTokenRequest {
  string service_account_id = 1;
  string tenant_id = 2;
}

message GetServiceAccountAccessTokenResponse {
  string token = 1;
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
message DeactivateUserRequest {
  string tenant_id = 1;
  string user_id = 2;
}

message DeactivateUserResponse{
}
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
message RefreshCloudTokenRequest {
  string token = 1;
}

message RefreshCloudTokenResponse{
  string token = 1;
}
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
message CreateAccessTokensRequest {
  string tenant_id = 1;
  string user_id = 2;
  uint64 uc_id = 3;
  string email = 4;
  repeated string extra_roles = 5;
}

message CreateAccessTokensResponse {
  TokenPair unified_console_tokens = 1;
  TokenPair cloud_tokens = 2;
  AccessInfo cloud_access_info = 3;
}
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// https://datatracker.ietf.org/doc/html/rfc6749#section-4.4.2
// These authenticate request and response are used to authenticate the user and get the access token
// using Oauth2 client credentials grant type.

// using Oauth2 client credentials Request
message AuthenticateRequest {
  string client_id = 1;
  string client_secret = 2;
  string grant_type = 3;
}


// using Oauth2 client credentials Response
message AuthenticateResponse {
  string access_token = 1;
  string token_type = 2;
  int32 expires_in = 3;
}

// using Oauth2 client credentials Error Response
message AuthenticateErrorResponse {
  string error = 1;
}
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

service AuthzcService {
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse);

  rpc Authorize(AuthorizeRequest) returns(AuthorizeResponse) {
    option (google.api.http) = {
      post: "/api/v1/authorize"
      body: "*"
    };
  }

  rpc Authenticate(AuthenticateRequest) returns(AuthenticateResponse) {
    option (google.api.http) = {
      post: "/api/v1/authenticate"
      body: "*"
    };
  }

  rpc CreateAccessTokens(CreateAccessTokensRequest) returns(CreateAccessTokensResponse);

  rpc Validate(ValidateRequest) returns(ValidateResponse);

  rpc Invalidate(InvalidateRequest) returns(InvalidateResponse);

  rpc GetServiceAccountAccessToken(GetServiceAccountAccessTokenRequest) returns(GetServiceAccountAccessTokenResponse);

  rpc GetValidationKeys(GetValidationKeysRequest) returns(GetValidationKeysResponse);

  rpc DeactivateUser(DeactivateUserRequest) returns(DeactivateUserResponse);

  rpc RefreshCloudToken(RefreshCloudTokenRequest) returns(RefreshCloudTokenResponse);
}
