package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	boltpb "illum.io/cloud/api/generated/bolt"
	boltpkg "illum.io/cloud/api/services/bolt/pkg/bolt"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type BoltClient struct {
	boltpb.BoltServiceClient
	con *grpc.ClientConn
}

func (c *BoltClient) Close() {
	_ = c.con.Close()
}

func NewBoltClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*BoltClient, error) {
	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := boltpb.NewBoltServiceClient(con)

	return &BoltClient{
		con:               con,
		BoltServiceClient: client,
	}, nil
}

// NewBoltClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewBoltClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*BoltClient, error) {
	url, ok := config.LookupGRPCService(boltpkg.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", boltpkg.ServiceName)
	}
	return NewBoltClient(ctx, url, opts...)
}
