// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package bolt;

import "google/protobuf/empty.proto";
import "common/version/version.proto";
import "google/api/annotations.proto";
import "common/protoc-gen-openapiv2/options/annotations.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "labeling/actors.proto";


option go_package = "illum.io/cloud/bolt/pkg/generated/bolt";

// Definition for the platform
message Slack {
  string id = 1; //unique id for the platform
  string webhook_url = 2 [(validate.rules).string = {pattern: "^https://hooks.slack.com/services/T[A-Z0-9]+/B[A-Z0-9]+/[A-Za-z0-9]+$"}];
  string channel_name = 3 [(validate.rules).string.min_len = 1];
  google.protobuf.Timestamp created_at = 4; // Not to be given as part of the request. This is configured by the backend.
  google.protobuf.Timestamp updated_at = 5; // Not to be given as part of the request. This is configured by the backend.
  string updated_by = 6; // Last updated by UserName - not to be given as part of the request. This is configured by the backend.
}

// Supported Platform Types
enum PlatformType {
  UNSPECIFIED = 0;
  SLACK = 1;
}

// Send API request and response
message BoltTask {
  string tenant_id = 1[(validate.rules).string.uuid = true];
  string message = 3 [(validate.rules).string.min_len = 1];// message to be passed
  TriggerType triggerType = 4;
  RuleFilter filter = 5;
  reserved 2;
}


// Platform - CRUD API Request and Responses
message AddPlatformRequest {
  PlatformType platform_type = 1;
  oneof request {
    Slack slack = 2;
  }
}

message AddPlatformResponse {
  oneof response {
    Slack slack = 1;
  }
}

message GetPlatformRequest {
  string platform_id = 1 [(validate.rules).string.uuid = true];
  PlatformType platform_type = 2;
}

message GetPlatformResponse {
  oneof response {
    Slack slack = 1;
  }
}

message ListPlatformsForTenantRequest {
  PlatformType platform_type = 1;
}

message ListPlatformsForTenantResponse {
  repeated Platform platforms = 1;
}

message Platform {
  oneof item {
    Slack slack = 1;
  }
}

message DeletePlatformRequest {
  string platform_id = 1 [(validate.rules).string.uuid = true];
  PlatformType platform_type = 2;
}

message UpdatePlatformRequest {
  PlatformType platform_type = 1;
  oneof request {
    Slack slack = 2;
  }
}

message UpdatePlatformResponse {
  oneof response {
    Slack slack = 1;
  }
}

//Rules
message PlatformInfo {
  string name = 1;
  PlatformType platform_type = 2;
}

enum TriggerType{
  UNSPECIFIED_TRIGGER = 0;
  POLICY_PROVISIONED = 1;
  AUDIT_EVENT = 2;
  SYSTEM_EVENT = 3;
}

message Rule {
  string id = 1;
  string name = 2 [(validate.rules).string.min_len = 1];
  TriggerType trigger_type = 3;
  map<string, PlatformInfo> platforms = 4; // map with channel_id as key
  string custom_message = 6; // custom message to append to message from SendAlert
  google.protobuf.Timestamp created_at = 7; // Not to be given as part of the request. This is configured by the backend.
  google.protobuf.Timestamp updated_at = 8; // Not to be given as part of the request. This is configured by the backend.
  string updated_by = 9; // Last updated by UserName - not to be given as part of the request. This is configured by the backend.
  AlertSchedule schedule = 10;
  RuleFilter filter = 11;
  reserved 5;
}

message CreateRuleRequest {
  Rule rule = 1;
}

message CreateRuleResponse {
  Rule rule = 1;
}

message UpdateRuleRequest {
  Rule rule = 1;
}

message UpdateRuleResponse {
  Rule rule = 1;
}

message GetRuleRequest {
  string rule_id = 1 [(validate.rules).string.uuid = true];
}

message GetRuleResponse {
  Rule rule = 1;
}

message ListRulesResponse {
  repeated Rule rules = 1;
}

message DeleteRuleRequest {
  string rule_id = 1 [(validate.rules).string.uuid = true];
}

message DeleteTenantDataRequest {
  string tenant_id = 1 [(validate.rules).string.uuid = true];
}

message DeleteTenantDataResponse {
}

//Configurations for the tenant schedule to alert - this is unique for each tenant and will apply across all rules for the tenant

enum DayOfWeek {
  UNSPECIFIED_DAY = 0;
  SUNDAY = 1;
  MONDAY = 2;
  TUESDAY = 3;
  WEDNESDAY = 4;
  THURSDAY = 5;
  FRIDAY = 6;
  SATURDAY = 7;
}

enum Frequency{
  UNSPECIFIED_FREQUENCY = 0;
  IMMEDIATELY = 1;
  HOURLY = 2;
  DAILY = 3;
  WEEKLY = 4;
  MONTHLY = 5;
}

message PolicyProvisionedFilter {
  repeated labeling.LabelObj labels = 1;
}

message AuditEventFilter {
  optional string type = 1;
  optional string category = 2;
  optional bool successful = 3;
}

message SystemEventFilter {
  optional string type = 1;
  optional string category = 2;
  optional bool successful = 3;
}

message RuleFilter {
  oneof filter {
      PolicyProvisionedFilter policy_provision_filter = 1;
      AuditEventFilter audit_event_filter = 2;
      SystemEventFilter system_event_filter = 3;
  }
}

message AlertSchedule {
  string id = 10;
  optional int32 hours = 1 [(validate.rules).int32 = {gte: 0, lte: 23}];
  optional int32 minutes = 2[(validate.rules).int32 = {gte: 0, lte: 59}];
  optional string timezone = 3[(validate.rules).string.min_len = 1];
  Frequency frequency = 4;
  optional DayOfWeek day_of_week = 5;
  optional int32 day_of_month = 6[(validate.rules).int32 = {gte: 0, lte: 31}];
  google.protobuf.Timestamp created_at = 7; // Not to be given as part of the request. This is configured by the backend.
  google.protobuf.Timestamp updated_at = 8; // Not to be given as part of the request. This is configured by the backend.
  string updated_by = 9; // Last updated by UserName - not to be given as part of the request. This is configured by the backend.
}

message CreateAlertScheduleRequest {
  AlertSchedule schedule = 1;
}

message CreateAlertScheduleResponse {
  AlertSchedule schedule = 1;
}

message UpdateAlertScheduleRequest {
  AlertSchedule schedule = 1;
}

message UpdateAlertScheduleResponse {
  AlertSchedule schedule = 1;
}

message GetAlertScheduleRequest{
  string id = 1 [(validate.rules).string.uuid = true];
}

message DeleteAlertScheduleRequest{
  string id = 1 [(validate.rules).string.uuid = true];
}

message GetAlertScheduleResponse {
  AlertSchedule schedule = 1;
}

message SendSlackMessageRequest {
  string platform_id = 1 [(validate.rules).string.uuid = true];
  optional string message = 2;
}

message SendSlackMessageResponse {
}

service BoltService {
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse);

  rpc DeleteTenant(DeleteTenantDataRequest) returns (DeleteTenantDataResponse);

  // Method to add a new platform
  rpc AddPlatform(AddPlatformRequest) returns (AddPlatformResponse) {
    option(google.api.http) = {
      post: "/api/v1/bolt/platforms"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "201"
        value: {
          description: "Successful platform creation"
          schema: {
            json_schema: {
              ref: ".bolt.AddPlatformResponse"
            }
          }
        }
      }
    };
  }

  // Method to get platforms
  rpc GetPlatform(GetPlatformRequest) returns (GetPlatformResponse) {
    option (google.api.http) = {
      get: "/api/v1/bolt/platforms/{platform_type}/{platform_id}"
    };
  }

  // Method to delete platforms
  rpc DeletePlatform(DeletePlatformRequest) returns (google.protobuf.Empty) {
    option(google.api.http) = {
      delete: "/api/v1/bolt/platforms/{platform_type}/{platform_id}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "Successful Platform Deletion"
          schema: {}
        }
      }
    };
  }

  // Method to update platforms
  rpc UpdatePlatform(UpdatePlatformRequest) returns (UpdatePlatformResponse) {
    option(google.api.http) = {
      put: "/api/v1/bolt/platforms/{platform_type}"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "201"
        value: {
          description: "Successfully updated Platform."
          schema: {
            json_schema: {
              ref: ".bolt.UpdatePlatformResponse"
            }
          }
        }
      }
    };
  }

  // Method to list platforms for a tenant
  rpc ListPlatformsForTenant(ListPlatformsForTenantRequest) returns (ListPlatformsForTenantResponse) {
    option(google.api.http) = {
      get: "/api/v1/bolt/platforms/{platform_type}"
    };
  }

  //Rule APIs
  // Method to create a new rule
  rpc CreateRule(CreateRuleRequest) returns (CreateRuleResponse) {
    option(google.api.http) = {
      post: "/api/v1/bolt/rules"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "201"
        value: {
          description: "Successful rule creation"
          schema: {
            json_schema: {
              ref: ".bolt.CreateRuleResponse"
            }
          }
        }
      }
    };
  }

  // Method to get a rule
  rpc GetRule(GetRuleRequest) returns (GetRuleResponse) {
    option (google.api.http) = {
      get: "/api/v1/bolt/rules/{rule_id}"
    };
  }

  // Method to delete a rule
  rpc DeleteRule(DeleteRuleRequest) returns (google.protobuf.Empty) {
    option(google.api.http) = {
      delete: "/api/v1/bolt/rules/{rule_id}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "Successful rule deletion"
          schema: {}
        }
      }
    };
  }

  // Method to update a rule
  rpc UpdateRule(UpdateRuleRequest) returns (UpdateRuleResponse) {
    option(google.api.http) = {
      put: "/api/v1/bolt/rules"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "201"
        value: {
          description: "Successfully updated rule"
          schema: {
            json_schema: {
              ref: ".bolt.UpdateRuleResponse"
            }
          }
        }
      }
    };
  }

  // Method to get all the rules for a tenant
  rpc ListRules(google.protobuf.Empty) returns (ListRulesResponse) {
    option(google.api.http) = {
      get: "/api/v1/bolt/rules"
    };
  }

  rpc CreateAlertSchedule(CreateAlertScheduleRequest) returns (CreateAlertScheduleResponse){
    option(google.api.http) = {
      post: "/api/v1/bolt/rules/schedules"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "201"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }

  rpc GetAlertSchedule(GetAlertScheduleRequest) returns (GetAlertScheduleResponse){
    option(google.api.http) = {
      get: "/api/v1/bolt/rules/schedules/{id}"
    };
  }

  rpc CreateRuleForExistingSlack(google.protobuf.Empty) returns (google.protobuf.Empty){
  }

  rpc UpdateAlertSchedule(UpdateAlertScheduleRequest) returns (UpdateAlertScheduleResponse){
    option(google.api.http) = {
      put: "/api/v1/bolt/rules/schedules"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "201"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }

  rpc DeleteAlertSchedule(DeleteAlertScheduleRequest) returns (google.protobuf.Empty){
    option(google.api.http) = {
      delete: "/api/v1/bolt/rules/schedules/{id}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }

  rpc SendSlackMessage(SendSlackMessageRequest) returns (SendSlackMessageResponse){
    option(google.api.http) = {
      post: "/api/v1/bolt/sendslackmessage"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "201"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }
}
