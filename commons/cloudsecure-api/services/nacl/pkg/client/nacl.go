/**
 * Copyright 2024 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	naclpb "illum.io/cloud/api/generated/nacl"
	"illum.io/cloud/api/services/nacl/pkg/nacl"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type NaclClient struct {
	naclpb.NaclServiceClient
	con *grpc.ClientConn
}

func (c *NaclClient) Close() {
	_ = c.con.Close()
}

// NewNaclClient creates a new gRPC client for service running on url endpoint
func NewNaclClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*NaclClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := naclpb.NewNaclServiceClient(con)

	return &NaclClient{
		con:               con,
		NaclServiceClient: client,
	}, nil
}

// NewNaclClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewNaclClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*NaclClient, error) {
	url, ok := config.LookupGRPCService(nacl.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", nacl.ServiceName)
	}
	return NewNaclClient(ctx, url, opts...)
}
