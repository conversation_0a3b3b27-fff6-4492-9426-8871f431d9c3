// Copyright 2024 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package nacl;

import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "common/version/version.proto";
import "common/wipeout/wipeout.proto";

import "common/policy/policy.proto";
import "common/policy/policyimpact.proto";

option go_package = "illum.io/cloud/api/generated/nacl";

message Nacl {
  string id      = 1 [(validate.rules).string.uuid = true];
  string csp_id  = 2 [(validate.rules).string.min_len = 1];
  optional string name = 3;
}

message EnforceRequest {
  Nacl nacl = 1 [(validate.rules).message.required = true];
  string region = 2 [(validate.rules).string.min_len = 1];
  string account_id = 3 [(validate.rules).string.min_len = 1];
  policy.Policy policy = 4;
  bool force = 5;
  google.protobuf.Timestamp originated_at = 6;
}

message EnforceResponse {
  Nacl nacl = 1 [(validate.rules).message.required = true];
  policy.PolicyDigest policy_digest = 2 [(validate.rules).message.required = true];
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;
}

message EnforcementTask {
  EnforceRequest enforce_request = 1;
  string tenant_id = 2 [(validate.rules).string.uuid = true];
  policy.EnforcementMode mode = 3 [(validate.rules).enum.defined_only = true];
  google.protobuf.Timestamp originated_at = 4;
}

message SyncEnforcementPointRequest {
  Nacl nacl = 1 [(validate.rules).message.required = true];
  string region_name = 2 [(validate.rules).string.min_len = 1];
  string aws_account_id = 3 [(validate.rules).string.min_len = 1];
  policy.PolicyDigest policy_digest = 4 [(validate.rules).message.required = true];
  policy.EnforcementMode mode = 5;
}

message SyncEnforcementPointResponse {}

message DeleteEnforcementPointsRequest {
  repeated string EnforcementPointIds = 1  [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      string: {
        uuid: true
      }
    }
  }];
}

message DeleteEnforcementPointsResponse {}

service NaclService {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
        get: "/api/v1/version"
      };
    }
    // Enforce programs aws Network ACL
    rpc Enforce(EnforceRequest) returns (EnforceResponse);
    // GetPolicyImpact calculates policy impact
    rpc GetPolicyImpact(policyimpact.EnforcementPointPolicyImpactRequest) returns (policyimpact.EnforcementPointPolicyImpactResponse);
    rpc DeleteTenant(wipeout.DeleteTenantDataRequest) returns (wipeout.DeleteTenantDataResponse);

    // updates enforcement_mode for enforcement point\
    // creates enforcement point record in case of data plane change
    rpc SyncEnforcementPoint(SyncEnforcementPointRequest) returns (SyncEnforcementPointResponse) {}

    // deletes given enforcement points
    rpc DeleteEnforcementPoints(DeleteEnforcementPointsRequest) returns (DeleteEnforcementPointsResponse) {}
}
