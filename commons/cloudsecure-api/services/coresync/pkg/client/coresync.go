/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	coresyncpb "illum.io/cloud/api/generated/coresync"
	"illum.io/cloud/api/services/coresync/pkg/coresync"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type CoresyncClient struct {
	coresyncpb.CoresyncServiceClient
	con *grpc.ClientConn
}

func (c *CoresyncClient) Close() {
	_ = c.con.Close()
}

// NewCoresyncClient creates a new gRPC client for service running on url endpoint
func NewCoresyncClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*CoresyncClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := coresyncpb.NewCoresyncServiceClient(con)

	return &CoresyncClient{
		con:                   con,
		CoresyncServiceClient: client,
	}, nil
}

// NewCoresyncClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewCoresyncClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*CoresyncClient, error) {
	url, ok := config.LookupGRPCService(coresync.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", coresync.ServiceName)
	}
	return NewCoresyncClient(ctx, url, opts...)
}
