package client

import (
	"go.uber.org/mock/gomock"

	"illum.io/cloud/api/generated/policyauthoring/mock"
)

// NewMockPolicyAuthoringClient creates a new mock client
func NewMockPolicyAuthoringClient(ctrl *gomock.Controller) (*PolicyAuthoringClient, *mock.MockPolicyAuthoringServiceClient) {
	m := mock.NewMockPolicyAuthoringServiceClient(ctrl)
	return &PolicyAuthoringClient{
		PolicyAuthoringServiceClient: m,
	}, m
}
