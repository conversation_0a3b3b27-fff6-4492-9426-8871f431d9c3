// Copyright 2023 Illumio, Inc. All Rights Reserved.

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	policy_authoringpb "illum.io/cloud/api/generated/policyauthoring"
	"illum.io/cloud/api/services/policyauthoring/pkg/policyauthoring"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type PolicyAuthoringClient struct {
	policy_authoringpb.PolicyAuthoringServiceClient
	con *grpc.ClientConn
}

func (c *PolicyAuthoringClient) Close() {
	_ = c.con.Close()
}

// NewPolicyAuthoringClient creates a new gRPC client for service running on url endpoint
func NewPolicyAuthoringClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*PolicyAuthoringClient, error) {
	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := policy_authoringpb.NewPolicyAuthoringServiceClient(con)

	return &PolicyAuthoringClient{
		con:                          con,
		PolicyAuthoringServiceClient: client,
	}, nil
}

// NewPolicyAuthoringClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewPolicyAuthoringClientLookup(ctx context.Context) (*PolicyAuthoringClient, error) {
	url, ok := config.LookupGRPCService(policyauthoring.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", policyauthoring.ServiceName)
	}
	return NewPolicyAuthoringClient(ctx, url)
}
