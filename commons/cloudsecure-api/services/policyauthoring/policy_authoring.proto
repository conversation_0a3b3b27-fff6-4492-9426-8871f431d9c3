// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package policy_authoring;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common/protoc-gen-openapiv2/options/annotations.proto";
import "validate/validate.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/policyauthoring-api/pkg/generated/policyauthoring";

message ResourceReference {
  string href = 1;
}

message PendingResourceReference {
  string href = 1;
  string name = 2;
  User   updated_by = 3;
  string updated_at = 4;
  string update_type = 5;
}

message User {
  optional string href = 1;
  string          username = 2;
  optional string full_name = 3;
}

// policy
message PolicyVersion {
  string policy_id = 1;
  string href = 2;
  int32  version = 3;
  string commit_message = 4;
  string created_at = 5;
  User   created_by = 6;
}

message AuditEventDetails {
  string message = 1;
  PolicyVersion response = 2;
}

message PolicyChangeSubset {
  repeated ResourceReference services = 1;
  repeated ResourceReference rule_sets = 2;
  repeated ResourceReference ip_lists = 3;
}

message ListPolicyVersionsRequest {
}
message ListPolicyVersionsResponse {
  repeated PolicyVersion policy_versions = 1;
}

message ProvisionPolicyRequest {
  optional string             update_description = 1;
  optional PolicyChangeSubset change_subset = 2;
}

message RevertPartialDraftPolicyRequest {
  optional PolicyChangeSubset change_subset = 1;
}

message PendingRuleSet {
  string                     ruleset_id = 1;
  string                     href = 2;
  string                     name = 3;
  User                       updated_by = 4;
  string                     updated_at = 5;
  string                     update_type = 6;
  repeated ResourceReference related_ip_lists = 7;
  repeated ResourceReference related_services = 8;
}

message PendingPolicyRequest {
}
message PendingPolicyResponse {
  repeated PendingRuleSet           rule_sets = 1;
  repeated PendingResourceReference ip_lists = 2;
  repeated PendingResourceReference services = 3;
}

message RevertPendingPolicyRequest {
}

message RestorePolicyVersionRequest {
  string policy_version = 1;
}

message ListDraftDependenciesRequest {
  string operation = 1 [(validate.rules).string = {in: ["commit", "revert"]}];
  PolicyChangeSubset change_subset = 2;
}

message DependencyInfo {
  Dependency dependency = 1;
  RequiredBy required_by = 2;
}

message ListDraftDependenciesResponse {
  repeated DependencyInfo dependencies = 1;
}

message Dependency {
    optional ResourceReference ip_lists = 1;
    optional ResourceReference services = 2;
}

message RequiredBy {
  repeated ResourceReference rule_sets = 1;
}

// ip lists
message IPListRequest {
  string policy_version = 1;
  string ip_list_id = 2;
  string policy_engine_url = 3;
}

message ListIPListsRequest {
  string policy_version = 1;
  string policy_engine_url = 2;
}

message IPListMultiDeleteRequest {
  repeated ResourceReference ip_lists = 1;
}

message IPRange {
  optional string description = 1;
  string          from_ip = 2;
  optional string to_ip = 3;
  bool            exclusion = 4;
}

message IPListResponse {
  string           ip_list_id = 1;
  string           href = 2;
  string           name = 3;
  string           description = 4;
  repeated IPRange ip_ranges = 5;

  string          created_at = 6;
  User            created_by = 7;
  string          updated_at = 8;
  User            updated_by = 9;
  optional string deleted_at = 10;
  optional User   deleted_by = 11;
  optional string update_type = 12;
}

message ListIPListsResponse {
  repeated IPListResponse ip_lists = 1;
}

message UpdateIPListRequest {
  string           policy_version = 1;
  string           ip_list_id = 2;
  optional string  name = 3;
  optional string  description = 4;
  repeated IPRange ip_ranges = 5;
}

message CreateIPListRequest {
  string           policy_version = 1;
  string           name = 2;
  optional string  description = 3;
  repeated IPRange ip_ranges = 4;
}

message IPListsAutocompleteRequest {
  string          policy_version = 1;
  string          query = 2;
  optional int32  max_results = 3;
  string policy_engine_url = 4;
}

message IPListsAutocompleteMatch {
  string href = 1;
  string name = 2;
}

message IPListsAutocompleteResponse {
  int32                             num_matches = 1;
  repeated IPListsAutocompleteMatch matches = 2;
}

// services
message ServiceRequest {
  string policy_version = 1;
  string service_id = 2;
}

message ListServicesRequest {
  string         policy_version = 1;
}

message ServicesMultiDeleteRequest {
  repeated ResourceReference services = 1;
}

message ServicePort {
  optional int32 port = 1;
  optional int32 to_port = 2;
  int32          proto = 3;
  optional int32 icmp_type = 4;
  optional int32 icmp_code = 5;
}

message ServiceResponse {
  string               service_id = 1;
  string               href = 2;
  string               name = 3;
  string               description = 4;
  repeated ServicePort service_ports = 5;

  string          created_at = 6;
  User            created_by = 7;
  string          updated_at = 8;
  User            updated_by = 9;
  optional string deleted_at = 10;
  optional User   deleted_by = 11;
  optional string update_type = 12;
}

message ListServicesResponse {
  repeated ServiceResponse services = 1;
}

message UpdateServiceRequest {
  string               policy_version = 1;
  string               service_id = 2;
  optional string      name = 3;
  optional string      description = 4;
  repeated ServicePort service_ports = 5;
}

message CreateServiceRequest {
  string               policy_version = 1;
  string               name = 2;
  optional string      description = 3;
  repeated ServicePort service_ports = 4;
}

message ServicesAutocompleteRequest {
  string          policy_version = 1;
  string          query = 2;
  optional int32  max_results = 3;
  optional string type = 4;
}

message ServicesAutocompleteMatch {
  string href = 1;
  string name = 2;
}
message ServicesAutocompleteResponse {
  int32                              num_matches = 1;
  repeated ServicesAutocompleteMatch matches = 2;
}

// labels
message LabelRequest {
  string label_id = 1;
  string policy_engine_url = 2;
}

message ListLabelsRequest {
  optional string key = 1;
  optional string value = 2;
  optional bool usage = 3;
  string policy_engine_url = 4;
}

message LabelsMultiDeleteRequest {
  repeated ResourceReference labels = 1;
}

message LabelsMultiDeleteResponse {
  message FailedLabelsDelete {
    string error = 2;
    repeated string label_ids = 1;
  }
  repeated FailedLabelsDelete failures = 1;
}

message LabelUsage {
  bool ruleset = 1;
  bool workload = 2;
  bool enforcement_point = 3;
}

message LabelResponse {
  string     label_id = 1;
  string     href = 2;
  string     key = 3;
  string     value = 4;
  LabelUsage usage = 5;

  string created_at = 6;
  User   created_by = 8;
  string updated_at = 7;
  User   updated_by = 9;

  optional bool deleted = 10;
}

message ListLabelsResponse {
  repeated LabelResponse labels = 1;
}

message UpdateLabelRequest {
  string  label_id = 1;
  string  value = 2;
}

message CreateLabelRequest {
  string key = 1;
  string value = 2;
}

message LabelsAutocompleteRequest {
  string          query = 1;
  optional int32  max_results = 2;
  optional string key = 3;
  optional string selected_scope = 4;
  optional string resource_type = 5;
  optional string exclude_keys = 6;
  string policy_engine_url = 7;
}

message LabelsAutocompleteMatch {
  string href = 1;
  string key = 2;
  string value = 3;
  string id = 4;
}
message LabelsAutocompleteResponse {
  int32                            num_matches = 1;
  repeated LabelsAutocompleteMatch matches = 2;
}

// label dimensions
message LabelDimensionRequest {
  string label_dimension_id = 1;
}

message ListLabelDimensionsRequest {
  optional string key = 1;
  optional bool include_deleted = 2;
  optional string external_data_set = 3;
  optional string external_data_reference = 4;
  optional string display_name = 5;
  optional int32 max_results = 6;
}

message LabelDimensionDisplayInfo {
  optional string initial = 1;
  optional string icon = 2;
  optional string background_color = 3;
  optional string foreground_color = 4;
  optional int32  sort_ordinal = 5;
  optional string display_name_plural = 6;
}

message LabelDimensionUsage {
  bool labels = 1;
}

message LabelDimensionResponse {
  string                             label_dimension_id = 1;
  string                             href = 2;
  string                             display_name = 3;
  string                             external_data_set = 4;
  string                             external_data_reference = 5;
  string                             key = 6;
  optional LabelDimensionDisplayInfo display_info = 7;
  LabelDimensionUsage                usage = 8;

  string                             created_at = 9;
  User                               created_by = 10;
  string                             updated_at = 11;
  User                               updated_by = 12;
  optional string                    deleted_at = 13;
  optional User                      deleted_by = 14;
  optional bool                      deleted = 15;
}

message ListLabelDimensionsResponse {
  repeated LabelDimensionResponse label_dimensions = 1;
}

message UpdateLabelDimensionRequest {
  string                             label_dimension_id = 1;
  optional string                    display_name = 2;
  optional LabelDimensionDisplayInfo display_info = 3;
  optional string                    external_data_set = 4;
  optional string                    external_data_reference = 5;
}

message CreateLabelDimensionRequest {
  string                             key = 1;
  string                             display_name = 2;
  optional LabelDimensionDisplayInfo display_info = 3;
  optional string                    external_data_set = 4;
  optional string                    external_data_reference = 5;
}

// rulesets
message RuleSetScope {
  // LabelScope is not used outside RuleSetScope, so it's declared and used within RuleSetScope
  message LabelScope {
    string href = 1;
    optional string key = 2;
    optional string value = 3;
  }

  message Scope {
    LabelScope label = 2;
  };

  repeated Scope scope = 1;
}

// CRUD for ruleset
message CreateRuleSetRequest {
  string policy_version = 1;

  string name = 2;
  optional string description = 3;
  optional string external_data_set = 4;
  optional string external_data_reference = 5;
  optional bool enabled = 6;
  repeated RuleSetScope scopes = 7;
  repeated SecRule rules = 8;
  repeated DenyRule deny_rules = 9;
}

message UpdateRuleSetRequest {
  message RuleReference {
    string href = 1;
    optional bool enabled = 2; // UI can enable/disable multiple rules at once
  }

  string policy_version = 1;
  string rule_set_id = 2;

  optional string name = 3;
  optional string description = 4;
  optional string external_data_set = 5;
  optional string external_data_reference = 6;
  optional bool enabled = 7;
  bool preserve_existing_rules = 8; // defaults to false
  optional string update_type = 9;
  repeated RuleSetScope scopes = 10;

  // rules and deny_rules support anyOf. json anyOf is not mapped 1:1 in proto
  // UI doesn't need anyOf for rules when updating ruleset.
  // Therefore, CloudSecure will use repeated href for rules and deny_rules
  // Don't need ip_tables_rules
  repeated RuleReference rules = 11;
  repeated RuleReference deny_rules = 12;
}

message GetRuleSetRequest {
  string policy_version = 1;
  string rule_set_id = 2;
}

message RuleSetResponse {
  string rule_set_id = 1;
  string href = 2;
  string name = 3;
  string description = 4;
  optional string external_data_set = 5;
  optional string external_data_reference = 6;
  optional bool enabled = 7;
  repeated RuleSetScope scopes = 8;
  repeated SecRuleResponse rules = 9;
  repeated DenyRuleResponse deny_rules = 10;

  string          created_at = 11;
  User            created_by = 12;
  string          updated_at = 13;
  User            updated_by = 14;
  optional string deleted_at = 15;
  optional User   deleted_by = 16;
  optional string update_type = 17;

  // If the ruleset if part of an application, these fileds may be set
  optional string application_name = 18;
  optional string application_id = 19;
  optional bool is_all_environment_scope = 20;
}

message ListSecPoliciesRequest {
  // Note: This only returns application policy rulesets as that's the only policy we have
  // It should return all policies for the tenant when org-policy is done as part of https://jira.illum.io/browse/CLOUD-145
  // It also should have more filters
  // example filters: policy_type: (application|org), pversion: (draft|active|<ver>), max_result, app_name
  string policy_type = 1 [(validate.rules).string = {in: ["application", "org"]}];
}

message SecPolicyResponse {
  string                rule_set_id = 1;
  string                rule_set_href = 2;
  optional bool         enabled = 3; // we want to return false if user has set false
  repeated RuleSetScope scopes = 4;
  string                created_at = 5;
  User                  created_by = 6;
  string                updated_at = 7;
  User                  updated_by = 8;
  optional string       deleted_at = 9;
  optional User         deleted_by = 10;
  optional bool         provisioning_pending = 11;
  string                application_name = 12;
  string                application_id = 13;
  optional bool         is_all_environment_scope = 14; // return false if it's a deployment ruleset
  string                rule_set_name = 15;
  string                update_type = 16;
}

message ListSecPoliciesResponse {
  repeated SecPolicyResponse policies = 1;
}

message ListRuleSetsRequest {
  string policy_version = 1;
}

message ListRuleSetsResponse {
  repeated RuleSetResponse rule_sets = 1;
}

message DeleteRuleSetRequest {
  string policy_version = 1;
  repeated ResourceReference rule_sets = 2;
}

message DeleteRuleSetByIDRequest {
  string policy_version = 1;
  string rule_set_id = 2;
}

// CRUD for Rules
// Deletion of rules are done by calling PUT ruleset and passing the rules that remain
message CreateSecRuleRequest {
  string policy_version = 1;
  string rule_set_id = 2;

  optional bool enabled = 3;
  optional string description = 4;
  optional string external_data_set = 5;
  optional string external_data_reference = 6;

  // policy-engine ingress_services can be a list of oneOf href, new tcp/udp service, windows service
  // CS uses href and, (port, to_port, proto). To keep policy-engine and cs request in sync, the proto is not
  // doing a oneof { href, (port, to_port, proto) }. policyauthoring will check from code if href is passed when
  // (port, to_port, proto) is also supplied.
  repeated IngressSvcRuleResource ingress_services = 7;
  repeated RuleActorReference providers = 8;
  repeated RuleActorReference consumers = 9;
  optional bool unscoped_consumers = 10;
}

message CreateDenyRuleRequest {
  string policy_version = 1;
  string rule_set_id = 2;

  optional bool enabled = 3;
  optional string description = 4;
  repeated RuleActorReference providers = 5;
  repeated RuleActorReference consumers = 6;
  // policy-engine ingress_services can be a list of oneOf href, new tcp/udp service, windows service
  // CS uses href and, (port, to_port, proto). To keep policy-engine and cs request in sync, the proto is not
  // doing a oneof { href, (port, to_port, proto) }. policyauthoring will check from code if href is passed when
  // (port, to_port, proto) is also supplied.
  repeated IngressSvcRuleResource ingress_services = 7;
  bool override = 8;
  optional bool unscoped_consumers = 9;
}

message IngressSvcRuleResource {
    optional string href = 1;
    optional int32  port = 2;
    optional int32  to_port = 3;
    optional int32  proto = 4;
}

message IngressSvcRuleResponse {
  optional string service_id = 1;
  optional string href = 2;

  // because policy-engine can return href along with name,description and other fields
  // and also return (port, to_port, proto)
  optional int32  port = 3;
  optional int32  to_port = 4;
  optional int32  proto = 5;
  optional string name = 6;
  optional string description = 7;
  repeated ServicePort service_ports = 8;

  optional string created_at = 9;
  optional User            created_by = 10;
  optional string          updated_at = 11;
  optional User            updated_by = 12;
  optional string deleted_at = 13;
  optional User   deleted_by = 14;
  optional string update_type = 15;
}

message UpdateSecRuleRequest {
  string policy_version = 1;
  string rule_id = 2;
  string rule_set_id = 3;

  optional bool enabled = 4;
  optional string description = 5;
  optional string external_data_set = 6;
  optional string external_data_reference = 7;

  // policy-engine ingress_services can be a list of oneOf href, new tcp/udp service, windows service
  // CS uses href and, (port, to_port, proto). To keep policy-engine and cs request in sync, the proto is not
  // doing a oneof { href, (port, to_port, proto) }. policyauthoring will check from code if href is passed when
  // (port, to_port, proto) is also supplied.
  repeated IngressSvcRuleResource ingress_services = 8;
  repeated RuleActorReference providers = 9;
  repeated RuleActorReference consumers = 10;
  optional bool unscoped_consumers = 11;
}

message UpdateDenyRuleRequest {
  string policy_version = 1;
  string rule_id = 2;
  string rule_set_id = 3;

  optional bool enabled = 4;
  optional string description = 5;
  repeated RuleActorReference providers = 6;
  repeated RuleActorReference consumers = 7;

  // policy-engine ingress_services can be a list of oneOf href, new tcp/udp service, windows service
  // CS uses href and, (port, to_port, proto). To keep policy-engine and cs request in sync, the proto is not
  // doing a oneof { href, (port, to_port, proto) }. policyauthoring will check from code if href is passed when
  // (port, to_port, proto) is also supplied.
  repeated IngressSvcRuleResource ingress_services = 8;
  bool override = 9;
  optional bool unscoped_consumers = 10;
}

// SecRuleResponse and DenyRuleResponse don't use SecRule and DenyRule messages because by doing so policy engines response
// maps 1:1 with proto definition
message SecRuleResponse {
  // Following options are not used by CloudSecure:
  // sec_connect, stateless, machine_auth, log_flow
  // consuming_security_principals, egress_services
  //
  // Defaults in code:
  //    1. network_type: 'brn'
  //    2. resolve_labels_as: 'workloads' for provider/consumer

  // enabled value needs to be set by client
  // we do NOT want the default value of bool (false) as that'll disable the rule even if that was not the intent
  // app-code will check for this bool presence
  optional bool enabled = 1;
  optional string description = 2;
  optional string external_data_set = 3;
  optional string external_data_reference = 4;
  repeated IngressSvcRuleResponse ingress_services = 5;
  repeated RuleActor providers = 6;
  repeated RuleActor consumers = 7;
  optional bool unscoped_consumers = 8;

  string rule_id = 9;
  string href = 10;

  string          created_at = 11;
  User            created_by = 12;
  string          updated_at = 13;
  User            updated_by = 14;
  optional string deleted_at = 15;
  optional User   deleted_by = 16;
  optional string update_type = 17;
}

// SecRuleCreateResponse and DenyRuleCreateResponse are a bit different than their counterparts without the Create substring
// because the "ingress_services" can contain non-href values when a rule is created. Having two similar but redundant proto
// messages keeps the UI work to minimal
message SecRuleCreateResponse {
  // Following options are not used by CloudSecure:
  // sec_connect, stateless, machine_auth, log_flow
  // consuming_security_principals, egress_services
  //
  // Defaults in code:
  //    1. network_type: 'brn'
  //    2. resolve_labels_as: 'workloads' for provider/consumer

  // enabled value needs to be set by client
  // we do NOT want the default value of bool (false) as that'll disable the rule even if that was not the intent
  // app-code will check for this bool presence
  optional bool enabled = 1;
  optional string description = 2;
  optional string external_data_set = 3;
  optional string external_data_reference = 4;
  repeated IngressSvcRuleResource ingress_services = 5;
  repeated RuleActor providers = 6;
  repeated RuleActor consumers = 7;
  optional bool unscoped_consumers = 8;

  string rule_id = 9;
  string href = 10;

  string          created_at = 11;
  User            created_by = 12;
  string          updated_at = 13;
  User            updated_by = 14;
  optional string deleted_at = 15;
  optional User   deleted_by = 16;
  optional string update_type = 17;
}

message DenyRuleCreateResponse {
  // enabled value needs to be set by client
  // we do NOT want the default value of bool (false) as that'll disable the rule
  // even if that was not the intent
  optional bool enabled = 1;
  optional string description = 2;
  repeated RuleActor providers = 3;
  repeated RuleActor consumers = 4;
  repeated IngressSvcRuleResource ingress_services = 5;
  bool    override = 6;
  string  deny_rule_id = 7;
  string  href = 8;
  string          created_at = 9;
  User            created_by = 10;
  string          updated_at = 11;
  User            updated_by = 12;
  optional string deleted_at = 13;
  optional User   deleted_by = 14;
  optional string update_type = 15;
}
message DenyRuleResponse {
  // enabled value needs to be set by client
  // we do NOT want the default value of bool (false) as that'll disable the rule
  // even if that was not the intent
  optional bool enabled = 1;
  optional string description = 2;
  repeated RuleActor providers = 3;
  repeated RuleActor consumers = 4;
  repeated IngressSvcRuleResponse ingress_services = 5;
  bool    override = 6;
  string  deny_rule_id = 7;
  string  href = 8;
  string          created_at = 9;
  User            created_by = 10;
  string          updated_at = 11;
  User            updated_by = 12;
  optional string deleted_at = 13;
  optional User   deleted_by = 14;
  optional string update_type = 15;
}

// Allow rule
message SecRule {
  // Following options are not used by CloudSecure:
  // sec_connect, stateless, machine_auth, log_flow
  // consuming_security_principals, egress_services
  //
  // Defaults in code:
  //    1. network_type: 'brn'
  //    2. resolve_labels_as: 'workloads' for provider/consumer

  // enabled value needs to be set by client
  // we do NOT want the default value of bool (false) as that'll disable the rule even if that was not the intent
  // app-code will check for this bool presence
  optional bool enabled = 1;
  optional string description = 2;
  optional string external_data_set = 3;
  optional string external_data_reference = 4;

  // policy-engine ingress_services can be a list of oneOf href, new tcp/udp service, windows service
  // CS uses service href only
  repeated ResourceReference ingress_services = 5;
  repeated RuleActorReference providers = 6;
  repeated RuleActorReference consumers = 7;
  optional bool unscoped_consumers = 8;
}

// override-deny and bottom-deny rule
message DenyRule {
  // enabled value needs to be set by client
  // we do NOT want the default value of bool (false) as that'll disable the rule
  // even if that was not the intent
  optional bool enabled = 1;
  optional string description = 2;
  repeated RuleActorReference providers = 3;
  repeated RuleActorReference consumers = 4;
  // policy-engine ingress_services can be a list of oneOf href, new tcp/udp service, windows service
  // CS uses service href only
  repeated ResourceReference ingress_services = 5;
  bool override = 6;
  optional bool unscoped_consumers = 7;
}

// policy-engine's label_group, virtual_service, virtual_server are not used by CloudSecure
message RuleActorReference {
  optional string actors = 1;
  optional ResourceReference label = 2;
  optional ResourceReference workload = 3;
  optional ResourceReference ip_list = 4;
  optional string label_dimension = 5;
}

message RuleActor {
  message WorkloadResponse {
    string  workload_id = 1;
    string  href = 2;
    string  name = 3;
    bool    deleted = 4;
  }

  optional LabelResponse label = 1;
  optional IPListResponse ip_list = 2;
  optional WorkloadResponse workload = 3;
  optional string actors = 4; // for ams (all workloads)
  optional string label_dimension = 5;
}

// RuleSearch (rule-inheritance)

message Rule {
  string id = 1;
  string href = 2;
  optional bool enabled = 3;
  optional string description = 4;
  repeated IngressSvcRuleResponse ingress_services = 5;
  repeated RuleActor providers = 6;
  repeated RuleActor consumers = 7;
  optional bool unscoped_consumers = 8;
  bool     override = 9;

  string          created_at = 10;
  User            created_by = 11;
  string          updated_at = 12;
  User            updated_by = 13;
  optional string deleted_at = 14;
  optional User   deleted_by = 15;
  optional string update_type = 16;

  RuleSetResponse rule_set = 17;
}

message GroupedRules {
  repeated Rule sec_rules = 1;
  repeated Rule deny_rules = 2;
  repeated Rule override_deny_rules = 3;
}

message RuleSetOverlappingRuleSearchRequest {
  string policy_version = 1;
  ResourceReference rule_set = 2;
  optional bool exclude_rules_not_resolving_to_ruleset_scope_actors = 3;
}

message RuleSetOverlappingRuleSearchResponse {
  message OverlappingRuleSearchResponse {
    ResourceReference rule = 1;
    GroupedRules overlapping_rules = 2;
  }

  repeated OverlappingRuleSearchResponse overlapping_rules = 1;
  GroupedRules inherited_rules = 2;
}

service PolicyAuthoringService {
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
    option (google.api.http) = {
      get: "/api/v1/version"
    };
  }

  // policy
  rpc ListPolicyVersions(ListPolicyVersionsRequest) returns (ListPolicyVersionsResponse) {
    option (google.api.http) = {
      get: "/api/v1/sec_policy"
    };
  }

  rpc ProvisionPolicy(ProvisionPolicyRequest) returns (PolicyVersion) {
    option (google.api.http) = {
      post: "/api/v1/sec_policy"
      body: "*"
    };
  }

  rpc RevertPartialDraftPolicy(RevertPartialDraftPolicyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/api/v1/sec_policy/delete"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }

  rpc GetPendingPolicy(PendingPolicyRequest) returns (PendingPolicyResponse) {
    option (google.api.http) = {
      get: "/api/v1/sec_policy/pending"
    };
  }

  rpc RevertPendingPolicy(RevertPendingPolicyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/api/v1/sec_policy/pending"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }

  rpc RestorePolicyVersion(RestorePolicyVersionRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/api/v1/sec_policy/{policy_version}/restore"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }

  // ip lists
  rpc ListIPlists(ListIPListsRequest) returns (ListIPListsResponse) {
    option (google.api.http) = {
      get: "/api/v1/sec_policy/{policy_version}/ip_lists"
    };
  }
  rpc GetIPList(IPListRequest) returns (IPListResponse) {
    option (google.api.http) = {
      get: "/api/v1/sec_policy/{policy_version}/ip_lists/{ip_list_id}"
    };
  }
  rpc UpdateIPList(UpdateIPListRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/api/v1/sec_policy/{policy_version}/ip_lists/{ip_list_id}"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }
  rpc CreateIPList(CreateIPListRequest) returns (IPListResponse) {
    option (google.api.http) = {
      post: "/api/v1/sec_policy/{policy_version}/ip_lists"
      body: "*"
    };
  }
  rpc DeleteMultipleIPLists(IPListMultiDeleteRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/api/v1/sec_policy/draft/ip_lists/delete"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }
  rpc DeleteIPList(IPListRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/api/v1/sec_policy/{policy_version}/ip_lists/{ip_list_id}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }
  rpc IPListsAutocomplete(IPListsAutocompleteRequest) returns (IPListsAutocompleteResponse) {
    option (google.api.http) = {
      get: "/api/v1/sec_policy/{policy_version}/ip_lists/autocomplete"
    };
  }

  // services
  rpc ListServices(ListServicesRequest) returns (ListServicesResponse) {
    option (google.api.http) = {
      get: "/api/v1/sec_policy/{policy_version}/services"
    };
  }
  rpc GetService(ServiceRequest) returns (ServiceResponse) {
    option (google.api.http) = {
      get: "/api/v1/sec_policy/{policy_version}/services/{service_id}"
    };
  }
  rpc UpdateService(UpdateServiceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/api/v1/sec_policy/{policy_version}/services/{service_id}"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }
  rpc CreateService(CreateServiceRequest) returns (ServiceResponse) {
    option (google.api.http) = {
      post: "/api/v1/sec_policy/{policy_version}/services"
      body: "*"
    };
  }
  rpc DeleteMultipleServices(ServicesMultiDeleteRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/api/v1/sec_policy/draft/services/delete"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }
  rpc DeleteService(ServiceRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/api/v1/sec_policy/{policy_version}/services/{service_id}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }
  rpc ServicesAutocomplete(ServicesAutocompleteRequest) returns (ServicesAutocompleteResponse) {
    option (google.api.http) = {
      get: "/api/v1/sec_policy/{policy_version}/services/autocomplete"
    };
  }

  // labels
  rpc ListLabels(ListLabelsRequest) returns (ListLabelsResponse) {
    option (google.api.http) = {
      get: "/api/v1/labels"
    };
  }
  rpc GetLabel(LabelRequest) returns (LabelResponse) {
    option (google.api.http) = {
      get: "/api/v1/labels/{label_id}"
    };
  }
  rpc UpdateLabel(UpdateLabelRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/api/v1/labels/{label_id}"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }
  rpc CreateLabel(CreateLabelRequest) returns (LabelResponse) {
    option (google.api.http) = {
      post: "/api/v1/labels"
      body: "*"
    };
  }
  rpc DeleteMultipleLabels(LabelsMultiDeleteRequest) returns (LabelsMultiDeleteResponse) {
    option (google.api.http) = {
      put: "/api/v1/labels/delete"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }
  rpc DeleteLabel(LabelRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/api/v1/labels/{label_id}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }
  rpc LabelsAutocomplete(LabelsAutocompleteRequest) returns (LabelsAutocompleteResponse) {
    option (google.api.http) = {
      get: "/api/v1/labeling/labels/autocomplete"
    };
  }

  // RuleSet

  rpc GetRuleSet(GetRuleSetRequest) returns (RuleSetResponse) {
    option (google.api.http) = {
      get: "/api/v1/sec_policy/{policy_version}/rule_sets/{rule_set_id}"
    };
  }

  rpc ListRuleSets(ListRuleSetsRequest) returns (ListRuleSetsResponse) {
    option (google.api.http) = {
      get: "/api/v1/sec_policy/{policy_version}/rule_sets"
    };
  }

  rpc ListSecPolicies(ListSecPoliciesRequest) returns (ListSecPoliciesResponse) {
    option (google.api.http) = {
      get: "/api/v1/sec_policies"
    };
  }

  rpc CreateRuleSet(CreateRuleSetRequest) returns (RuleSetResponse) {
    option (google.api.http) = {
      post: "/api/v1/sec_policy/{policy_version}/rule_sets"
      body: "*"
    };
  }

  // UpdateRuleSet is also used by UI to delete rules
  rpc UpdateRuleSet(UpdateRuleSetRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/api/v1/sec_policy/{policy_version}/rule_sets/{rule_set_id}"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }

  // TODO: https://jira.illum.io/browse/CLOUD-2611
  // Rename since this doesn't delete a single ruleset but potentially, multiple at once.
  // Should also rename endpoint below this one to steal this name since it's a true
  // single ruleset delete.
  rpc DeleteRuleSet(DeleteRuleSetRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      // Note: The delete ruleset(s) used by UI calls a PUT endpoint in core. Using PUT here is not a bug
      put: "/api/v1/sec_policy/{policy_version}/rule_sets/delete"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }

  // NOTE: Adding this to allow single ruleset-deletes and have easier way of handling
  // errors. Other bulk ruleset delete endpoint stores errors in string response making
  // things hard to parse. 
  rpc DeleteRuleSetByID(DeleteRuleSetByIDRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/api/v1/sec_policy/{policy_version}/rule_sets/{rule_set_id}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }

  // Allow Rules
  rpc CreateSecRule(CreateSecRuleRequest) returns (SecRuleCreateResponse) {
    option (google.api.http) = {
      post: "/api/v1/sec_policy/{policy_version}/rule_sets/{rule_set_id}/sec_rules"
      body: "*"
    };
  }

  rpc UpdateSecRule(UpdateSecRuleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/api/v1/sec_policy/{policy_version}/rule_sets/{rule_set_id}/sec_rules/{rule_id}"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }

  // Deny Rules
  rpc CreateDenyRule(CreateDenyRuleRequest) returns (DenyRuleCreateResponse) {
    option (google.api.http) = {
      post: "/api/v1/sec_policy/{policy_version}/rule_sets/{rule_set_id}/deny_rules"
      body: "*"
    };
  }

  rpc UpdateDenyRule(UpdateDenyRuleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/api/v1/sec_policy/{policy_version}/rule_sets/{rule_set_id}/deny_rules/{rule_id}"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }

  rpc ListDraftDependencies(ListDraftDependenciesRequest) returns (ListDraftDependenciesResponse) {
    option (google.api.http) = {
      post: "/api/v1/sec_policy/draft/dependencies"
      body: "*"
    };
  }

  // label dimensions
  rpc ListLabelDimensions(ListLabelDimensionsRequest) returns (ListLabelDimensionsResponse) {
    option (google.api.http) = {
      get: "/api/v1/label_dimensions"
    };
  }
  rpc GetLabelDimension(LabelDimensionRequest) returns (LabelDimensionResponse) {
    option (google.api.http) = {
      get: "/api/v1/label_dimensions/{label_dimension_id}"
    };
  }
  rpc UpdateLabelDimension(UpdateLabelDimensionRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/api/v1/label_dimensions/{label_dimension_id}"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }
  rpc CreateLabelDimension(CreateLabelDimensionRequest) returns (LabelDimensionResponse) {
    option (google.api.http) = {
      post: "/api/v1/label_dimensions"
      body: "*"
    };
  }
  rpc DeleteLabelDimension(LabelDimensionRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/api/v1/label_dimensions/{label_dimension_id}"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }

  rpc RuleSetOverlappingRuleSearch(RuleSetOverlappingRuleSearchRequest) returns (RuleSetOverlappingRuleSearchResponse) {
    option (google.api.http) = {
      post: "/api/v1/sec_policy/{policy_version}/ruleset_overlapping_rule_search"
      body: "*"
    };
  }
}
