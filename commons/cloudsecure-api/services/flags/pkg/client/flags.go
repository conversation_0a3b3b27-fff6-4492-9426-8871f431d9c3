/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	flagspb "illum.io/cloud/api/generated/flags"
	"illum.io/cloud/api/services/flags/pkg/flags"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type FlagsClient struct {
	flagspb.FlagsServiceClient
	con *grpc.ClientConn
}

func (c *FlagsClient) Close() {
	_ = c.con.Close()
}

// NewFlagsClient creates a new gRPC client for service running on url endpoint
func NewFlagsClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*FlagsClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := flagspb.NewFlagsServiceClient(con)

	return &FlagsClient{
		con:                con,
		FlagsServiceClient: client,
	}, nil
}

// NewFlagsClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewFlagsClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*FlagsClient, error) {
	url, ok := config.LookupGRPCService(flags.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", flags.ServiceName)
	}
	return NewFlagsClient(ctx, url, opts...)
}
