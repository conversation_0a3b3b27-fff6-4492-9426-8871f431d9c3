// Copyright 2023 Illumio, Inc. All Rights Reserved.
syntax = "proto3";

package flags;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/api/generated/flags";

service FlagsService {
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
    option (google.api.http) = {
      get: "/api/v1/version"
    };
  }

  rpc GetBool(GetBoolRequest) returns (GetBoolResponse);

  rpc GetBoolForActiveContext(GetBoolForActiveContextRequest) returns (GetBoolForActiveContextResponse){
    option (google.api.http) = {
      post: "/api/v1/bool"
      body: "*"
    };
  }

  rpc GetBoolsForActiveContext(GetBoolsForActiveContextRequest) returns (GetBoolsForActiveContextResponse){
    option (google.api.http) = {
      post: "/api/v1/bools"
      body: "*"
    };
  }

  rpc GetString(GetStringRequest) returns (GetStringResponse);
  rpc GetStringForActiveContext(GetStringForActiveContextRequest) returns (GetStringForActiveContextResponse);
}

message FlagContext {
  string tenant_id = 1;
  string user_id = 2;
  string user_email = 3;
}

message GetBoolRequest {
  FlagContext context = 1;
  string flag = 2;
  optional bool default = 3;
}

message GetBoolResponse {
  bool value = 1;
}

message GetBoolForActiveContextRequest {
  string flag = 1;
  optional bool default = 2;
}

message GetBoolForActiveContextResponse {
  bool value = 1;
}

message GetStringRequest {
  FlagContext context = 1;
  string flag = 2;
  optional string default = 3;
}

message GetStringResponse {
  string value = 1;
}

message GetStringForActiveContextRequest {
  string flag = 1;
  optional string default = 2;
}

message GetStringForActiveContextResponse {
  string value = 1;
}

message GetBoolsForActiveContextRequest {
  repeated string flags = 1;
}

message BoolFlagValue {
  bool value = 1;
  bool set = 2;
}

message GetBoolsForActiveContextResponse {
  map<string, BoolFlagValue> values = 1;
}
