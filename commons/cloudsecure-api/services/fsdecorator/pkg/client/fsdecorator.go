/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	"illum.io/cloud/api/services/fsdecorator/pkg/fsdecorator"

	fsdecoratorpb "illum.io/cloud/api/generated/fsdecorator"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type FsdecoratorClient struct {
	fsdecoratorpb.FsdecoratorServiceClient
	con *grpc.ClientConn
}

func (c *FsdecoratorClient) Close() {
	_ = c.con.Close()
}

// NewFsdecoratorClient creates a new gRPC client for service running on url endpoint
func NewFsdecoratorClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*FsdecoratorClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := fsdecoratorpb.NewFsdecoratorServiceClient(con)

	return &FsdecoratorClient{
		con:                      con,
		FsdecoratorServiceClient: client,
	}, nil
}

// NewFsdecoratorClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewFsdecoratorClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*FsdecoratorClient, error) {
	url, ok := config.LookupGRPCService(fsdecorator.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", fsdecorator.ServiceName)
	}
	return NewFsdecoratorClient(ctx, url, opts...)
}
