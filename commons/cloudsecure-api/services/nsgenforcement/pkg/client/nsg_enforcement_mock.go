package client

import (
	"go.uber.org/mock/gomock"

	"illum.io/cloud/api/generated/nsgenforcement/mock"
)

// NewMockNSGEnforcementClient creates a new mock client
func NewMockNSGEnforcementClient(ctrl *gomock.Controller) (*NSGEnforcementClient, *mock.MockNSGEnforcementServiceClient) {
	m := mock.NewMockNSGEnforcementServiceClient(ctrl)
	return &NSGEnforcementClient{
		NSGEnforcementServiceClient: m,
	}, m
}
