package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	nsg_enforcementpb "illum.io/cloud/api/generated/nsgenforcement"
	"illum.io/cloud/api/services/nsgenforcement/pkg/nsgenforcement"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type NSGEnforcementClient struct {
	nsg_enforcementpb.NSGEnforcementServiceClient
	con *grpc.ClientConn
}

func (c *NSGEnforcementClient) Close() {
	_ = c.con.Close()
}

// NewNSGEnforcementClient creates a new gRPC client for service running on url endpoint
func NewNSGEnforcementClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*NSGEnforcementClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := nsg_enforcementpb.NewNSGEnforcementServiceClient(con)

	return &NSGEnforcementClient{
		con:                         con,
		NSGEnforcementServiceClient: client,
	}, nil
}

// NewNsgEnforcementClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewNsgEnforcementClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*NSGEnforcementClient, error) {
	url, ok := config.LookupGRPCService(nsgenforcement.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", nsgenforcement.ServiceName)
	}
	return NewNSGEnforcementClient(ctx, url, opts...)
}
