// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package generated;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "common/version/version.proto";
import "common/wipeout/wipeout.proto";
import "validate/validate.proto";

import "common/policy/policy.proto";

option go_package = "illum.io/cloud/api/generated/nsgenforcement";

message NSG {
  string nsg_id       = 1 [(validate.rules).string.uuid = true];
  string nsg_csp_id   = 2 [(validate.rules).string.min_len = 1];
  string nsg_name = 3;
}

message Subscription {
  string subscription_csp_id = 1 [(validate.rules).string.min_len = 1];
}

message ResourceGroup {
  string resource_group_name  = 1 [(validate.rules).string.min_len = 1];
}

message NSGPolicyRequest {
  NSG nsg = 1 [(validate.rules).message.required = true];
  Subscription subscription = 2;
  ResourceGroup resource_group = 3 [(validate.rules).message.required = true];
  string azure_tenant_id = 4;
  policy.Policy policy = 5;
  google.protobuf.Timestamp originated_at = 6;
  bool force = 7;
}

message NSGPolicyDigestRequest {
  NSG nsg = 1 [(validate.rules).message.required = true];
}

message NSGPolicyResponse {
  NSG nsg = 1 [(validate.rules).message.required = true];
  policy.PolicyDigest policy_digest = 2;
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;
}

// PolicyImpactRequest is the request sent to the GetPolicyImpact call for generating policy
// that's either already applied to the NSG or will be applied to NSG.
//
// `policy_version` has to be provided and can be one of draft, active. If the policy_version is `draft`, GetPolicyImpact
// expects a draft `policy.Policy policy`. If the policy_version is `active`, GetPolicyImpact will ignore the `policy.Policy policy` value
message NSGPolicyImpactRequest {
  NSG nsg = 1 [(validate.rules).message.required = true];
  string policy_version = 2 [(validate.rules).string = {in: ["draft", "active"]}];
  optional policy.Policy policy = 3;
  optional Subscription subscription = 4;
  optional string azure_tenant_id = 5;
  optional ResourceGroup resource_group = 6;
}

message NSGPolicyImpactResponse {
  NSG nsg = 1 [(validate.rules).message.required = true];

  // policy-impact will show (eventually) all rules for a azure NSG. Some/All of those rules may not be written by CloudSecure
  // and they won't have digest, policy or timestamp. We don't want to return defaults, so we'll use optional
  optional policy.PolicyDigest policy_digest = 2;
  optional policy.CSPPolicy  csp_policy = 3;
  optional google.protobuf.Timestamp created_at = 4;
  optional google.protobuf.Timestamp updated_at = 5;
}

// If running nsg-enforcement service in MQ mode, AzureNsgEnforcementTask is the message format that the producer
// consumer should use
message AzureNSGEnforcementTask {
  NSGPolicyRequest nsg_request = 1 [(validate.rules).message.required = true];
  string tenant_id = 2 [(validate.rules).string.uuid = true];
  policy.EnforcementMode mode = 3 [(validate.rules).enum.defined_only = true];
  google.protobuf.Timestamp originated_at = 4;
}

message SyncEnforcementPointRequest {
  NSG nsg = 1 [(validate.rules).message.required = true];
  Subscription subscription = 2;
  ResourceGroup resource_group = 3 [(validate.rules).message.required = true];
  string azure_tenant_id = 4;
  policy.PolicyDigest policy_digest = 5 [(validate.rules).message.required = true];
  policy.EnforcementMode mode = 6;
}

message SyncEnforcementPointResponse {}

message DeleteEnforcementPointsRequest {
  repeated string EnforcementPointIds = 1  [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      string: {
        uuid: true
      }
    }
  }];
}

message DeleteEnforcementPointsResponse {}

service NSGEnforcementService {
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse);
  rpc EnforceNSGPolicy(NSGPolicyRequest) returns (NSGPolicyResponse);
  rpc GetNSGPolicyDigest(NSGPolicyDigestRequest) returns (policy.PolicyDigest);
  // policy impact needs the following RPC calls
  rpc GetNSGPolicyImpact(NSGPolicyImpactRequest) returns (NSGPolicyImpactResponse);

  // updates enforcement_mode for enforcement point
  // creates enforcement point record in case of data plane change
  rpc SyncEnforcementPoint(SyncEnforcementPointRequest) returns (SyncEnforcementPointResponse) {}
  // deletes given enforcement points
  rpc DeleteEnforcementPoints(DeleteEnforcementPointsRequest) returns (DeleteEnforcementPointsResponse) {}
  rpc DeleteTenant(wipeout.DeleteTenantDataRequest) returns (wipeout.DeleteTenantDataResponse);
}
