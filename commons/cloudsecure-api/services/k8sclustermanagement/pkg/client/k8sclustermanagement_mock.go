package client

import (
	"go.uber.org/mock/gomock"

	"illum.io/cloud/api/generated/k8sclustermanagement/mock"
)

// NewMockK8SclustermanagementClient creates a new mock client
func NewMockK8SclustermanagementClient(ctrl *gomock.Controller) (*K8SclustermanagementClient, *mock.MockK8SclustermanagementServiceClient) {
	m := mock.NewMockK8SclustermanagementServiceClient(ctrl)
	return &K8SclustermanagementClient{
		K8SclustermanagementServiceClient: m,
	}, m
}
