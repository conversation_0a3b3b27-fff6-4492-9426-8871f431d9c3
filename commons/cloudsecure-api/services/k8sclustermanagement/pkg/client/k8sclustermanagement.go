/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	k8sclustermanagementpb "illum.io/cloud/api/generated/k8sclustermanagement"
	"illum.io/cloud/api/services/k8sclustermanagement/pkg/k8sclustermanagement"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type K8SclustermanagementClient struct {
	k8sclustermanagementpb.K8SclustermanagementServiceClient
	con *grpc.ClientConn
}

func (c *K8SclustermanagementClient) Close() {
	_ = c.con.Close()
}

// NewK8SclustermanagementClient creates a new gRPC client for service running on url endpoint
func NewK8SclustermanagementClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*K8SclustermanagementClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := k8sclustermanagementpb.NewK8SclustermanagementServiceClient(con)

	return &K8SclustermanagementClient{
		con:                               con,
		K8SclustermanagementServiceClient: client,
	}, nil
}

// NewK8SclustermanagementClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewK8SclustermanagementClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*K8SclustermanagementClient, error) {
	url, ok := config.LookupGRPCService(k8sclustermanagement.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", k8sclustermanagement.ServiceName)
	}
	return NewK8SclustermanagementClient(ctx, url, opts...)
}
