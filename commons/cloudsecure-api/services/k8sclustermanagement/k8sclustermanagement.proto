// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package k8sclustermanagement;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common/version/version.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/api/generated/k8sclustermanagement";

// Represents a Cluster Onboarding Credential, which will be used by the Operator to onboard and get cluster client credentials.
// The client_secret is optional because it is only returned in the CreateClusterOnboardingCredential RPC.
// It will be stored hashed in the onboarding_credentials table, but no other RPCs will return it again in cleartext.
message ClusterOnboardingCredential {
  string client_id = 1;
  string tenant_id = 2;
  optional string client_secret = 3;
  string name = 4;
  optional string description = 5;
  google.protobuf.Timestamp created_at = 6;
  string illumio_region = 7;
  google.protobuf.Timestamp last_used = 8;
}

// Request message for creating a new Cluster Onboarding Credential.
message CreateClusterOnboardingCredentialRequest {
  string name = 1 [(validate.rules).string.min_len = 1];
  optional string description = 2;
  string illumio_region = 3 [(validate.rules).string.min_len = 1];
}

// Response message containing the newly created Cluster Onboarding Credential.
message CreateClusterOnboardingCredentialResponse {
  ClusterOnboardingCredential credential = 1;
}

// Request for retrieving a list of Cluster Onboarding Credentials for a given tenant with pagination support.
message ListClusterOnboardingCredentialsRequest {
  uint32 max_results = 1;
  optional string page_token = 2;
  optional SortBy sortBy = 3;
  optional FilterBy filterBy = 4;
}

message SortBy {
  enum Field {
    CREATED_AT = 0;
    NAME = 1;
    ILLUMIO_REGION = 2;
    ENABLED = 3;
    LAST_CONNECTED_AT = 4;
    TYPE = 5;
    CLOUD_REGION = 6;
    CLOUD = 7;
  }
  Field field = 1;
  SortOrder sort_order = 2;
}

enum SortOrder {
  DESC = 0;
  ASC = 1;
}

message FilterBy {
  enum Field {
    ILLUMIO_REGION = 0;
  }
  Field field = 1;
  string value = 2; // The value to filter by
}

// Response to a request for listing Cluster Onboarding Credentials belonging to a given tenant, includes pagination tokens.
message ListClusterOnboardingCredentialsResponse {
  repeated ClusterOnboardingCredential credentials = 1;
  uint32 total_size = 2; // Number of records across all pages
  optional string next_page_token = 3;
  optional string prev_page_token = 4;
  uint32 page = 5;
}

// Request for retrieving a specific Cluster Onboarding Credential by ID, where ID is the onboarding client id.
message GetClusterOnboardingCredentialRequest {
  string id = 1 [(validate.rules).string.uuid = true];
}

// Response containing the requested Cluster Onboarding Credential.
message GetClusterOnboardingCredentialResponse {
  ClusterOnboardingCredential credential = 1;
}

// Request for updating the metadata (name and description) of a specific Cluster Onboarding Credential, where ID is the onboarding client id.
message UpdateClusterOnboardingCredentialRequest {
  string id = 1 [(validate.rules).string.uuid = true];
  string name = 2;
  string description = 3;
}

// Response containing the updated Cluster Onboarding Credential (name and/or description can be updated).
message UpdateClusterOnboardingCredentialResponse {
  ClusterOnboardingCredential credential = 1;
}

// Request for deleting Cluster Onboarding Credentials by their IDs.
message DeleteClusterOnboardingCredentialsRequest {
  repeated string ids = 1 [(validate.rules).repeated = {items: {string: {uuid: true}}, min_items: 1}];
}

// Response containing the IDs of Cluster Onboarding Credentials that failed to be deleted in the bulk delete operation.
message DeleteClusterOnboardingCredentialsResponse {
  repeated string failed_ids = 1;
}

message IllumioRegion {
  string id = 1;
  string display_name = 2;
  uint32 total_clusters = 3;
  uint32 managed_clusters = 4;
}

message ListIllumioRegionsResponse {
  repeated IllumioRegion illumio_regions = 1;
}

// Request for registering a new Operator with the onboarding client id and client secret pair.
message OnboardClusterRequest {
  string onboarding_client_id = 1 [(validate.rules).string.min_len = 1];
  string onboarding_client_secret = 2 [(validate.rules).string.min_len = 1];
}

// Response containing new cluster client credentials after successful Operator registration.
message OnboardClusterResponse {
  string cluster_client_id = 1;
  string cluster_client_secret = 2;
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// https://datatracker.ietf.org/doc/html/rfc6749#section-4.4.2
// The below access token request and response are used to authenticate the Operator and get the access token
// using OAuth 2.0 client credentials grant type.
// This follows the RFC 6749 Access Token Request format, and the message structure used in authzc to authenticate service accounts:
// https://stash.ilabs.io/projects/CLOUD/repos/storm/browse/api/authzc/authzc.proto#139-162

// OAuth 2.0 client credentials Request
message AuthenticateClusterRequest {
  string client_id = 1 [(validate.rules).string.min_len = 1]; // The cluster_client_id returned by the OnboardCluster RPC
  string client_secret = 2 [(validate.rules).string.min_len = 1]; // The cluster_client_secret returned by the OnboardCluster RPC
  string grant_type = 3 [(validate.rules).string.min_len = 1]; // Value MUST be set to "client_credentials"
}

// OAuth 2.0 client credentials Response
message AuthenticateClusterResponse {
  string access_token = 1; // JWT access token with aud claim set to the dataplane fqdn.
  string token_type = 2;
  int32 expires_in = 3;
}

// OAuth 2.0 client credentials Error Response
message AuthenticateClusterErrorResponse {
  string error = 1;
}
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// https://datatracker.ietf.org/doc/html/rfc7662
// RFC 7662 outlines an HTTP/1.1 and JSON-based protocol for OAuth 2.0 token introspection; however, since this is an
// internal-only endpoint, we will instead implement a similar token introspection API using gRPC.

// Token introspection request to validate the given access token
// and verify that the token's aud claim contains the given server_aud.
message IntrospectTokenRequest {
  // token is the JWT to validate.
  string token = 1 [(validate.rules).string.min_len = 1];
  // server_aud is the audience (FQDN) of the resource server (k8sclustersync) that received the token in an RPC from a client.
  // The token must have an aud claim that contains that audience.
  string server_aud = 2 [(validate.rules).string.min_len = 1];
}

// Token introspection response indicating the token's validity (active, exp) and audience claim.
message IntrospectTokenResponse {
  bool active = 1; // True iff the token was issued by the Cluster service and it's not expired.
  int64 exp = 2; // Expiration time of the token as a Unix timestamp.
  string aud = 3; // Claim that identifies the recipient that the JWT access token is intended for (set to the appropriate dataplane fqdn).
  string client_id = 4; // Required by the K8sclustersync API to identify what cluster made an RPC using the access token; stored in the sub claim of the JWT.
}
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

message Cluster {
  string id = 1;
  string illumio_region = 2;
  bool enabled = 3;

  // The below fields are optional and will only be set after the cluster connects to k8sclustersync.
  optional string uid = 4;
  optional string kubernetes_version = 5;
  optional string operator_version = 6;
  optional google.protobuf.Timestamp last_connected_at = 7;

  // The below fields are optional and will only be set after the cluster connects to k8sclustersync and if the cluster is a managed cluster. Unmanaged clusters are not guaranteed to have these fields.
  optional string name = 8;
  optional string type = 9; // The CloudSecure type of the cluster, e.g. types.EKSCluster ("AWS::EKS::Cluster") or types.ManagedClusters ("Microsoft.ContainerService/managedClusters")
  optional string cloud_region = 10;
  optional string cloud = 11;
  optional google.protobuf.Timestamp resource_stream_last_open = 12;
  optional google.protobuf.Timestamp log_stream_last_open = 13;
  optional google.protobuf.Timestamp flow_stream_last_open = 14;
}

// Request for retrieving a specific Cluster by ID.
message GetClusterRequest {
  string id = 1 [(validate.rules).string.uuid = true];
}

// Response containing the requested Cluster.
message GetClusterResponse {
  Cluster cluster = 1;
}

// Request message to retrieve the clusters for a particular tenant with pagination.
message GetClustersRequest {
  uint32 max_results = 1;
  optional string page_token = 2;
  optional SortBy sortBy = 3;
}

// Response message containing the clusters belonging to a particular tenant.
message GetClustersResponse {
  repeated Cluster clusters = 1;
  uint32 total_size = 2; // Number of records across all pages
  optional string next_page_token = 3;
  optional string prev_page_token = 4;
  uint32 page = 5;
  bool inventory_data = 6;
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Cluster State Transitions
// The possible cluster state transitions are: (enabled -> disabled, enabled -> offboarded, disabled -> enabled, disabled -> offboarded).
// Bulk cluster state transition RPCs are implemented to enable the customer to select multiple clusters from the "Kubernetes Clusters" UI table and perform Offboard, Disable, or Enable operations.

// Request to disable multiple enabled clusters; disabling a cluster means closing the bidirectional gRPC stream between the Operator within the cluster and the K8sclustersync service.
message DisableClustersRequest {
	repeated string cluster_ids = 1 [(validate.rules).repeated = {items: {string: {min_len: 1}}, min_items: 1}];
}

// Response containing the cluster ids of the clusters that failed to be disabled.
message DisableClustersResponse {
  repeated string failed_cluster_ids = 1;
}

// Request to enable multiple disabled clusters; enabling a cluster means opening the bidirectional gRPC stream between the Operator within the cluster and the K8sclustersync service.
message EnableClustersRequest {
	repeated string cluster_ids = 1 [(validate.rules).repeated = {items: {string: {min_len: 1}}, min_items: 1}];
}

// Response containing the cluster ids of the clusters that failed to be enabled.
message EnableClustersResponse {
  repeated string failed_cluster_ids = 1;
}

// Request to offboard multiple enabled or disabled clusters; offboarding means closing the bidirectional gRPC stream between the Operator and K8sclustersync service
// and removing the cluster resource from the clusters table (and, eventually, from inventory). Offboarded is the terminal state of a cluster.
message OffboardClustersRequest {
	repeated string cluster_ids = 1 [(validate.rules).repeated = {items: {string: {min_len: 1}}, min_items: 1}];
}

// Response containing the cluster ids of the clusters that failed to be offboarded.
message OffboardClustersResponse {
  repeated string failed_cluster_ids = 1;
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Messages used in RPCs invoked by the k8sclustersync service via the internal ingress.

// Request used by the k8sclustersync service to get its enabled clusters.
message GetEnabledClustersRequest {
  repeated string cluster_ids = 1 [(validate.rules).repeated = {items: {string: {min_len: 1}}, min_items: 1}];
}

// Response message containing the enabled clusters.
message GetEnabledClustersResponse {
  repeated string cluster_ids = 1;
}

// Request message containing the cluster metadata extracted from within the cluster and node annotations, used to update the cluster entry in the clusters table.
message UpdateClusterMetadataRequest {
  string cluster_id = 1 [(validate.rules).string.uuid = true];
  string cluster_uid = 2 [(validate.rules).string.uuid = true];
  string kubernetes_version = 3 [(validate.rules).string.min_len = 1];
  string operator_version = 4 [(validate.rules).string.min_len = 1];
  google.protobuf.Timestamp last_connected_at = 5  [(validate.rules).timestamp.required = true];

  // Unmanaged clusters are not guaranteed to have these fields set.
  string cloud_region = 6;
  string cloud = 7;
  string cluster_type = 8;
  string cluster_name = 9;
}

// Represents the different connection types reported for a cluster.
enum ClusterConnectionType {
  CLUSTER_CONNECTION_TYPE_UNKNOWN = 0;
  CLUSTER_CONNECTION_TYPE_RESOURCE_STREAM = 1;  // Used to report the connection status of the resource stream for a cluster (i.e. the last time it was seen open).
  CLUSTER_CONNECTION_TYPE_LOG_STREAM = 2; // Used to report the connection status of the logs stream for a cluster (i.e. the last time it was seen open).
  CLUSTER_CONNECTION_TYPE_NETWORK_FLOW_STREAM = 3; // Used to report the connection status of the network flows stream for a cluster (i.e. the last time it was seen open).
}

// Represents an open stream connection of a particular type for a specific cluster.
message ClusterOpenConnection {
  string cluster_id = 1 [(validate.rules).string.min_len = 1];
  ClusterConnectionType type = 2;
}

// Request message for reporting an open connection for a cluster.
message ReportClusterOpenConnectionRequest {
  ClusterOpenConnection cluster_open_connection = 1;
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

service K8SclustermanagementService {
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
      option (google.api.http) = {
      get: "/api/v1/version"
    };
  }

  // Creates and returns a new Cluster Onboarding Credential, storing it in the onboarding_credentials table of the db.
  rpc CreateClusterOnboardingCredential(CreateClusterOnboardingCredentialRequest) returns (CreateClusterOnboardingCredentialResponse) {
    option (google.api.http) = {
      post: "/api/v1/k8s_cluster_onboarding_credentials"
      body: "*"
    };
  }

  // Retrieves a specific Cluster Onboarding Credential by its ID.
  rpc GetClusterOnboardingCredential(GetClusterOnboardingCredentialRequest) returns (GetClusterOnboardingCredentialResponse) {
    option (google.api.http) = {
      get: "/api/v1/k8s_cluster_onboarding_credentials/{id}"
    };
  }

  // Lists all Cluster Onboarding Credentials for a given tenant, supporting pagination through page tokens.
  rpc ListClusterOnboardingCredentials(ListClusterOnboardingCredentialsRequest) returns (ListClusterOnboardingCredentialsResponse) {
    option (google.api.http) = {
      get: "/api/v1/k8s_cluster_onboarding_credentials"
    };
  }

  // Updates the name and/or description of an existing Cluster Onboarding Credential and returns the updated credential.
  rpc UpdateClusterOnboardingCredential(UpdateClusterOnboardingCredentialRequest) returns (UpdateClusterOnboardingCredentialResponse) {
    option (google.api.http) = {
      put: "/api/v1/k8s_cluster_onboarding_credentials/{id}"
      body: "*"
    };
  }

  // Deletes specific Cluster Onboarding Credentials by their IDs, returning the IDs of the Cluster Onboarding Credentials that failed to be deleted.
  rpc DeleteClusterOnboardingCredentials(DeleteClusterOnboardingCredentialsRequest) returns (DeleteClusterOnboardingCredentialsResponse) {
    option (google.api.http) = {
      post: "/api/v1/k8s_cluster_onboarding_credentials/delete"
      body: "*"
    };
  }

  // Lists the available illumio regions for the UI, returning a list of (region id, region display name, corresponding dataplane fqdn).
  rpc ListIllumioRegions(google.protobuf.Empty) returns (ListIllumioRegionsResponse) {
    option (google.api.http) = {
      get: "/api/v1/illumio_regions"
    };
  }

  // Onboards a new cluster by creating cluster client credentials.
  rpc OnboardCluster(OnboardClusterRequest) returns (OnboardClusterResponse) {
    option (google.api.http) = {
      post: "/api/v1/k8s_cluster/onboard"
      body: "*"
    };
  }

  // Generates a new JWT access token for an Operator using OAuth 2.0 Client Credentials flow for authentication.
  rpc AuthenticateCluster(AuthenticateClusterRequest) returns (AuthenticateClusterResponse) {
    option (google.api.http) = {
      post: "/api/v1/k8s_cluster/authenticate"
      body: "*"
    };
  }

  // Performs OAuth 2.0 token introspection on a given access token to determine its validity and returns token details (client_id, active, exp, and aud claim).
  rpc IntrospectToken(IntrospectTokenRequest) returns (IntrospectTokenResponse) {
    option (google.api.http) = {
      post: "/api/v1/k8s_cluster/token_introspection"
      body: "*"
    };
  }

  // Retrieves a specific Cluster by its ID.
  rpc GetCluster(GetClusterRequest) returns (GetClusterResponse) {
    option (google.api.http) = {
      get: "/api/v1/k8s_cluster/{id}"
    };
  }

  // Retrieves the cluster metadata of all clusters belonging to the tenant.
  rpc GetClusters(GetClustersRequest) returns (GetClustersResponse) {
    option (google.api.http) = {
      get: "/api/v1/k8s_cluster"
    };
  }

  // Disables multiple clusters by updating their states to disabled, resulting in the k8sclustersync service preventing the bidirectional gRPC stream between itself and the clusters' Operators.
  // This RPC is idempotent, so invoking it on a cluster that is already in disabled state results in no change.
  rpc DisableClusters(DisableClustersRequest) returns (DisableClustersResponse) {
    option (google.api.http) = {
      put: "/api/v1/k8s_cluster/disable"
      body: "*"
    };
  }

  // Enables multiple clusters by updating their states to enabled, resulting in the k8sclustersync service allowing the bidirectional gRPC stream between itself and the clusters' Operators.
  // This RPC is idempotent, so invoking it on a cluster that is already in enabled state results in no change.
  rpc EnableClusters(EnableClustersRequest) returns (EnableClustersResponse) {
    option (google.api.http) = {
      put: "/api/v1/k8s_cluster/enable"
      body: "*"
    };
  }

  // Offboards multiple clusters by deleting them from the clusters table, resulting in the k8sclustersync service preventing the bidirectional gRPC stream between itself and the clusters' Operators.
  rpc OffboardClusters(OffboardClustersRequest) returns (OffboardClustersResponse) {
    option (google.api.http) = {
      put: "/api/v1/k8s_cluster/offboard"
      body: "*"
    };
  }

  // Invoked by the k8sclustersync service to get its enabled clusters.
  rpc GetEnabledClusters(GetEnabledClustersRequest) returns (GetEnabledClustersResponse) {
    option (google.api.http) = {
      post: "/api/v1/k8s_cluster/states"
      body: "*"
    };
  }

  // Invoked by the k8sclustersync service to update an entry in the clusters table with metadata extracted from within the cluster and node annotations.
  rpc UpdateClusterMetadata(UpdateClusterMetadataRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      put: "/api/v1/k8s_cluster/update"
      body: "*"
    };
  }

  // Invoked by the k8sclustersync service to report an open connection status of a cluster with which it maintains a gRPC stream connection.
  rpc ReportClusterOpenConnection(stream ReportClusterOpenConnectionRequest) returns (google.protobuf.Empty);
}