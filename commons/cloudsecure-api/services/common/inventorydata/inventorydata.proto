// Copyright 2023 Illumio, Inc. All Rights Reserved.
syntax = "proto3";

package inventorydata;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "google/protobuf/struct.proto";
import "common/label/label.proto";

// Golang
option go_package = "illum.io/cloud/api/generated/common/inventorydata";
// Java
option java_multiple_files = true;
option java_package = "com.illumio.cloudsecure.protos.common.inventorydata";
option java_outer_classname = "InventoryProtos";

message stringList {
  repeated string values = 1;
}

message SortBy {
  enum Field {
    UNSPECIFIED = 0;
    TYPE = 1;
    REGION = 2;
    ACCOUNT = 3;
    CLOUD = 4;
    STATE = 5;
    CATEGORY = 6;
    SUBCATEGORY = 7;
    CREATED_AT = 8;
  }
  Field field = 1;
  bool asc = 2;
}

enum CompState {
  COMP_STATE_UNSPECIFIED = 0;
  COMP_STATE_CREATED = 1;
  COMP_STATE_UPDATED = 2;
  COMP_STATE_DELETED = 3;
}

message Account {
  string id = 1;
  string name = 2;
}

message Relation {
  string id = 1;
  string csp_id = 2;
  string account_id = 3;
  string tenant_id = 4;
  string cloud = 5;
  string name = 6;
  string object_type = 7;
  string category = 8;
  string region = 9;
  string state = 10;
  map<string, string> tags = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Struct properties = 13;
  string subcategory = 14;
  string account_name = 15;
  Account owner_account = 16;
}

// To see some API use cases, refer to: https://confluence.illum.io/display/Lumos/Inventory+Use+Cases
message Resource {
  string id = 1;
  string csp_id = 2;
  string account_id = 3;
  string tenant_id = 4;
  string cloud = 5;
  string name = 6;
  string object_type = 7;
  string category = 8;
  string region = 9;
  string state = 10;
  map<string, string> tags = 11;
  google.protobuf.Timestamp created_at = 12;
  oneof resource {
    AzureResource azure = 13;
    AWSResource aws = 14;
  }
  string detail_type = 15;
  oneof details {
    EC2ENI ec2_eni = 16;
    EC2Instance ec2_instance = 17;
    EC2SecurityGroup ec2_security_group = 18;
    EC2Subnet ec2_subnet = 19;
    EC2VPC ec2_vpc = 20;
    EKSCluster eks_cluster = 21;
    EKSNodeGroup eks_nodegroup = 22;
    RDSCluster rds_cluster = 23;
    EC2NatGateway ec2_nat_gateway = 24;
    EC2EIP ec2_eip = 25;
    EC2InternetGateway ec2_internet_gateway = 26;
    EC2RouteTable ec2_route_table = 27;
    EC2NetworkAcl ec2_network_acl = 28;
    EC2FlowLog ec2_flow_log = 29;
    RDSInstance rds_instance = 46;

    ComputeVirtualMachine virtual_machine = 30;
    NetworkSecurityGroup network_security_group = 31;
    NetworkInterface network_interface = 32;
    NetworkIPConfigs ip_config = 33;
    NetworkSubnets subnet = 34;
    StorageAccounts storage_account = 35;
    VirtualNetwork virtual_network = 37;
    ResourceGroups resource_groups = 38;
    NSGFlowLogs nsg_flow_logs = 39;
    NetworkWatchers network_watchers = 40;
    NetworkPrivateEndpoint private_endpoint = 41;
    SqlServer sql_server = 42;
  }

  // is_primary_resource indicates whether the resource was part of the target query
  // a primary resource can also be a referenced resource if it happens to be a reference for another primary resource
  bool is_primary_resource = 43;

  repeated Relation relations = 44;

  // token is used in pagination
  string token = 45;
  google.protobuf.Struct properties = 47;

  optional Ips ips = 48;

  optional CompState comp_state = 49;

  string subcategory = 50;

  string account_name = 51;

  Account owner_account = 52;

  repeated label.Label labels = 53;

  //CSP API response for the resource as JSON data
  google.protobuf.Struct json_view = 54;

  optional string illumio_region = 55;
}

message AWSResource {
  oneof resource {
    EC2ENI ec2_eni = 1;
    EC2Instance ec2_instance = 2;
    EC2SecurityGroup ec2_security_group = 3;
    EC2Subnet ec2_subnet = 5;
    EC2VPC ec2_vpc = 6;
    EKSCluster eks_cluster = 7;
    EKSNodeGroup eks_nodegroup = 8;
    RDSCluster rds_cluster = 9;
    EC2NatGateway ec2_nat_gateway = 10;
    EC2EIP ec2_eip = 11;
    EC2InternetGateway ec2_internet_gateway = 12;
    EC2RouteTable ec2_route_table = 13;
    EC2NetworkAcl ec2_network_acl = 14;
    EC2FlowLog ec2_flow_log = 15;
    RDSInstance rds_instance = 16;
  }
}

message EC2NatGateway{
}

message RDSCluster {
  string vpc_id = 1;
  repeated string subnet_ids = 2;
  repeated string security_group_ids = 3;
  repeated string eni_ids = 4;
}

message RDSInstance {
  string vpc_id = 1;
  repeated string subnet_ids = 2;
  repeated string security_group_ids = 3;
  repeated string eni_ids = 4;
}

message EKSCluster {
}

message EKSNodeGroup {
}

message EC2VPC {
}

message EC2ENI {
  string subnet_id = 1;
  string vpc_id = 2;
  repeated string security_group_ids = 3;
  string attached_to_id = 4;
  string attached_to_type = 5;
  repeated string private_ipv4_addresses = 6;
  repeated string ipv6_addresses = 7;
  string public_ipv4_address = 8;
}

message EC2Instance {
  string vpc_id = 1;
  string subnet_id = 2; // TODO: remove after beta, aws allows enis from different subnets to be connected to same ec2 instance
  repeated string subnet_ids = 3;
  repeated string security_group_ids = 4;
  repeated string eni_ids = 5;
  repeated string eip_ids = 6;
}

message EC2EIP {
  string private_ip = 1;
  string public_ip = 2;
}

message EC2InternetGateway {
}

message EC2RouteTable {
}

message EC2NetworkAcl {
}

message EC2FlowLog {
}

// AWS security groups can either be attached to EC2 instances or ENIs
// if attached to a EC2, it's inherited by the EC2's ENIs
message EC2SecurityGroup {
  repeated string ec2_ids = 1;
  repeated string eni_ids = 2;
  repeated string rds_cluster_ids = 3;
}

message EC2Subnet {
  string vpc_id = 1;
}

message AzureResource {
  oneof resource {
    ComputeVirtualMachine virtual_machine = 1;
    NetworkSecurityGroup network_security_group = 2;
    NetworkInterface network_interface = 3;
    NetworkIPConfigs ip_config = 4;
    NetworkSubnets subnet = 5;
    StorageAccounts storage_account = 6;
    VirtualNetwork virtual_network = 7;
    ResourceGroups resource_groups = 8;
    NSGFlowLogs nsg_flow_logs = 9;
    NetworkWatchers network_watchers = 10;
    NetworkPrivateEndpoint private_endpoint = 11;
    SqlServer sql_server = 12;
  }
}

message VirtualNetwork {

}

message ComputeVirtualMachine {
  string vnet_id = 1;
  string subnet_id = 2; // TODO: remove after beta , azure allows nics from different subnets to be connected to same vm
  repeated string subnet_ids = 3;
  repeated string nsg_ids = 4; // nsgs attached to NICs of VM, doesn't include subnet nsgs
  repeated string nic_ids = 5;
  repeated string ip_config_ids = 6;
}

message SqlServer {
  string vnet_id = 1;
  repeated string subnet_ids = 2;
  repeated string nsg_ids = 3; // nsgs attached to NICs of sql server, doesn't include subnet nsgs
  repeated string nic_ids = 4;
  repeated string private_endpoint_ids = 5;
}

message NetworkSecurityGroup {
  repeated string subnet_ids = 1;
  repeated string nic_ids = 2;
  repeated string vm_ids = 3;
  repeated string sql_server_ids = 4;
}

message NetworkInterface {
  string subnet_id = 1;
  string vnet_id = 2;
  string attached_to_id = 3;
  string attached_to_type = 4;
  repeated string nsg_ids = 5;
  repeated string private_ipv4_addresses = 6;
  repeated string ipv6_addresses = 7;
  string public_ipv4_address = 8;
}

message NetworkPrivateEndpoint {
  string nic_id = 1;
  string subnet_id = 2;
  string vnet_id = 3;
  string attached_to_id = 4;
  string attached_to_type = 5;
}

message NetworkIPConfigs {
  string private_ip = 1;
}

message NetworkSubnets {
  string vnet_id = 1;
}

message StorageAccounts {
}

message ResourceGroups {
}

message NSGFlowLogs {
}

message NetworkWatchers {
}


message FlowLogConfig {
  string id = 1;
  string tenant_id = 2;
  string account_id = 3;
  string csp_id = 4;
  optional bool access = 5;
  string cloud = 6;
  google.protobuf.Timestamp created_at = 7;
  string destination = 8;
  string log_format = 9;
  string object_type = 10;
  string region = 11;
  string source = 12;
  string status = 13;
  string traffic_type = 14;
  map<string,string> tags = 15;
  optional string destination_type = 16;
  string dst_account_id = 17;
  string dst_region = 18;
}

message FlowLogPair {
  string source = 1;
  string destination = 2;
  optional bool access = 3;
//  string dst_account_csp_id = 4;
  string src_account_csp_id = 5;
  reserved 4;
}

message FlowLogPairs {
  repeated FlowLogPair pairs = 1;
}

message FlowLogSummary {
  string account_csp_id = 1;
  string account_name = 2;
  string access = 3;
  string cloud = 4;
  FlowLogPairs configs = 5;
}

// Tag message
message Tag {
  string key = 1; // The key of the tag
  string value = 2; // The value of the tag
}

// Enum for the metadata type
enum MetadataType {
  REGIONS = 0;
  SUBNETS = 1;
  VPCS = 2;
  OBJECTTYPE = 3;
  STATE = 4;
  CSPID = 5;
  NAME = 6;
  CLOUDS = 7;
  SUBCATEGORIES = 8;
  ACCOUNTS = 9;
  RESOURCEGROUPS = 10;
  TAG = 11;
}

message PropertyFilter {
  string prop_name = 1;
  repeated string prop_values = 2;
  repeated bool bool_values = 3;
}

message DecoratedRegion {
  string region = 1;
  string cloud = 2;
  string account_id = 3;
}

message Workload {
  string id = 1 [(validate.rules).string.uuid = true];
  string object_type = 2;
  repeated WorkloadNic network_interfaces = 3;
  repeated WorkloadSubnet subnets = 4;
  WorkloadVnet vnet = 5[deprecated=true];
  // enforcement_points represents the enforcement_points directly attached to the workload (e.g. sg directly attached to ec2 instance)
  // enforcement_points attached to the nic/subnet will be shown in those objects
  repeated EnforcementPoint enforcement_points = 6[deprecated=true];
  string csp_id = 7;
  string name = 8;
  string cloud = 9;
  string region = 10;
  bool online = 11;
}

message WorkloadSubnet {
  string id = 1 [(validate.rules).string.uuid = true];
  string object_type = 2;
  WorkloadVnet vnet = 3;
  repeated EnforcementPoint enforcement_points = 4;
}

message WorkloadVnet {
  string id = 1 [(validate.rules).string.uuid = true];
  string object_type = 2;
  // Contains VPC/Vnet Cloud Secure uuid
  repeated string peering_vpc_ids = 3;
  string csp_id = 4;
}

message Ips {
  repeated string private_ipv4_addresses = 1 [deprecated=true];
  repeated string ipv6_addresses = 2 [deprecated=true];
  string public_ipv4_address = 3 [deprecated=true];

  repeated string ipv4_private = 4;
  repeated string ipv4_public = 5;
  repeated string ipv6_public = 6;
  repeated string ipv6_private = 7;
}

message WorkloadNic {
  string id = 1 [(validate.rules).string.uuid = true];
  string object_type = 2;
  WorkloadSubnet subnet = 3;
  // a nic always has a subnet, so remove vnet from nic
  WorkloadVnet vnet = 4[deprecated=true];
  // can be empty, can intersect with the subnet eps set
  repeated EnforcementPoint enforcement_points = 5;
  Ips ips = 6;
}

message EnforcementPoint {
  string id = 1 [(validate.rules).string.uuid = true];
  string object_type = 2; // (e.g. AWS::EC2::SecurityGroup, Microsoft.Network/networkSecurityGroups)
  string csp_id = 3;
  string name = 4;
  string account_id = 5;
}


enum FilterType {
  UNSPECIFIED = 0;
  SUBCATEGORY = 1;
  RESOURCE_TYPE = 2;
  CATEGORY = 3;
  CLOUD = 4;
}

message ResourceDistributionCount {
  string field_name = 1;
  float count = 2;
}

enum PartialMatchMeta {
  META_UNSPECIFIED = 0;
  TAGS = 1;
}

message PartialMatchFilter {
  PartialMatchMeta meta = 1;
  repeated string values = 2;
}

// EndpointIDs represent the mapping between a resource id and list of flow endpoint ids
// a flow endpoint id is defined as a resource id corresponding to the following types (nic, eni, vnic, workload)
message EndpointIDs {
  string resource_id = 1;
  repeated string endpoint_ids = 2;
}

message ResourceEvent {
  string id = 1;
  string csp_id = 2;
  string account_id = 3;
  string tenant_id = 4;
  string cloud = 5;
  string name = 6;
  string object_type = 7;
  string region = 8;
  string state = 9;
  map<string, string> tags = 10;
  repeated ResourceRelation relations = 11;

  // IP
  Ips ips = 12;
  repeated string mac_addresses = 13;
  repeated string network_csp_ids = 14;

  PolicyType policy_type = 16;

  // whether resource was upserted or deleted
  EventType event_type = 17;

  // resource version
  uint32 version = 18;

  // timestamp of last update
  google.protobuf.Timestamp last_updated_at = 19;

  // is_resync - indicates if this is a resync message
  bool is_resync = 20;

  string dataplane = 21;

  string key = 22;
}

message ResourceRelation {
  string id = 1;
  string csp_id = 2;
  string account_id = 3;
  string object_type = 4;
  repeated ResourceRelation relations = 5;
  Ips ips = 6;
  repeated string mac_addresses = 7;
  PolicyType policy_type = 8;
  string name = 9;
}

enum EventType {
  EVENT_TYPE_UNSPECIFIED = 0;
  EVENT_TYPE_UPSERT = 1;
  EVENT_TYPE_DELETED = 2;
  EVENT_TYPE_RESYNC = 3;
}

enum PolicyType {
  POLICY_TYPE_UNSPECIFIED = 0;
  POLICY_TYPE_WORKLOAD = 1;
  POLICY_TYPE_NETWORK = 2;
  POLICY_TYPE_INTERFACE = 3;
  POLICY_TYPE_SUBNET = 4;
  POLICY_TYPE_ENFORCEMENT_POINT = 5;
}


/*
message OnPremDevice {
  string host_name = 1;
  string dns_domain = 2;
  string ip_addr = 3;
  string mac_addr = 4;
  repeated string service_names = 5;
  string system = 6;
}

message User {
  string external_id = 1;
  string name = 2;
  string user_privileges = 3;
}

message Firewall {
  string address = 1;
  string mac_addr = 2;
  string name = 3;
  repeated string outbound_interfaces = 4;
  string product = 5;
  repeated string translated_addrs = 6;
  string version = 7;
  string time_zone = 8;
  string external_id = 9;
}
*/


enum DestinationFlowType{
  UNSPECIFIED_DESTINATION_FLOW_TYPE = 0;
  WITHIN_THIS_ACCOUNT = 1;
  INCOMING_FROM_EXTERNAL_ACCOUNT = 2;
  OUTGOING_TO_EXTERNAL_DESTINATIONS = 3;
}

message FlowSources{
  string type = 1;
  int64 count = 2;
}

message DestinationFlows {
  string destination_csp_id = 1;
  bool access = 2;
  repeated FlowSources sources=3;
  map<string, string> tags = 4;
  string region = 5;
  string account_csp_id = 6;
  string account_name = 7;
  string token = 8;
  google.protobuf.Timestamp last_accessed_at = 9;
}

message FlowLogDetailsSortBy {
  enum Field {
    UNSPECIFIED = 0;
    DESTINATION_ACCOUNT_CSP_ID = 1;
    DESTINATION_ACCOUNT_CSP_NAME = 2;
    DESTINATION_CSP_ID = 3;
    ACCESS = 4;
  }
  Field field = 1;
  bool asc = 2;
}