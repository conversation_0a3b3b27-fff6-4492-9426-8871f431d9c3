// Copyright 2023 Illumio, Inc. All Rights Reserved.
syntax = "proto3";

package inventorydata;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "google/protobuf/struct.proto";
import "common/label/label.proto";

option go_package = "illum.io/cloud/api/generated/common/inventorydata";

message ExternalRelation {
  string resource_id = 1;
  google.protobuf.Timestamp created_at = 2;
}

message ExternalSortBy {
  enum Field {
    UNSPECIFIED = 0;
    TYPE = 1;
    RESOURCE_ID = 2;
    CREATED_AT = 3;
    EXT_TENANT_ID = 4;
    ENTITY_ID = 5;
  }
  Field field = 1;
  bool asc = 2;
}

message ExternalResource {
  string id = 1 [(validate.rules).string.uuid = true];
  string resource_id = 2 [(validate.rules).string.uuid = true];
  string tenant_id = 3 [(validate.rules).string.uuid = true];
  string external_tenant_id = 4 [(validate.rules).string.uuid = true];
  string type = 5;
  string entity_id = 6;
  // token is used in pagination
  string token = 7;
  // Please see below commented out messages
  // for the properties for specific types of ext resource
  google.protobuf.Struct properties = 11;
  repeated ExternalRelation relations = 12;
  string ip = 13;
  string mac_addr = 14;
  google.protobuf.Timestamp created_at = 15;
}

message ExternalResourceEvent {
  enum EventType {
    EVENT_TYPE_UNSPECIFIED = 0;
    EVENT_TYPE_UPSERT = 1;
    EVENT_TYPE_DELETED = 2;
  }

  ExternalResource msg = 1;
  EventType type = 2;
}