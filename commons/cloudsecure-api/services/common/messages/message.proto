
syntax = "proto3";

package flow;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/api/generated/common/messages";

message RawLog {
  // Source IP Address. This field will be filled in case flow is reported by CORE, Firewall or Cloud Secure
  // required
  string src_ip = 1 [json_name="SrcIP"];

  //In case flow is coming from cloud then this field is cloud resource id on cloud. In case this flow is coming from
  // CORE, this field is workload id. This field not be filled when this flow is reported by Firewall
  string src_id = 2;

  // CloudSecure Internal UUID of the source. Consider this as illumio platform id for source workload.
  string cs_src_id = 3 [json_name = "CSSrcId"];

  // Destination IP Address. This field will be filled in case flow is reported by CORE, Firewall or Cloud Secure.
  // required
  string dest_ip = 4 [json_name="DestIP"];

  // In case flow is coming from cloud then this field is cloud resource id on cloud. In case this flow is coming from
  // CORE, this field is workload id. This field not be filled when this flow is reported by Firewall
  string dest_id = 5;

  // CloudSecure Internal UUID of the destination. Consider this as illumio platform id for destination workload.
  string cs_dest_id = 6 [json_name = "CSDestId"];

  // Destination port. Valid values 0-65535.
  // required
  int64 port = 7;

  // Transport protocol that identifies the Layer-4 protocol used. Possible values include protocol names, such as tcp or udp.
  // required
  string proto = 8;

  // Number of bytes transferred outbound
  // required
  int64 send_bytes = 9;

  // Number of bytes transferred inbound.
  // required
  int64 received_bytes = 10;

  // CloudSecure tenant ID
  // required
  string illumio_tenant_id = 11;

  // Source Azure Tenant ID
  string src_tenant_id = 12;

  //Source Azure Subscription ID
  string src_sub_id = 13;

  // Source Azure Region
  string src_region = 14;

  // Source Azure Resource ID
  string src_res_id = 15;

  // Source Azure VNET
  string src_vnet_id = 16;

  // Destination Azure Tenant ID
  string dest_tenant_id = 18;

  // Destination Azure Subscription ID
  string dest_sub_id = 19;

  // Destination Azure Region
  string dest_region = 20;

  // Destination Azure Resource ID
  string dest_res_id = 21;

  // Destination Azure VNET
  string dest_vnet_id = 22;

  // Valid values azure, dc, or internet
  string src_flow_type = 24;

  // Valid values azure, dc, or internet
  string dest_flow_type = 25;

  // DC Device ID if source is in DC. Not filled in today.
  string src_device_id = 26;

  // Firewall ID if source is in DC. Not filled in today.
  string src_firewall_id = 27;

  // Not filled in today.
  string source_user_id = 28;

  // Not filled in today.
  string dest_device_id = 29;

  // Not filled in today.
  string dest_firewall_id = 30;

  // Not filled in today.
  string destination_user_id = 31;

  // If Src was Azure, the resource type. Not filled in today.
  string src_resource_type = 32;

  // If Destination was Azure, the resource type. Not filled in today.
  string dest_resource_type = 33;

  // Valid values 0-3 or empty. From IP Classification. Field to store threat information.
  int64 src_threat_level = 34;

  // Valid values 0-3 or empty. From IP Classification.  Field to store threat information.
  int64 dest_threat_level = 35;

  // Valid values true, false or empty. From IP Classification.
  bool src_is_wellknown = 36;

  // Valid values true, false or empty. From IP Classification.
  bool dest_is_wellknown = 37;

  // From IP Classification.
  string src_domain = 38;

  // From IP Classification.
  string dest_domain = 39;

  // From IP Classification.
  string src_country = 40;

  // From IP Classification.
  string dest_country = 41;

  // From IP Classification.
  string src_city = 42;

  // From IP Classification.
  string dest_city = 43;

  // From IP Classification.
  string src_cloud_provider = 44;

  // From IP Classification.
  string dest_cloud_provider = 45;

  // Identifies the source that event refers to in an IP network. Format should be a fully qualified domain name (DQDN)
  // associated with the source node, when a node is available. For example host or host.domain.com.
  string source_host_name = 46;

  // Source MAC address.
  string source_mac_address = 47 [json_name = "SourceMACAddress"];

  // The Windows domain name for the source address.
  string source_nt_domain = 48 [json_name = "SourceNTDomain"];

  // The ID of the source process associated with the event.
  int64 source_process_id = 49;

  // The name of the event's source process.
  string source_process_name = 50;

  // Identifies the source user by name. Email addresses are also mapped into the UserName fields.
  // The sender is a candidate to put into this field.
  string source_user_name = 51;

  // The source user's privileges. Valid values include Administrator, User, Guest.
  string source_user_privileges = 52;

  // The action mentioned in the event. Device is FW in this case. (Values allow, deny, reset-both)
  string device_action = 53;

  // The IPv4/IPv6 address of the device generating the event.  Device is FW in this case.
  string device_address = 54;

  // The DNS part of the fully-qualified domain name (FQDN).
  string destination_dns_domain = 55;

  // The destination that the event refers to in an IP network. The format should be an FQDN associated with the
  // destination node, when a node is available. For example host.domain.com or host.
  string destination_host_name = 56;

  // The destination MAC address (FQDN).
  string destination_mac_address = 57 [json_name = "DestinationMACAddress"];

  // The Windows domain name of the destination address.
  string destination_nt_domain = 58 [json_name = "DestinationNTDomain"];

  // The ID of the destination process associated with the sevent.
  int64 destination_process_id = 59;

  // The name of the event’s destination process, such as telnetd or sshd.
  string destination_process_name = 60;

  // The service that is targeted by the event. For example sshd.
  string destination_service_name = 61;

  // Identifies the translated destination referred to by the event in an IP network, as an IPv4 IP address.
  string destination_translated_address = 62;

  // Identifies the destination user by name
  string destination_user_name = 63;

  // A string or integer that describes the importance of the event. Valid string values Unknown , Low, Medium, High,
  // Very-High Valid integer values are 0-3 = Low, 4-6 = Medium, 7-8 = High, 9-10 = Very-High.
  string log_severity = 64;

  // If one of the IP in the message was correlate with the current TI feed we have it will show up here.
  string malicious_ip = 65 [json_name="MaliciousIP"];

  // The country of the MaliciousIP according to the GEO information at the time of the record ingestion.
  string malicious_ip_country = 66 [json_name="MaliciousIPCountry"];

  // The Latitude of the MaliciousIP according to the GEO information at the time of the record ingestion.
  double malicious_ip_latitude = 67 [json_name="MaliciousIPLatitude"];

  // The Longitude of the MaliciousIP according to the GEO information at the time of the record ingestion.
  double malicious_ip_longitude = 68 [json_name="MaliciousIPLongitude"];

  // The Log Analytics workspace ID
  string law_tenant_id = 69 [json_name="LAWTenantId"];

  // The threat confidence of the MaliciousIP according to our TI feed.
  string threat_confidence = 70;

  // The threat description of the MaliciousIP according to our TI feed.
  string threat_description = 71;

  // The threat severity of the MaliciousIP according to our TI feed at the time of the record ingestion.
  string threat_severity = 72;

  // The time when the activity that the event refers to started. Earlier start time
  // Examples: 2024-06-10T20:22:17.0000000Z  2024-06-11T19:17:47.5321764Z
  /// required
  google.protobuf.Timestamp start_time = 73;

  // The time at which the activity related to the event ended.  Latest end time
  // required
  google.protobuf.Timestamp end_time = 74;

  // The DNS domain part of the complete FQDN.
  string source_dns_domain = 75;

  // The service responsible for generating the event.
  string source_service_name  = 76;

  // The type of agent the event was collected by. For example, OpsManager for Windows agent, either direct connect or
  // Operations Manager, Linux for all Linux agents, or Azure for Azure Diagnostics
  string source_system = 77;

  // The MAC address of the device generating the event.
  string device_mac_address = 78 [json_name = "DeviceMACAddress"];

  // The FQDN associated with the device node, when a node is available. For example host.domain.com or host.
  string device_name = 79;

  // Interface on which the packet or data left the device.
  string device_outbound_interface = 80;

  // String that together with device product and version definitions, uniquely identifies the type of sending device.
  string device_product = 81;

  // Identifies the translated device address that the event refers to, in an IP network. The format is an Ipv4 address.
  string device_translated_address = 82;

  // String that together with device product and version definitions, uniquely identifies the type of sending device.
  string device_version = 83;

  // Timezone of the device generating the event.
  string device_time_zone = 84;

  // A name that uniquely identifies the device generating the event.
  string device_external_id = 85 [json_name = "DeviceExternalID"];

  // Session time. Elapsed time in seconds.
  int64 device_custom_number3 = 86;

  // Time that device recorded the event.
  google.protobuf.Timestamp receipt_time = 87;

  // Possible values are  Traffic, Threat
  string activity = 88;

  // A placeholder for additional fields. Fields are logged as key-value pairs. Example format key1=value1;key2=value2;key3=value3
  string additional_extensions = 89;

  // The networking zone from which the traffic originated. CEF field name cs4. CommonSecurityLog field name DeviceCustomString4
  string source_zone = 90;

  // Networking zone to which the traffic was sent. CEF field name cs5. CommonSecurityLog field name DeviceCustomString5
  string destination_zone = 91;

  // The URL accessed for an HTTP request, including the protocol. For example http//www/secure.com.
  string request_url = 92 [json_name = "RequestURL"];

  // IP of the firewall from Syslog.
  string computer = 93 ;

  // Comma separated cloud tags on source workload. Use Json here.
  string src_cloud_tags	= 94;

  // Comma separated cloud tags on destination workload. Use Json here.
  string dest_cloud_tags	= 95;

  // Count of flow between source and destination Workloads. CloudSecure currently records flow count in hour buckets. Each datasource increases the flow count by one.
  int64 flow_count = 96;

  // Number of packets received in the flow
  int64 packets_received	= 97;

  // Number of packets sent in the flow
  int64 packets_sent	= 98;

  // Traffic flow status. Valid values are DENIED or ALLOWED
  string traffic_status = 99;

  // Json to hold key value of labels
  string source_label = 100;

  // Json to hold key value of labels
  string destination_label	= 101;

  // Source Account Name
  string src_account_name	= 102;

  // DestAccount Name
  string dest_account_name = 103;

  // Source Resource Category
  string src_resource_category = 104;

  // Destination Resource Category
  string dest_resource_category	= 105;
}
