// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package policyimpact;

import "google/protobuf/timestamp.proto";
import "common/policy/policy.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/api/generated/common/policy";

message EnforcementPoint {
  string id = 1;
  string csp_id = 2;
  optional string name = 3;
  optional string type = 4;
}

// EnforcementPointPolicyImpactRequest is the request sent to the GetEnforcementPointPolicyImpact rpc defined by enforcement services
// providing policy impact
//
// `policy_version` has to be provided and can be one of draft, active. If the policy_version is `draft`, GetEnforcementPointPolicyImpact
// expects a draft `policy.Policy policy`. If the policy_version is `active`, GetEnforcementPointPolicyImpact will ignore the `policy.Policy policy` value
message EnforcementPointPolicyImpactRequest {
  EnforcementPoint enforcement_point = 1;
  string policy_version = 2 [(validate.rules).string = {in: ["draft", "active"]}];
  optional policy.Policy policy = 3;
  optional string region = 4;
  optional string account_id = 5;
}

message EnforcementPointPolicyImpactResponse {
  EnforcementPoint enforcement_point = 1;

  // policy-impact will show (eventually) all rules for an enforcement point. Some/All of those rules may not be written by CloudSecure
  // and they won't have digest, policy or timestamp. We don't want to return defaults, so we'll use optional
  optional policy.PolicyDigest policy_digest = 2;
  optional policy.CSPPolicy  csp_policy = 3;
  optional google.protobuf.Timestamp created_at = 4;
  optional google.protobuf.Timestamp updated_at = 5;
}