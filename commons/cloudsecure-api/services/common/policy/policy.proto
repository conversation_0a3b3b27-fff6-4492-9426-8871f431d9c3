syntax = "proto3";

package policy;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/api/generated/common/policy";

enum RuleAction {
  RuleActionUnspecified = 0; // ref: https://protobuf.dev/programming-guides/proto3/#enum
  Allow = 1;
  Deny  = 2;
}

enum RuleDirection {
  RuleDirectionUnspecified = 0;
  Egress = 1;
  Ingress  = 2;
  Both = 3; // both ingress and egress
}

enum RuleType {
  RuleTypeUnspecified = 0;
  TopDenyRule    = 1;
  AllowRule      = 2;
  BottomDenyRule = 3;
}

message Service {
  // ICMP does not need a port number
  // https://stackoverflow.com/questions/58583995/how-can-icmp-not-use-a-port-but-tcp-ip-does
  optional int32 from_port = 1;
  optional int32 to_port = 2; // if not set, port range will be [from_port, from_port]
  int32 protocol = 3; // https://www.iana.org/assignments/protocol-numbers/protocol-numbers.xhtml

  // only if protocol is ICMP
  optional int32 icmp_type = 4;
  optional int32 icmp_code = 5;
}

message RuleActor {
  string actor_type = 1;
  string actor_id   = 2;
}

message Actors {
  // TODO: Depending on the response from PCE, ip_version may not be needed
  optional string ip_version = 1; // if actors are IP, CIDRs
  repeated string actor = 2; // IPs, CIDR, service tags etc
}

message PolicyDigest {
  string sets_digest  = 1;
  string rules_digest = 2;
}

message Rule {
  RuleAction action = 1;
  RuleType type     = 2;
  RuleDirection direction        = 3;
  repeated Service service       = 4;
  repeated RuleActor source      = 5;
  repeated RuleActor destination = 6;
}

message Policy {
  PolicyDigest policy_digest = 1;
  repeated Rule rule = 2;
  map<string, Actors> actors  = 3;
}

message IPSetActors {
  repeated string actors = 1;
}

message CSPRuleActors {
  string type = 1;
  oneof actors {
    IPSetActors ip_list = 2;
  }
}

message PortRange {
  int32 port = 1;
  optional int32 to_port = 2;
}

message PortRanges {
  repeated PortRange port_range = 1;
}

message Tag {
  string key = 1;
  string value = 2;
}

message TagSpecifications {
  repeated Tag tags = 1;
}

message CSPRule {
  optional string csp_rule_name = 1;
  optional string csp_rule_id   = 2;
  optional string csp_rule_description   = 3;

  RuleDirection direction = 4;
  RuleAction action = 5;

  optional PortRanges src_ports = 6;
  optional PortRanges dst_ports = 7;

  int32 protocol = 8;
  optional int32 icmp_type = 9;
  optional int32 icmp_code = 10;

  CSPRuleActors source = 11;
  CSPRuleActors destination = 12;

  optional TagSpecifications tags = 13;
  optional int32 priority = 14;
}

message CSPPolicy {
  repeated CSPRule rules = 4;
}

enum EnforcementMode {
  UNKNOWN = 0;
  NON_MANAGED = 1;
  MANAGED = 2;
  // Enables/disables policy enforcement
  // Controlled by a FF and only done for tests
  SKIP_POLICY_ENFORCEMENT = 15;
}

message EnforcementStatus {
  string csp_id = 1;
  string status = 2 [(validate.rules).string = {in: ["syncing", "succeeded", "failed", "read-only"]}];
  optional google.protobuf.Timestamp updated_at = 3;
}
