syntax = "proto3";

package authservice;
option go_package = "illum.io/cloud/api/pkg/generated/authservice";

message CoreAuthHeader {
    string Authorization = 1;
}

message CloudAuthHeader {
    string x_tenant_id = 1;
    string cookie = 2;
}

message SessionCoreProduct {
    string org_id = 1;
    string org_uuid = 2;
    CoreAuthHeader auth_header = 3;
}

message SessionCloudProduct {
    string tenant_id = 1;
    CloudAuthHeader auth_header = 2;
}
message SessionProducts {
    SessionCoreProduct core = 1;
    SessionCloudProduct cloudsecure = 2;
}

message GetSessionByCSIRequest {
    string csi = 1;
    repeated string product_sessions_to_renew = 2;
}

message GetSessionByCSIResponse {
    string user_id = 1;
    SessionProducts products = 2;
    string tenant_id = 3;
}

service AuthService {
    // ================ Auth ================
    rpc GetSessionByCSI(GetSessionByCSIRequest) returns (GetSessionByCSIResponse) {}
}
