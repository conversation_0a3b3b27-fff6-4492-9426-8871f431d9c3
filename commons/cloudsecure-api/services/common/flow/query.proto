// Copyright 2024 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package flow;

import "validate/validate.proto";
import "common/flow/flow.proto";

option go_package = "illum.io/cloud/api/generated/common/flow";

enum Op {
  OP_UNSPECIFIED = 0; // Same as EQUAL
  EQUAL = 1;          // EQUAL ANY when used on list, i.e. IN
  NOT_EQUAL = 2;      // NOT EQUAL ALL when used on list, i.e. NOT IN
}

message OpAnd {
  repeated Expr args = 1;
}

message OpOr {
  repeated Expr args = 1;
}

message OpNot {
  Expr arg = 1;
}

message OpStatus {
  Op op = 1;
  Expr status = 2;
}

message OpService {
  Op op = 1;
  repeated Expr services = 2;
}

message OpEndPointAddr {
  SrcDst src_dst = 1;
  Op op = 2;
  repeated Expr addrs = 3;
}

message OpEndPointId {
  SrcDst src_dst = 1;
  Op op = 2;
  repeated Expr ids = 3;
}

message List {
  repeated Expr items = 1;
}

message ValString {
  string value = 1;
}

message ValInt {
  int64 value = 1;
}

message ValBool {
  bool value = 1;
}

message ValAddr {
  oneof type {
    IpAddress ip_addr = 1;
//    IpCidr ip_cidr = 2;
  }
}

message ValId {
  oneof type {
    string uuid = 1 [(validate.rules).string.uuid = true];
  }
}

message ValService {
  Service service = 1;
}

message ValStatus {
  FlowStatus status = 1;
}

// Attr* can only be used in queries to bridge
message AttrCsp {
  Expr name = 1;
}

message AttrAccount {
  Expr cspid = 1;
}

enum AttrType { // AttrType is used in both AttrIds and AttrAddrs (hopefully temporary...)
  // AttrIds:
  AT_UNSPECIFIED = 0; // Illegal value
  AT_CLOUD = 1;
  AT_ACCOUNT_CSPID = 2;
  AT_OBJECT_TYPE = 3;
  AT_CATEGORY = 4;
  AT_REGION = 5;
  AT_CSPID = 6;
  AT_TAGS = 7; // a bit more complicates as it requires some kind of subquery
  AT_SUBCATEGORIES = 8;
  AT_NETWORK_CSPID = 9;
  AT_SUBNET_CSPID = 10;
  AT_NETWORK_ID = 11;
  AT_SUBNET_ID = 12;
  AT_RESOURCE = 13;
  AT_RESOURCEGRP = 14;
  AT_LABEL_ID = 15;

  // AttrAddrs:
  AT_IP_LIST = 20;

  // AttrIds:
  AT_EKS_ID = 21;
  AT_AKS_ID = 22;
  AT_EKS_CSP_ID = 23;
  AT_AKS_CSP_ID = 24;
}

message AttrIds {
  AttrType type = 1;
  Expr name = 2;
}

message AttrAddrs {
  AttrType type = 1;
  Expr name = 2;
}

message AttrLabel {
  Expr id = 1;
}

message Expr {
  oneof type {
    List list = 1;
    ValString val_string = 2;
    ValInt val_int = 3;
    ValBool val_bool = 4;
    ValAddr val_addr = 5;
    ValId val_id = 6;
    ValService val_service = 7;
    ValStatus val_status = 8;
    
    AttrCsp attr_csp = 10; // deprecated
    AttrAccount attr_account = 11; // deprecated
    AttrLabel attr_label = 12; // deprecated

    AttrIds attr_ids = 13;
    AttrAddrs attr_addrs = 14;

    OpService op_service = 20;
    OpEndPointAddr op_endpoint_addr = 21;
    OpEndPointId op_endpoint_id = 22;
    OpStatus op_status = 23;

    OpAnd op_and = 30;
    OpOr op_or = 31;
    OpNot op_not = 32;
  }
}
