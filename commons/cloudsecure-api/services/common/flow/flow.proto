// Copyright 2024 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package flow;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/api/generated/common/flow";

enum FlowStatus {
  FLOWSTATUS_UNSPECIFIED = 0;
  ALLOWED = 1;
  DENIED = 2;
}

enum SrcDst {
  SRCDST_UNSPECIFIED = 0; // Same as SRC_OR_DST in search
  SRC = 1;
  DST = 2;
  SRC_OR_DST = 3;
}

// Enums for different parts of a Flow
enum FlowPart {
  FP_UNSPECIFIED = 0;
  FP_STATUS = 1;
  FP_START_TIME = 2;
  FP_END_TIME = 3;
  FP_SRC_IP_ADDRESS = 4;
  FP_SRC_ENTITY_ID = 5;
  FP_DST_IP_ADDRESS = 6;
  FP_DST_ENTITY_ID = 7;
  FP_DST_PORT = 8;
  FP_PROTOCOL = 9;
  FP_BYTES = 10;
  FP_SRC = 11; // SRC is SRC_ENTITY_ID if present, otherwise SRC_IP_ADDRESS
  FP_DST = 12; // DST is DST_ENTITY_ID if present, otherwise DST_IP_ADDRESS
}

message IpAddress { // TODO: need global message for this, including separate ipv4 and ipv6
  string ip = 1;
}

//message IpCIDR {
//  string ip = 1;
//  uint32 prefix_len = 2;
//}

message EntityId {
  string id = 1 [(validate.rules).string.uuid = true];
}

message EndPoint {
  IpAddress ip_address = 1;
  EntityId entity_id = 2;
}

message Service {
  optional uint32 protocol = 1 [(validate.rules).uint32.lte = 255];
  optional uint32 port = 2 [(validate.rules).uint32.lte = 65535];
  optional PortRange port_range = 3;
}

message PortRange {
  optional uint32 start_port = 1 [(validate.rules).uint32.lte = 65535];
  optional uint32 end_port = 2 [(validate.rules).uint32.lte = 65535];
}

message Flow {
  FlowStatus status = 1;
  google.protobuf.Timestamp start_time = 2;     // Inclusive
  google.protobuf.Timestamp end_time = 3;       // Exclusive
  EndPoint src = 4;
  EndPoint dst = 5;
  uint32 dst_port = 6;
  uint32 protocol = 7;
  uint64 bytes = 8;
  uint64 count = 9; // Number of flows in this summarized flow. This is always 1 for pre ~Nov24 indexes.
}

