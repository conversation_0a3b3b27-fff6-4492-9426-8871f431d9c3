// Copyright 2024 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package insights;

import "google/type/interval.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common/version/version.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/api/generated/common/insights";

// Could not find this defined elsewhere. Basically, we need to tie an insight
// to cloud or core such that cloud insights are not displayed for core only
// customers, etc
enum Product {
  PRODUCT_UNSPECIFIED = 0;
  PRODUCT_CLOUD = 1;
  PRODUCT_CORE = 2;
}

message Filters {
  reserved 1;

  //optional Status traffic_status = 1; // Changed to a string below
  optional bool only_risky_traffic = 2;
  optional string traffic_status = 3;
  optional string source_ip_list = 4;
  optional string destination_ip_list = 5;
  optional string flow_service = 6;
}

message GetInsightRequest {
  string id = 1 [(validate.rules).string.min_len = 1]; // path parameter
  google.type.Interval timeframe = 2 [(validate.rules).timestamp.required = true]; // required request's body (json)
  google.type.Interval comparison_timeframe = 3; // optional request's body (json)
  optional Filters filters = 4; // optional request's body (json)
}

enum DisplayType {
  display_type_unspecified = 0;
  text = 1;
  account = 2;
  tenant = 3;
  short_csp = 4;
  long_csp = 5;
  flows_diff = 6;
  bytes_diff = 7;
  raw_bytes = 8;
  single_date = 9;
  date_range = 10;
  region = 11;
  status = 12;
  ip_address = 13;
  endpoint = 14;
  arrow = 15;
  services = 16;
  labels = 17;
  domain = 18;
  risk_level = 19;
  location = 20;
  // Going forward new types should probably prepend "dt_" to avoid conflicts. "boolean" already had an issue.
  dt_boolean = 21;
}

enum InsightKey {
  // reserve misc stuff we ended up not using
  reserved 8, 9, 10, 11, 12, 13;
  reserved "csp", "csp_service", "csp_region", "site", "threat_ip_vendor", "threat_category";

  insight_key_unspecified = 0;
  source = 1;
  destination = 2;
  flow_direction = 3;
  flow_status = 4;
  flows = 5;
  bytes = 6;
  date = 7;
  destination_country = 14;
  flow_arrow = 15;
  flow_services = 16;
  destination_domain = 17;
  destination_risk_level = 18;
  destination_known_flag = 19;
  destination_location = 20;
  destination_asn = 21;
  destination_cloud = 22;
}

enum Direction {
  direction_unspecified = 0;
  ingress = 1;
  egress = 2;
}

enum Arrow {
  ARROW_UNSPECIFIED = 0;
  ARROW_LEFT = 1;
  ARROW_RIGHT = 2;
  ARROW_BOTH = 3;
}

// There are some seemingly redundant types here needed to make core work until
// we can get PM, etc all on the same page between products. Note that this enum
// is currently only used to generated strings for json value's. It can be removed
// and replaced with some string constants somewhere else if we start running into
// enum name conflicts.
enum Status {
  status_unspecified = 0;
  any = 1;
  allowed = 2;
  denied = 3;
  blocked = 4;
  mixed = 5;
}

enum FilterKey {
  filter_key_unspecified = 0;
  traffic_status = 1;
  only_risky_traffic = 2;
  source_ip_list = 3;
  destination_ip_list = 4;
  flow_service = 5;
}

// Note that this enum is currently only used to generated strings for json
// value's. It can be removed and replaced with some string constants somewhere
// else if we start running into enum name conflicts.
enum FilterType {
  filter_type_unspecified = 0;
  single_selection = 1;
  toggle = 2;
}

message InsightColumn {
  string header = 1;
  InsightKey key = 2;
  DisplayType display_type = 3;
  repeated string context_columns = 4;
  optional string preset_toggle = 5;
}

message InsightPreset {
  string label = 1;
  bool default = 2;
  repeated string columns = 3;
}

// TODO - if UI starts using comparison_value to compute the percent diff
//        we can retire percent_diff / show_percent_diff here
message FlowDiff {
  uint64 value = 1;
  optional double percent_diff = 2;
  bool show_percent_diff = 3;
  optional uint64 comparison_value = 4;
}

message FilterOption {
  reserved 2;

  string label = 1 [(validate.rules).string.min_len = 1];
  //Status value = 2; // Changed to a string below
  optional string description = 3;
  string value = 4;
}

message AvailFilter {
  reserved 4;

  string label = 1;
  FilterKey key = 2;
  repeated FilterOption options = 3;
  //FilterType type = 4; // Changed to a string below
  optional string description = 5;
  string default = 6 [(validate.rules).string.min_len = 1];
  string type = 7;
}

// TODO - if UI starts using comparison_value to compute the percent diff
//        we can retire percent_diff / show_percent_diff here
message ByteDiff {
  uint64 value = 1;
  optional double percent_diff = 2;
  bool show_percent_diff = 3;
  optional uint64 comparison_value = 4;
}

message Date {
  string from_date = 1;
  optional string to_date = 2;
}

message Country {
  string value = 1;
}

message Text {
  optional string value = 1;
}

message Location {
  optional string city = 1;
  optional string country = 2;
}

message RiskLevel {
  optional int32 level = 1; // translates to a pretty badge in the UI (currently 0 - low, 1 - medium, 2 - high, 3 - critical)
  optional int32 score = 2; // raw score from the vendor
  optional string source = 3;
  optional string description = 4;
}

message BoolWithIcon {
  optional bool show_icon = 1;
  optional bool value = 2;
}

enum EnforcementMode {
  ENFORCEMENT_UNSPECIFIED = 0;
  ENFORCEMENT_FULL = 1;
  ENFORCEMENT_VISIBILITY = 2;
  ENFORCEMENT_SELECTIVE = 3;
}

message Agent {
  optional string instance_id = 1;
}

message Workload {
  optional string name = 1;
  optional string hostname = 2;
  optional string href = 3;
  optional Agent agent = 4;
  optional bool managed = 5;
  optional EnforcementMode enforcement_mode = 6;
  optional bool deleted = 7;
}

message IpList {
  string name = 1;
  optional string id = 2;
  optional string href = 3;
  optional bool fqdn = 4;
}

message VirtualServerService {
  optional string name = 1;
  string href = 2;
}

message Labels {
  string key = 1;
  string value = 2;
  optional string name = 3;
  optional string id = 4;
  optional string href = 5;
}

message PortProto {
  uint32 port = 1;
  uint32 proto = 2;
}

message Services {
  optional string id = 1;
  optional string href = 2;
  optional string name = 3;
  optional string description = 4;
  repeated PortProto service_ports = 5;
}

message Talker {
  reserved 5;
  reserved "csp"; // @deprecated: we use cloud everywhere

  optional string cloud = 1; // internal cloud abbreviations saved in our database
  optional string account_id = 2;
  optional string account_name = 3;
  optional string tenant_name = 4;
  optional string region_name = 6;
  optional string ip_address = 7;
  optional string domain = 8;
  optional string cloud_name = 9; // external cloud names exposed to clients
  optional Workload workload = 10;
  optional IpList ip_list = 11;
  optional VirtualServerService virtual_service = 12;
  optional VirtualServerService virtual_server = 13;
  optional Labels key_labels = 14;
  optional string ip = 15; // yes this is redundant with ip_address, but core endpoints need this temporarily
}

// These insight data field names map to InsightKey's. These essentially
// map to insight columns in the UI.
message InsightData {
  // reserve misc stuff we ended up not using
  reserved 4, 8, 9, 10, 11, 12, 13;
  reserved "csp", "csp_service", "csp_region", "site", "threat_ip_vendor", "threat_category";

  optional Talker source = 1;
  optional Talker destination = 2;
  optional Direction flow_direction = 3;
  //optional Status flow_status = 4; // Changed to a string below
  optional FlowDiff flows = 5;
  optional ByteDiff bytes = 6;
  optional Date date = 7;
  optional Country destination_country = 14;
  optional Arrow flow_arrow = 15;
  repeated Services flow_services = 16;
  optional string flow_status = 17;
  optional Text destination_domain = 18;
  optional RiskLevel destination_risk_level = 19;
  optional BoolWithIcon destination_known_flag = 20;
  optional Location destination_location = 21;
  optional Text destination_asn = 22;
  optional Talker destination_cloud = 23;
}

message GetInsightResponse {
  reserved 4;
  reserved "available_filters"; // @deprecated: we moved available_filters under Insight within InsightsListResponse

  repeated InsightColumn columns = 1;
  repeated InsightData data = 2;
  Filters applied_filters = 3;
  repeated InsightPreset presets = 5;
}

message Insight {
  string name = 1;
  string description = 2;
  repeated string categories = 3;
  string id = 4;
  google.protobuf.Timestamp last_synced_at = 5;
  repeated AvailFilter available_filters = 6;
  Product product = 7;
  optional string service = 8; // for local services using GRPC
  optional string registered_url = 9; // for core services using REST
}

message InsightsListRequest {
}

message InsightsListResponse {
  repeated Insight insights = 1;
}

message RegisterInsightsRequest {
  string service = 1;
  repeated string insights = 2;
}

message RegisterInsightsResponse {
}

message DeRegisterInsightsRequest {
  repeated string insights = 1;
}

message DeRegisterInsightsResponse {
}

message InsightTimestamp {
  string id = 1;
  google.protobuf.Timestamp last_synced_time = 2;
}

message InsightLastSyncTimesResponse {
  repeated InsightTimestamp insight_sync_times= 1;
}
