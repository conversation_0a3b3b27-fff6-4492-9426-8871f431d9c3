// Copyright 2024 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package labelingdata;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "integrations/integrations.proto";

option go_package = "illum.io/cloud/api/generated/common/labeling";

// Message to indicate the start of a sync between control plane and data plane labeling
// This is always the first message sent on the stream
message SyncStart {
  // Timestamp after which the sending party requires updates for
  google.protobuf.Timestamp last_successful_sync = 1;
  // Optional data plane name indicating the data plane that is syncing
  // This field is only set when the message is sent from Data-Plane labeling
  optional string data_plane_name = 2;
}

message Tag {
  string key = 1;
  string value = 2;
  string cloud = 3;
}

message TagList {
  repeated Tag tags = 1;
}

message TagSet {
  repeated TagList tag_lists = 1;
}

enum RuleType {
  // Can't use "UNAVAILABLE" as already used by another enum in same package
  RULE_TYPE_UNAVAILABLE = 0;
  ACCOUNT = 1;
  VIRTUAL_NETWORK = 2;
  SUBNET = 3;
  TAG = 4;
}

message CloudTagKey {
  integrations.Cloud cloud = 1;
  string key = 2;
}

message CloudResource {
  string csp_id = 1;
  string cloud = 2;
}

message CloudMetadata {
  optional integrations.Cloud cloud = 1;
  string id = 2;
}

enum Action {
  // Indicates that the label definition is being created
  CREATE = 0;
  // Indicates that the label definition is being updated
  UPDATE = 1;
  // Indicates that the label definition is being deleted
  DELETE = 2;
}

message LabelMessage {
  Action action = 1;
  // CS ID of that label
  string id = 2 [(validate.rules).string.uuid = true];
  // The key of the label
  string key = 3 [(validate.rules).string.min_len = 1];
  // The value of the label
  string value = 4 [(validate.rules).string.min_len = 1];
  // PCE href of that label
  optional string href = 5;
  // Source of the label
  string source = 6 [(validate.rules).string.min_len = 1];
}

message DiscoveryRuleMetadata {
  string prefix = 1;
  RuleType rule_type = 2;
  repeated integrations.Cloud clouds = 3;
  repeated CloudTagKey tag_keys = 4;
}

message ApplicationDefinitionMetadata {
  repeated CloudMetadata networks = 1;
  repeated CloudMetadata subnets = 2;
  repeated TagSet tags = 3;
  repeated CloudMetadata accounts = 4;
  repeated CloudMetadata regions = 5;
  optional string rule_id = 6;
}

message DeploymentMetadata {
  repeated CloudMetadata accounts = 1;
  repeated CloudMetadata regions = 2;
  repeated CloudMetadata availability_zones = 3;
  repeated CloudMetadata virtual_networks = 4;
  repeated CloudMetadata subnets = 5;
  repeated TagSet tags = 6;
}

message TagsToLabelMetadata {
  repeated CloudTagKey tag_keys = 1;
}

message LabelDefinitionUpdate {
  Action action = 1;
  // Control-Plane ID of the label definition
  // Can only be empty when action = CREATE
  optional string cs_id = 2;
  string label_key = 3;
  optional string label_value = 4;
  string source = 5;
  google.protobuf.Timestamp auto_approve_until = 6;
  oneof definition_metadata {
    DiscoveryRuleMetadata discovery_rule = 7;
    ApplicationDefinitionMetadata application_definition = 8;
    DeploymentMetadata deployment = 9;
    TagsToLabelMetadata tags_to_label = 10;
  }
}

message StreamMessage {
  google.protobuf.Timestamp sent_at = 1;
  oneof message {
    SyncStart sync_start = 2;
    LabelMessage label = 3;
    LabelDefinitionUpdate label_definition_update = 4;
  }
}