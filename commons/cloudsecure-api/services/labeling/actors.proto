// Copyright 2023 Illumio, Inc. All Rights Reserved.
syntax = "proto3";
package labeling;

import "google/protobuf/timestamp.proto";
import "common/labeling/labeling.proto";

option go_package = "illum.io/cloud/api/generated/labeling";

message User {
  string username = 1;
  optional string email = 2;
}

message LabelObj {
  string id = 1;
  string href = 2;
  string key = 3;
  string value = 4;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  User created_by = 8;
  User updated_by = 9;
}

message Metadata {
  // TODO: refactor to use integrations.Cloud type
  string cloud = 1;
  string id = 2;
}

// wrappers to make protobuf arrays optional
message UpdateTags {
  repeated labelingdata.Tag tags = 1;
}

message UpdateStrings {
  repeated string values = 1;
}

message UpdateMetadata {
  repeated Metadata metadata = 1;
}

// Temporary fix to have multiple proto files.
//TODO: remove after its fixed.
service MockActors {}