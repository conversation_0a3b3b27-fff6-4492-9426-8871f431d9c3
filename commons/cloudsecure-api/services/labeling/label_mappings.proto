// Copyright 2023 Illumio, Inc. All Rights Reserved.
syntax = "proto3";

package labeling;

import "labeling/actors.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "common/version/version.proto";
import "common/protoc-gen-openapiv2/options/annotations.proto";

option go_package = "illum.io/cloud/api/generated/labeling";

//
// Below are dumps i.e. mappings for ALL tenants
//

// 1 Resource -> N Labels
message ResourceToLabels {
  string inventory_resource_id = 1;
  repeated string label_hrefs = 2;
}

// Per tenant, each resource mapped to respective labels
message TenantResourcesToLabels {
  string tenant_id = 1;
  repeated ResourceToLabels resources_to_labels = 2;
}

// List resources -> labels for one or more tenants
message DumpResourcesToLabelsRequest {
  optional google.protobuf.Timestamp last_synced_at = 1;
}

message DumpResourcesToLabelsResponse {
  repeated TenantResourcesToLabels tenant_resources_to_labels = 1;
}

//
// Below are per tenant requests i.e. mappings for a SPECIFIC tenant
//

enum Operator {
  OR = 0;
  AND = 1;
}

message ListLabelsToResourcesRequest {
  repeated string label_ids = 1;
  Operator operator = 2;
}

message ListLabelsToResourcesResponse {
  message ResourceList {
    repeated string resource_ids = 1;
  }

  // label id -> list of resource ids
  // if label id does not exist or has no resources, it will not be in the map
  map<string, ResourceList> labels_to_resources = 1;
}

message ListResourcesToLabelsRequest {
  repeated string resource_ids = 1;
}

message LabelList {
  repeated LabelObj labels = 1;
}

message ListResourcesToLabelsResponse {
  // resource id -> list of labels
  // if resource id does not exist or has no labels, it will not be in the map
  map<string, LabelList> resources_to_labels = 1;
}

message AssignLabelsToResourcesRequest {
  string tenant_id = 1 [(validate.rules).string.uuid = true];
  string source = 2 [(validate.rules).string = {in: ["ML"]}];
  map<string, LabelList> resources_to_labels = 3;
}

message AssignLabelsToResourcesResponse {
  bool success = 1;
}

// XXX: for demo purposes, remove all ML sourced entries
message DeleteLabelsToResourcesRequest {
  string tenant_id = 1 [(validate.rules).string.uuid = true];
  string source = 2 [(validate.rules).string = {in: ["ML"]}];
}

message DeleteLabelsToResourcesResponse {
  bool success = 1;
}

// Temporary fix to have multiple proto files.
//TODO: remove after its fixed.
service MockLabelMappings {}