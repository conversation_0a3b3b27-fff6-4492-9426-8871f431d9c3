// Copyright 2023 Illumio, Inc. All Rights Reserved.
syntax = "proto3";

package labeling;

import "labeling/actors.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/type/interval.proto";
import "validate/validate.proto";
import "common/version/version.proto";
import "common/protoc-gen-openapiv2/options/annotations.proto";
import "common/labeling/labeling.proto";
import "common/inventorydata/inventorydata.proto";
import "common/label/label.proto";

option go_package = "illum.io/cloud/api/generated/labeling";

enum DiscoveryState {
  UNAVAILABLE = 0;
  DISCOVERING = 1;
  COMPLETE = 2;
  FAILED = 3;

  reserved 4;

  QUEUED = 5;
}

enum RecommendationStatus {
  REC_UNAVAILABLE = 0;
  REC_PENDING = 1;
  REC_APPROVED = 2;
  REC_DENIED = 3;
}

message Recommendation {
  string id = 1;
  CompactApplication application = 2;
  inventorydata.Resource resource = 3;

  string label_key = 4;
  string label_value = 5;

  RecommendationStatus status = 6;
  google.protobuf.Timestamp recommended_at = 7;

  optional google.protobuf.Timestamp status_changed_at = 8;
  optional string status_changed_by = 9;

  string reason = 10;

  optional LabelObj conflict = 11;
}

message ListRecommendationsRequest {
  optional uint32 max_results = 1 [(validate.rules).uint32.lte = 500];
  bool with_total_count = 2;
  string page_token = 3;
  SortBy sort_by = 4;

  // if set, only return recommendations for resources in an application (if true) or not in one (if false)
  optional bool application_based = 5;

  repeated RecommendationStatus statuses = 6 [(validate.rules).repeated.unique = true];
  repeated string account_id = 7 [(validate.rules).repeated.max_items = 500];
  repeated string label_keys = 8 [(validate.rules).repeated.max_items = 500];
  repeated string label_values = 9 [(validate.rules).repeated.max_items = 500];
  google.type.Interval recommended_at_timeframe = 13;

  repeated string app_label_ids = 10 [(validate.rules).repeated = {
    unique: true
    max_items: 500
  }];
  repeated string deployment_label_ids = 11 [(validate.rules).repeated = {
    unique: true
    max_items: 500
  }];
  repeated string resource_ids = 12 [(validate.rules).repeated = {
    unique: true
    max_items: 500
    items: {
      string: {
        uuid: true
      }
    }
  }];
  repeated string ids = 14 [(validate.rules).repeated = {
    unique: true
    max_items: 500
    items: {
      string: {
        uuid: true
      }
    }
  }];
  repeated string resource_names = 15 [(validate.rules).repeated = {
    unique: true
    max_items: 500
  }];
}

message ListRecommendationsResponse {
  repeated Recommendation recommendations = 1;

  uint32 total_count = 2;
  uint32 matched_count = 3;
  string next_token = 4;
  string prev_token = 5;
}

message BulkOperationResult {
  string id = 1;
  bool success = 2;
}

message BulkUpdateRecommendationsRequest {
  repeated string recommendation_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 1000
    unique: true
    items: {
      string: {
        uuid: true
      }
    }
  }];
  RecommendationStatus status = 2;
}

message BulkUpdateRecommendationsResponse {
  repeated BulkOperationResult results = 1;
}

message ListRecommendationCountsRequest {}

message ListRecommendationCountsResponse {
  message RecommendationCount {
    RecommendationStatus status = 1;
    uint32 count = 2;
  }

  repeated RecommendationCount application_based_counts = 1;
  repeated RecommendationCount non_application_based_counts = 2;
}

message RecommendationAutocompleteRequest {
  string query = 1;
  optional uint32 max_results = 2 [(validate.rules).uint32.lte = 100];
}

message RecommendationAutocompleteResponse {
  message KV {
    string key = 1;
    string value = 2;
  }

  repeated KV recommended_labels = 1;
}

//
// Application Definitions API
//

message ApplicationDefinition {
	string id = 1;

	LabelObj app_label = 2;
	optional string description = 4;

	repeated labelingdata.Tag tags = 5;
	DiscoveryState discovery_state = 9;

  repeated ApplicationDefinitionDeployment deployments = 10;

	google.protobuf.Timestamp created_at = 11;
	google.protobuf.Timestamp updated_at = 12;

  message CloudResource {
    string csp_id = 1;
    string cloud = 2;
  }
  repeated CloudResource networks = 15;
  repeated CloudResource subnets = 16;

  reserved 13, 14;

  User created_by = 17;
  User updated_by = 18;

  repeated Metadata accounts = 19;

  string rule_id = 20;

  bool auto_approve = 21;

  string source = 22;

  repeated labelingdata.TagSet tag_sets = 23;
}

message ApplicationDefinitionDeployment {
  string id = 1;
  LabelObj env_label = 2;
  bool is_approved = 3;
  bool is_pending_approval = 16;

  repeated Metadata accounts = 4;

  google.protobuf.Timestamp last_approved_at = 6;
  User last_approved_by = 7;

  google.protobuf.Timestamp created_at = 8;
	google.protobuf.Timestamp updated_at = 9;
  User created_by = 10;
	User updated_by = 11;

  repeated string approved_resource_ids = 12;
  repeated string pending_resource_ids = 14;
  repeated LabelObj approved_associated_labels = 13;
  repeated LabelObj pending_associated_labels = 15;

  string source = 17;

  reserved 5;
}

message ListApplicationDefinitionsRequest {
  optional int32 max_results = 1;
  string rule_id = 2;
}

message ListApplicationDefinitionsResponse {
  repeated ApplicationDefinition application_definitions = 1;
}

message GetApplicationDefinitionRequest {
  string id = 1 [(validate.rules).string.uuid = true];
}

message GetApplicationDefinitionResponse {
  reserved 1;
  Definition application_definition = 2;
}

message CreateApplicationDefinitionRequest {
  string app_name = 1;
  optional string description = 3;
  repeated labelingdata.Tag tags = 4;
  repeated string network_ids = 5;
  repeated string subnet_ids = 6;
  repeated string account_ids = 7;
  bool auto_approve = 8;
}

message CreateApplicationDefinitionResponse {
  reserved 1;

  Definition application_definition = 2;
}

message UpdateApplicationDefinitionRequest {
  string id = 1 [(validate.rules).string.uuid = true];

  optional string app_name = 2;
  optional string description = 3;
  optional UpdateTags tags = 4;
  optional UpdateStrings network_ids = 5;
  optional UpdateStrings subnet_ids = 6;
  optional UpdateStrings account_ids = 7;
  optional bool auto_approve = 8;
}

message UpdateApplicationDefinitionResponse {
  reserved 1;

  Definition application_definition = 2;
}

message BulkApproveDefinitionDeploymentsRequest {
  string id = 1 [(validate.rules).string.uuid = true];
  repeated string application_deployment_ids = 2 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      string: {
        uuid: true
      }
    }
  }];
}

message BulkApproveDefinitionDeploymentsResponse {
  message ApprovalFailure {
    string id = 1;
    string error_message = 2;
  }

	repeated string success = 3;
  repeated ApprovalFailure failed = 4;
}

message DeleteApplicationDefinitionRequest {
  string id = 1 [(validate.rules).string.uuid = true];
}

message DeleteApplicationDefinitionDeploymentRequest {
  string id = 1 [(validate.rules).string.uuid = true];
  string application_deployment_id = 2 [(validate.rules).string.uuid = true];
  string version = 3 [(validate.rules).string = {in: ["draft", "approved"]}];
}

message BulkDeleteApplicationDefinitionsRequest {
  repeated string ids = 1 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      string: {
        uuid: true
      }
    }
  }];
}

message BulkDeleteApplicationDefinitionsResponse {
  message DeleteResult {
    string id = 1;
    bool success = 2;
    string error_message = 3;
  }

  repeated DeleteResult results = 1;
}

//
// Applications API
//

message Application {
  string id = 1;
  string name = 2;
  LabelObj app_label = 3;

  repeated ApplicationInstanceDeployment deployments = 4;

  string all_env_ruleset_href = 5;

  google.protobuf.Timestamp created_at = 8;
	google.protobuf.Timestamp updated_at = 9;
  User created_by = 10;
	User updated_by = 11;
}

message ApplicationInstanceDeployment {
  string id = 1;
  LabelObj env_label = 2;

  repeated Metadata accounts = 4;
  string ruleset_href = 6;

  google.protobuf.Timestamp last_approved_at = 7;
  User last_approved_by = 8;

  google.protobuf.Timestamp created_at = 9;
	google.protobuf.Timestamp updated_at = 10;
  User created_by = 11;
	User updated_by = 12;

  repeated string resource_ids = 13;
  repeated LabelObj associated_labels = 14;

  reserved 5;
}

message ListApplicationsResponse {
  repeated Application applications = 1;
}

message GetApplicationRequest {
  string id = 1 [(validate.rules).string.uuid = true];
  // If GetApplication is called with an application_id, and include_stats is set to true,
  // we return the stats for that specific application
  // If GetApplication is called with an application_definition_id, and include_stats is set to true,
  // we return the stats for all applications created by that definition
  bool include_stats = 2;
}

message GetApplicationResponse {
  reserved 1;

  CompactApplication application = 2;
}

// definitions are defined via metadata (can be thought of as a "Class")
// applications are app x env (can be though of as an "Instance")
message Definition {
  message Application {
    string id = 1;
    string deployment_id = 2;
    string deployment_name = 3;
    bool pending = 4;
    bool approved = 5;
    uint32 pending_resource_count = 6;
    uint32 approved_resource_count = 7;
    repeated LabelObj associated_labels = 8;
    google.protobuf.Timestamp last_approved_at = 9;
    string last_approved_by = 10;
  }

  string id = 1;
  string name = 2;
  string description = 3;
  bool approved = 4;
  DiscoveryState discovery_state = 5;
  bool auto_approval_enabled = 6;
  string rule_id = 7;
  // discovered applications based on definition's metadata
  repeated Application applications = 8;
  google.protobuf.Timestamp created_at = 13;
  google.protobuf.Timestamp updated_at = 14;
  string created_by = 15;
  string updated_by = 16;

  // only set if GetApplicationDefinitionsRequest.expanded == true
  repeated labelingdata.Tag tags = 9;
  repeated Metadata accounts = 10;
  repeated ApplicationDefinition.CloudResource networks = 11;
  repeated ApplicationDefinition.CloudResource subnets = 12;
  string source = 17;
  repeated labelingdata.TagSet tag_sets = 18;
}

message GetApplicationDefinitionsRequest {
  optional uint32 max_results = 1 [(validate.rules).uint32.lte = 1000];
  bool with_total_count = 2;
  bool expanded = 3;
  string page_token = 13;
  SortBy sort_by = 14;

  repeated string ids = 4 [(validate.rules).repeated.max_items = 1000];
  repeated string app_label_ids = 5 [(validate.rules).repeated.max_items = 1000];
  repeated string deployment_ids = 6 [(validate.rules).repeated.max_items = 1000];
  repeated string deployment_label_ids = 7 [(validate.rules).repeated.max_items = 1000];
  repeated string account_ids = 8 [(validate.rules).repeated.max_items = 1000];
  repeated string resource_ids = 9 [(validate.rules).repeated.max_items = 1000];
  repeated string associated_label_ids = 10 [(validate.rules).repeated.max_items = 1000];
  repeated string rule_ids = 11 [(validate.rules).repeated.max_items = 1000];
  optional bool approved = 12;
  repeated string application_ids = 15 [(validate.rules).repeated.max_items = 1000];
  repeated string sources = 16 [(validate.rules).repeated = {unique: true, items: {string: {in: ["CS", "AWS CodeDeploy"]}}}];
}

message GetApplicationDefinitionsResponse {
  repeated Definition application_definitions = 1;
  uint32 total_count = 2;
  uint32 matched_count = 3;
  string next_token = 4;
  string prev_token = 5;
}

// Applications can be created either directly via application definitions or via discovery rules
enum ApplicationCreationTypes {
  UNKNOWN = 0;
  APPLICATION_DEFINITION = 1;
  DISCOVERY_RULE = 2;
}

// Stores information about how an application was created.
// In case of application definitions, it stores the application definition id and name.
// In case of discovery rules, it stores the discovery rule id and name.
message ApplicationCreatedVia {
  string id = 1;
  string name = 2;
  User created_by = 5;
  bool auto_approval_setting = 3;
  ApplicationCreationTypes type = 4;
}

message RoleCount {
  string label_id = 1;
  uint32 security_control_count = 2;
  uint32 resource_count = 3;
}

message ResourceCategoryStats {
  string label_id = 2;
  map<string, RoleCount> role_count = 1;
}

message ApplicationStats {
  string deployment_id = 1;
  string deployment_name = 2;
  uint32 resource_count = 3;
  ApplicationCreatedVia created_via = 4;
  map<string, ResourceCategoryStats> resource_map = 5;
  uint64 total_security_control_count = 6;
}

message CompactApplication {
  string id = 1;
  string name = 2;
  string deployment_id = 3;
  string deployment_name = 4;
  repeated Metadata accounts = 5;
  uint32 resource_count = 6;
  repeated LabelObj associated_labels = 7;
  string ruleset_href = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  string owner = 12;

  // Fields are only set if the include_stats flag is set to true in GetApplicationRequest
  // Points To Note:
  //   1. stats is an array and not just a single ApplicationStatus instance
  //   2. Application stats structure duplicate fields deployment_id, deployment_name and resource_count
  // Reason:
  // When requesting stats for all deployments using application_definition_id, we want to send an array
  // of just the ApplicationStats structure, one for each deployment instead of an array of CompactApplications
  repeated ApplicationStats stats = 11;
}

message GetApplicationsRequest {
  optional uint32 max_results = 1 [(validate.rules).uint32.lte = 1000];
  bool with_total_count = 2;
  string page_token = 10;
  SortBy sort_by = 11;

  repeated string ids = 3 [(validate.rules).repeated.max_items = 1000];
  repeated string app_label_ids = 4 [(validate.rules).repeated.max_items = 1000];
  repeated string deployment_ids = 5 [(validate.rules).repeated.max_items = 1000];
  repeated string deployment_label_ids = 6 [(validate.rules).repeated.max_items = 1000];
  repeated string account_ids = 7 [(validate.rules).repeated.max_items = 1000];
  repeated string resource_ids = 8 [(validate.rules).repeated.max_items = 1000];
  repeated string associated_label_ids = 9 [(validate.rules).repeated.max_items = 1000];
}

message GetApplicationsResponse {
  repeated CompactApplication applications = 1;
  uint32 total_count = 2;
  string next_page_token = 3;
  string prev_page_token = 4;
}

// used to get siblings for a given application, e.g. for Payments:Prod, need to return values
// Payments:Dev, Payments:Stage, etc.. to populate the UI dropdown
message GetApplicationSiblingsRequest {
  string id = 1[(validate.rules).string.uuid = true];
}

message GetApplicationSiblingsResponse {
  message Sibling {
    string id = 1;
    string name = 2;
    // Workaround for UI bug where refreshing on AllDeployments page would lead to an error
    // because the application id is not an actual application deployment which would lead to the GET request failing
    string application_name = 4;
    string ruleset_href = 3;
  }

  repeated Sibling siblings = 1;
}

//
message ApplicationFilter {
  string id = 1 [(validate.rules).string.uuid = true];
  bool draft = 2;
}

message GetApplicationResourcesRequest {
  repeated label.ApplicationFilter application_filter = 1;
}

message GetApplicationResourcesResponse {
  repeated string resource_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    unique: true
  }];
}

//
// Tags to Label API
//

message TagKey {
  string cloud = 1;
  string key = 2;
}

message DisplayInfo {
  optional string initial = 1;
  optional string icon = 2;
  optional string background_color = 3;
  optional string foreground_color = 4;
  optional int32  sort_ordinal = 5;
  optional string display_name_plural = 6;
}

message LabelDimension {
  string id = 1;
  string href = 2;
  string key = 3;
  string display_name = 4;
  optional DisplayInfo display_info = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  User created_by = 8;
  User updated_by = 9;
}

message TagsToLabel {
  string id = 1;

  repeated TagKey tag_keys = 2;
  LabelDimension label_dimension = 3;

  google.protobuf.Timestamp created_at = 4;
	google.protobuf.Timestamp updated_at = 5;
	string created_by = 6;
	string updated_by = 7;
}

message TagsToLabelFilter {
  repeated TagKey tag_keys = 1;
  repeated string label_types = 2;
}

message ListTagsToLabelRequest {
  TagsToLabelFilter filter = 1;

  optional uint32 max_results = 2 [(validate.rules).uint32.lte = 1000];
  bool with_total_count = 3;
  string page_token = 4;
  SortBy sort_by = 5;
}

message ListTagsToLabelResponse {
  repeated TagsToLabel tags_to_labels = 1;

  uint32 total_count = 2;
  uint32 matched_count = 3;
  string next_token = 4;
  string prev_token = 5;
}

message GetTagsToLabelRequest {
  string id = 1 [(validate.rules).string.uuid = true];
}

message GetTagsToLabelResponse {
  TagsToLabel tags_to_label = 1;
}

message CreateTagsToLabelRequest {
  repeated TagKey tag_keys = 1 [(validate.rules).repeated.min_items = 1];

  reserved 2, 3;

  string key = 4;
  string display_name = 5;
  optional DisplayInfo display_info = 6;
}

message CreateTagsToLabelResponse {
  TagsToLabel tags_to_label = 1;
}

message UpdateTagsToLabelRequest {
  message UpdateTagKeys {
    repeated TagKey tag_keys = 1 [(validate.rules).repeated.min_items = 1];
  }

  string id = 2 [(validate.rules).string.uuid = true];

  optional UpdateTagKeys tag_keys = 3;

  reserved 4, 5;

  string key = 6;
  string display_name = 7;
  optional DisplayInfo display_info = 8;
}

message UpdateTagsToLabelResponse {
  TagsToLabel tags_to_label = 1;
}

message DeleteTagsToLabelRequest {
  string id = 1 [(validate.rules).string.uuid = true];
}

enum RuleState {
  RULE_STATE_UNAVAILABLE = 0;
  RULE_STATE_SYNCING = 1;
  RULE_STATE_SYNCED = 2;
  RULE_STATE_FAILED = 3;
  RULE_STATE_QUEUED = 4;
}

message DiscoveryRule {
  string id = 1;
  string name = 2;
  string prefix = 3;
  labelingdata.RuleType rule_type = 4;
  repeated TagKey tag_keys = 5;
  repeated string clouds = 6;
  RuleState rule_state = 7;

  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  User created_by = 10;
  User updated_by = 11;

  bool auto_approve_applications = 12;
}

message CreateDiscoveryRuleRequest {
  string name = 1 [(validate.rules).string.min_len = 1];
  string prefix = 2;
  labelingdata.RuleType rule_type = 3;
  repeated TagKey tag_keys = 4;
  repeated string clouds = 5;
  bool auto_approve_applications = 6;
}

message CreateDiscoveryRuleResponse {
  DiscoveryRule discovery_rule = 1;
}

message ListDiscoveryRulesRequest {
  repeated string names = 1[(validate.rules).repeated = {unique: true, max_items: 500}];
  repeated string prefixes = 2[(validate.rules).repeated = {unique: true, max_items: 500}];
  repeated labelingdata.RuleType rule_types = 3[(validate.rules).repeated.unique = true];
  repeated TagKey tag_keys = 4[(validate.rules).repeated.max_items = 500];

  SortBy sort_by = 5;
  optional uint32 max_results = 6 [(validate.rules).uint32.lte = 1000];
  bool with_total_count = 7;
  string page_token = 8;
}

message ListDiscoveryRulesResponse {
  repeated DiscoveryRule discovery_rules = 1;

  uint32 total_count = 2;
  uint32 matched_count = 3;
  string next_token = 4;
  string prev_token = 5;
}

message GetDiscoveryRuleRequest {
  string id = 1 [(validate.rules).string.uuid = true];
}

message GetDiscoveryRuleResponse {
  DiscoveryRule discovery_rule = 1;
}

message UpdateDiscoveryRuleRequest {
  string id = 1;
  string name = 2;
  optional string prefix = 3;
  optional labelingdata.RuleType rule_type = 4;
  repeated TagKey tag_keys = 5;
  repeated string clouds = 6;
  optional bool auto_approve_applications = 7;
}

message UpdateDiscoveryRuleResponse {
  DiscoveryRule discovery_rule = 1;
}

// When sorting is implemented, add all sortable columns here
message SortBy {
  enum Field {
      CREATED_AT = 0;
      APPLICATION_NAME = 1;
      APPROVAL_STATUS = 2;
      LABEL_KEY = 3;
      LABEL_VALUE = 4;
      RULE_NAME = 5;
  }
  Field field = 1;
  bool asc = 3;

  reserved 2;
}

message DeleteDiscoveryRuleRequest {
  string id = 1 [(validate.rules).string.uuid = true];
}

message ListSystemLabelsRequest {
}

message LabelAutocompleteRequest {
  string query = 1;
  optional uint32 max_results = 2 [(validate.rules).uint32.lte = 1000];
  repeated string keys = 3 [(validate.rules).repeated.unique = true];
  repeated string exclude_keys = 4 [(validate.rules).repeated.unique = true];
}

message LabelAutocompleteResponse {
  repeated LabelObj labels = 1;
  uint32 total_count = 2;
}


message ListSystemLabelsResponse {
  message SystemLabelMapping {
    message SysLabelObj {
      string key = 1;
      string value = 2;
      string cloud = 3;
      string id = 5;
    }
    LabelObj service_category = 1;
    repeated SysLabelObj service_roles = 2;
  }
  repeated SystemLabelMapping system_labels = 1;
}

message ListLabelDimensionsRequest {
    optional string key = 1;
    optional string display_name = 2;
    optional uint32 max_results = 3;
}

message ListLabelDimensionsResponse {
    repeated LabelDimension label_dimensions = 1;
}

message ListLabelsRequest {
    optional string key = 1;
    optional string value = 2;
    optional uint32 max_results = 3;
}

message ListLabelsResponse {
    repeated LabelObj labels = 1;
}

message DiscoveryRuleFilterMetadataParams {
  enum FilterMetadataType {
    UNKNOWN = 0;
    NAME = 1;
    PREFIX = 2;
    TYPE = 3;
    TAG_KEY = 4;
  }
  FilterMetadataType type = 1;
  DiscoveryRuleFilters filters = 2;
}

message TagsToLabelFilterMetadataParams {
  enum FilterMetadataType {
    UNKNOWN = 0;
    TAG_KEY = 1;
    LABEL_TYPE = 2;
  }
  FilterMetadataType type = 1;
  TagsToLabelFilter filters = 2;
}

message DiscoveryRuleFilters {
  repeated string names = 1;
  repeated string prefixes = 2;
  repeated labelingdata.RuleType types = 3;
  repeated TagKey tag_keys = 4;
}

message GetFilterMetadataRequest {
  uint32 max_results = 1 [(validate.rules).uint32.lt = 100];

  // optional filters for context-aware search
  optional string partial_filter = 2;
  oneof params {
    DiscoveryRuleFilterMetadataParams discovery_rule_params = 3;
    TagsToLabelFilterMetadataParams tags_to_label_params = 4;
  }
}

message GetFilterMetadataResponse {
  google.protobuf.Value values = 1;
}

// Temporary fix to have multiple proto files.
//TODO: remove after its fixed.
service MockApplications {}
