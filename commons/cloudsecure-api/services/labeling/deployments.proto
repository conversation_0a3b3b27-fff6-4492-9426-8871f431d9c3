// Copyright 2023 Illumio, Inc. All Rights Reserved.
syntax = "proto3";

package labeling;

import "labeling/actors.proto";
import "labeling/applications.proto";
import "labeling/label_mappings.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "common/version/version.proto";
import "common/protoc-gen-openapiv2/options/annotations.proto";
import "common/labeling/labeling.proto";

option go_package = "illum.io/cloud/api/generated/labeling";

// TODO: Iron out - this is temporary
message DiscoveryTask {
	string name = 1;
}

message Deployment {
	string id = 1;

	LabelObj env_label = 2;
	optional string description = 3;

	google.protobuf.Timestamp created_at = 5;
	google.protobuf.Timestamp updated_at = 6;
	string created_by = 7;
	string updated_by = 8;

	repeated Metadata accounts = 9;
	repeated labelingdata.Tag tags = 10;
	repeated Metadata regions = 11;
	repeated Metadata availability_zones = 12;
	repeated Metadata vnets = 13;
	repeated Metadata subnets = 14;

	// TODO: Rename this to something more accurate/understandable
	// This flag is set when client wants to create a deployment specifically for some set of applications
	// In this case, the deployment will not be discovered when new applications are created/updated and it will
	// simply live on the application definitions it is currently attached to.
	bool custom = 15;

	string source = 16;

	repeated labelingdata.TagSet tag_sets = 17;
}

message ListDeploymentsRequest {
	optional int32 max_results = 1;
}

message ListDeploymentsResponse {
	repeated Deployment deployments = 1;
}

message GetDeploymentRequest {
	string id = 1 [(validate.rules).string.uuid = true];
}

message GetDeploymentResponse {
	Deployment deployment = 1;
}

message CreateDeploymentRequest {
	optional string description = 2;

	repeated Metadata accounts = 3;
	repeated labelingdata.Tag tags = 4;
	repeated Metadata regions = 5;
	repeated Metadata availability_zones = 6;
	repeated Metadata vnets = 7;
	repeated Metadata subnets = 8;

	// TODO: If this flag it set, it should have app ids associated since the deployment will be specific to some applications.
	// Figure out how this request needs to change based on the above.
	bool custom = 9;

	reserved 1;

	string name = 10;
	repeated labelingdata.TagSet tag_sets = 11;
}

message CreateDeploymentResponse {
	Deployment deployment = 1;
}

message UpdateDeploymentRequest {
	string id = 1 [(validate.rules).string.uuid = true];

	optional string description = 3;

	// Using wrapper fields to distinguish between empty array ([]) and null (no array passed at all)
	optional UpdateMetadata accounts = 4;
	optional UpdateTags tags = 5;
	optional UpdateMetadata regions = 6;
	optional UpdateMetadata availability_zones = 7;
	optional UpdateMetadata vnets = 8;
	optional UpdateMetadata subnets = 9;
	optional bool custom = 10;

	string name = 11;
}

message UpdateDeploymentResponse {
	Deployment deployment = 1;
}

message DeleteDeploymentRequest {
	string id = 1 [(validate.rules).string.uuid = true];
}

message DumpVisibilityOnlyRequest {}

message DumpVisibilityOnlyResponse {
	map<string, bool> visibility_only_tenants = 1;
}

message SetVisibilityOnlyRequest {
	string tenant_id = 1 [(validate.rules).string.uuid = true];
	bool enabled = 2;
}

message SetVisibilityOnlyResponse {
}

message DeleteRTLSyncRequest {
	google.protobuf.Timestamp last_synced_at = 1;
}

message DeleteRTLSyncResponse { }

message DeleteTenantRequest {}
message DeleteTenantResponse {}

message IngestRecommendationsRequest {
	string tenant_id = 1 [(validate.rules).string.uuid = true];

	reserved 2;
}

message IngestRecommendationsResponse {
	uint32 recommendations_ingested = 1;
}

service LabelingService {
	rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
			option (google.api.http) = {
			get: "/api/v1/version"
		};
	}
  rpc GetBridgeVersion(google.protobuf.Empty) returns (version.VersionResponse);

	// Deployments
	rpc ListDeployments(ListDeploymentsRequest) returns (ListDeploymentsResponse) {
		option (google.api.http) = {
			get: "/api/v1/deployments"
		};
	}
	rpc GetDeployment(GetDeploymentRequest) returns (GetDeploymentResponse) {
		option (google.api.http) = {
			get: "/api/v1/deployments/{id}"
		};
	}
	rpc CreateDeployment(CreateDeploymentRequest) returns (CreateDeploymentResponse) {
		option (google.api.http) = {
			post: "/api/v1/deployments",
			body: "*"
		};
	}
	rpc UpdateDeployment(UpdateDeploymentRequest) returns (UpdateDeploymentResponse) {
		option (google.api.http) = {
			put: "/api/v1/deployments/{id}"
			body: "*"
		};
		option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
			responses: {
				key: "202"
				value: {
					description: "A successful response."
					schema: {}
				}
			}
		};
	}
	rpc DeleteDeployment(DeleteDeploymentRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			delete: "/api/v1/deployments/{id}"
		};
		option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
			responses: {
				key: "204"
				value: {
					description: "A successful response."
					schema: {}
				}
			}
		};
	}

	// Application Definitions
	rpc ListApplicationDefinitions(ListApplicationDefinitionsRequest) returns (ListApplicationDefinitionsResponse) {
		option (google.api.http) = {
			get: "/api/v1/applications_definitions"
		};
	}
	rpc GetApplicationDefinition(GetApplicationDefinitionRequest) returns (GetApplicationDefinitionResponse) {
		option (google.api.http) = {
			get: "/api/v1/applications_definitions/{id}"
		};
	}
	rpc CreateApplicationDefinition(CreateApplicationDefinitionRequest) returns (CreateApplicationDefinitionResponse) {
		option (google.api.http) = {
			post: "/api/v1/applications_definitions",
			body: "*"
		};
	}
	rpc UpdateApplicationDefinition(UpdateApplicationDefinitionRequest) returns (UpdateApplicationDefinitionResponse) {
		option (google.api.http) = {
			put: "/api/v1/applications_definitions/{id}",
			body: "*"
		};
		option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
			responses: {
				key: "202"
				value: {
					description: "A successful response."
					schema: {}
				}
			}
		};
	}

	rpc BulkApproveDefinitionDeployments(BulkApproveDefinitionDeploymentsRequest) returns (BulkApproveDefinitionDeploymentsResponse) {
		option (google.api.http) = {
			put: "/api/v1/applications_definitions/{id}/deployments/bulk_approve"
			body: "*"
		};
		option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
			responses: {
				key: "202"
				value: {
					description: "A successful response."
					schema: {}
				}
			}
		};
	}
	rpc DeleteApplicationDefinition(DeleteApplicationDefinitionRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			delete: "/api/v1/applications_definitions/{id}"
		};
		option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
			responses: {
				key: "202"
				value: {
					description: "A successful response."
					schema: {}
				}
			}
		};
	}
	rpc BulkDeleteApplicationDefinitions(BulkDeleteApplicationDefinitionsRequest) returns (BulkDeleteApplicationDefinitionsResponse) {
		option (google.api.http) = {
			post: "/api/v1/applications_definitions/bulk_delete"
			body: "*"
		};
		option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
			responses: {
				key: "202"
				value: {
					description: "Deleted application definitions, deleting policy objects asynchronously."
					schema: {}
				}
			}
		};
	}
	rpc DeleteApplicationDefinitionDeployment(DeleteApplicationDefinitionDeploymentRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			delete: "/api/v1/applications_definitions/{id}/deployments/{application_deployment_id}/{version}"
		};
		option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
			responses: {
				key: "202"
				value: {
					description: "A successful response."
					schema: {}
				}
			}
		};
	}
	// V2 of ListApplicationDefinitions (to be deprecated)
	rpc GetApplicationDefinitions(GetApplicationDefinitionsRequest) returns (GetApplicationDefinitionsResponse) {
		option (google.api.http) = {
			post: "/api/v1/application_definitions",
			body: "*"
		};
	}

	// Applications
	rpc ListApplications(google.protobuf.Empty) returns (ListApplicationsResponse) {
		option (google.api.http) = {
			get: "/api/v1/applications"
		};
	}
	rpc GetApplication(GetApplicationRequest) returns (GetApplicationResponse) {
		option (google.api.http) = {
			get: "/api/v1/applications/{id}"
		};
	}
	// V2 of ListApplications (to be deprecated)
	rpc GetApplications(GetApplicationsRequest) returns (GetApplicationsResponse) {
		option (google.api.http) = {
			post: "/api/v1/applications",
			body: "*"
		};
	}
	// used to get siblings for a given application, e.g. for Payments:Prod, need to return values Payments:Dev, Payments:Stage, etc..
	rpc GetApplicationSiblings(GetApplicationSiblingsRequest) returns (GetApplicationSiblingsResponse) {
		option (google.api.http) = {
			get: "/api/v1/applications/{id}/siblings"
		};
	}
	rpc GetApplicationResources(GetApplicationResourcesRequest) returns (GetApplicationResourcesResponse);

	// Tag to Label Mappings
	rpc ListTagsToLabel(ListTagsToLabelRequest) returns (ListTagsToLabelResponse) {
		option (google.api.http) = {
			get: "/api/v1/tags_to_label"
			additional_bindings {
				post: "/api/v1/tags_to_label/list",
				body: "*"
			}
		};
	}
	rpc GetTagsToLabel(GetTagsToLabelRequest) returns (GetTagsToLabelResponse) {
		option (google.api.http) = {
			get: "/api/v1/tags_to_label/{id}"
		};
	}
	rpc CreateTagsToLabel(CreateTagsToLabelRequest) returns (CreateTagsToLabelResponse) {
		option (google.api.http) = {
			post: "/api/v1/tags_to_label",
			body: "*"
		};
	}
	rpc UpdateTagsToLabel(UpdateTagsToLabelRequest) returns (UpdateTagsToLabelResponse) {
		option (google.api.http) = {
			put: "/api/v1/tags_to_label/{id}"
			body: "*"
		};
		option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
			responses: {
				key: "202"
				value: {
					description: "A successful response."
					schema: {}
				}
			}
		};
	}
	rpc DeleteTagsToLabel(DeleteTagsToLabelRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
      delete: "/api/v1/tags_to_label/{id}"
    };
		option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
			responses: {
				key: "204"
				value: {
					description: "A successful response."
					schema: {}
				}
			}
		};
	}

	// Label Mappings
	rpc DumpResourcesToLabels(DumpResourcesToLabelsRequest) returns (DumpResourcesToLabelsResponse);

	rpc ListLabelsToResources(ListLabelsToResourcesRequest) returns (ListLabelsToResourcesResponse) {
		option (google.api.http) = {
			post: "/api/v1/label_mappings/resources"
			body: "*"
		};
	}
	rpc ListResourcesToLabels(ListResourcesToLabelsRequest) returns (ListResourcesToLabelsResponse) {
		option (google.api.http) = {
			post: "/api/v1/label_mappings/labels"
			body: "*"
		};
	}
	rpc AssignLabelsToResources(AssignLabelsToResourcesRequest) returns (AssignLabelsToResourcesResponse) {
		// no REST interface for now - only for internal use
	}
	rpc DeleteLabelsToResources(DeleteLabelsToResourcesRequest) returns (DeleteLabelsToResourcesResponse) {
		// no REST interface for now - only for internal use
	}

	// Discovery Rules
	rpc ListDiscoveryRules(ListDiscoveryRulesRequest) returns (ListDiscoveryRulesResponse) {
		option (google.api.http) = {
			get: "/api/v1/discovery_rules"
		};
	}
	rpc GetDiscoveryRule(GetDiscoveryRuleRequest) returns (GetDiscoveryRuleResponse) {
		option (google.api.http) = {
			get: "/api/v1/discovery_rules/{id}"
		};
	}
	rpc CreateDiscoveryRule(CreateDiscoveryRuleRequest) returns (CreateDiscoveryRuleResponse) {
		option (google.api.http) = {
			post: "/api/v1/discovery_rules",
			body: "*"
		};
	}
	rpc UpdateDiscoveryRule(UpdateDiscoveryRuleRequest) returns (UpdateDiscoveryRuleResponse) {
		option (google.api.http) = {
			put: "/api/v1/discovery_rules/{id}",
			body: "*"
		};
		option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
			responses: {
				key: "202"
				value: {
					description: "A successful response."
					schema: {}
				}
			}
		};
	}
	rpc DeleteDiscoveryRule(DeleteDiscoveryRuleRequest) returns (google.protobuf.Empty) {
		option (google.api.http) = {
			delete: "/api/v1/discovery_rules/{id}"
		};
		option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
			responses: {
				key: "204"
				value: {
					description: "A successful response."
					schema: {}
				}
			}
		};
	}
	rpc ListSystemLabels(ListSystemLabelsRequest) returns (ListSystemLabelsResponse) {
		option (google.api.http) = {
			get: "/api/v1/system_labels"
		};
	}

	// API for providing autocomplete suggestions for label names
	// The API does a substring match for the provided query string and returns the results
	//
	// Request:
	// - query: The query string to match against the label names
	// - max_results: The maximum number of results to return
	// - keys: The list of keys to be included in the results
	// - exclude_keys: The list of keys to be excluded from the results
	//
	// Response:
	// - labels: The list of labels that match the query string
	// - total_count: The total number of results that match the query string
	rpc LabelsAutoComplete(LabelAutocompleteRequest) returns (LabelAutocompleteResponse) {}

	rpc ListLabels(ListLabelsRequest) returns (ListLabelsResponse) {
		option (google.api.http) = {
			get: "/api/v1/labeling/labels"
		};
	}

	rpc ListLabelDimensions(ListLabelDimensionsRequest) returns (ListLabelDimensionsResponse) {
		option (google.api.http) = {
			get: "/api/v1/labeling/label_dimensions"
		};
	}

	rpc ListRecommendations(ListRecommendationsRequest) returns (ListRecommendationsResponse) {
		option (google.api.http) = {
			post: "/api/v1/recommendations"
			body: "*"
		};
	}
	rpc BulkUpdateRecommendations(BulkUpdateRecommendationsRequest) returns (BulkUpdateRecommendationsResponse) {
		option (google.api.http) = {
			put: "/api/v1/recommendations/bulk_update"
			body: "*"
		};
	}
	rpc ListRecommendationCounts(ListRecommendationCountsRequest) returns (ListRecommendationCountsResponse) {
		option (google.api.http) = {
			get: "/api/v1/recommendations/counts"
		};
	}
	rpc RecommendationAutocomplete(RecommendationAutocompleteRequest) returns (RecommendationAutocompleteResponse) {
		option (google.api.http) = {
			get: "/api/v1/recommendations/autocomplete"
		};
	}

	rpc DumpVisibilityOnly(DumpVisibilityOnlyRequest) returns (DumpVisibilityOnlyResponse) {}
	rpc SetVisibilityOnly(SetVisibilityOnlyRequest) returns (SetVisibilityOnlyResponse) {}
	rpc DeleteRTLSyncEntries(DeleteRTLSyncRequest) returns (DeleteRTLSyncResponse) {}


	rpc DeleteTenant(DeleteTenantRequest) returns (DeleteTenantResponse) {}

	rpc IngestRecommendations(IngestRecommendationsRequest) returns (IngestRecommendationsResponse) {}

	rpc GetFilterMetadata(GetFilterMetadataRequest) returns (GetFilterMetadataResponse) {
		option (google.api.http) = {
			post: "/api/v1/labeling/metadata"
			body: "*"
		};
	}

	rpc SyncWithControlPlane(stream labelingdata.StreamMessage) returns (stream labelingdata.StreamMessage);
}