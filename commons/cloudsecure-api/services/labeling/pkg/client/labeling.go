package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	labelingpb "illum.io/cloud/api/generated/labeling"
	"illum.io/cloud/api/services/labeling/pkg/labeling"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type LabelingClient struct {
	labelingpb.LabelingServiceClient
	con *grpc.ClientConn
}

func (c *LabelingClient) Close() {
	_ = c.con.Close()
}

// NewLabelingClient creates a new gRPC client for service running on url endpoint
func NewLabelingClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*LabelingClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := labelingpb.NewLabelingServiceClient(con)

	return &LabelingClient{
		con:                   con,
		LabelingServiceClient: client,
	}, nil
}

// NewLabelingClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewLabelingClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*LabelingClient, error) {
	url, ok := config.LookupGRPCService(labeling.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", labeling.ServiceName)
	}
	return NewLabelingClient(ctx, url, opts...)
}
