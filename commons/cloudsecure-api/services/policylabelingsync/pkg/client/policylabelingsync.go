package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	policylabelingsyncpb "illum.io/cloud/api/generated/policylabelingsync"
	"illum.io/cloud/api/services/policylabelingsync/pkg/policylabelingsync"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type PolicylabelingsyncClient struct {
	policylabelingsyncpb.PolicylabelingsyncServiceClient
	con *grpc.ClientConn
}

func (c *PolicylabelingsyncClient) Close() {
	_ = c.con.Close()
}

// NewPolicylabelingsyncClient creates a new gRPC client for service running on url endpoint
func NewPolicylabelingsyncClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*PolicylabelingsyncClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := policylabelingsyncpb.NewPolicylabelingsyncServiceClient(con)

	return &PolicylabelingsyncClient{
		con:                             con,
		PolicylabelingsyncServiceClient: client,
	}, nil
}

// NewPolicylabelingsyncClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewPolicylabelingsyncClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*PolicylabelingsyncClient, error) {
	url, ok := config.LookupGRPCService(policylabelingsync.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", policylabelingsync.ServiceName)
	}
	return NewPolicylabelingsyncClient(ctx, url, opts...)
}
