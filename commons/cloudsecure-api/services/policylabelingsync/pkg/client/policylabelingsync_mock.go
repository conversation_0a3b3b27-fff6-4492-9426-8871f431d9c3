package client

import (
	"go.uber.org/mock/gomock"

	"illum.io/cloud/api/generated/policylabelingsync/mock"
)

// NewMockPolicylabelingsyncClient creates a new mock client
func NewMockPolicylabelingsyncClient(ctrl *gomock.Controller) (*PolicylabelingsyncClient, *mock.MockPolicylabelingsyncServiceClient) {
	m := mock.NewMockPolicylabelingsyncServiceClient(ctrl)
	return &PolicylabelingsyncClient{
		PolicylabelingsyncServiceClient: m,
	}, m
}
