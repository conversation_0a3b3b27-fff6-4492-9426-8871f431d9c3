// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package policylabelingsync;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/api/generated/policylabelingsync";

message DeleteTenantRequest {
  string tenant_id = 1 [(validate.rules).string.uuid = true];
}

message DeleteTenantResponse {
}

message DisableTenantRequest {
  string tenant_id = 1 [(validate.rules).string.uuid = true];
}

message DisableTenantResponse {
}

service PolicylabelingsyncService {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
        get: "/api/v1/version"
      };
    }

    rpc DeleteTenant(DeleteTenantRequest) returns (DeleteTenantResponse) {}
    rpc DisableTenant(DisableTenantRequest) returns (DisableTenantResponse) {}
}
