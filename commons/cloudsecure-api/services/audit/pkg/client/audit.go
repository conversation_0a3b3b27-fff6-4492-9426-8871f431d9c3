/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	auditpb "illum.io/cloud/api/generated/audit"
	"illum.io/cloud/api/services/audit/pkg/audit"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type AuditClient struct {
	auditpb.AuditServiceClient
	con *grpc.ClientConn
}

func (c *AuditClient) Close() {
	_ = c.con.Close()
}

// NewAuditClient creates a new gRPC client for service running on url endpoint
func NewAuditClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*AuditClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := auditpb.NewAuditServiceClient(con)

	return &AuditClient{
		con:                con,
		AuditServiceClient: client,
	}, nil
}

// NewAuditClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewAuditClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*AuditClient, error) {
	url, ok := config.LookupGRPCService(audit.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", audit.ServiceName)
	}
	return NewAuditClient(ctx, url, opts...)
}
