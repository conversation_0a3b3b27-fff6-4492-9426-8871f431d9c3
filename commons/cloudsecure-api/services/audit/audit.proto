// Copyright 2024 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package audit;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/type/interval.proto";
import "validate/validate.proto";
import "google/protobuf/any.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/api/generated/audit";

message AuditRecord {
  optional string id = 1;        // only to be used for the return value (setting it on Create is an error)
  optional string tenant_id = 2; // only to be used for the return value (setting it on Create is an error)
  optional string user = 3;      // only to be used for the return value (setting it on Create is an error)
  string type = 4;
  google.protobuf.Struct details = 5;
  google.protobuf.Timestamp occurred_at = 6;
  string category = 7;
  bool successful = 8;
}

message CreateAuditRecordRequest{
  AuditRecord record = 1;
}

message CreateAuditRecordResponse {
}

message GetAuditRecordRequest {
  string id = 1 [(validate.rules).string.uuid = true];
}

message GetAuditRecordResponse {
  AuditRecord record = 1;
}

message ListAuditRecordsRequest {
  optional int32 max_results = 1;
}

message ListAuditRecordsResponse {
  repeated AuditRecord records = 1;
  // total_records are used in UI to show (eg. Showing 401-450 of 15,550 records)
  uint32 total_records = 3;
  // next_token and prev_token can be traded for next and prev pages by the UI
  string next_token = 4;
  string prev_token = 5;
  int64 page_number = 6;
}

message ListSortAuditRecordsRequest {
  optional uint32 max_results = 1;
  SortBy sortby = 2;
  repeated bool successful = 3;
  repeated string categories = 4;
  repeated string types = 5;
  repeated string user_ids = 6;
  repeated string csps = 7;
  repeated string account_ids = 8;
  google.protobuf.Timestamp from_time = 9;
  google.protobuf.Timestamp to_time = 10;
  repeated string users = 11;
  // token is used for pagination.
  string token = 12;
}

message SortBy {
  enum Field {
    UNSPECIFIED = 0;
    SUCCESSFUL = 1;
    CATEGORY = 2;
    TYPE = 3;
    USER_ID = 4;
    OCCURRED_AT = 5;
    // TBD - CSP, ACCOUNTID, USER - within json
  }
  Field field = 1;
  bool asc = 2;
}

// Enum for the metadata type
enum MetadataType {
  CATEGORIES = 0;
  TYPES = 1;
  USERIDS = 2;
}

message AuditMetadataRequest {
  MetadataType metadata_type = 1;
  /* TBD
  repeated string category = 2;
  repeated string type = 3;
  repeated string user = 4;
  */
}

message AuditMetadataResponse {
  repeated string values = 1;
}

/********* System events for tenants/users ***********/

message SysEvent {
  optional string id = 1;        // only to be used for the return value (setting it on Create is an error)
  optional string tenant_id = 2; // only to be used for the return value (setting it on Create is an error)
  string message = 3 [(validate.rules).string.max_len=400];
  string type = 4;
  google.protobuf.Struct details = 5;
  google.protobuf.Timestamp first_occurred_at = 6;
  google.protobuf.Timestamp last_occurred_at = 7;
  string category = 8;
  bool successful = 9;
  uint32 count=10;
}

message CreateSysEventRequest{
  SysEvent event = 1;
}

message CreateSysEventResponse {
}

message GetSysEventRequest {
  string id = 1 [(validate.rules).string.uuid = true];
}

message GetSysEventResponse {
  SysEvent event = 1;
}

message ListSortSysEventsRequest {
  optional uint32 max_results = 1;
  SysEventSortBy sortby = 2;
  repeated bool successful = 3;
  repeated string categories = 4;
  repeated string types = 5;
  repeated string csps = 6;
  repeated string account_ids = 7;
  google.protobuf.Timestamp from_time = 8;
  google.protobuf.Timestamp to_time = 9;
  // token is used for pagination.
  string token = 10;
}

message SysEventSortBy {
  enum Field {
    UNSPECIFIED = 0;
    SUCCESSFUL = 1;
    CATEGORY = 2;
    TYPE = 3;
    LAST_OCCURRED_AT = 4;
    // TBD - CSP, ACCOUNTID - within json
  }
  Field field = 1;
  bool asc = 2;
}

message ListSortSysEventsResponse {
  repeated SysEvent events = 1;
  // total_events are used in UI to show (eg. Showing 401-450 of 15,550 events)
  uint32 total_events = 2;
  // next_token and prev_token can be traded for next and prev pages by the UI
  string next_token = 3;
  string prev_token = 4;
  int64 page_number = 5;
}

// Enum for the metadata type
enum SysEventMetadataType {
  SYSEVENT_UNSPECIFIED = 0;
  SYSEVENT_CATEGORIES = 1;
  SYSEVENT_TYPES = 2;
}

message SysEventMetadataRequest {
  SysEventMetadataType metadata_type = 1;
  /* TBD
  repeated string category = 2;
  repeated string type = 3;
  */
}

message SysEventMetadataResponse {
  repeated string values = 1;
}

message DeleteTenantDataRequest {}

message DeleteTenantDataResponse {}

/********* Service definition ***********/

service AuditService {
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
    option (google.api.http) = {
      get: "/api/v1/audit/version"
    };
  }

  rpc CreateAuditRecord(CreateAuditRecordRequest) returns (CreateAuditRecordResponse) {}

  rpc GetAuditRecord(GetAuditRecordRequest) returns (GetAuditRecordResponse) {
    option (google.api.http) = {
      get: "/api/v1/audit/records/{id}"
    };
  }

  rpc ListAuditRecords(ListAuditRecordsRequest) returns (ListAuditRecordsResponse) {
    option (google.api.http) = {
      get: "/api/v1/audit/records"
    };
  }

  rpc AuditMetadata(AuditMetadataRequest) returns (AuditMetadataResponse) {
    option (google.api.http) = {
      get: "/api/v1/audit/metadata"
    };
  }

  rpc ListSortAuditRecords(ListSortAuditRecordsRequest) returns (ListAuditRecordsResponse) {
    option (google.api.http) = {
      post: "/api/v1/audit/records"
      body: "*"
    };
  }

  rpc CreateSysEvent(CreateSysEventRequest) returns (CreateSysEventResponse) {}

  rpc GetSysEvent(GetSysEventRequest) returns (GetSysEventResponse) {
    option (google.api.http) = {
      get: "/api/v1/sysevents/events/{id}"
    };
  }

  rpc SysEventMetadata(SysEventMetadataRequest) returns (SysEventMetadataResponse) {
    option (google.api.http) = {
      get: "/api/v1/sysevents/metadata"
    };
  }

  rpc ListSortSysEvents(ListSortSysEventsRequest) returns (ListSortSysEventsResponse) {
    option (google.api.http) = {
      post: "/api/v1/sysevents/events"
      body: "*"
    };
  }

  rpc DeleteTenantData(DeleteTenantDataRequest) returns (DeleteTenantDataResponse) {
    // Not adding REST API as this is just used internally
  }

}
