// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package dcsync;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/dcsync/pkg/generated/dcsync";

service DcsyncService {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
        get: "/api/v1/version"
      };
    }
    // Add more API methods here
}
