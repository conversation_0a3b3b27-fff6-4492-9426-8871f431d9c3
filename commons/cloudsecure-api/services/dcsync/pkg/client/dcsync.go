/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	dcsyncpb "illum.io/cloud/api/generated/dcsync"
	"illum.io/cloud/api/services/dcsync/pkg/dcsync"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type DcsyncClient struct {
	dcsyncpb.DcsyncServiceClient
	con *grpc.ClientConn
}

func (c *DcsyncClient) Close() {
	_ = c.con.Close()
}

// NewDcsyncClient creates a new gRPC client for service running on url endpoint
func NewDcsyncClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*DcsyncClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := dcsyncpb.NewDcsyncServiceClient(con)

	return &DcsyncClient{
		con:                 con,
		DcsyncServiceClient: client,
	}, nil
}

// NewDcsyncClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewDcsyncClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*DcsyncClient, error) {
	url, ok := config.LookupGRPCService(dcsync.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", dcsync.ServiceName)
	}
	return NewDcsyncClient(ctx, url, opts...)
}
