
syntax = "proto3";

package inventory;

import "common/inventorydata/inventorydata.proto";
import "common/inventorydata/external.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "common/version/version.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/inventory-api/pkg/generated/inventory";

// MacIP is used to performs a lookup using MAC or IP address
// - If a MAC address is provided, it is prioritized for the lookup.
// - If no matching resource is found for the MAC, an IP-based lookup is attempted.
// - If `network_csp_id` is provided, the results are filtered to only include entries with this ID.
// - If `network_csp_id` is not provided, all matching MAC/IP entries are returned.
message MacIP {
  string mac = 1;
  string ip = 2;
  optional string network_csp_id = 3;
}

message LookupRequest {
  string tenant_id = 1;
  repeated MacIP mac_ip = 2;
}

message CacheId {
  enum Type {
    CACHEID_TYPE_UNSPECIFIED = 0;
    CACHEID_TYPE_CLOUD = 1;
    CACHEID_TYPE_EXTERNAL = 2;
  }

  string id = 1;
  Type type = 2;
}

message MacIPRsp {
  MacIP mac_ip = 1;
  repeated CacheId id = 2;
}

message LookupResponse {
  repeated MacIPRsp rsp = 1;
}

message GetCachedResourcesRequest {
  string tenant_id = 1;
  repeated string ids = 2;
}

message GetCachedResourcesResponse {
  repeated CachedResource resources = 1;
}

message ExternalResource {}

message CachedResource {
  oneof item {
    inventorydata.ResourceEvent cloud_resource = 1;
    inventorydata.ExternalResource external_resource = 2;
  }
}

service InventoryCacheService {
  // Lookup makes a search by source/designation mac/ip and return corresponding resource ids
  rpc Lookup(LookupRequest) returns (LookupResponse) {}

  // GetCachedResources fetches resources by their ids
  rpc GetCachedResources(GetCachedResourcesRequest) returns (GetCachedResourcesResponse) {}

  // GetVersion is helper method to check if the service is up and running
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {}
}
