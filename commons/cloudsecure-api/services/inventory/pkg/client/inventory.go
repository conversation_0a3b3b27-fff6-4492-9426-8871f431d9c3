package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	inventorypb "illum.io/cloud/api/generated/inventory"
	"illum.io/cloud/api/services/inventory/pkg/inventory"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type InventoryClient struct {
	inventorypb.InventoryServiceClient
	con *grpc.ClientConn
}

func (c *InventoryClient) Close() {
	_ = c.con.Close()
}

// NewInventoryClient creates a new gRPC client for service running on url endpoint
func NewInventoryClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*InventoryClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := inventorypb.NewInventoryServiceClient(con)

	return &InventoryClient{
		con:                    con,
		InventoryServiceClient: client,
	}, nil
}

// NewInventoryClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewInventoryClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*InventoryClient, error) {
	url, ok := config.LookupGRPCService(inventory.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", inventory.ServiceName)
	}
	return NewInventoryClient(ctx, url, opts...)
}
