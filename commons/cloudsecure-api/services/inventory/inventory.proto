// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package inventory;

import "common/inventorydata/inventorydata.proto";
import "common/inventorydata/external.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "common/version/version.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/inventory-api/pkg/generated/inventory";

// To see some API use cases, refer to: https://confluence.illum.io/display/Lumos/Inventory+Use+Cases

message ListResourcesRequest {
  int32 max_results = 1;
  repeated string clouds = 2;
  repeated string object_types = 3;
  repeated string categories = 4;
  repeated string regions = 5;
  repeated string account_ids = 6;
  repeated string network_ids = 7;
  repeated string subnet_ids = 8;
  repeated string resource_ids = 9;
  repeated string csp_ids = 10;
  map<string, inventorydata.stringList> tags = 11;
  optional bool exclude_references = 12; // boolean to excluded referenced resources from resource map. Returned resources will still have relationships.
}

message ListResourcesResponse {
  map<string, inventorydata.Resource> resource_map = 1;
  optional bool truncated_results = 2;
  uint32 primary_resource_count = 3;
}

enum GroupRelationFilterType {
  RELATION_TYPE_UNSPECIFIED = 0;
  EKS = 1;
  AKS = 2;
  SUBNET = 3;
  NETWORK = 4;
}

message GroupRelationFilter {
    GroupRelationFilterType filter_type = 1;
    repeated string resource_ids = 2;
    repeated string resource_csp_ids = 3;
}

message GetResourcesRequest {
  int32 max_results = 1;
  repeated string clouds = 2;
  repeated string object_types = 3;
  repeated string categories = 4;
  repeated string regions = 5;
  repeated string account_ids = 6;
  repeated string network_ids = 7;
  repeated string subnet_ids = 8;
  repeated string resource_ids = 9;
  repeated string csp_ids = 10;
  map<string, inventorydata.stringList> tags = 11;
  bool exclude_references = 12;
  optional string page_token = 13;
  // withTotal says if response should provide total size of resources
  bool withTotalCount = 14;
  optional inventorydata.SortBy sortBy = 15;
  repeated string subcategories = 16;

  // Relationship filter: support for VPC/Vnet and Subnet CSP_IDs
  repeated string network_csp_ids = 17;
  repeated string subnet_csp_ids = 18;

  // Relationship filters
  repeated RelationsFilter relation_filters = 19;
  repeated string resource_names = 20;

  // Partial Match Filter
  repeated inventorydata.PartialMatchFilter partial_match_filters = 21;

  // Custom Property filters
  repeated inventorydata.PropertyFilter property_filters = 22;
  repeated string states = 23;

  bool json_view = 24;
  repeated string ip_addresses = 25;

  optional GroupRelationFilter group_relation_filter = 26;
}

message GetResourcesResponse {
  repeated inventorydata.Resource items = 1;
  int32 total_size = 2;
  optional string next_page_token = 3;
  optional string prev_page_token = 4;
  string illumio_region = 5;
}

message GetCountsRequest {
  map<string, inventorydata.stringList> tags = 1;
  repeated string clouds = 2;
  repeated string object_types = 3;
  repeated string categories = 4;
  repeated string regions = 5;
  repeated string account_ids = 6;
  repeated string network_ids = 7;
  repeated string subnet_ids = 8;
  repeated string resource_ids = 9;
  repeated string csp_ids = 10;
}

message GetCountsResponse {
  map<string, uint32> count_map = 1;
}

message GetSyncStatusRequest {}

message GetSyncStatusResponse {
  repeated SyncStatusDetails sync_status_details = 1;
}

message SyncStatusDetails {
  string dataplane_name = 1;
  string account_id = 2;
  string sync_status = 3;
  google.protobuf.Timestamp finished_at = 4;
}

message ResourceDistributionRequest {
  inventorydata.FilterType filter_type = 1;
}

message ResourceDistributionResponse {
  repeated inventorydata.ResourceDistributionCount resource_distribution = 1;
}

message ListFlowLogConfigsRequest {
  string cloud = 1;
  repeated string regions = 2;
  repeated string account_ids = 3;
  repeated string resource_ids = 4;
  repeated string csp_ids = 5;
}

message ListFlowLogConfigsResponse {
  map<string, inventorydata.FlowLogConfig> configs = 1;
}



message ListTagsRequest {
  repeated string clouds = 1;
  repeated string regions = 2;
  repeated string account_ids = 3;
  repeated string object_types = 5;
}

message ListTagsResponse {
  repeated inventorydata.Tag tags = 1;
}

message ListMetadataRequest {
  repeated string clouds = 1;
  inventorydata.MetadataType metadata_type = 2;
  repeated string regions = 3;
  repeated string account_ids = 4;
  repeated string object_types = 5;
  repeated string subcategories = 6;
  string partial_filter = 7;

  optional uint32 max_results = 8;
}

message ListMetadataResponse {
  repeated string values = 1;
  // want to eventually move to new_values since metadata values like account & tags can't just be a list of strings
  google.protobuf.Struct new_values = 2;
}

message GetNetworkInterfacesByIPsRequest {
  repeated string ips = 1;
}

message GetNetworkInterfacesByIPsResponse {
  map<string, string> ip_to_network_map = 1;
}

message GetEndpointIDsByIPsRequest {
  repeated string ips = 1;
}

message GetEndpointIDsByIPsResponse {
  map<string, string> ip_to_endpoint_id_map = 1;
}


message ListRegionsRequest {}

message ListRegionsResponse {
  repeated inventorydata.DecoratedRegion regions = 1;
}

message ListWorkloadsRequest {
  int32 max_results = 1;
  optional string page_token = 2;
  repeated string clouds = 3;
  repeated string regions = 4;
}

message ListWorkloadsResponse {
  repeated inventorydata.Workload workloads = 1;
  int32 total_size = 2;
  optional string next_page_token = 3;

}

message GetNetworkInterfacesByResourceIdsRequest{
  repeated string resource_ids = 1 [(validate.rules).repeated.items.string.uuid = true];
}

message GetNetworkInterfacesByResourceIdsResponse{
  google.protobuf.Struct resource_id_to_nic_ids = 1;
}

message GetEndpointIDsByResourceIDsRequest{
  repeated string resource_ids = 1 [(validate.rules).repeated.items.string.uuid = true];
}

message GetEndpointIDsByResourceIDsResponse{
  repeated inventorydata.EndpointIDs resource_id_to_endpoint_ids = 1;
}

message GetHistoryRequest {
  google.protobuf.Timestamp start_time = 1;
  google.protobuf.Timestamp end_time = 2;
  GetResourcesRequest filters = 3;
}

message GetHistoryResponse {
  repeated inventorydata.Resource items = 1;
}

enum Operator {
  OR = 0;
  //AND = 1; we only currently support OR operator for relation filters
}

// RelationsFilter specifies resources of resource_type with any relation specified in relation_types
// resource_type specifies the type that the relation filters applies
// relation_types specifies the types that will be included or excluded
// exclude is a boolean that specifies whether we should include or exclude the relation_types
// operator specifies whether we AND/OR the relations_types (e.g. exclude resource if ALL relation_types exists or exclude resource if ANY relation_type exists)
// example request (fetches all nsgs without flow logs) :
// {
// "with_total_count": false,
// "object_types": ["Microsoft.Network/networkSecurityGroups"],
// "relation_filters": [{"resource_type": "Microsoft.Network/networkSecurityGroups", "exclude": true, "relation_types": ["Microsoft.Network/networkWatchers/FlowLogs"]}],
// "max_results": 50
// }
message RelationsFilter {
  string resource_type = 1;
  repeated string relation_types = 2;
  bool exclude = 3;
  Operator operator = 4;
}

message EnhancedResource {
  inventorydata.Resource resource = 1;
  string tenant_id = 2;
  string vnet_id = 3;
}

message GetResourcesByIPRequest {
  repeated string ips = 1;
  bool workloads_only = 2;
}

message GetResourcesByIPResponse {
  // key is ip address, value is inventory resource
  map<string, EnhancedResource> items = 1;
}

message ListExternalResourcesRequest {
  int32 max_results = 1;
  repeated string types = 2;
  repeated string resource_ids = 3;
  repeated string ips = 4;
  bool exclude_references = 5;
  optional string page_token = 6;
  // withTotal says if response should provide total size of resources
  bool withTotalCount = 7;

  inventorydata.ExternalSortBy sortBy = 8;

  // Relationship filters
  repeated ExtRelationsFilter ext_relation_filters = 9;
  /*
  repeated string resource_names = 9;
  */
}

message ExtRelationsFilter {
  string resource_type = 1;
  repeated string relation_types = 2;
  bool exclude = 3;
}

message ListExternalResourcesResponse {
  repeated inventorydata.ExternalResource items = 1;
  uint32 total_size = 2;
  optional string next_page_token = 3;
  optional string prev_page_token = 4;
  uint32 page = 5;
}

message GetExternalResourceByResourceIDRequest {
  string resource_id = 1;
}

message GetExternalResourceByIPRequest {
  string ip = 1;
}

message GetExternalResourceResponse {
  inventorydata.ExternalResource ext_resource = 1;
}

message Nic {
  string id = 1;
  string csp_id = 2;
  string addr = 3;
  string object_type = 4;
  repeated string ips = 5;
}

message ListNicsRequest {
  uint64 cursor = 1;
  // maxResults doesn't guarantee exact maxResults would be returned
  // the result can contain more than maxResults
  int64 maxResults = 3;
}

message ListNicsResponse {
  repeated Nic items = 1;
  uint64 nextCursor = 2;
}

message Mac {
  option deprecated = true;
  string id = 1;
  string csp_id = 2;
  string addr = 3;
  string object_type = 4;
}

message ListMacsRequest {
  option deprecated = true;
  uint64 cursor = 1;

  // maxResults doesn't guarantee exact maxResults would be returned
  // the result can contain more than maxResults
  int64 maxResults = 3;
}

message ListMacsResponse {
  option deprecated = true;
  repeated Mac items = 1;
  uint64 nextCursor = 2;
}

message ListFlowConfigDetailsRequest{
  string cloud = 1 [(validate.rules).string.min_len = 3];
  string account_csp_id = 2 [(validate.rules).string.min_len = 1];
  inventorydata.DestinationFlowType flow_type = 3;
  optional string page_token = 4;
  optional int32 max_results = 5;
}

message ListFlowConfigDetailsResponse{
  repeated inventorydata.DestinationFlows flows = 1;
  uint32 total_size = 2;
  optional string next_page_token = 3;
  optional string prev_page_token = 4;
}

service InventoryService {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
        get: "/api/v1/version"
      };
    }
    // Add more API methods here
    rpc ListResources(ListResourcesRequest) returns (ListResourcesResponse) {}

    // GetResources support pagination of resources
    rpc GetResources(GetResourcesRequest) returns (GetResourcesResponse) {}
    rpc GetResourcesByIP(GetResourcesByIPRequest) returns (GetResourcesByIPResponse) {}

    rpc GetResourceDistribution(ResourceDistributionRequest) returns (ResourceDistributionResponse) {}

    rpc GetSyncStatus(GetSyncStatusRequest) returns (GetSyncStatusResponse) {}
    rpc GetCounts(GetCountsRequest) returns (GetCountsResponse) {}
    rpc ListFlowLogConfigs(ListFlowLogConfigsRequest) returns (ListFlowLogConfigsResponse) {}
    rpc ListFlowLogConfigDetails(ListFlowConfigDetailsRequest) returns (ListFlowConfigDetailsResponse) {}
    rpc ListTags(ListTagsRequest) returns (ListTagsResponse) {}
    rpc ListMetadata(ListMetadataRequest) returns (ListMetadataResponse) {}
    rpc GetNetworkInterfacesByIPs(GetNetworkInterfacesByIPsRequest) returns (GetNetworkInterfacesByIPsResponse) {
      option deprecated = true;
    }
    rpc ListWorkloads(ListWorkloadsRequest) returns(ListWorkloadsResponse){}
    rpc GetNetworkInterfacesByResourceIds(GetNetworkInterfacesByResourceIdsRequest) returns (GetNetworkInterfacesByResourceIdsResponse) {}
    rpc GetEndpointIDsByIPsStream(stream GetEndpointIDsByIPsRequest) returns (stream GetEndpointIDsByIPsResponse) {}
    rpc GetEndpointIDsByResourceIDs(GetEndpointIDsByResourceIDsRequest) returns (GetEndpointIDsByResourceIDsResponse) {}

    // Note: ListRegions returns a list of regions decorated with accountId & cloud
    // ListMetadata returns regions as a list of strings
    // This API may return duplicate regions since the same region can be in different accounts & cloud
    rpc ListRegions(ListRegionsRequest) returns (ListRegionsResponse) {}

    rpc GetHistory(GetHistoryRequest) returns (GetHistoryResponse) {}

    rpc ListExternalResources(ListExternalResourcesRequest) returns (ListExternalResourcesResponse) {}
    rpc GetExternalResourceByResourceID(GetExternalResourceByResourceIDRequest) returns (GetExternalResourceResponse) {}
    rpc GetExternalResourceByIP(GetExternalResourceByIPRequest) returns (GetExternalResourceResponse) {}


    rpc ListMacs(ListMacsRequest) returns (ListMacsResponse) {
      option deprecated = true;
    }
    rpc ListNics(ListNicsRequest) returns (ListNicsResponse) {}

}
