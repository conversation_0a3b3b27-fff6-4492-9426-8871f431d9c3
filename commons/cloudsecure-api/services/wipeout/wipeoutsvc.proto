// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package wipeout;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/api/generated/wipeout";

message DeleteTenantRequest {
  string tenant_id = 1 [(validate.rules).string.uuid = true];
}

enum Status {
  UNSPECIFIED = 0;
  STARTED = 1;
  INPROGRESS = 2;
  FAILED = 3;
  COMPLETED = 4;
  DISABLED = 5;
}

message DeleteTenantResponse {
  string tenant_id = 1;
  Status status = 2;
}

message DisableTenantRequest {
  string tenant_id = 1 [(validate.rules).string.uuid = true];
}

message DisableTenantResponse {
  string tenant_id = 1;
  Status status = 2;
}

message GetTenantStatusRequest {
  string tenant_id = 1 [(validate.rules).string.uuid = true];
}

message GetTenantStatusResponse {
  string tenant_id = 1;
  Status status = 2;
}

service WipeoutService {
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
    option (google.api.http) = {
      get: "/api/v1/version"
    };
  }

  // Add more API methods here
  rpc DeleteTenant(DeleteTenantRequest) returns (DeleteTenantResponse) {
    option (google.api.http) = {
      delete: "/api/v1/tenant/{tenant_id}"
    };
  }

  rpc DisableTenant(DisableTenantRequest) returns (DisableTenantResponse) {
    option (google.api.http) = {
      post: "/api/v1/tenant/{tenant_id}"
      body: "*"
    };
  }

  rpc GetDeletionStatus(GetTenantStatusRequest) returns (GetTenantStatusResponse) {
    option (google.api.http) = {
      get: "/api/v1/status/{tenant_id}"
    };
  }
    // Add more API methods here
}
