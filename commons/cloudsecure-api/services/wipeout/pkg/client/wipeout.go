/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	wipeoutpb "illum.io/cloud/api/generated/wipeout"
	"illum.io/cloud/api/services/wipeout/pkg/wipeout"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type WipeoutClient struct {
	wipeoutpb.WipeoutServiceClient
	con *grpc.ClientConn
}

func (c *WipeoutClient) Close() {
	_ = c.con.Close()
}

// NewWipeoutClient creates a new gRPC client for service running on url endpoint
func NewWipeoutClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*WipeoutClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := wipeoutpb.NewWipeoutServiceClient(con)

	return &WipeoutClient{
		con:                  con,
		WipeoutServiceClient: client,
	}, nil
}

// NewWipeoutClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewWipeoutClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*WipeoutClient, error) {
	url, ok := config.LookupGRPCService(wipeout.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", wipeout.ServiceName)
	}
	return NewWipeoutClient(ctx, url, opts...)
}
