package client

import (
	"go.uber.org/mock/gomock"

	"illum.io/cloud/api/generated/policyinventorysync/mock"
)

// NewMockPolicyinventorysyncClient creates a new mock client
func NewMockPolicyinventorysyncClient(ctrl *gomock.Controller) (*PolicyinventorysyncClient, *mock.MockPolicyinventorysyncServiceClient) {
	m := mock.NewMockPolicyinventorysyncServiceClient(ctrl)
	return &PolicyinventorysyncClient{
		PolicyinventorysyncServiceClient: m,
	}, m
}
