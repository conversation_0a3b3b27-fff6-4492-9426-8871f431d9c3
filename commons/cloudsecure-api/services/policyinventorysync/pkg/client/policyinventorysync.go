package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	policyinventorysyncpb "illum.io/cloud/api/generated/policyinventorysync"
	"illum.io/cloud/api/services/policyinventorysync/pkg/policyinventorysync"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type PolicyinventorysyncClient struct {
	policyinventorysyncpb.PolicyinventorysyncServiceClient
	con *grpc.ClientConn
}

func (c *PolicyinventorysyncClient) Close() {
	_ = c.con.Close()
}

// NewPolicyinventorysyncClient creates a new gRPC client for service running on url endpoint
func NewPolicyinventorysyncClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*PolicyinventorysyncClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := policyinventorysyncpb.NewPolicyinventorysyncServiceClient(con)

	return &PolicyinventorysyncClient{
		con:                              con,
		PolicyinventorysyncServiceClient: client,
	}, nil
}

// NewPolicyinventorysyncClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewPolicyinventorysyncClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*PolicyinventorysyncClient, error) {
	url, ok := config.LookupGRPCService(policyinventorysync.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", policyinventorysync.ServiceName)
	}
	return NewPolicyinventorysyncClient(ctx, url, opts...)
}
