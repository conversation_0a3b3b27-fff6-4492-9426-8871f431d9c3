// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package policyinventorysync;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/api/generated/policyinventorysync";

service PolicyinventorysyncService {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
        get: "/api/v1/version"
      };
    }
}
