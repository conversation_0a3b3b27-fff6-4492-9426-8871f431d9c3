// Copyright 2024 Illumio, Inc. All Rights Reserved.
syntax = "proto3";

package pcesync.pce;

import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

// Golang
option go_package = "illum.io/cloud/api/generated/pcesync";

// Java
option java_multiple_files = true;
option java_package = "com.illumio.cloudsecure.protos.pcesync.pce";
option java_outer_classname = "PceSyncProtos";

message PolicyResources {
  google.protobuf.Timestamp timestamp = 1 [json_name="timestamp"];
  repeated PolicyResource resources = 2 [json_name="resources"];
}

enum PolicyResourceType {
  resource_type_undefined = 0;
  network = 1;
  workload = 2;
  enforcement_point = 3;
}

enum PolicyResourceChangeType {
  change_type_undefined = 0;
  create = 1;
  update = 2;
  delete = 3;
}

message PolicyResource {
  PolicyResourceType resource_type = 1 [json_name="resource_type"]; // "network", "workload", "enforcement_point"
  PolicyResourceChangeType resource_change_type = 2 [json_name="resource_change_type"]; // create, update, delete
  oneof attributes {
    PolicyNetwork network = 3;
    PolicyWorkload workload = 4;
    PolicyEnforcementPoint enforcement_point = 5;
  }
}

enum PolicyIpVer {
  ip_ver_undefined = 0;
  ipv4 = 1;
  ipv6 = 2;
}

message PolicyNetwork {
  string name = 1 [json_name="network_name"]; // Synthetic unique name
  optional PolicyIpVer ip_version = 2 [json_name="ip_version"];
}

message PolicyEnforcementPoint {
  string id = 1 [json_name="external_data_set"];
  string csp_id = 2 [json_name = "external_data_reference"];
  optional string name = 3 [json_name="name"];
  optional string type = 4 [json_name="type"];
  message WorkloadRefs {
    repeated string deletedIds = 1 [json_name="delete"];
    repeated string addedIds = 2 [json_name="set"];
  }
  optional WorkloadRefs workload_refs = 5 [json_name="workloads"];
}

message PolicyWorkload {
  string id = 1 [json_name="external_data_set"];
  string csp_id = 2 [json_name = "external_data_reference"];
  optional string name = 3 [json_name="name"];
  optional bool online = 4 [json_name="online"];

  // Network Interfaces
  message NetworkInterface {
    string name = 1 [json_name="name"];
    string address = 2 [json_name="address"];
    string network_name = 3 [json_name="network_name"];
  }
  message NetworkInterfaces {
    repeated NetworkInterface deleted = 1 [json_name="delete"];
    repeated NetworkInterface upserted = 2 [json_name="set"];
  }
  optional NetworkInterfaces interfaces = 5 [json_name = "interfaces"];
}
