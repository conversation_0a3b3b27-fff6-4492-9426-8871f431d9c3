// Copyright 2024 Illumio, Inc. All Rights Reserved.
syntax = "proto3";

package pcesync.cs;

import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

// Golang
option go_package = "illum.io/cloud/api/generated/pcesync";

// Java
option java_multiple_files = true;
option java_package = "com.illumio.cloudsecure.protos.pcesync.cs";
option java_outer_classname = "CsSyncProtos";

message Resource {
    string tenant_id = 1;
    string data_plane = 2;
    google.protobuf.Timestamp last_updated_at = 3;
    uint32 version = 4;
    oneof resource {
        Network network = 5;
        Workload workload = 6;
    }
    bool is_deleted = 7;
}

message Network{
    string id = 1;
    string csp_id = 2;
    string name = 3;
}

message Workload {
    string id = 1;
    string csp_id = 2;
    string name = 3;
    string type = 4;
    bool online = 5;
    repeated Network nets = 6;
    repeated Subnet subnets = 7;
    repeated NetworkInterface nics = 8;
    repeated EnforcementPoint eps = 9;
}

message EnforcementPoint {
    string id = 1;
    string csp_id = 2;
    string name = 3;
    string type = 4;
}

message Subnet{
    string id = 1;
    string csp_id = 2;
    string name = 3;
    string network_id = 4;
    repeated string enforcement_point_ids = 5;
}

message Ips {
    repeated string publicIpV4 = 1;
    repeated string privateIpV4 = 2;
    repeated string publicIpV6 = 3;
    repeated string privateIpV6 = 4;
}

message NetworkInterface {
    string id = 1;
    string csp_id = 2;
    string name = 3;
    Ips ips = 4;
    Subnet subnet = 5;
    repeated string enforcement_point_ids = 6;
}
