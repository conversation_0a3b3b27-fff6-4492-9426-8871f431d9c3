// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package secret;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/secret-api/pkg/generated/secret";

message SecretKey {
  // service name
  string service = 1;
  // secret kind
  string secret_type = 2;
}

message GetSecretsRequest {
  // service name
  string service = 1;
  // a list of service's secret kind to retrieve
  repeated string secret_types = 2;
}

message GetSecretsResponse {
  // secret_kind -> value
  map<string, string> secrets = 1;
  // requested, but not configured
  repeated string missing = 2;
}

message GetSecretRequest {
  // service name
  string service = 1;
  // secret kind
  string secret_type = 2;
}

message GetSecretResponse {
  string secret = 1;
}

// Customer secret messages
message CustomerSecretKey {
  // customer ID
  string customer = 1;
  // service name
  string service = 2;
  // secret kind
  string secret_type = 3;
}

message GetCustomerSecretRequest {
  CustomerSecretKey key = 1;
}

message GetCustomerSecretResponse {
  string secret = 1;
}

message SetCustomerSecretResponse {
}
message SetCustomerSecretRequest {
  CustomerSecretKey key = 1;
  string value = 2;
}

message DeleteCustomerSecretRequest {
  CustomerSecretKey key = 1;
}

message DeleteCustomerSecretResponse {
}
//

// Service definition
service SecretService {
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
    option (google.api.http) = {
      get: "/api/v1/version"
    };
  }

  // read multiple system secrets at once
  rpc GetSecrets(GetSecretsRequest) returns(GetSecretsResponse) {}
  // read a system secret
  rpc GetSecret(GetSecretRequest) returns(GetSecretResponse) {}

  // customer secrets
  // ---
  // read a customer secret
  rpc GetCustomerSecret(GetCustomerSecretRequest) returns(GetCustomerSecretResponse) {}
  // write a customer secret
  rpc SetCustomerSecret(SetCustomerSecretRequest) returns(SetCustomerSecretResponse) {}
  // delete a customer secret
  rpc DeleteCustomerSecret(DeleteCustomerSecretRequest) returns(DeleteCustomerSecretResponse) {}
}
