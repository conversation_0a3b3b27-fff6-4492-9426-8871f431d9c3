package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	secretpb "illum.io/cloud/api/generated/secret"
	"illum.io/cloud/api/services/secret/pkg/secret"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type SecretClient struct {
	secretpb.SecretServiceClient
	con *grpc.ClientConn
}

func (c *SecretClient) Close() {
	_ = c.con.Close()
}

// NewSecretClient creates a new gRPC client for service running on url endpoint
func NewSecretClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*SecretClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := secretpb.NewSecretServiceClient(con)

	return &SecretClient{
		con:                 con,
		SecretServiceClient: client,
	}, nil
}

// NewSecretClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewSecretClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*SecretClient, error) {
	url, ok := config.LookupGRPCService(secret.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", secret.ServiceName)
	}
	return NewSecretClient(ctx, url, opts...)
}
