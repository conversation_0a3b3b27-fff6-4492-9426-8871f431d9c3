// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package mllabeling;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "common/protoc-gen-openapiv2/options/annotations.proto";
import "validate/validate.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/api/generated/mllabeling";

// CloudSecure does not let you specify tenant_id - instead you have to be
// logged in as the tenant.
//
// Similarly for request_id, internal CS functions handle populating this field.
// 'Request's are not allowed to have them.

message Verdict {
  enum VerdictValue {
    UNKNOWN_VERDICT = 0;
    ACCEPTED = 1;
    REJECTED = 2;
    PENDING = 3;
  }

  VerdictValue verdict = 1;
}

message Pagination {
  int64 page_size = 1;
  int64 page_number = 2;
}

message ListRecommendationsRequest {
  message OrderBy {
    OrderField field = 1;
    bool asc = 2;

    enum OrderField {
      UNKNOWN_ORDERFIELD = 0;
      UPDATED_AT = 1;
      RECOMMENDATION_ID = 2;
    }
  }

  // string tenant_id = 1 [(validate.rules).string.uuid = true];
  reserved 1;
  repeated string recommendation_ids = 2;
  optional Verdict verdict = 3;
  optional Pagination pagination = 4;
  repeated OrderBy order_by = 5;
  optional google.protobuf.Timestamp after = 6;
  optional string resource_id = 7 [(validate.rules).string.uuid = true];
  // optional string request_id = 8 [(validate.rules).string.uuid = true];
  reserved 8;
}

message ListRecommendationsResponse {
  repeated Recommendation recommendations = 1;
  string tenant_id = 2;
  int64 page_number = 3;
}

message AcceptRecommendationsRequest {
  reserved 1;
  // string tenant_id = 2 [(validate.rules).string.uuid = true];
  reserved 2;
  repeated string recommendation_ids = 3 [(validate.rules).repeated = {
    items: {string: {uuid: true}}
  }];
  // optional string request_id = 4 [(validate.rules).string.uuid = true];
  reserved 4;
}

message RejectRecommendationsRequest {
  reserved 1;
  // string tenant_id = 2 [(validate.rules).string.uuid = true];
  reserved 2;
  repeated string recommendation_ids = 3 [(validate.rules).repeated = {
    items: {string: {uuid: true}}
  }];
  // optional string request_id = 4 [(validate.rules).string.uuid = true];
  reserved 4;
}

message MarkRecommendationsAsPendingRequest {
  reserved 1;
  // string tenant_id = 2 [(validate.rules).string.uuid = true];
  reserved 2;
  repeated string recommendation_ids = 3 [(validate.rules).repeated = {
    items: {string: {uuid: true}}
  }];
  // optional string request_id = 4 [(validate.rules).string.uuid = true];
  reserved 4;
}

message RecommendationsResponse {
  repeated Recommendation recommendations = 1;
  string tenant_id = 2;
}

message Recommendation {
  string id = 1;
  Label label = 2;
  Resource resource = 3;
  string created_at = 4;
  Verdict verdict = 5;
  string reason = 6;
}

message Resource {
  string id = 1 [(validate.rules).string.uuid = true];
  string cloud = 2;
}

message Label {
  string key = 1;
  string value = 2;
}

service MllabelingService {
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
    option (google.api.http) = {
      get: "/api/v1/version"
    };
  }

  rpc ListRecommendations(ListRecommendationsRequest) returns (RecommendationsResponse) {
    option (google.api.http) = {
      get: "/api/v1/mllabeling/recommendations"
    };
  }

  rpc AcceptRecommendations(AcceptRecommendationsRequest) returns (RecommendationsResponse) {
    option (google.api.http) = {
      put: "/api/v1/mllabeling/recommendations/accept"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }

  rpc RejectRecommendations(RejectRecommendationsRequest) returns (RecommendationsResponse) {
    option (google.api.http) = {
      put: "/api/v1/mllabeling/recommendations/reject"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }

  rpc MarkRecommendationsAsPending(MarkRecommendationsAsPendingRequest) returns (RecommendationsResponse) {
    option (google.api.http) = {
      put: "/api/v1/mllabeling/recommendations/mark_as_pending"
      body: "*"
    };
    option (grpc.gateway.protoc_gen_openapiv2.options.openapiv2_operation) = {
      responses: {
        key: "204"
        value: {
          description: "A successful response."
          schema: {}
        }
      }
    };
  }
}
