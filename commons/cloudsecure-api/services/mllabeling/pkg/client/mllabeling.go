/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	mllabelingpb "illum.io/cloud/api/generated/mllabeling"
	"illum.io/cloud/api/services/mllabeling/pkg/mllabeling"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type MllabelingClient struct {
	mllabelingpb.MllabelingServiceClient
	con *grpc.ClientConn
}

func (c *MllabelingClient) Close() {
	_ = c.con.Close()
}

// NewMllabelingClient creates a new gRPC client for service running on url endpoint
func NewMllabelingClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*MllabelingClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := mllabelingpb.NewMllabelingServiceClient(con)

	return &MllabelingClient{
		con:                     con,
		MllabelingServiceClient: client,
	}, nil
}

// NewMllabelingClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewMllabelingClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*MllabelingClient, error) {
	url, ok := config.LookupGRPCService(mllabeling.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", mllabeling.ServiceName)
	}
	return NewMllabelingClient(ctx, url, opts...)
}
