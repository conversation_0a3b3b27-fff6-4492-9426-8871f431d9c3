package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	sgpolicy "illum.io/cloud/api/generated/sgpolicyenforcement"
	"illum.io/cloud/api/services/sgpolicyenforcement/pkg/sg_policy_enforcement"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type SGEnforcementClient struct {
	sgpolicy.SGPolicyEnforcementServiceClient
	con *grpc.ClientConn
}

func (c *SGEnforcementClient) Close() {
	_ = c.con.Close()
}

func NewSGEnforcementClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*SGEnforcementClient, error) {
	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := sgpolicy.NewSGPolicyEnforcementServiceClient(con)

	return &SGEnforcementClient{
		con:                              con,
		SGPolicyEnforcementServiceClient: client,
	}, nil
}

func NewSGEnforcementClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*SGEnforcementClient, error) {
	url, ok := config.LookupGRPCService(sg_policy_enforcement.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %s service", sg_policy_enforcement.ServiceName)
	}
	return NewSGEnforcementClient(ctx, url, opts...)
}
