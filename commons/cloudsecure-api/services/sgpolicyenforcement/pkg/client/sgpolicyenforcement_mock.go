// Copyright 2023 Illumio, Inc. All Rights Reserved.

package client

import (
	"go.uber.org/mock/gomock"

	"illum.io/cloud/api/generated/sgpolicyenforcement/mock"
)

func NewMockSGEnforcementClient(ctrl *gomock.Controller) (*SGEnforcementClient, *mock.MockSGPolicyEnforcementServiceClient) {
	m := mock.NewMockSGPolicyEnforcementServiceClient(ctrl)
	return &SGEnforcementClient{
		SGPolicyEnforcementServiceClient: m,
	}, m
}
