// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package sg_policy_enforcement;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "common/version/version.proto";
import "common/wipeout/wipeout.proto";

import "common/policy/policy.proto";
import "common/policy/policyimpact.proto";

option go_package = "illum.io/cloud/sgpolicyenforcement-api/pkg/generated/sgpolicyenforcement";

message SecurityGroup {
  string security_group_id      = 1 [(validate.rules).string.uuid = true];
  string security_group_csp_id  = 2 [(validate.rules).string.min_len = 1];
  optional string security_group_name = 3;
}

message SecurityGroupDigestRequest {
  SecurityGroup security_group = 1 [(validate.rules).message.required = true];
  string region_name = 2 [(validate.rules).string.min_len = 1];
  string aws_account_id = 3 [(validate.rules).string.min_len = 1];
}

message SecurityGroupPolicyRequest {
  SecurityGroup security_group = 1 [(validate.rules).message.required = true];
  string region_name = 2 [(validate.rules).string.min_len = 1];
  string aws_account_id = 3 [(validate.rules).string.min_len = 1];
  policy.Policy policy = 4;
  bool force = 5;
  google.protobuf.Timestamp originated_at = 6;
}

message SecurityGroupPolicyResponse {
  SecurityGroup security_group = 1 [(validate.rules).message.required = true];
  policy.PolicyDigest policy_digest = 2 [(validate.rules).message.required = true];
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;
}

// Use PepEnforcementAwsSGTask as the payload for the MQ message
message AwsSGEnforcementTask {
  SecurityGroupPolicyRequest security_group_request = 1;
  string tenant_id = 2 [(validate.rules).string.uuid = true];
  policy.EnforcementMode mode = 3 [(validate.rules).enum.defined_only = true];
  google.protobuf.Timestamp originated_at = 4;
}

message SyncEnforcementPointRequest {
  SecurityGroup security_group = 1 [(validate.rules).message.required = true];
  string region_name = 2 [(validate.rules).string.min_len = 1];
  string aws_account_id = 3 [(validate.rules).string.min_len = 1];
  policy.PolicyDigest policy_digest = 4 [(validate.rules).message.required = true];
  policy.EnforcementMode mode = 5;
}

message SyncEnforcementPointResponse {}

message DeleteEnforcementPointsRequest {
  repeated string EnforcementPointIds = 1  [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      string: {
        uuid: true
      }
    }
  }];
}

message DeleteEnforcementPointsResponse {}

service SGPolicyEnforcementService {
  rpc EnforceSGRules(SecurityGroupPolicyRequest) returns (SecurityGroupPolicyResponse);
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse);
  rpc GetSGPolicyDigest(SecurityGroupDigestRequest) returns (policy.PolicyDigest);

  // policy impact needs the following RPC calls
  rpc GetEnforcementPointPolicyImpact(policyimpact.EnforcementPointPolicyImpactRequest) returns (policyimpact.EnforcementPointPolicyImpactResponse);

  // updates enforcement_mode for enforcement point
  // creates enforcement point record in case of data plane change
  rpc SyncEnforcementPoint(SyncEnforcementPointRequest) returns (SyncEnforcementPointResponse) {}

  // deletes given enforcement points
  rpc DeleteEnforcementPoints(DeleteEnforcementPointsRequest) returns (DeleteEnforcementPointsResponse) {}
  rpc DeleteTenant(wipeout.DeleteTenantDataRequest) returns (wipeout.DeleteTenantDataResponse);
}
