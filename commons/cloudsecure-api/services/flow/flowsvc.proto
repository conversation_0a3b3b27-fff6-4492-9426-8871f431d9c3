// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package flow;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/type/interval.proto";
import "google/protobuf/timestamp.proto";
import "common/version/version.proto";
import "validate/validate.proto";
import "common/flow/flow.proto";
import "common/flow/query.proto";

option go_package = "illum.io/cloud/api/generated/flow";

message DirectionEntity {
  SrcDst src_dst = 1;
  oneof type {
    IpAddress ip_address = 2;
    EntityId entity_id = 3;
  }
}

message PeriodData {
  google.type.Interval period = 1;
  uint64 active_flows = 2;
}


message SearchRequest {
  google.type.Interval period = 1;
  repeated DirectionEntity entities = 2;
  optional FlowStatus status = 3;
  optional uint32 dst_port = 4 [(validate.rules).uint32.lte = 65535];
  optional uint32 protocol = 5 [(validate.rules).uint32.lte = 255];
  optional int32 max_results = 6;
  repeated Service services = 7;
  optional flow.Expr query = 8; // query can only be used instead of all other filter params
  repeated FlowPart group_bys = 9;
  optional bool sort_asc_end_time = 1001; // Preliminary, do not use
  optional uint32 num_period_data = 10; // Number of periods of active flow data to be returned
}

message SearchResponse {
  repeated Flow flows = 1;
  optional bool truncated_results = 2;
  repeated PeriodData period_datas = 3;
}

// the new api will use query paramter 
message QueryRequest {
  google.type.Interval period = 1;
  optional flow.Expr query = 2; 
  optional int32 max_results = 3;
  repeated FlowPart group_bys = 4;
  // currently sort_asc_end_time is not enforced; will be implemented later
  optional bool sort_asc_end_time = 5;
}

message ListFlow {
  repeated Flow flows = 1;
}

message IndexSizeRequest {
  google.protobuf.Timestamp time = 1;     // Hour for which to get size
}

message IndexSizeResponse {
  uint64 size_bytes = 1; // Size of index as stored
}

message SignalLoginRequest {
}

message SignalLoginResponse {
}
message DeleteTenantDataRequest {
}

message DeleteTenantDataResponse {}

service FlowSearchService {
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
    option (google.api.http) = {
      get: "/api/v1/version"
    };
  }

  rpc Search(SearchRequest) returns (SearchResponse) {
  }

  rpc StreamFlows(QueryRequest) returns (stream ListFlow) {

  }

  rpc IndexSize(IndexSizeRequest) returns (IndexSizeResponse) {
  }

  rpc SignalLogin(SignalLoginRequest) returns (SignalLoginResponse) {
  }
  rpc DeleteTenantData(DeleteTenantDataRequest) returns (DeleteTenantDataResponse) {}
}

// FlowState and FlowStateStats are only used for encoding data for storage, not over grpc
// Commented out, will be rekoved after state code stabilized and we knwo we are not using this.
// message FlowState {
//   google.protobuf.Timestamp start = 1;
//   repeated FlowStateFlow flows = 2;
//   map<string, FlowStateStats> cloud_stats = 3;
// }

// message FlowStateFlow {
//   bool allowed = 1;
//   uint32 proto = 2;
//   string src = 3;
//   bytes src_ip = 15;
//   uint32 src_port = 4;
//   string dst = 5;
//   bytes dst_ip = 16;
//   uint32 dst_port = 6;
//   int64 start_time = 7;
//   int64 end_time = 8;
//   string src_id = 9;
//   string dst_id = 10;
//   uint64 bytes_to_dst = 11;
//   uint64 bytes_from_dst = 12;
//   uint32 packets_to_dst = 13;
//   uint32 packets_from_dst = 14;
// }

// message FlowStateStats {
//   uint64 num_allowed_flows = 1;
//   uint64 num_denied_flows = 2;
//   uint64 num_network_bytes = 3;
//   uint64 num_raw_allowed_flows = 4;
//   uint64 num_raw_denied_flows = 5;
//   uint32 num_raw_packets = 6;
//   uint64 num_raw_obj_bytes = 7;
// }
