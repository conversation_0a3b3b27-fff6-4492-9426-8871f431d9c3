package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	flowpb "illum.io/cloud/api/generated/flow"
	"illum.io/cloud/api/services/flow/pkg/flow"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type FlowClient struct {
	flowpb.FlowSearchServiceClient
	con *grpc.ClientConn
}

func (c *FlowClient) Close() {
	_ = c.con.Close()
}

// NewFlowClient creates a new gRPC client for service running on url endpoint
func NewFlowClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*FlowClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := flowpb.NewFlowSearchServiceClient(con)

	return &FlowClient{
		con:                     con,
		FlowSearchServiceClient: client,
	}, nil
}

// NewFlowClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewFlowClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*FlowClient, error) {
	url, ok := config.LookupGRPCService(flow.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", flow.ServiceName)
	}
	return NewFlowClient(ctx, url, opts...)
}
