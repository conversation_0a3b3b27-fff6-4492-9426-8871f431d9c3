// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package preferences;

import "google/protobuf/any.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/version/version.proto";
import "validate/validate.proto";
import "preferences/value_types.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/rpc/status.proto";

option go_package = "illum.io/cloud/api/generated/preferences";

/*
	Definitions:
		preference_context: refers to the general use-case of a given preference
										- examples: "aws", "azure", "on-prem", "general", "cloudsecure", "core", etc.
		preference_name: (self-explanatory)
		preference_type: refers to a string that will map to a `Validator`. Refer to README
 */

// Public/Client-facing Preference
message ClientPreference {
	string preference_context = 1 [(validate.rules).string.min_len = 1];
	string preference_name = 2 [(validate.rules).string.min_len = 1];
	PreferenceValueType value = 3 [(validate.rules).message.required = true];
}

message ClientPreferenceResult {
	string context = 1 [(validate.rules).string.min_len = 1];
	string setting_name = 2 [(validate.rules).string.min_len = 1];
	google.rpc.Status status = 3;
}


message PreferenceTemplateEntry {
	string preference_context = 1 [(validate.rules).string.min_len = 1];
	string preference_name = 2 [(validate.rules).string.min_len = 1];
	string preference_type = 3 [(validate.rules).string.min_len = 1];
	PreferenceValueType default_value = 4 [(validate.rules).any.required = true];
	PreferenceValueType acceptable_value = 5 [(validate.rules).any.required = true];
}

// Getters Requests
message GetAllPreferencesRequest {
}

// unimplemented/not currently used
message GetPreferencesForContextRequest {
	string preference_context = 1 [(validate.rules).string.min_len = 1];
}

// unimplemented/not currently used
message GetPreferencesForSettingRequest {
	string name = 1 [(validate.rules).string.min_len = 1];
}

message GetPreferenceRequest {
	string preference_context = 1 [(validate.rules).string.min_len = 1];
	string preference_name = 2 [(validate.rules).string.min_len = 1];
}

// Getter Responses
message GetPreferencesResponse {
	repeated ClientPreference preferences = 1;
}

// List Preferences Request
message ListPreferencesRequest {
}

// Update Request
message UpdatePreferenceRequest {
	repeated ClientPreference preferences = 1;
}

// List Preferences Response
message ListPreferencesResponse {
	repeated PreferenceTemplateEntry preferences = 1;
}

// Update Response
message UpdatePreferenceResponse {
	repeated ClientPreferenceResult preferences = 1;
}

service PreferencesService {
		rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
			option (google.api.http) = {
				get: "/api/v1/version"
			};
		}

		// returns list of all preferences in the system (not tenant specific).
		// for each preference, returns `preference_context`, `preference_name`, `preference_type`, `default_value`, `acceptable_values`
		rpc ListPreferences(ListPreferencesRequest) returns (ListPreferencesResponse) {
			option (google.api.http) = {
				get: "/api/v1/preferences/list"
			};
		}

		// returns tenant's current preferences (both explicitly set and not set by the tenant)
		rpc GetAllPreferences(GetAllPreferencesRequest) returns (GetPreferencesResponse) {
			option (google.api.http) = {
				get: "/api/v1/preferences"
			};
		}

	 // returns current tenant preference provided a `preference_context` and `preference_name`
	 rpc GetPreference(GetPreferenceRequest) returns (GetPreferencesResponse) {
	 	option (google.api.http) = {
	 		get: "/api/v1/preferences/{preference_context}/{preference_name}"
	 	};
	 }

	 // updates a tenant's specified preferences, and returns if operation was successful for each update
	 rpc UpdatePreferences(UpdatePreferenceRequest) returns (UpdatePreferenceResponse) {
			option (google.api.http) = {
				put: "/api/v1/preferences/update"
				body: "*"
			};
	 }

}
