/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	preferencespb "illum.io/cloud/api/generated/preferences"
	"illum.io/cloud/api/services/preferences/pkg/preferences"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type PreferencesClient struct {
	preferencespb.PreferencesServiceClient
	con *grpc.ClientConn
}

func (c *PreferencesClient) Close() {
	_ = c.con.Close()
}

// NewPreferencesClient creates a new gRPC client for service running on url endpoint
func NewPreferencesClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*PreferencesClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := preferencespb.NewPreferencesServiceClient(con)

	return &PreferencesClient{
		con:                      con,
		PreferencesServiceClient: client,
	}, nil
}

// NewPreferencesClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewPreferencesClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*PreferencesClient, error) {
	url, ok := config.LookupGRPCService(preferences.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", preferences.ServiceName)
	}
	return NewPreferencesClient(ctx, url, opts...)
}
