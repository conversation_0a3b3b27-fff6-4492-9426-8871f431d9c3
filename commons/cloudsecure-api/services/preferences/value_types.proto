syntax = "proto3";

package preferences;

import "google/protobuf/any.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/version/version.proto";
import "validate/validate.proto";
import "preferences/policy_preferences.proto";
import "preferences/labeling_preferences.proto";

option go_package = "illum.io/cloud/api/generated/preferences";

message PreferenceValueType {
  oneof value_type {
    EnforcementType enforce = 1;
    CodeDeployPreferences code_deploy = 2;
  }
}