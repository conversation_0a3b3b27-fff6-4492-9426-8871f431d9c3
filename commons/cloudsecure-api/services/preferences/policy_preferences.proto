syntax = "proto3";

package preferences;

import "google/protobuf/any.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/version/version.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/api/generated/preferences";

message EnforcementType {
  repeated string enforcement = 1 [(validate.rules).repeated = {items: {string: { in: ["subnet", "nic", "nic-subnet"]}}}];
}
