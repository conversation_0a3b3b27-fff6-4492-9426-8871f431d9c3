/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	nimbuspb "illum.io/cloud/api/generated/nimbus"
	"illum.io/cloud/api/services/nimbus/pkg/nimbus"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type NimbusClient struct {
	nimbuspb.NimbusServiceClient
	con *grpc.ClientConn
}

func (c *NimbusClient) Close() {
	_ = c.con.Close()
}

// NewNimbusClient creates a new gRPC client for service running on url endpoint
func NewNimbusClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*NimbusClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := nimbuspb.NewNimbusServiceClient(con)

	return &NimbusClient{
		con:                 con,
		NimbusServiceClient: client,
	}, nil
}

// NewNimbusClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewNimbusClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*NimbusClient, error) {
	url, ok := config.LookupGRPCService(nimbus.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", nimbus.ServiceName)
	}
	return NewNimbusClient(ctx, url, opts...)
}
