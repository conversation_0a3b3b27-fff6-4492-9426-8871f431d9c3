// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package nimbus;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/api/generated/nimbus";


message InvalidateUserSessionsRequest {
    string user_id = 1;
}

message InvalidateUserSessionsResponse {
}

service NimbusService {
    rpc InvalidateUserSessions(InvalidateUserSessionsRequest) returns (InvalidateUserSessionsResponse);
}
