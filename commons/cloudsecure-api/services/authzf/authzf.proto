// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package authzf;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/auth/auth.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/api/generated/authzf";


///////////////////////////////////////////////////////////////////////////////

message User {
  string tenant_id = 1;
  string user_id = 2;
}

message ServiceAccount {
  string tenant_id = 1;
  string service_account_id = 2;
}

message Role {
  string id = 1;
  string name = 2;
}

message RoleExt {
  string id = 1;
  string name = 2;
  string description = 3;
}

message AccessInfo {
  string permission = 1;
  string mode = 2;
}

message RoleDetails {
  string id = 1;
  string name = 2;
  string description = 3;
  repeated AccessInfo access = 4;
}

service AuthzfService {
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
    option (google.api.http) = {
      get: "/api/v1/version"
    };
  }

  rpc ListRoles(ListRolesRequest) returns (ListRolesResponse) {
    option (google.api.http) = {
      get: "/api/v1/roles"
    };
  };

  // Get details of roles and associated permissions
  rpc GetRolesInfo(GetRolesInfoRequest) returns(GetRolesInfoResponse) {
    option (google.api.http) = {
      get: "/api/v1/role_permissions"
    };
  }

  rpc GetUserAccess(GetUserAccessRequest) returns (GetUserAccessResponse);

  rpc GetUserRoles(GetUserRolesRequest) returns (GetUserRolesResponse) {
//    option (auth.required) = "user:r";
  }

  rpc SetUserRoles(SetUserRolesRequest) returns (SetUserRolesResponse){
//    option (auth.required) = "user:w";
  }

  rpc AddUserRoles(AddUserRolesRequest) returns (AddUserRolesResponse){
//    option (auth.required) = "user:w";
  }

  rpc RemoveUserRoles(RemoveUserRolesRequest) returns (RemoveUserRolesResponse) {
//    option (auth.required) = "user:w";
  }

  rpc UpdateUserRoles(UpdateUserRolesRequest) returns (UpdateUserRolesResponse) {
    //    option (auth.required) = "user:w";
  }

  rpc RemoveUser(RemoveUserRequest) returns (RemoveUserResponse) {
//    option (auth.required) = "user:w";
  }

  rpc GetServiceAccountAccess(GetServiceAccountAccessRequest) returns (GetServiceAccountAccessResponse);

  rpc GetServiceAccountRoles(GetServiceAccountRolesRequest) returns (GetServiceAccountRolesResponse);
  rpc SetServiceAccountRoles(SetServiceAccountRolesRequest) returns (SetServiceAccountRolesResponse);
  rpc AddServiceAccountRoles(AddServiceAccountRolesRequest) returns (AddServiceAccountRolesResponse);
  rpc RemoveServiceAccountRoles(RemoveServiceAccountRolesRequest) returns (RemoveServiceAccountRolesResponse);
  rpc RemoveServiceAccount(RemoveServiceAccountRequest) returns (RemoveServiceAccountResponse);

  // Give all users for a given role
  rpc UsersForRole(UsersForRoleRequest) returns (UsersForRoleResponse);

  // Get all service account for a given role
  rpc ServiceAccountsForRole(ServiceAccountsForRoleRequest) returns (ServiceAccountsForRoleResponse);
}
///////////////////////////////////////////////////////////////////////////////


///////////////////////////////////////////////////////////////////////////////
message ListRolesRequest {}
message ListRolesResponse {
  repeated RoleExt roles = 1;
}
///////////////////////////////////////////////////////////////////////////////

message GetRolesInfoRequest {};
message GetRolesInfoResponse {
  repeated RoleDetails roles = 1;
}

///////////////////////////////////////////////////////////////////////////////
message GetUserAccessRequest {
  User user = 1;
  repeated string extra_roles = 2;
}
message GetUserAccessResponse {
  repeated string access_items = 1;
}
///////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
message GetUserRolesRequest {
  User user = 1;
}
message GetUserRolesResponse {
  repeated string role_ids = 1;
}
///////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
message SetUserRolesRequest {
  User user = 1;
  repeated string role_ids = 2;
}
message SetUserRolesResponse {
}
///////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
message AddUserRolesRequest {
  User user = 1;
  repeated string role_ids = 2;
}
message AddUserRolesResponse {
  repeated Role roles = 1;
}
///////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
message UpdateUserRolesRequest {
  User user = 1;
  repeated string add = 2;
  repeated string remove = 3;
}
message UpdateUserRolesResponse {
  repeated Role roles = 1;
}
///////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
message RemoveUserRolesRequest {
  User user = 1;
  repeated string role_ids = 2;
}
message RemoveUserRolesResponse {
  repeated Role roles = 1;
}
///////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
message RemoveUserRequest {
  User user = 1;
}
message RemoveUserResponse {
}
///////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
message GetServiceAccountAccessRequest {
  ServiceAccount service_account = 1;
}
message GetServiceAccountAccessResponse {
  repeated string access_items = 1;
}
///////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
message GetServiceAccountRolesRequest {
  ServiceAccount service_account = 1;
}
message GetServiceAccountRolesResponse {
  repeated string role_ids = 1;
}
///////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
message SetServiceAccountRolesRequest {
  ServiceAccount service_account = 1;
  repeated string role_ids = 2;
}
message SetServiceAccountRolesResponse {
}
///////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
message AddServiceAccountRolesRequest {
  ServiceAccount service_account = 1;
  repeated string role_ids = 2;
}
message AddServiceAccountRolesResponse {
  repeated Role roles = 1;
}
///////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
message RemoveServiceAccountRolesRequest {
  ServiceAccount service_account = 1;
  repeated string role_ids = 2;
}
message RemoveServiceAccountRolesResponse {
  repeated Role roles = 1;
}
///////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
message RemoveServiceAccountRequest {
  ServiceAccount service_account = 1;
}
message RemoveServiceAccountResponse {
}
///////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
message UsersForRoleRequest {
  string tenant_id = 1;
  string role = 2;
}

message UsersForRoleResponse {
  repeated User users = 1;
}
///////////////////////////////////////////////////////////////////////////////

///////////////////////////////////////////////////////////////////////////////
message ServiceAccountsForRoleRequest {
  string tenant_id = 1;
  string role = 2;
}

message ServiceAccountsForRoleResponse {
  repeated ServiceAccount service_accounts = 1;
}
///////////////////////////////////////////////////////////////////////////////
