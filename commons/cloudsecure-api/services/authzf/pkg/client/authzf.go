package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	authzfpb "illum.io/cloud/api/generated/authzf"
	"illum.io/cloud/api/services/authzf/pkg/authzf"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type AuthzfClient struct {
	authzfpb.AuthzfServiceClient
	con *grpc.ClientConn
}

func (c *AuthzfClient) Close() {
	_ = c.con.Close()
}

// NewAuthzfClient creates a new gRPC client for service running on url endpoint
func NewAuthzfClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*AuthzfClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := authzfpb.NewAuthzfServiceClient(con)

	return &AuthzfClient{
		con:                 con,
		AuthzfServiceClient: client,
	}, nil
}

// NewAuthzfClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewAuthzfClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*AuthzfClient, error) {
	url, ok := config.LookupGRPCService(authzf.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", authzf.ServiceName)
	}
	return NewAuthzfClient(ctx, url, opts...)
}
