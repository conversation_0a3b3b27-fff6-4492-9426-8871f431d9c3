// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package syncscheduler;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/api/generated/syncscheduler";

message SyncAWSTask {
  string account_id = 1;
  string default_region = 2;
}

message SyncAzureTask {
  string subscription_id = 1;
  string tenant_id = 2;
}

message SyncOCITask {
  string tenant_id = 1;
  repeated string enabled_compartments = 2;
}

message SyncTask {
  string cs_tenant_id = 1;
  repeated string included_regions = 2;
  repeated string excluded_regions = 3;
  oneof cloud {
    SyncAWSTask aws = 4;
    SyncAzureTask azure = 5;
    SyncOCITask oci = 6;
  }
}

service SyncSchedulerService {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
        get: "/api/v1/version"
      };
    }
    // Add more API methods here
}
