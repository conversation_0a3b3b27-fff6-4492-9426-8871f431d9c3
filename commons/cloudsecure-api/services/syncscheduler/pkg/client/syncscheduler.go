package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	syncschedulerpb "illum.io/cloud/api/generated/syncscheduler"
	"illum.io/cloud/api/services/syncscheduler/pkg/syncscheduler"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type SyncSchedulerClient struct {
	syncschedulerpb.SyncSchedulerServiceClient
	con *grpc.ClientConn
}

func (c *SyncSchedulerClient) Close() {
	_ = c.con.Close()
}

// NewSyncSchedulerClient creates a new gRPC client for service running on url endpoint
func NewSyncSchedulerClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*SyncSchedulerClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := syncschedulerpb.NewSyncSchedulerServiceClient(con)

	return &SyncSchedulerClient{
		con:                        con,
		SyncSchedulerServiceClient: client,
	}, nil
}

// NewSyncSchedulerClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewSyncSchedulerClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*SyncSchedulerClient, error) {
	url, ok := config.LookupGRPCService(syncscheduler.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", syncscheduler.ServiceName)
	}
	return NewSyncSchedulerClient(ctx, url, opts...)
}
