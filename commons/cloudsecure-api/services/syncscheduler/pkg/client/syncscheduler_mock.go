package client

import (
	"go.uber.org/mock/gomock"

	"illum.io/cloud/api/generated/syncscheduler/mock"
)

// NewMockSyncSchedulerClient creates a new mock client
func NewMockSyncSchedulerClient(ctrl *gomock.Controller) (*SyncSchedulerClient, *mock.MockSyncSchedulerServiceClient) {
	m := mock.NewMockSyncSchedulerServiceClient(ctrl)
	return &SyncSchedulerClient{
		SyncSchedulerServiceClient: m,
	}, m
}
