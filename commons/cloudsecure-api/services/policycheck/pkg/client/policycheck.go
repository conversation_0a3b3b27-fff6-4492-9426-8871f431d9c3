package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	policycheckpb "illum.io/cloud/api/generated/policycheck"
	"illum.io/cloud/api/services/policycheck/pkg/policycheck"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type PolicycheckClient struct {
	policycheckpb.PolicycheckServiceClient
	con *grpc.ClientConn
}

func (c *PolicycheckClient) Close() {
	_ = c.con.Close()
}

// NewPolicycheckClient creates a new gRPC client for service running on url endpoint
func NewPolicycheckClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*PolicycheckClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := policycheckpb.NewPolicycheckServiceClient(con)

	return &PolicycheckClient{
		con:                      con,
		PolicycheckServiceClient: client,
	}, nil
}

// NewPolicycheckClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewPolicycheckClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*PolicycheckClient, error) {
	url, ok := config.LookupGRPCService(policycheck.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", policycheck.ServiceName)
	}
	return NewPolicycheckClient(ctx, url, opts...)
}
