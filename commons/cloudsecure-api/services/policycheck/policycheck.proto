// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package policycheck;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "common/version/version.proto";
import "common/wipeout/wipeout.proto";
import "google/protobuf/timestamp.proto";

import "common/policy/policy.proto";

option go_package = "illum.io/cloud/api/generated/policycheck";

message PolicyResponseProtocolPort { // #/definitions/pp
  optional int32 proto = 1;
  repeated string port = 2; // both port and src_port and repeated string
  repeated string src_port = 3;
}

message PolicyResponseSetBasedRule {
  repeated string src_sets = 1;
  repeated string dst_sets = 2;
  repeated PolicyResponseProtocolPort dst_pp = 3;
  string network_type = 4;
}

message PolicyResponseNetworkRule { // #/definitions/sb_network_rule
  repeated PolicyResponseSetBasedRule rules = 1; // #/definitions/set_based_rules
  repeated string networks = 2;
}

message RuleProcessOrdering {
  string key = 1;
  string action = 2;
}

message PolicyResponseRules {
  string ver = 1;
  repeated PolicyResponseNetworkRule override_deny = 2;
  repeated PolicyResponseNetworkRule network_access = 3;
  repeated PolicyResponseNetworkRule network_deny = 4;
  repeated RuleProcessOrdering rule_process_ordering = 5;
}

message PolicyResponseIP { // #/definitions/id_ips
  int64 id = 1;
  repeated string ips = 2;
}

message PolicyResponseIPSets {
  optional string id = 1;
  repeated PolicyResponseIP local = 2; // #/definitions/local_peer_ipset
  repeated PolicyResponseIP peer = 3; // #/definitions/local_peer_ipset
  repeated string ips = 4; // #/definitions/ipl_set_ids
  repeated string names = 5; // #/definitions/fqdn_set_ids
}

message PolicyResponseNetworks {
  optional string net = 1;
  optional string type = 2;
  repeated PolicyResponseIPSets sets = 3;
}

message PolicyResponseSets {
  string ver = 1;
  repeated PolicyResponseNetworks networks = 2;
}

message PolicyResponse {
  PolicyResponseRules policy = 1;
  PolicyResponseSets sets = 2;
}

message EnforcementPointDefinition {
  string          csp_enforcement_point_id = 1;
  string          account_id = 2;
  string          enforcement_point_id = 3 [(validate.rules).string.uuid = true];
  string          policy_engine_enforcement_point_id = 4 [(validate.rules).string.uuid = true];
  string          resource_group_name = 5;
  string          region = 6;
  string          cloud = 7;
  optional string azure_tenant_csp_id = 8;
  string          type = 9;
  optional string name = 10;
  optional string association = 11;
}

message SetEnforcementPointsRequest {
  repeated EnforcementPointDefinition enforcement_points = 1;
  string data_plane = 2;
}

message FailedEnforcementPointUpdate {
  string csp_enforcement_point_id = 1;
  string status = 2;
  optional string message = 3;
}

message SetEnforcementPointsResponse {
  repeated FailedEnforcementPointUpdate failed_enforcement_point_updates = 1;
}

message ListEnforcementPointsPolicyStatusRequest {
  string enforcement_status = 1 [(validate.rules).string = {in: ["failed"]}];
}

message ListEnforcementPointsPolicyStatusResponse {
  repeated EnforcementPointPolicyStatusResponse enforcement_status = 1;
}

message ListEnforcementsStatusRequest {
  message EnforcementPoint {
    string csp_id = 1;
    string account_id = 2;
  }
  repeated EnforcementPoint eps = 1;
}

message ListEnforcementsStatusResponse {
  repeated policy.EnforcementStatus statuses = 1;
}

message EnforcementPointPolicyStatusRequest {
  string enforcement_point_id = 1 [(validate.rules).string.uuid = true];
  string enforcement_status = 2 [(validate.rules).string = {in: ["failed"]}];
}

message EnforcementPointPolicyStatusResponse {
  string          csp_enforcement_point_id = 1;
  string          account_id = 2;
  string          enforcement_point_id = 3 [(validate.rules).string.uuid = true];
  string          region = 4;
  string          cloud = 5;
  string          name = 6;

  string    enforcement_status = 7;
  optional  PolicyEnforcementError enforcement_state_error = 8;
  google.protobuf.Timestamp enforcement_status_last_updated_at = 9;
}

message EnforcementPointPolicyImpactRequest {
  string id = 1;
  optional string update_type = 2 [(validate.rules).string = {in: ["create", "update", "delete"]}];
  optional bool is_illumio_managed = 3; // Security control rules that are managed by Illumio CloudSecure
}

message EnforcementPointRuleForPolicyImpact {
  optional string update_type = 1;
  policy.CSPRule rule = 2;
  bool is_illumio_managed = 3; // Security control rules that are managed by Illumio CloudSecure
}

message EnforcementPointPolicyImpactResponse {
  string id   = 1;
  string csp_id = 2;
  string name = 3;
  string type = 4;
  string cloud = 5;
  repeated EnforcementPointRuleForPolicyImpact rules = 6;
}

message Account {
  string account_id = 1;
  optional string name = 2;
  string cloud = 3;
  string access_mode = 4; // Expected values: Read, ReadWrite
}

message ListEnforcementPointsPolicyImpactRequest {
  string rule_set_id = 1;
  string policy_version = 2 [(validate.rules).string = {in: ["draft", "active"]}];
}

message ImpactedEnforcementPoint {
  string id = 1;
  string csp_id = 2;
  optional string name = 3;
  string type = 4;
  Account account = 5;
  optional int32 workloads_impacted_count = 6;
}

message ListEnforcementPointsPolicyImpactResponse {
  repeated ImpactedEnforcementPoint enforcement_points = 1;
}

message ListPolicyImpactRequest {
  PolicyChangeSubset change_subset = 1;
}

message PolicyChangeSubset {
  repeated ResourceReference rule_sets = 1;
}

message ResourceReference {
  string href = 1;
}

message ListPolicyImpactResponse {
  repeated PolicyImpactResponse policy_impact =1;
}

message PolicyImpactResponse {
  ResourceReference rule_set = 1;
  repeated ImpactedEnforcementPoint enforcement_points = 2;
}

message PolicyEnforcementError {
  string error_token = 1;
  string error_message = 2;
  optional string error_fault = 3;
  google.protobuf.Timestamp created_at = 4;
}

message PolicyDigestResponse {
  policy.PolicyDigest policy_digest = 1;
  google.protobuf.Timestamp created_at = 2;
}

message CSPPolicyEnforcementResponseTask {
  string tenant_id = 1 [(validate.rules).string.uuid = true];
  string csp_enforcement_point_id = 2;
  string enforcement_point_id = 3 [(validate.rules).string.uuid = true];

  optional PolicyDigestResponse policy_digest = 4;
  optional PolicyEnforcementError error = 5;
  google.protobuf.Timestamp originated_at = 6;
}

message SetEnforcementPointPolicyStatusResponse {}

message SetEnforcementModeRequest {
  policy.EnforcementMode mode = 1 [(validate.rules).enum.defined_only = true];
  repeated string enforcement_point_ids = 2 [(validate.rules).repeated = {min_items: 1, items: {string: {uuid: true}}}];
}

message SetEnforcementModeResponse {}

message EnforcePolicyRequest {
  repeated string csp_enforcement_point_ids = 1 [(validate.rules).repeated = {min_items: 1}];
}

message EnforcePolicyResponse {}

service PolicycheckService {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
        get: "/api/v1/version"
      };
    }

    // Enforcement points CRUD API
    rpc ListEnforcementsStatus(ListEnforcementsStatusRequest) returns (ListEnforcementsStatusResponse);

    rpc SetEnforcementPoints(SetEnforcementPointsRequest) returns (SetEnforcementPointsResponse) {
      option (google.api.http) = {
        put: "/api/v1/enforcement_points"
        body: "*"
      };
    }

    rpc ListEnforcementPointsPolicyStatus(ListEnforcementPointsPolicyStatusRequest) returns (ListEnforcementPointsPolicyStatusResponse) {
      option (google.api.http) = {
        get: "/api/v1/enforcement_points/policy_status"
      };
    }

    rpc GetEnforcementPointPolicyStatus(EnforcementPointPolicyStatusRequest) returns (EnforcementPointPolicyStatusResponse) {
      option (google.api.http) = {
        get: "/api/v1/enforcement_points/{enforcement_point_id}/policy_status"
      };
    }

    // Policy Impact API
    rpc GetEnforcementPointPolicyImpact(EnforcementPointPolicyImpactRequest) returns (EnforcementPointPolicyImpactResponse) {
      option (google.api.http) = {
        get: "/api/v1/enforcement_points/{id}/policy_impact"
      };
    }

    rpc ListEnforcementPointsPolicyImpact(ListEnforcementPointsPolicyImpactRequest) returns (ListEnforcementPointsPolicyImpactResponse) {
      option (google.api.http) = {
        post: "/api/v1/sec_policy/{policy_version}/rule_sets/{rule_set_id}/enforcement_point_impact"
        body: "*"
      };
    }

    rpc ListPolicyImpact(ListPolicyImpactRequest) returns (ListPolicyImpactResponse) {
      option (google.api.http) = {
        post: "/api/v1/sec_policy/draft/rule_sets/enforcement_point_impact"
        body: "*"
      };
    }

    // Enforcement Point Mode API
    rpc SetEnforcementMode(SetEnforcementModeRequest) returns (SetEnforcementModeResponse) {
    option (google.api.http) = {
      post: "/api/v1/enforcement_points/mode"
      body: "*"
    };
  }

  rpc EnforcePolicy(EnforcePolicyRequest) returns (EnforcePolicyResponse) {
    option (google.api.http) = {
      post: "/api/v1/enforcement_points/enforce"
      body: "*"
    };
  }

  rpc DeleteTenant(wipeout.DeleteTenantDataRequest) returns (wipeout.DeleteTenantDataResponse);
}
