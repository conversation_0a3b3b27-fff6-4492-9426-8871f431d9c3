// Copyright 2024 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package illumio.cloud.k8scluster.v1;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "common/version/version.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "illum.io/cloud/api/generated/k8sclustersync";

service K8SclustersyncService {
    rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
        option (google.api.http) = {
        get: "/api/v1/version"
      };
    }
    // Add more API methods here
}