package client

import (
	"go.uber.org/mock/gomock"

	"illum.io/cloud/api/generated/k8sclustersync/mock"
)

// NewMockK8SclustersyncClient creates a new mock client
func NewMockK8SclustersyncClient(ctrl *gomock.Controller) (*K8SclustersyncClient, *mock.MockK8SclustersyncServiceClient) {
	m := mock.NewMockK8SclustersyncServiceClient(ctrl)
	return &K8SclustersyncClient{
		K8SclustersyncServiceClient: m,
	}, m
}
