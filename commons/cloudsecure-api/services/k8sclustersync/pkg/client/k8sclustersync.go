/**
 * Copyright 2023 Illumio, Inc. All Rights Reserved.
 */

package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	k8sclustersyncpb "illum.io/cloud/api/generated/k8sclustersync"
	"illum.io/cloud/api/services/k8sclustersync/pkg/k8sclustersync"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type K8SclustersyncClient struct {
	k8sclustersyncpb.K8SclustersyncServiceClient
	con *grpc.ClientConn
}

func (c *K8SclustersyncClient) Close() {
	_ = c.con.Close()
}

// NewK8SclustersyncClient creates a new gRPC client for service running on url endpoint
func NewK8SclustersyncClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*K8SclustersyncClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := k8sclustersyncpb.NewK8SclustersyncServiceClient(con)

	return &K8SclustersyncClient{
		con:                         con,
		K8SclustersyncServiceClient: client,
	}, nil
}

// NewK8SclustersyncClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewK8SclustersyncClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*K8SclustersyncClient, error) {
	url, ok := config.LookupGRPCService(k8sclustersync.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", k8sclustersync.ServiceName)
	}
	return NewK8SclustersyncClient(ctx, url, opts...)
}
