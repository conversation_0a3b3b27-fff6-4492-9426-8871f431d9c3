package client

import (
	"context"
	"fmt"

	"google.golang.org/grpc"

	administrationpb "illum.io/cloud/api/generated/administration"
	administration "illum.io/cloud/api/services/administration/pkg"
	"illum.io/cloud/pkg/config"
	"illum.io/cloud/pkg/grpcutil"
)

type AdministrationClient struct {
	administrationpb.AdministrationClient
	con *grpc.ClientConn
}

func (c *AdministrationClient) Close() {
	_ = c.con.Close()
}

// NewAdministrationClient creates a new gRPC client for service running on url endpoint
func NewAdministrationClient(ctx context.Context, url string, opts ...grpcutil.ClientOption) (*AdministrationClient, error) {

	con, err := grpcutil.SetupGRPCClientCon(ctx, url, opts...)
	if err != nil {
		return nil, err
	}

	client := administrationpb.NewAdministrationClient(con)

	return &AdministrationClient{
		con:                  con,
		AdministrationClient: client,
	}, nil
}

// NewAdministrationClientLookup creates a new gRPC client trying to locate its endpoint internally
func NewAdministrationClientLookup(ctx context.Context, opts ...grpcutil.ClientOption) (*AdministrationClient, error) {
	url, ok := config.LookupGRPCService(administration.ServiceName)
	if !ok {
		return nil, fmt.Errorf("failed to locate service %v", administration.ServiceName)
	}
	return NewAdministrationClient(ctx, url, opts...)
}
