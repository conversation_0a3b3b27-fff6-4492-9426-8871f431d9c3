// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package administration;

import "google/protobuf/empty.proto";

import "google/protobuf/timestamp.proto";
import "common/auth/auth.proto";

import "google/api/annotations.proto";
import "validate/validate.proto";
import "common/version/version.proto";

option go_package = "illum.io/cloud/api/administration";

message Tenant {
  string id = 1;
  string preferred_region = 2;
  string country_code = 3;
  optional bool disabled = 4;
  optional bool trial = 5;
  optional google.protobuf.Timestamp trial_end_date = 6;
  string name = 7;
}

message PolicyEngine {
  string url = 1;
  string api_key_id = 2;
  string secret = 3;
}

message User {
  string user_id = 1;
  string email = 2;
  string role = 3;
  string oidc_provider_id = 4;
  string name = 5;

  string tenant_id = 6;
  uint64 uc_id = 7;
  bool is_active = 8;
  string first_name = 9;
  string last_name = 10;
  google.protobuf.Timestamp created_at = 11;
  repeated string roles = 12;
}

message OIDCProvider {
  string oidc_provider_id = 1;
  string issuer_url = 2;
  string client_id = 3;
}

message Domain {
  string domain_id = 1;
  string name = 2;
  string oidc_provider_id = 3;
  string tenant_id = 4;
}

message ServiceAccount {
  string service_account_id = 1;
  string service_account_key_id = 2 ;
  string service_account_role = 3;
  optional string description = 4;
  string name = 5;
  repeated string service_account_roles = 6;
}

message ServiceAccountSecret {
  string service_account_secret_id = 1;
  string name = 2;
  google.protobuf.Timestamp created_at = 3;
}

message CheckActiveUserRequest {
  string tenant_id = 1;
  string user_id = 2;
}

message CheckActiveUserResponse {
}

message GetUserRequest {
  string user_id = 1 [(validate.rules).string.uuid = true];
}

message GetUserResponse {
  User user = 1;
}

message LookupUserRequest {
  string email = 1 [(validate.rules).string.email = true];
}

message LookupUserResponse {
  User user = 1;
}

message AddUserRequest {
  string email = 1 [(validate.rules).string.email = true];
  string tenant_id = 2 [(validate.rules).string.uuid = true];
  string role = 3;
  string first_name = 4;
  string last_name = 5;
  optional uint64 uc_id = 6;
  repeated string roles = 7;
}

message AddUserResponse {
  User user = 1;
  bool created = 2;
}

message LookupAndAddUserRequest {
  string email = 1 [(validate.rules).string.email = true];
}

message LookupAndAddUserResponse {
  User user = 1;
  bool added = 2;
}

message UpdateUserRoleRequest {
  string user_id = 1 [(validate.rules).string.uuid = true];
  string role = 2;
  repeated string roles = 3;
}

message UpdateUserRoleResponse {
}

message UpdateUserRequest {
  string user_id = 1;
  string first_name = 2;
  string last_name = 3;
}

message UpdateUserResponse {
}

message UpdateUserRolesRequest {
  string user_id = 1;
  repeated string add = 2;
  repeated string remove = 3;
}

// return the full list of user roles after applying changes in UpdateUserRolesRequest
// useful, when we have concurrent clients and Client Role state (CRS) + requested role delta (d) can be != CRS+d
message UpdateUserRolesResponse {
  repeated string roles = 1;
}

message DeleteUserRequest {
  string user_id = 1 [(validate.rules).string.uuid = true];
}

message DeleteUserResponse {
  reserved 1;
  string email = 2;
}

message DeleteExternalUserRequest {
  string tenant_id = 1;
  string user_id = 2;
}

message DeleteExternalUserResponse{};

message AddServiceAccountRequest {
  string name = 1;
  string role = 2;
  optional string description = 4;
  repeated string roles = 5;
}

message AddServiceAccountResponse {
  string service_account_id = 1 [(validate.rules).string.uuid = true];
  string service_account_key_id = 2;
  string service_account_token = 3;
  string name = 4;
  string description = 5;
}

message AddServiceAccountSecretRequest {
  string name = 1 [(validate.rules).string.max_len = 100];
  string service_account_id = 2 [(validate.rules).string.uuid = true];
}

message AddServiceAccountSecretResponse{
  string service_account_secret_id = 1;
  string service_account_secret = 2;
  string name = 3;
  google.protobuf.Timestamp created_at = 4;
};

message DeleteServiceAccountSecretsRequest {
  repeated string ids = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      string: {
        uuid: true
      }
    }
  }];
  reserved 1;
}

message DeleteServiceAccountSecretsResponse{};

message UpdateServiceAccountRequest {
  string service_account_id = 1 [(validate.rules).string.uuid = true];
  optional string role = 2;
  optional string description = 3;
  repeated string roles = 4;
}

message UpdateServiceAccountResponse {
  bool success = 1;
}

message GetServiceAccountRequest {
  string service_account_id = 1 [(validate.rules).string.uuid = true];
}

message GetServiceAccountResponse {
  ServiceAccount service_account = 1;
}

message GetServiceAccountByKeyRequest {
  string service_account_key_id = 1;
}

message GetServiceAccountByKeyResponse {
  ServiceAccount service_account = 1;
  string tenant_id = 2;
}

message VerifyServiceAccountRequest {
  string service_account_key_id = 1;
  string service_account_secret = 2;
}

message VerifyServiceAccountResponse {
  string tenant_id = 1;
  string service_account_id = 2;
}

message ListServiceAccountResponse {
  repeated ServiceAccount service_accounts = 1;
}

message DeleteServiceAccountsRequest {
  string service_account_id = 1 [(validate.rules).string.uuid = true, deprecated = true, (validate.rules).string.ignore_empty = true];
  repeated string service_account_ids = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      string: {
        uuid: true
      }
    }
  }, (validate.rules).repeated.ignore_empty = true];
}

message ListServiceAccountSecretsRequest {
  string service_account_id = 1 [(validate.rules).string.uuid = true];
}

message ListServiceAccountSecretsResponse {
  repeated ServiceAccountSecret service_account_secrets = 1;
}

message DeleteServiceAccountsResponse {
  bool success = 1;
}

message CreateTenantRequest {
  string name = 1;
  string preferred_region = 2;
  string country_code = 3;
}

message CreateTenantResponse {
  Tenant tenant = 1;
}

message DeleteTenantRequest {
  string tenant_id = 1;
}

message DisableTenantRequest {
  string tenant_id = 1;
}

message DisableTenantResponse {
}

message DeleteTenantResponse {
  reserved 1;
}

message ActivateOrExtendTrialTenantRequest {
  string tenant_id = 1;
}

message ActivateOrExtendTrialTenantResponse {
  bool success = 1;
}

message GetTenantRequest {
  string tenant_id = 1 [(validate.rules).string.uuid = true];
}

message GetTenantResponse {
  Tenant tenant = 1;
}

message UpdateTenantRequest {
  bool trial = 1;
}

message UpdateTenantResponse {
  bool success = 1;
}

message ListTenantsResponse {
  repeated Tenant tenants = 1;
}

message DisableExpiredTrialTenantsResponse {
  repeated string tenant_ids = 1;
}

message LookUpTenantRequest {
  string name = 1;
}

message LookUpTenantResponse {
  Tenant tenant = 1;
}

message ListTenantUsersRequest {
  string tenant_id = 1 [(validate.rules).string.uuid = true];
}

message ListTenantUsersResponse {
  repeated User users = 1;
}

message GetTenantPolicyEngineRequest {
  string tenant_id = 1;
  string policy_engine_url = 2;
}

message TenantPolicyEngineResponse {
  PolicyEngine policy_engine = 1;
}

message SetTenantPolicyEngineRequest {
  string tenant_id = 1;
  string url = 2;
  string api_key_id = 3;
  string secret = 4;
}

message SetTenantPolicyEngineResponse {
  bool result = 1;
}

message AddDomainRequest {
  string name = 1;
  string oidc_provider_id = 2 [(validate.rules).string.uuid = true];
  string tenant_id = 3 [(validate.rules).string.uuid = true];
}

message AddDomainResponse {
  Domain domain = 1;
}

message DisableDomainRequest {
  string domain_id = 1 [(validate.rules).string.uuid = true];
}

message DisableDomainResponse {
  bool result = 1;
}

message DeleteDomainRequest {
  string domain_id = 1 [(validate.rules).string.uuid = true];
}

message DeleteDomainResponse {
  bool result = 1;
}

message GetDomainRequest {
  string name = 1;
}

message GetDomainResponse {
  Domain domain = 1;
}

message AddOIDCProviderRequest{
  string name = 1;
  string issuer_url = 2;
  string client_id = 3;
}

message AddOIDCProviderResponse{
  OIDCProvider oidc_provider = 1;
}

message LookUpOIDCProviderRequest{
  string name = 1;
}

message LookUpOIDCProviderResponse{
  OIDCProvider oidc_provider = 1;
}

message DisableOIDCProviderRequest{
  string oidc_provider_id = 1 [(validate.rules).string.uuid = true];
}

message DisableOIDCProviderResponse{
  bool result = 1;
}

message DeleteOIDCProviderRequest{
  string oidc_provider_id = 1 [(validate.rules).string.uuid = true];
}

message DeleteOIDCProviderResponse{
  bool result = 1;
}

message GetOIDCProviderRequest {
  string oidc_provider_id = 1 [(validate.rules).string.uuid = true];
}

message GetOIDCProviderResponse {
  OIDCProvider oidc_provider = 1;
}

message SignupOptions {
  bool ok_to_receive_emails = 1;
}

message SignupRequest {
  string first_name = 1 [(validate.rules).string = {ignore_empty: false}];
  string last_name = 2  [(validate.rules).string = {ignore_empty: false}];
  string email = 3 [(validate.rules).string = {email: true, ignore_empty: false}];
  string company_name = 4;
  string preferred_region = 5 [(validate.rules).string = {ignore_empty: false}];
  string country_code = 6 [(validate.rules).string = {ignore_empty: false}];
  SignupOptions options = 7;
}

message SignupResponse {
  bool result = 1;
}

message SignupPCEOptions {
  string org_id = 1;
  string org_uuid = 2;
  string pce_cluster = 3;
  string domain = 4;
}

message CensusSignupRequest {
  int64 uc_id = 1;
  string first_name = 2 [(validate.rules).string = {ignore_empty: false}];
  string last_name = 3  [(validate.rules).string = {ignore_empty: false}];
  string email = 4 [(validate.rules).string = {email: true, ignore_empty: false}];
  string company_name = 5;
  string preferred_region = 6 [(validate.rules).string = {ignore_empty: false}];
  string country_code = 7 [(validate.rules).string = {ignore_empty: false}];
  SignupOptions options = 8;
  SignupPCEOptions pce_options = 9;
}

message CensusSignupResponse {
  string tenant_id = 1;
  string user_id = 2;
}

message OIDCConfigRequest {
  string username = 1;
}

message OIDCConfigResponse {
  string user_type = 1;
  string issuer_url = 2;
  string client_id = 3;
}

message CreateAccessTokensRequest {
  string email = 1;
  repeated string extra_roles = 2;
}

message UserInfo {
  string id = 1;
  string email = 2;
  uint64 uc_id = 3;
  string first_name = 4;
  string last_name = 5;
  google.protobuf.Timestamp created_at = 6;
  string tenant_id = 7;
}

message TokenPair {
  string access = 1;
  string refresh = 2;
}

message AccessInfo {
  repeated string permissions = 1;
  repeated string scopes = 2;
}

message CreateAccessTokensResponse {
  TokenPair cloud_tokens = 1;
  UserInfo user_info = 2;
  AccessInfo access_info = 3;
}

message UsersForRoleRequest {
  string role = 1;
}

message UsersForRoleResponse {
  repeated User users = 1;
}

message ServiceAccountsForRoleRequest {
  string role = 1;
}

message ServiceAccountsForRoleResponse {
  repeated ServiceAccount service_accounts = 1;
}

service Administration {
  rpc GetVersion(google.protobuf.Empty) returns (version.VersionResponse) {
    option (google.api.http) = {
      get: "/api/v1/version"
    };
  }

  rpc OIDCConfig(OIDCConfigRequest) returns (OIDCConfigResponse) {
    option (google.api.http) = {
      post: "/api/v1/oidc_config"
    };
  }

  rpc GetUser(GetUserRequest) returns (GetUserResponse) {
    option (google.api.http) = {
      get: "/api/v1/user/{user_id}"
    };
  }

  rpc LookupUser(LookupUserRequest) returns (LookupUserResponse) {
    option (google.api.http) = {
      get: "/api/v1/user/lookup/{email}"
    };
  }

  rpc AddUser(AddUserRequest) returns (AddUserResponse) {
    option (google.api.http) = {
      post: "/api/v1/{tenant_id}/user/add"
      body: "*"
    };
  }

  // Deprecated
  rpc UpdateUserRole(UpdateUserRoleRequest) returns (UpdateUserRoleResponse);

  rpc UpdateUser(UpdateUserRequest) returns (UpdateUserResponse){
    option (google.api.http) = {
      post: "/api/v1/user/{user_id}"
      body: "*"
    };
  }

  rpc UpdateUserRoles(UpdateUserRolesRequest) returns (UpdateUserRolesResponse){
    option (google.api.http) = {
      post: "/api/v1/user/{user_id}/roles"
      body: "*"
    };
  }

  rpc DeleteUser(DeleteUserRequest) returns (DeleteUserResponse) {
    option (google.api.http) = {
      delete: "/api/v1/user/{user_id}"
    };
  }

  rpc AddServiceAccount(AddServiceAccountRequest) returns (AddServiceAccountResponse) {
    option (google.api.http) = {
      post: "/api/v1/service_account/add"
      body: "*"
    };
  }

  rpc GetServiceAccount(GetServiceAccountRequest) returns (GetServiceAccountResponse) {
    option (google.api.http) = {
      get: "/api/v1/service_account/{service_account_id}"
    };
  }

  rpc GetServiceAccountByKey(GetServiceAccountByKeyRequest) returns (GetServiceAccountByKeyResponse) {
    option (google.api.http) = {
      get: "/api/v1/service_account/by_key/{service_account_key_id}"
    };
  }

  // No HTTP endpoint as will be used by Authn service only
  rpc VerifyServiceAccount(VerifyServiceAccountRequest) returns (VerifyServiceAccountResponse);

  rpc UpdateServiceAccount(UpdateServiceAccountRequest) returns (UpdateServiceAccountResponse) {
    option (google.api.http) = {
      post: "/api/v1/service_account/update"
      body: "*"
    };
  }

  rpc ListServiceAccounts(google.protobuf.Empty) returns (ListServiceAccountResponse) {
    option (google.api.http) = {
      get: "/api/v1/service_account/list"
    };
  }

  rpc DeleteServiceAccounts(DeleteServiceAccountsRequest) returns (DeleteServiceAccountsResponse) {
    option (google.api.http) = {
      post: "/api/v1/service_account/delete"
      body: "*"
    };
  }

  rpc ListServiceAccountSecrets(ListServiceAccountSecretsRequest) returns (ListServiceAccountSecretsResponse) {
    option (google.api.http) = {
      get: "/api/v1/service_account_secret/list/{service_account_id}"
    };
  }

  rpc AddServiceAccountSecret(AddServiceAccountSecretRequest) returns (AddServiceAccountSecretResponse) {
    option (google.api.http) = {
      post: "/api/v1/service_account_secret/add/{service_account_id}"
      body: "*"
    };
  }

  rpc DeleteServiceAccountSecrets(DeleteServiceAccountSecretsRequest) returns (DeleteServiceAccountSecretsResponse) {
    option (google.api.http) = {
      post: "/api/v1/service_account_secret/delete",
      body: "*"
    };
  }

  rpc CreateTenant(CreateTenantRequest) returns (CreateTenantResponse);

  rpc DeleteTenant(DeleteTenantRequest) returns (DeleteTenantResponse);

  rpc DisableTenant(DisableTenantRequest) returns (DisableTenantResponse);

  rpc LookUpTenant(LookUpTenantRequest) returns (LookUpTenantResponse);

  rpc GetTenant(GetTenantRequest) returns (GetTenantResponse);

  rpc UpdateTenant(UpdateTenantRequest) returns (UpdateTenantResponse) {
    //        option (auth.required) = "tenant:w";
  }

  rpc ActivateOrExtendTrialTenant(ActivateOrExtendTrialTenantRequest) returns (ActivateOrExtendTrialTenantResponse);

  rpc ListTenantUsers(ListTenantUsersRequest) returns (ListTenantUsersResponse) {
    option (google.api.http) = {
      get: "/api/v1/{tenant_id}/users"
    };
  }

  //
  rpc ListTenants(google.protobuf.Empty) returns (ListTenantsResponse) {
    //        option (auth.required) = "tenant:r";
  }

  rpc DisableExpiredTrialTenants(google.protobuf.Empty) returns (DisableExpiredTrialTenantsResponse);

  rpc GetTenantPolicyEngine(GetTenantPolicyEngineRequest) returns (TenantPolicyEngineResponse);

  rpc SetTenantPolicyEngine(SetTenantPolicyEngineRequest) returns (SetTenantPolicyEngineResponse);

  // Deprecated
  // No http endpoint as the following APIs are not exposed via API GW (yet)
  rpc AddOIDCProvider(AddOIDCProviderRequest) returns (AddOIDCProviderResponse);

  // Deprecated
  rpc LookUpOIDCProvider(LookUpOIDCProviderRequest) returns (LookUpOIDCProviderResponse);

  // Deprecated
  rpc DisableOIDCProvider(DisableOIDCProviderRequest) returns (DisableOIDCProviderResponse);

  // Deprecated
  rpc DeleteOIDCProvider(DeleteOIDCProviderRequest) returns (DeleteOIDCProviderResponse);

  // Deprecated
  rpc GetOIDCProvider(GetOIDCProviderRequest) returns (GetOIDCProviderResponse);

  // Deprecated
  rpc AddDomain(AddDomainRequest) returns (AddDomainResponse);

  // Deprecated
  rpc DisableDomain(DisableDomainRequest) returns (DisableDomainResponse);

  // Deprecated
  rpc DeleteDomain(DeleteDomainRequest) returns (DeleteDomainResponse);

  // Deprecated
  rpc GetDomain(GetDomainRequest) returns (GetDomainResponse);

  // Deprecated
  rpc Signup(SignupRequest) returns (SignupResponse) {
    option (google.api.http) = {
      post: "/api/v1/signup"
      body: "*"
    };
  }

  rpc CheckActiveUser(CheckActiveUserRequest) returns (CheckActiveUserResponse);

  // Give all users for a given role
  rpc UsersForRole(UsersForRoleRequest) returns (UsersForRoleResponse) {
    option (google.api.http) = {
      get: "/api/v1/administration/role_users"
    };
  };
  // Get all service account for a given role
  rpc ServiceAccountsForRole(ServiceAccountsForRoleRequest) returns (ServiceAccountsForRoleResponse);
}

message LegacyTenantsRequest {}

message LegacyUser {
  string id = 1;
  uint64 uc_id = 2;
  string email = 3;
  string first_name = 4;
  string last_name = 5;
  google.protobuf.Timestamp created_at = 6;
  bool  active = 7;

  string okta_user_id = 8;
}

message LegacyTenant {
  string id = 1;
  string name = 2;

  string preferred_region = 3;
  string country_code = 4;
  bool disabled = 5;
  bool deleted = 6;
  bool trial = 7;
  optional google.protobuf.Timestamp trial_end_date = 8;

  bool emails_ok = 9;
  string okta_group_id = 10;

  google.protobuf.Timestamp created_at = 11;

  repeated string domains = 12;
  string policy_engine = 13;
}

message LegacyTenantsResponse {
  repeated LegacyTenant tenants = 1;
}

//
message TenantMigrationDataRequest {
  string tenant_id = 1;
}

message TenantMigrationDataResponse {
  LegacyTenant tenant = 1;
  repeated LegacyUser users = 2;
}
//

//
message CompleteTenantMigrationRequest {
  string tenant_id = 1;
}

message CompleteTenantMigrationResponse {
}
//

//
message DeleteUserCensusRequest {
  string tenant_id = 1;
  string user_id = 2;
}

message DeleteUserCensusResponse {

}
//


service AdministrationInternal {
  rpc Signup(CensusSignupRequest) returns (CensusSignupResponse);
  rpc DeleteTenant(DeleteTenantRequest) returns (DeleteTenantResponse);
  rpc AddExternalUser(AddUserRequest) returns (AddUserResponse);
  rpc DeleteExternalUser(DeleteExternalUserRequest) returns (DeleteExternalUserResponse);
  rpc CreateAccessTokens(CreateAccessTokensRequest) returns (CreateAccessTokensResponse);

  // tenant migration
  rpc LegacyTenants(LegacyTenantsRequest) returns (LegacyTenantsResponse);
  rpc TenantMigrationData(TenantMigrationDataRequest) returns (TenantMigrationDataResponse);
  rpc CompleteTenantMigration(CompleteTenantMigrationRequest) returns (CompleteTenantMigrationResponse);
  rpc DeleteUser(DeleteUserCensusRequest) returns(DeleteUserCensusResponse);
  //
}