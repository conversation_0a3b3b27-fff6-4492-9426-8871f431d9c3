plugins {
    id 'java-library'
}

dependencies {
    // logging
    implementation 'org.slf4j:slf4j-api'

    // implementation
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.boot:spring-boot-starter-aop'
    implementation 'io.projectreactor:reactor-core'

    // Jackson for JSON processing
    implementation 'com.fasterxml.jackson.core:jackson-core'
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'

    // Apache Commons Lang for Pair utility
    implementation 'org.apache.commons:commons-lang3'

    // testing
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.10.0'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.10.0'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.boot:spring-boot-starter-web'

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // Adding jsonwebtoken API
    implementation 'io.jsonwebtoken:jjwt-api'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson' // for JSON serialization
}