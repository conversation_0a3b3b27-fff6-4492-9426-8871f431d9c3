package com.illumio.data.model;

import io.jsonwebtoken.security.Keys;
import java.security.Key;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(name = "jwt.symmetric", havingValue = "true", matchIfMissing = false)

/**
 * Symmetric keys are generated similar with @KeyGenUtil.generateSymmetricKey;
 * The signkey and verifykey are the same.
 *
 * 1. generate HS256 key
 * 2. convert to PEM format
 * 3. encoded with base64 ( the kubernetes secret manager requires base64 encoded strings)
 * 4. copy and paste into jwt.verifykey and jwt.signkey
 */
public class SymmetricJWT extends AbstractJWT {

    @Override
    protected boolean isSymmetric() {
        return true;
    }

    @Override
    protected String getSignAlgorithm() {
        return SYMMETRIC_ALGORITHM;
    }

    @Override
    protected Key buildSigningKey() {
        if (this.signingKey != null) {
            return this.signingKey;
        }

        byte[] keyBytes = this.decodeSecret(this.jwtSignSecret);
        this.signingKey = Keys.hmacShaKeyFor(keyBytes);
        return this.signingKey;
    }

    @Override
    protected Key buildVerifyKey() {
        if (this.verifyKey != null) {
            return this.verifyKey;
        }

        byte[] keyBytes = this.decodeSecret(this.jwtVerifySecret);
        this.verifyKey = Keys.hmacShaKeyFor(keyBytes);
        return this.verifyKey;
    }
}
