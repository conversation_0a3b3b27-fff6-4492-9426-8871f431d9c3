package com.illumio.data.util;

import java.io.File;
import java.net.URISyntaxException;
import java.net.URL;

import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@UtilityClass
public class Util {

    @SneakyThrows
    public File getFileFromClassPathOrAbsolutePath(final String classPath, final String absolutePath) {
        if (null != absolutePath) {
            return new File(absolutePath);
        }
        if (null != classPath) {
            final URL fileURL = Util.class.getResource(classPath);
            if (null == fileURL) {
                log.info("Couldn't find file at class path {}", classPath);
            } else {
                try {
                    return new File(fileURL.toURI());
                } catch (final URISyntaxException e) {
                    log.info("Couldn't load file at class path {}", classPath);
                    throw e;
                }
            }
        }

        return null;
    }
}