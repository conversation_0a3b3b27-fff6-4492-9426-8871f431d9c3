package com.illumio.data.model;

import java.security.Key;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Component
// will create Asymmetric if missing
@ConditionalOnProperty(name = "jwt.symmetric", havingValue = "false", matchIfMissing = false)

/**
 * Asymmetric keys are generated similar with @KeyGenUtil.generateAsymmetricKeys;
 * The signkey is the private key and verifykey is the public key.
 *
 * 1. generate RSA key pairs
 * 2. convert to PEM format
 * 3. encoded with base64 ( the kubernetes secret manager requires base64 encoded strings)
 * 4. copy and paste into jwt.verifykey and jwt.signkey
 */
public class AsymmetricJWT extends AbstractJWT {

    @Override
    protected boolean isSymmetric() {
        return false;
    }

    @Override
    protected String getSignAlgorithm() {
        return ASYMMETRIC_ALGORITHM;
    }

    @Override
    protected Key buildSigningKey() throws NoSuchAlgorithmException, InvalidKeySpecException {
        if (this.signingKey != null ){
            return this.signingKey;
        }

        byte[] der = decodeSecret(this.jwtSignSecret);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(der);
        KeyFactory kf = KeyFactory.getInstance(KEY_ALGORITHM);
        this.signingKey = kf.generatePrivate(keySpec);
        return this.signingKey;
    }

    @Override
    protected Key buildVerifyKey() throws NoSuchAlgorithmException, InvalidKeySpecException {
        if (this.verifyKey != null ){
            return this.verifyKey;
        }

        byte[] der = decodeSecret(this.jwtVerifySecret);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(der);
        KeyFactory kf = KeyFactory.getInstance(KEY_ALGORITHM);
        this.verifyKey = kf.generatePublic(keySpec);
        return this.verifyKey;
    }
}
