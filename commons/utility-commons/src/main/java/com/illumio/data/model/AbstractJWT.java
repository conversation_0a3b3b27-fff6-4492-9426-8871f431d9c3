package com.illumio.data.model;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.time.Duration;
import java.util.Base64;
import java.util.Date;
import java.util.Map;
import org.springframework.beans.factory.annotation.Value;

/**
 * The common logic for JWT generation and verification.
 */
public abstract class AbstractJWT {
    public static final String SYMMETRIC_ALGORITHM = SignatureAlgorithm.HS256.getValue();
    public static final String ASYMMETRIC_ALGORITHM = SignatureAlgorithm.RS256.getValue();

    public static final String KEY_ALGORITHM = "RSA";

    // jwt signkey and verifykey are the base64 encoded PEM format of the key
    @Value("${jwt.signkey}")
    protected String jwtSignSecret;

    @Value("${jwt.verifykey}")
    protected String jwtVerifySecret;

    protected Key signingKey = null;
    protected Key verifyKey = null;

    protected abstract boolean isSymmetric() ;

    protected abstract String getSignAlgorithm() ;

    protected abstract Key buildSigningKey() throws NoSuchAlgorithmException, InvalidKeySpecException;

    protected abstract Key buildVerifyKey()throws NoSuchAlgorithmException, InvalidKeySpecException;

    protected byte[] decodeSecret(String secretKeyString) {
        byte[] outerDecoded = Base64.getDecoder().decode(secretKeyString);
        String pem = new String(outerDecoded, StandardCharsets.UTF_8);

        // Step 2: Strip headers and footers
        String keyPEM = pem
            .replaceAll("-----BEGIN ([A-Z ]*)-----", "")
            .replaceAll("-----END ([A-Z ]*)-----", "")
            .replaceAll("\\s", "");

        // Step 3: Decode the inner Base64 content to get DER bytes
        return Base64.getDecoder().decode(keyPEM);
    }

    public String generatePermissionsJwt(Map<String, Object> permissions, int timeoutMinutes) throws NoSuchAlgorithmException, InvalidKeySpecException {
        if (permissions == null || permissions.isEmpty() || timeoutMinutes < 1) {
            throw new IllegalArgumentException("permissions cannot be null or empty and timeoutMinutes must be greater than 0");
        }
        if (this.jwtSignSecret == null) {
            throw new IllegalStateException("jwt.signkey is not configured");
        }

        Duration duration = Duration.ofMinutes(timeoutMinutes);
        long expirationMillis = System.currentTimeMillis() + duration.toMillis();

        return Jwts.builder()
            .setClaims(permissions)
            .setIssuedAt(new Date())
            .setExpiration(new Date(expirationMillis))
            .signWith(this.buildSigningKey(), SignatureAlgorithm.forName(this.getSignAlgorithm()))
            .compact();
    }

    public Claims decodePermissionsJwt(String jwt) throws Exception {
        if (this.jwtVerifySecret == null) {
            throw new IllegalStateException("jwt.verifykey is not configured");
        }

        return Jwts.parser()
            .setSigningKey(this.buildVerifyKey())
            .parseClaimsJws(jwt)
            .getBody();
    }

}
