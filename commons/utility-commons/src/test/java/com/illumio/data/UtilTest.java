package com.illumio.data;

import com.illumio.data.util.Util;
import lombok.SneakyThrows;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

class UtilTest {

    @Test
    @SneakyThrows
    void testGetFileFromClassPathOrAbsolutePath_withAbsolutePath(@TempDir Path tempDir) {
        File tempFile = new File(tempDir.toFile(), "tempFile.txt");
        assertTrue(tempFile.createNewFile());

        File result = Util.getFileFromClassPathOrAbsolutePath(null, tempFile.getAbsolutePath());

        assertNotNull(result);
        assertEquals(tempFile.getAbsolutePath(), result.getAbsolutePath());
    }

    @Test
    void testGetFileFromClassPathOrAbsolutePath_withClasspathResource() {
        String classPath = "/testResource.txt";
        File result = Util.getFileFromClassPathOrAbsolutePath(classPath, null);

        assertNotNull(result);
        assertTrue(result.exists());
    }

    @Test
    void testGetFileFromClassPathOrAbsolutePath_withNonExistentClasspathResource() {
        String classPath = "/nonExistentResource.txt";
        File result = Util.getFileFromClassPathOrAbsolutePath(classPath, null);

        assertNull(result);
    }

    @Test
    void testGetFileFromClassPathOrAbsolutePath_withBothNullPaths() {
        File result = Util.getFileFromClassPathOrAbsolutePath(null, null);

        assertNull(result);
    }
}