package com.illumio.data.util;

import com.illumio.data.util.FlowDataValidator.ValidationResult;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Example usage of FlowDataValidator.
 */
class FlowDataValidatorExampleTest {

    @Test
    void demonstrateValidatorUsage() {
        // Example 1: Valid JSON
        String validJson = """
            {
                "SrcIP": "*************",
                "DestIP": "*********",
                "Port": 443,
                "Proto": "TCP",
                "SentBytes": 2048,
                "ReceivedBytes": 4096,
                "IllumioTenantId": "tenant-abc-123",
                "StartTime": "2023-12-01T14:30:00.123Z",
                "EndTime": "2023-12-01T14:35:00.456Z"
            }
            """;

        ValidationResult result = FlowDataValidator.validate(validJson);
        assertTrue(result.isValid());
        assertNull(result.getErrorCode());
        assertNull(result.getDetailMessage());
        System.out.println("Valid JSON: " + result.isValid());
        
        // Example 2: Invalid JSON - missing required field
        String invalidJson1 = """
            {
                "SrcIP": "*************",
                "DestIP": "*********",
                "Port": 443,
                "Proto": "TCP",
                "SentBytes": 2048,
                "ReceivedBytes": 4096
            }
            """;
        
        result = FlowDataValidator.validate(invalidJson1);
        assertFalse(result.isValid());
        assertNotNull(result.getErrorCode());
        assertNotNull(result.getDetailMessage());
        System.out.println("Invalid JSON (missing field) - Error Code: " + result.getErrorCode());
        System.out.println("Invalid JSON (missing field) - Message: " + result.getDetailMessage());
        
        // Example 3: Invalid JSON - wrong data type
        String invalidJson2 = """
            {
                "SrcIP": "*************",
                "DestIP": "*********",
                "Port": "four-four-three",
                "Proto": "TCP",
                "SentBytes": 2048,
                "ReceivedBytes": 4096,
                "IllumioTenantId": "tenant-abc-123",
                "StartTime": "2023-12-01T14:30:00.123Z",
                "EndTime": "2023-12-01T14:35:00.456Z"
            }
            """;
        
        result = FlowDataValidator.validate(invalidJson2);
        assertFalse(result.isValid());
        assertNotNull(result.getErrorCode());
        assertNotNull(result.getDetailMessage());
        System.out.println("Invalid JSON (wrong type) - Error Code: " + result.getErrorCode());
        System.out.println("Invalid JSON (wrong type) - Message: " + result.getDetailMessage());
        
        // Example 4: Invalid JSON - malformed JSON
        String malformedJson = """
            {
                "SrcIP": "*************",
                "DestIP": "*********",
                "Port": 443,
                "Proto": "TCP"
                // Missing comma and closing brace
            """;
        
        result = FlowDataValidator.validate(malformedJson);
        assertFalse(result.isValid());
        assertNotNull(result.getErrorCode());
        assertNotNull(result.getDetailMessage());
        System.out.println("Malformed JSON - Error Code: " + result.getErrorCode());
        System.out.println("Malformed JSON - Message: " + result.getDetailMessage());
    }
}
