package com.illumio.data.model;

import static org.junit.jupiter.api.Assertions.assertEquals;

import io.jsonwebtoken.Claims;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;


@SpringBootTest(classes = {AsymmetricJWT.class})

/**
 * Asymmetric keys are generated from @KeyGenUtil.generateAsymmetricKeys,
 *
 * 1. generate RSA key pairs
 * 2. convert to PEM format
 * 3. encoded with base64 ( the kubernetes secret manager requires base64 encoded strings)
 * 4. copy and paste into jwt.verifykey and jwt.signkey
 */
@TestPropertySource(
    properties = {
        "jwt.verifykey=LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUFyT3Nsd3U0aEVMN1hpYTlsanJnTApwOE5EQ1B6SXF4MXJUQW1QazZWOVdoTW9NT3BnZ1pqUGpsMGlOUVFiVStleFNiMFBjVWpEYWl4OG44MHhLZTd1CjVTVlF6ZTJZTmE3dFRCZ01pYmNlVGp5YmNpdEJKeE0yQjFZb2tLNlpxUTRjL2ZSekZEK2FmcHZ0MWNWTmoxWVAKSTdpdk0xZ1haS0lScjFEcDFFdTFmd0g1UzdjdzBpRFNNZ1VKMHFrQ00weSsydzhxRXRxRnNhMXR4T3NuM2RBKwpER3lkKzhCRHRabWRYSGFOMGg3ak1VcTVpbXA3OGRPc3BVMHU1ZVhQSWhkQXFvNDMwY3I0R01wYktTVXpwN2Z2CnR6dnMxaVNRa0NHeDVhOFQ0MWVCTnpvUmxKYnM4a0ozTm1uN0dEYVhQUmQrMys4Y0phQXhzOU43a3JPdE1RQ1oKNlFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t",
        "jwt.symmetric=false" // will create Asymmetric if missing
    })

public class AsymmetricJwtDecodeTest {
    @Autowired
    private AbstractJWT commonJWT;

    @Test
    void testAsymmetric() throws Exception {
        /**
         * This jwt token is generated by AsymmetricJWTTest.testAsymmetric
         * without expiration time (by changed the code directly)
         */
        String jwt =
                "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";

        Claims claims = commonJWT.decodePermissionsJwt(jwt);
        assertEquals("af84345d-0f7a-43ad-b19f-dcce53af2b9d", claims.get("tenant"));
        assertEquals("admin", claims.get("role"));
    }

}