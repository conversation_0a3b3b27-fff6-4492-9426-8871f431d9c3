package com.illumio.data.model;

import static org.junit.jupiter.api.Assertions.assertEquals;

import io.jsonwebtoken.Claims;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest(classes = {AsymmetricJWT.class})

/**
 * Asymmetric keys are generated from @KeyGenUtil.generateAsymmetricKeys,
 *
 * 1. generate RSA key pairs
 * 2. convert to PEM format
 * 3. encoded with base64 ( the kubernetes secret manager requires base64 encoded strings)
 * 4. copy and paste into jwt.verifykey and jwt.signkey
 */
@TestPropertySource(
        properties = {
            "jwt.verifykey=LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUFyT3Nsd3U0aEVMN1hpYTlsanJnTApwOE5EQ1B6SXF4MXJUQW1QazZWOVdoTW9NT3BnZ1pqUGpsMGlOUVFiVStleFNiMFBjVWpEYWl4OG44MHhLZTd1CjVTVlF6ZTJZTmE3dFRCZ01pYmNlVGp5YmNpdEJKeE0yQjFZb2tLNlpxUTRjL2ZSekZEK2FmcHZ0MWNWTmoxWVAKSTdpdk0xZ1haS0lScjFEcDFFdTFmd0g1UzdjdzBpRFNNZ1VKMHFrQ00weSsydzhxRXRxRnNhMXR4T3NuM2RBKwpER3lkKzhCRHRabWRYSGFOMGg3ak1VcTVpbXA3OGRPc3BVMHU1ZVhQSWhkQXFvNDMwY3I0R01wYktTVXpwN2Z2CnR6dnMxaVNRa0NHeDVhOFQ0MWVCTnpvUmxKYnM4a0ozTm1uN0dEYVhQUmQrMys4Y0phQXhzOU43a3JPdE1RQ1oKNlFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t",
            "jwt.signkey=LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JSUV2UUlCQURBTkJna3Foa2lHOXcwQkFRRUZBQVNDQktjd2dnU2pBZ0VBQW9JQkFRQ3M2eVhDN2lFUXZ0ZUoKcjJXT3VBdW53ME1JL01pckhXdE1DWStUcFgxYUV5Z3c2bUNCbU0rT1hTSTFCQnRUNTdGSnZROXhTTU5xTEh5Zgp6VEVwN3U3bEpWRE43WmcxcnUxTUdBeUp0eDVPUEp0eUswRW5FellIVmlpUXJwbXBEaHo5OUhNVVA1cCttKzNWCnhVMlBWZzhqdUs4eldCZGtvaEd2VU9uVVM3Vi9BZmxMdHpEU0lOSXlCUW5TcVFJelRMN2JEeW9TMm9XeHJXM0UKNnlmZDBENE1iSjM3d0VPMW1aMWNkbzNTSHVNeFNybUthbnZ4MDZ5bFRTN2w1YzhpRjBDcWpqZlJ5dmdZeWxzcApKVE9udCsrM08reldKSkNRSWJIbHJ4UGpWNEUzT2hHVWx1enlRbmMyYWZzWU5wYzlGMzdmN3h3bG9ER3owM3VTCnM2MHhBSm5wQWdNQkFBRUNnZ0VBQk9xWCtPemEvOURpbHByb0M2REJUcjhjZnRCQkhGY0tkVVQ5cDJNREoyRWkKdVdhbDRSNitkL3UvUEpGUXFFSzVld2FSdm45NmREV3VRcitMY0UwY3p3MjZqY0JTOHVQTEFoT0U5NzFweDlaWAo5OEpjYUlkV29wVnVpSDZPVytJaUxPT2JDZ09zN3RYeGN5eUM3Syt6dFNqMmJPMUtRbEEycWRkbWJvOTExT3M3CmN5VTE4ekRHeCtDRjdWK1hkSnZQVGRMS2FpYUJtQ1Z2dmhUUkJiUC9mRVJXaEdrUXN4ZnNqZmJMeVl4aEh2Y2MKSHE0ckk1ZFRIbStGRC9XVC8vc0FteUQrSjZoRTluR2JTbzBKZ0tTcm9Od0FUU0NqaWpsdloxTmNpaFZEMzl5ZQptcEhmK2xOZGFRTEQyalRtMDV5VEF1NGZvVnlPUWxoUEIwSVB1Vm5PeVFLQmdRRERKald6SkttL1htQ2dCZ3ZRCjlDK2l1UThPNFpKZWMycGNmM1EwRUx1aHhQbUVhNFg2MjlJVTdTaHZWdi8rYiswTUY1d3lsVUx5bDBndVNoK3QKcnNWaHUrYkJzMXVDbHg4TVZSZlkvMWhzUW1mSWJ1L3BPWFJ2N3VheUlkRkg5eXFWcGhVODZzK3FkSlVOK2FmOQoyT0pYSWRNVHltTTdHSXpPMEZiNkxZVkUyd0tCZ1FEaTFsNytUelZuNnVraVh5TE5rUmtYZ3hNM1hYeGxXRGxmCnZZNkY2U2FWR1RoSUZMb3RpTkZodE9tdE9jcm5PZzE2R0JjSkdOQ204ZWVVSkxVeEZPOE56ek9hcXBySjIwSjQKcWc3UDhJWWd5TjZiZ0VPc3R5d3A5RFJqTk1ZcEtJdUhDTHBPMG9IRUFSa0xOaFdPUFA4MnBneExHekNQRTBaOApkVVptS0M3Vml3S0JnR2VUZ0N6MDZLT0prLzliTmlwUmNIY05nZENFS1F1NGRsdGh5bXRYOWo2R0xXajdLTUZjCnFzUXdtNlY4cDZLRUJXekZtMEc5dHlySzRLU3c4dDljUTZiNzlCNGE5QmI3M3A3TkN6dEJTODBwOGF1UU5nV3oKV1BtUDh5SFlrUnVCTkRqNVRZeG5sSkVFRDVpN1NVNlNMby9RaEJlYzg4NEoyYkpySWI3bktqVEJBb0dCQU45NQovMEJnWE9jeVFkakJEWktrbnFuUlVyODY5UEVMU0FzUXZ6Y2lpZ1plUUZxVFFSRFhPQ0Vna1E4K1hxNExQWGZKCi8vZlFZTS80aHdRbVhmUXJBWkVEVDMzVEVvaEg0V2JyYzRoZEdHZDRQZ2ZKQitOQzJ0bkNZMUpSamVEQ3R5T2oKNzVqMFB6QVJQTkVVeVl2R2dGRWdxZi9jbTZ4NWV2bm11Q0t3OStNZEFvR0FVaFNjYmhUZ1ZMVzRqZnRuY0FHNgpTN0lHZklnWUdOSk1MaXpPMFh4Y2JRditxWTAvQmtiRWpxbVZVT1JvV1BqTXFtSXdidXArWFRxa0lCeXJTWHNKCmpYVzI5OS85R3dOeXc5Uk52WDkvZWlDS1JkZ3VCVUZnY0kwUWJTLy9aSFkzR3RBQ0lYTjJyU3dsVlo0K2hERjIKclpXS2ZiOHNhelhLU2I5bWE3M0dsb1E9Ci0tLS0tRU5EIFBSSVZBVEUgS0VZLS0tLS0=",
            "jwt.symmetric=false" // will create Asymmetric if missing
        })

public class AsymmetricJWTTest {
    @Autowired
    private AbstractJWT commonJWT;

    @Test
    void testAsymmetric() throws Exception {
        Map<String, Object> permissions = new HashMap<>();
        permissions.put("tenant", "af84345d-0f7a-43ad-b19f-dcce53af2b9d");
        permissions.put("role", "admin");
        String jwt = commonJWT.generatePermissionsJwt(permissions, 100);
        System.out.println("JWT Token: " + jwt);

        Claims claims = commonJWT.decodePermissionsJwt(jwt);
        assertEquals("af84345d-0f7a-43ad-b19f-dcce53af2b9d", claims.get("tenant"));
        assertEquals("admin", claims.get("role"));
    }

}
