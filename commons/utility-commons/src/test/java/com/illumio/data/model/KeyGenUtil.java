package com.illumio.data.model;

import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Base64;
import javax.crypto.SecretKey;
import org.junit.jupiter.api.Test;

public class KeyGenUtil {

    @Test
    void generateSymmetricKey() {
        SecretKey key = Keys.secretKeyFor(SignatureAlgorithm.HS256);
        // Encode keys to PEM format
        String publicKeyPEM = convertToPem(key.getEncoded(), "PUBLIC KEY");

        // Print key
        System.out.println(publicKeyPEM);
        System.out.println("--------------------");
        String encodedPublicKey = java.util.Base64.getEncoder().encodeToString(publicKeyPEM.getBytes());
        System.out.println(encodedPublicKey);
        System.out.println("--------------------");

        /**
         * -----BEGIN PUBLIC KEY-----
         * C8WTiUQRfuZS0fIKfBVVFKk+X89XImtrY0m6Hq9tNJ8=
         * -----END PUBLIC KEY-----
         * --------------------
         * LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KQzhXVGlVUVJmdVpTMGZJS2ZCVlZGS2srWDg5WEltdHJZMG02SHE5dE5KOD0KLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t
         */
    }

    @Test
    void generateAsymmetricKeys() throws Exception{
        KeyPairGenerator keyGen = KeyPairGenerator.getInstance(AbstractJWT.KEY_ALGORITHM);
        keyGen.initialize(2048);
        KeyPair keyPair = keyGen.generateKeyPair();

        // Get the public and private keys
        PublicKey publicKey = keyPair.getPublic();
        PrivateKey privateKey = keyPair.getPrivate();

        // Encode keys to PEM format
        String publicKeyPEM = convertToPem(publicKey.getEncoded(), "PUBLIC KEY");
        String privateKeyPEM = convertToPem(privateKey.getEncoded(), "PRIVATE KEY");

        // Print both keys
        System.out.println(publicKeyPEM);
        System.out.println("--------------------");
        String encodedPublicKey = java.util.Base64.getEncoder().encodeToString(publicKeyPEM.getBytes());
        System.out.println(encodedPublicKey);
        System.out.println("--------------------");
        System.out.println(privateKeyPEM);
        System.out.println("--------------------");
        String encodedPrivateKey = java.util.Base64.getEncoder().encodeToString(privateKeyPEM.getBytes());
        System.out.println(encodedPrivateKey);

        /**
         * -----BEGIN PUBLIC KEY-----
         * MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArOslwu4hEL7Xia9ljrgL
         * p8NDCPzIqx1rTAmPk6V9WhMoMOpggZjPjl0iNQQbU+exSb0PcUjDaix8n80xKe7u
         * 5SVQze2YNa7tTBgMibceTjybcitBJxM2B1YokK6ZqQ4c/fRzFD+afpvt1cVNj1YP
         * I7ivM1gXZKIRr1Dp1Eu1fwH5S7cw0iDSMgUJ0qkCM0y+2w8qEtqFsa1txOsn3dA+
         * DGyd+8BDtZmdXHaN0h7jMUq5imp78dOspU0u5eXPIhdAqo430cr4GMpbKSUzp7fv
         * tzvs1iSQkCGx5a8T41eBNzoRlJbs8kJ3Nmn7GDaXPRd+3+8cJaAxs9N7krOtMQCZ
         * 6QIDAQAB
         * -----END PUBLIC KEY-----
         * --------------------
         * LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUFyT3Nsd3U0aEVMN1hpYTlsanJnTApwOE5EQ1B6SXF4MXJUQW1QazZWOVdoTW9NT3BnZ1pqUGpsMGlOUVFiVStleFNiMFBjVWpEYWl4OG44MHhLZTd1CjVTVlF6ZTJZTmE3dFRCZ01pYmNlVGp5YmNpdEJKeE0yQjFZb2tLNlpxUTRjL2ZSekZEK2FmcHZ0MWNWTmoxWVAKSTdpdk0xZ1haS0lScjFEcDFFdTFmd0g1UzdjdzBpRFNNZ1VKMHFrQ00weSsydzhxRXRxRnNhMXR4T3NuM2RBKwpER3lkKzhCRHRabWRYSGFOMGg3ak1VcTVpbXA3OGRPc3BVMHU1ZVhQSWhkQXFvNDMwY3I0R01wYktTVXpwN2Z2CnR6dnMxaVNRa0NHeDVhOFQ0MWVCTnpvUmxKYnM4a0ozTm1uN0dEYVhQUmQrMys4Y0phQXhzOU43a3JPdE1RQ1oKNlFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t
         * --------------------
         * -----BEGIN PRIVATE KEY-----
         * MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCs6yXC7iEQvteJ
         * r2WOuAunw0MI/MirHWtMCY+TpX1aEygw6mCBmM+OXSI1BBtT57FJvQ9xSMNqLHyf
         * zTEp7u7lJVDN7Zg1ru1MGAyJtx5OPJtyK0EnEzYHViiQrpmpDhz99HMUP5p+m+3V
         * xU2PVg8juK8zWBdkohGvUOnUS7V/AflLtzDSINIyBQnSqQIzTL7bDyoS2oWxrW3E
         * 6yfd0D4MbJ37wEO1mZ1cdo3SHuMxSrmKanvx06ylTS7l5c8iF0CqjjfRyvgYylsp
         * JTOnt++3O+zWJJCQIbHlrxPjV4E3OhGUluzyQnc2afsYNpc9F37f7xwloDGz03uS
         * s60xAJnpAgMBAAECggEABOqX+Oza/9DilproC6DBTr8cftBBHFcKdUT9p2MDJ2Ei
         * uWal4R6+d/u/PJFQqEK5ewaRvn96dDWuQr+LcE0czw26jcBS8uPLAhOE971px9ZX
         * 98JcaIdWopVuiH6OW+IiLOObCgOs7tXxcyyC7K+ztSj2bO1KQlA2qddmbo911Os7
         * cyU18zDGx+CF7V+XdJvPTdLKaiaBmCVvvhTRBbP/fERWhGkQsxfsjfbLyYxhHvcc
         * Hq4rI5dTHm+FD/WT//sAmyD+J6hE9nGbSo0JgKSroNwATSCjijlvZ1NcihVD39ye
         * mpHf+lNdaQLD2jTm05yTAu4foVyOQlhPB0IPuVnOyQKBgQDDJjWzJKm/XmCgBgvQ
         * 9C+iuQ8O4ZJec2pcf3Q0ELuhxPmEa4X629IU7ShvVv/+b+0MF5wylULyl0guSh+t
         * rsVhu+bBs1uClx8MVRfY/1hsQmfIbu/pOXRv7uayIdFH9yqVphU86s+qdJUN+af9
         * 2OJXIdMTymM7GIzO0Fb6LYVE2wKBgQDi1l7+TzVn6ukiXyLNkRkXgxM3XXxlWDlf
         * vY6F6SaVGThIFLotiNFhtOmtOcrnOg16GBcJGNCm8eeUJLUxFO8NzzOaqprJ20J4
         * qg7P8IYgyN6bgEOstywp9DRjNMYpKIuHCLpO0oHEARkLNhWOPP82pgxLGzCPE0Z8
         * dUZmKC7ViwKBgGeTgCz06KOJk/9bNipRcHcNgdCEKQu4dlthymtX9j6GLWj7KMFc
         * qsQwm6V8p6KEBWzFm0G9tyrK4KSw8t9cQ6b79B4a9Bb73p7NCztBS80p8auQNgWz
         * WPmP8yHYkRuBNDj5TYxnlJEED5i7SU6SLo/QhBec884J2bJrIb7nKjTBAoGBAN95
         * /0BgXOcyQdjBDZKknqnRUr869PELSAsQvzciigZeQFqTQRDXOCEgkQ8+Xq4LPXfJ
         * //fQYM/4hwQmXfQrAZEDT33TEohH4Wbrc4hdGGd4PgfJB+NC2tnCY1JRjeDCtyOj
         * 75j0PzARPNEUyYvGgFEgqf/cm6x5evnmuCKw9+MdAoGAUhScbhTgVLW4jftncAG6
         * S7IGfIgYGNJMLizO0XxcbQv+qY0/BkbEjqmVUORoWPjMqmIwbup+XTqkIByrSXsJ
         * jXW299/9GwNyw9RNvX9/eiCKRdguBUFgcI0QbS//ZHY3GtACIXN2rSwlVZ4+hDF2
         * rZWKfb8sazXKSb9ma73GloQ=
         * -----END PRIVATE KEY-----
         * --------------------
         * 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
         */
    }

    private static String convertToPem(byte[] keyBytes, String type) {
        String base64Encoded = Base64.getMimeEncoder(64, "\n".getBytes()).encodeToString(keyBytes);
        return String.format("-----BEGIN %s-----\n%s\n-----END %s-----", type, base64Encoded, type);
    }
}
