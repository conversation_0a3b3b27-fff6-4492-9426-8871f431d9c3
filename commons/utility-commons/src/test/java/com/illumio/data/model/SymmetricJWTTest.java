package com.illumio.data.model;

import static org.junit.jupiter.api.Assertions.assertEquals;

import io.jsonwebtoken.Claims;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

@SpringBootTest(classes = {SymmetricJWT.class})

/**
 * Symmetric keys are generated from @KeyGenUtil.generateSymmetricKey,
 *
 * 1. generate HS256 key
 * 2. convert to PEM format
 * 3. encoded with base64 ( the kubernetes secret manager requires base64 encoded strings)
 * 4. copy and paste into jwt.verifykey and jwt.signkey
 */
@TestPropertySource(
    properties = {
        "jwt.verifykey=LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KQzhXVGlVUVJmdVpTMGZJS2ZCVlZGS2srWDg5WEltdHJZMG02SHE5dE5KOD0KLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t",
        "jwt.signkey=LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KQzhXVGlVUVJmdVpTMGZJS2ZCVlZGS2srWDg5WEltdHJZMG02SHE5dE5KOD0KLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0t",
        "jwt.symmetric=true"
    })

public class SymmetricJWTTest {
    @Autowired
    private AbstractJWT commonJWT;

    @Test
    void testSymmetric() throws Exception{
        Map<String, Object> permissions = new HashMap<>();
        permissions.put("tenant", "bf84345d-0f7a-43ad-b19f-dcce53af2b9d");
        permissions.put("role", "user");
        String jwt = commonJWT.generatePermissionsJwt(permissions, 100);
        System.out.println("JWT Token: " + jwt);

        Claims claims = commonJWT.decodePermissionsJwt(jwt);
        assertEquals("bf84345d-0f7a-43ad-b19f-dcce53af2b9d", claims.get("tenant"));
        assertEquals("user", claims.get("role"));
    }
}
