plugins {
    id 'java-library'
}

dependencies {
    // logging
    implementation 'org.slf4j:slf4j-api'

    // implementation
    implementation project(':commons:azure-commons')
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'io.projectreactor:reactor-core'
    implementation 'org.springframework.kafka:spring-kafka'
    implementation "io.projectreactor.kafka:reactor-kafka"
    implementation 'com.github.ben-manes.caffeine:caffeine'
    implementation 'com.azure:azure-storage-blob'
    implementation 'org.springframework.boot:spring-boot-starter-validation'

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
}