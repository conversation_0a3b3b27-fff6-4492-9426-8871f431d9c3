package com.illumio.data.component;

import static com.illumio.data.util.Constants.KAFKA_SASL_JAAS_CONFIG_OAUTHBEARER;
import static com.illumio.data.util.Constants.KAFKA_SASL_MECHANISM_OAUTHBEARER;
import static com.illumio.data.util.Constants.KAFKA_SASL_MECHANISM_PLAIN;
import static com.illumio.data.util.Constants.KAFKA_SECURITY_MECHANISM_SASL_SSL;

import com.illumio.data.config.KafkaConfig;
import com.illumio.data.managedidentity.kafka.KafkaOAuth2AuthenticateCallbackHandler;
import java.util.Properties;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.stereotype.Component;

@Component
public class ReceiverPropertyBuilder {

    public Properties build(KafkaConfig.receiverConfig receiverConfig) {
        Properties props = new Properties();

        props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, KAFKA_SECURITY_MECHANISM_SASL_SSL);
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, receiverConfig.getBootstrapServers());
        props.put(ConsumerConfig.GROUP_ID_CONFIG, receiverConfig.getGroupId());
        props.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, receiverConfig.getRequestTimeoutMs());
        props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, receiverConfig.getAutoOffsetReset());
        props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, receiverConfig.getMaxPollRecords());
        props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, receiverConfig.getMaxPartitionFetchBytes());

        if (Boolean.TRUE.equals(receiverConfig.getIsManagedIdentity())) {
            props.put(SaslConfigs.SASL_MECHANISM, KAFKA_SASL_MECHANISM_OAUTHBEARER);
            props.put(SaslConfigs.SASL_JAAS_CONFIG, KAFKA_SASL_JAAS_CONFIG_OAUTHBEARER);
            props.put(SaslConfigs.SASL_LOGIN_CALLBACK_HANDLER_CLASS, KafkaOAuth2AuthenticateCallbackHandler.class.getName());
        } else if (Boolean.TRUE.equals(receiverConfig.getIsConnectionString())) {
            props.put(SaslConfigs.SASL_MECHANISM, KAFKA_SASL_MECHANISM_PLAIN);
            props.put(SaslConfigs.SASL_JAAS_CONFIG, receiverConfig.getSaslJaasConfig());
        }

        return props;
    }
}
