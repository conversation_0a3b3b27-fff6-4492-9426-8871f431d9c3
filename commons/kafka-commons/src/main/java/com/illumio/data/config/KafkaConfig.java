package com.illumio.data.config;

import static com.illumio.data.util.Constants.KAFKA_PREFIX;

import jakarta.validation.constraints.NotBlank;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@ConfigurationProperties(prefix = KAFKA_PREFIX)
@Validated
@Getter
@Setter
public class KafkaConfig {

    private Map<String, SenderConfig> senders = new HashMap<>();
    private Map<String, receiverConfig> receivers = new HashMap<>();

    @Getter
    @Setter
    public static abstract class AbstractConfig {
        @NotBlank(message = "Mandatory property 'bootstrapServers' is missing in KafkaConfig")
        private String bootstrapServers;

        @NotBlank(message = "Mandatory property 'topic' is missing in KafkaConfig")
        private String topic;

        private Boolean isConnectionString = false;
        private Boolean isManagedIdentity = false;
        private String saslJaasConfig;
        private Integer requestTimeoutMs;
    }

    @Getter
    @Setter
    public static class SenderConfig extends AbstractConfig {
        private Integer deliveryTimeoutMs;
        private Integer lingerMs;
        private Integer batchSize;
        private Integer bufferMemory;
        private Integer maxBlockMs;
        private Integer maxInflight = 1024;
        private Integer senderMaxAttempts = 10;
        private Duration senderMinBackOff = Duration.ofSeconds(5);
    }

    @Getter
    @Setter
    public static class receiverConfig extends AbstractConfig {
        private String groupId;
        private String autoOffsetReset;
        private Integer maxPollRecords;
        private Integer maxPartitionFetchBytes;
        private Integer receiverMaxAttempts = 10;
        private Duration receiverBackoff = Duration.ofSeconds(5);
        private Duration commitFrequency = Duration.ofSeconds(10);
    }
}