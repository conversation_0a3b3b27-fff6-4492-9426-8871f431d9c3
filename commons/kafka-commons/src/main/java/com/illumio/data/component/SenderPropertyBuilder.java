package com.illumio.data.component;

import static com.illumio.data.util.Constants.KAFKA_SASL_JAAS_CONFIG_OAUTHBEARER;
import static com.illumio.data.util.Constants.KAFKA_SASL_MECHANISM_OAUTHBEARER;
import static com.illumio.data.util.Constants.KAFKA_SASL_MECHANISM_PLAIN;
import static com.illumio.data.util.Constants.KAFKA_SECURITY_MECHANISM_SASL_SSL;

import com.illumio.data.config.KafkaConfig;
import com.illumio.data.managedidentity.kafka.KafkaOAuth2AuthenticateCallbackHandler;
import java.util.Properties;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.stereotype.Component;

@Component
public class SenderPropertyBuilder {

    public Properties build(KafkaConfig.SenderConfig senderConfig) {
        Properties props = new Properties();

        props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, KAFKA_SECURITY_MECHANISM_SASL_SSL);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, senderConfig.getBootstrapServers());
        props.put(CommonClientConfigs.REQUEST_TIMEOUT_MS_CONFIG, senderConfig.getRequestTimeoutMs());
        props.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, senderConfig.getDeliveryTimeoutMs());

        if (Boolean.TRUE.equals(senderConfig.getIsManagedIdentity())) {
            props.put(SaslConfigs.SASL_MECHANISM, KAFKA_SASL_MECHANISM_OAUTHBEARER);
            props.put(SaslConfigs.SASL_JAAS_CONFIG, KAFKA_SASL_JAAS_CONFIG_OAUTHBEARER);
            props.put(SaslConfigs.SASL_LOGIN_CALLBACK_HANDLER_CLASS, KafkaOAuth2AuthenticateCallbackHandler.class.getName());
        } else if (Boolean.TRUE.equals(senderConfig.getIsConnectionString())) {
            props.put(SaslConfigs.SASL_MECHANISM, KAFKA_SASL_MECHANISM_PLAIN);
            props.put(SaslConfigs.SASL_JAAS_CONFIG, senderConfig.getSaslJaasConfig());
        }

        if (null != senderConfig.getLingerMs()) {
            props.put(ProducerConfig.LINGER_MS_CONFIG, senderConfig.getLingerMs());
        }
        if (null != senderConfig.getBatchSize()) {
            props.put(ProducerConfig.BATCH_SIZE_CONFIG, senderConfig.getBatchSize());
        }
        if (null != senderConfig.getBufferMemory()) {
            props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, senderConfig.getBufferMemory());
        }
        if (null != senderConfig.getMaxBlockMs()) {
            props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, senderConfig.getMaxBlockMs());
        }

        return props;
    }
}

