package com.illumio.data.util;

import com.illumio.data.config.KafkaConfig;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@Slf4j
public class KafkaConfigUtil {

    public static KafkaConfig.SenderConfig getSenderConfig(KafkaConfig kafkaConfig, String senderName) {
        Objects.requireNonNull(kafkaConfig, "kafkaConfig cannot be null");

        if (!StringUtils.hasText(senderName)) {
            throw new IllegalArgumentException(String.format("Sender name cannot be null/empty: %s", senderName));
        }

        return Optional.ofNullable(kafkaConfig.getSenders().get(senderName))
                .orElseThrow(()-> new IllegalArgumentException(String.format("SenderConfig for sender '%s' not found in KafkaConfig", senderName)));
    }

    public static KafkaConfig.receiverConfig getReceiverConfig(KafkaConfig kafkaConfig, String receiverName) {
        Objects.requireNonNull(kafkaConfig, "kafkaConfig cannot be null");

        if(!StringUtils.hasText(receiverName)) {
            throw new IllegalArgumentException(String.format("Receiver name cannot be null/empty: %s", receiverName));
        }

        return Optional.ofNullable(kafkaConfig.getReceivers().get(receiverName))
            .orElseThrow(() -> new IllegalStateException(String.format("ReceiverConfig for receiver '%s' not found in KafkaConfig", receiverName)));
    }

    public static String getSenderTopic(KafkaConfig kafkaConfig, String producerName) {
        return getSenderConfig(kafkaConfig, producerName).getTopic();
    }

    public static String getReceiverTopic(KafkaConfig kafkaConfig, String consumerName) {
        return getReceiverConfig(kafkaConfig, consumerName).getTopic();
    }
}