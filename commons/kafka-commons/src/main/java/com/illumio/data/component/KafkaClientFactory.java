package com.illumio.data.component;

import com.illumio.data.config.KafkaConfig;
import com.illumio.data.util.KafkaConfigUtil;
import java.util.Collections;
import java.util.Properties;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;

@Component
@RequiredArgsConstructor
public class KafkaClientFactory {

    private final KafkaConfig kafkaConfig;
    private final SenderPropertyBuilder senderPropertyBuilder;
    private final ReceiverPropertyBuilder receiverPropertyBuilder;

    public KafkaSender<String, String> createKafkaSender(String senderName) {
        KafkaConfig.SenderConfig senderConfig = KafkaConfigUtil.getSenderConfig(kafkaConfig, senderName);
        Properties properties = senderPropertyBuilder.build(senderConfig);

        SenderOptions<String, String> senderOptions = SenderOptions.<String, String>create(properties)
                .maxInFlight(senderConfig.getMaxInflight());

        return KafkaSender.create(senderOptions);
    }

    public KafkaReceiver<String, String> createKafkaReceiver(String receiverName) {
        KafkaConfig.receiverConfig receiverConfig = KafkaConfigUtil.getReceiverConfig(kafkaConfig, receiverName);
        Properties properties = receiverPropertyBuilder.build(receiverConfig);

        ReceiverOptions<String, String> receiverOptions = ReceiverOptions.<String, String>create(properties)
                .subscription(Collections.singleton(receiverConfig.getTopic()));

        return KafkaReceiver.create(receiverOptions);
    }
}
