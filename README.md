# Data Connector
A monorepo of data connector services in Java 17.
Services are in `services/` folder.
Shared common libraries are in `commons/`.

## Development tools
- Java 17
- IntelliJ IDEA Community or Ultimate
- Optional tools:
  - jenv: `brew install jenv`
  - Opentelemetry: https://opentelemetry.io/docs/zero-code/java/agent/
  - Metricat: https://metricat.dev/

## Building all services
`./gradlew build`

## Creating a docker image locally
`./gradlew :services:common-security-log:jibDockerBuild`