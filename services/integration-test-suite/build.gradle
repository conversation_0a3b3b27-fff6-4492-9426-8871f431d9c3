plugins {
    id 'java'
    id 'com.google.cloud.tools.jib'
    id 'jacoco'
}

apply from: "${project.rootDir}/gradle/scripts/jib-build.gradle"

version = "1.0.0"

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.junit.platform:junit-platform-console-standalone:1.10.2'
    testImplementation 'org.apache.commons:commons-text:1.10.0'
    testImplementation 'org.junit.jupiter:junit-jupiter:5.9.3'
    implementation 'io.prometheus:simpleclient:0.16.0'
    implementation 'io.prometheus:simpleclient_pushgateway:0.16.0'
    testImplementation project(':commons:insights-library')
    implementation project(':services:insights-query-service')
    implementation 'io.rest-assured:rest-assured:5.3.2'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    implementation 'org.yaml:snakeyaml:2.0'
    implementation 'org.slf4j:slf4j-api:2.0.13'
    implementation 'com.fasterxml.jackson.core:jackson-core:2.15.2'
    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.15.2'
    implementation 'org.testcontainers:junit-jupiter:1.19.7'
    implementation 'org.testcontainers:postgresql:1.19.7'
}

jib {
    container {
        mainClass = 'com.illumio.integration.TestSuiteRunner'
        entrypoint = [
            'java', '-cp', '@/app/jib-classpath-file',
            'com.illumio.integration.TestSuiteRunner'
        ]
    }
}

test {
    useJUnitPlatform()

    testLogging {
        events()
        showStandardStreams = false
    }

    afterSuite { desc, result ->
        if (!desc.parent) {
            println """
═══════════════════════ TEST SUMMARY ═══════════════════════
   Total tests   : ${String.format("%3d", result.testCount)}                          
   Passed        : ${String.format("%3d", result.successfulTestCount)}                          
   Failed        : ${String.format("%3d", result.failedTestCount)}                          
   Skipped       : ${String.format("%3d", result.skippedTestCount)}                          
════════════════════════════════════════════════════════════
"""
        }
    }
}