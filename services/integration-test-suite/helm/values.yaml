image:
  repositoryBase: "illum.azurecr.io/"
  repositoryName: "integration-test-suite"
  pullPolicy: Always
  tag: # value given at helm deployment

insightsConfig:
  baseUrl: "https://insights.sunnyvale.ilabs.io"
  tenantId: "c881063c-f156-4293-b5ad-516f283322e8"
  hourlyDiff: "6"

# Runs every 6hrs
cronJob:
  schedule: "0 */6 * * *"
  env:
    - name: PUSHGATEWAY_URL
      value: "prometheus-pushgateway.prometheus-pushgateway.svc.cluster.local:9091"