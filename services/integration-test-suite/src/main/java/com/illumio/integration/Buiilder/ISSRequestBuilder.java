package com.illumio.integration.Buiilder;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.illumio.integration.applications;

import java.io.IOException;
import java.time.ZonedDateTime;
import java.time.ZoneOffset;

public class ISSRequestBuilder {
    private static final ObjectMapper mapper = new ObjectMapper();
    private static final JsonNode configRoot;
    private static final Integer HOUR_DIFF = Integer.parseInt(System.getenv().getOrDefault("hourlyDiff", applications.getHourlyDiff("key_sunnyvale")));

    static {
        try {
            configRoot = mapper.readTree(ISSRequestBuilder.class.getClassLoader().getResourceAsStream("iss_config.json"));
        } catch (IOException e) {
            throw new RuntimeException("Failed to load template or config JSON", e);
        }
    }
    public static String buildFinalRequest(String testCaseName) throws JsonProcessingException {
        JsonNode template = mapper.createObjectNode();
        JsonNode config = configRoot.get(testCaseName);
        if (config == null) {
            throw new IllegalArgumentException("Cannot find config for testName: " + testCaseName);
        }
    
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC).withMinute(0).withSecond(0).withNano(0);
        String currStart = now.minusHours(HOUR_DIFF).toString();
        String currEnd = now.minusSeconds(1).toString();
    
        ObjectNode currentTimeFrame = mapper.createObjectNode()
                .put("startTime", currStart)
                .put("endTime", currEnd);
        
        if (config.has("currentTimeFrame")) {
            currentTimeFrame = (ObjectNode) config.get("currentTimeFrame");
        }
        
        ((ObjectNode) template).set("currentTimeFrame", currentTimeFrame);
        ((ObjectNode) template).putObject("pagination").put("pageNumber", 1).put("rowLimit", 1000);
    
        if (config.has("filters")) {
            ((ObjectNode) template).set("filters", config.get("filters"));
        }
        if (config.has("sortByFields")) {
            ((ObjectNode) template).set("sortByFields", config.get("sortByFields"));
        }
        if (config.has("projectFields")) {
            ((ObjectNode) template).set("projectFields", config.get("projectFields"));
        }
    
        return mapper.writeValueAsString(template);
    }
    
}