package com.illumio.integration;

import org.yaml.snakeyaml.Yaml;
import java.io.InputStream;
import java.util.Map;

public class applications {
    private static final Map<String, Object> config;

    static {
        try {
            Yaml yaml = new Yaml();
            InputStream inputStream = applications.class
                .getClassLoader()
                .getResourceAsStream("application.yaml");
            config = yaml.load(inputStream);
        } catch (Exception e) {
            throw new RuntimeException("Failed to load application.yaml", e);
        }
    }

    @SuppressWarnings("unchecked")
    private static String getConfigValue(String... path) {
        Map<String, Object> current = config;
        for (int i = 0; i < path.length - 1; i++) {
            current = (Map<String, Object>) current.get(path[i]);
            if (current == null) {
                throw new IllegalStateException("Configuration path not found: " + String.join(".", path));
            }
        }
        Object value = current.get(path[path.length - 1]);
        if (value == null) {
            throw new IllegalStateException("Configuration value not found: " + String.join(".", path));
        }
        return value.toString();
    }

    public static String getValueString(String service) {
        return getConfigValue(service, "base", "url");
    }

    public static String getTenantId(String service) {
        return getConfigValue(service, "tenant", "id");
    }

    public static String getHourlyDiff(String service) {return getConfigValue(service, "time_duration", "hourly_diff");}

}
