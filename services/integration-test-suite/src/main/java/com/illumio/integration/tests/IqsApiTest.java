package com.illumio.integration.tests;
import com.illumio.data.model.constants.WidgetId;
import com.illumio.integration.applications;
import io.prometheus.client.CollectorRegistry;
import io.prometheus.client.Counter;
import io.prometheus.client.exporter.PushGateway;
import org.junit.jupiter.api.AfterAll;
import java.util.stream.Stream;

import lombok.extern.slf4j.Slf4j;
import java.io.IOException;

import io.restassured.RestAssured;
import io.restassured.response.Response;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.Arguments;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.integration.Buiilder.IQSRequestBuilder;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;

@Slf4j
public class IqsApiTest {
    private static final List<String> passedWidgets = new ArrayList<>();
    private static final List<String> failedWidgets = new ArrayList<>();
    private static final String PUSHGATEWAY_URL = System.getenv().getOrDefault("PUSHGATEWAY_URL", "prometheus-pushgateway.prometheus-pushgateway.svc.cluster.local:9091");
    private static final String JOB_NAME = "iqs-api-test-suite";
    private static String INSTANCE = "unknown";
    private static final String IQS_BASE_URL = System.getenv().getOrDefault("baseUrl", applications.getValueString("key_sunnyvale"));
    private static final String TENANT_ID = System.getenv().getOrDefault("tenantId", applications.getTenantId("key_sunnyvale"));
    private static final Integer HOUR_DIFF = Integer.parseInt(System.getenv().getOrDefault("hourlyDiff", applications.getHourlyDiff("key_sunnyvale")));

    private static final CollectorRegistry registry = new CollectorRegistry();
    private static final Counter testSuccessCounter = Counter.build()
            .name("iqs_test_success_total")
            .help("Number of successful IQS tests")
            .labelNames("instance", "widget_id")
            .register(registry);
    private static final Counter testFailureCounter = Counter.build()
            .name("iqs_test_failure_total")
            .help("Number of failed IQS tests")
            .labelNames("instance", "widget_id")
            .register(registry);


    private static JsonNode widgetsRoot;

    static {
        try {
            INSTANCE = java.net.InetAddress.getLocalHost().getHostName();
            ObjectMapper mapper = new ObjectMapper();
            widgetsRoot = mapper.readTree(
                    IqsApiTest.class.getClassLoader().getResourceAsStream("iqs_config.json")
            );
        } catch (Exception e) {
            INSTANCE = "unknown";
            throw new RuntimeException("Failed to load widgets_requests.json", e);
        }
    }

    private static final Counter testFailureByStatusCodeCounter = Counter.build()
            .name("iqs_test_failure_by_status_code_total")
            .help("Number of IQS test failures categorized by HTTP status code")
            .labelNames("instance", "widget_id", "status_code")
            .register(registry);

    private String buildFinalRequest(JsonNode widgetConfigNode) throws JsonProcessingException {
        ObjectMapper mapper = new ObjectMapper();
        IQSRequestBuilder request = new IQSRequestBuilder();

        DateTimeFormatter formatter = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

        if (widgetConfigNode.has("currentTimeFrame") && widgetConfigNode.has("comparisonTimeFrame")) {
            JsonNode curr = widgetConfigNode.get("currentTimeFrame");
            JsonNode comp = widgetConfigNode.get("comparisonTimeFrame");

            String currStart = curr.get("startTime").asText();
            String currEnd = curr.get("endTime").asText();
            String compStart = comp.get("startTime").asText();
            String compEnd = comp.get("endTime").asText();

            request.setCurrentTimeFrame(new IQSRequestBuilder.TimeFrame(currStart, currEnd));
            request.setComparisonTimeFrame(new IQSRequestBuilder.TimeFrame(compStart, compEnd));

        } else {
            ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC).withMinute(0).withSecond(0).withNano(0);
            ZonedDateTime currentEnd = now;
            ZonedDateTime currentStart = currentEnd.minusHours(HOUR_DIFF);
            ZonedDateTime comparisonEnd = currentStart.minusSeconds(1);
            ZonedDateTime comparisonStart = comparisonEnd.minusHours(HOUR_DIFF);

            request.setCurrentTimeFrame(new IQSRequestBuilder.TimeFrame(currentStart.format(formatter), currentEnd.minusSeconds(1).format(formatter)));
            request.setComparisonTimeFrame(new IQSRequestBuilder.TimeFrame(comparisonStart.format(formatter), comparisonEnd.format(formatter)));
        }

        if (widgetConfigNode.has("filters")) {
            request.setFilters(mapper.convertValue(
                    widgetConfigNode.get("filters"),
                    new TypeReference<List<IQSRequestBuilder.Filter>>() {})
            );
        }

        if (widgetConfigNode.has("pagination")) {
            request.setPagination(mapper.convertValue(widgetConfigNode.get("pagination"), IQSRequestBuilder.Pagination.class));
        }

        if (widgetConfigNode.has("sortByFields")) {
            request.setSortByFields(mapper.convertValue(
                    widgetConfigNode.get("sortByFields"),
                    new TypeReference<List<IQSRequestBuilder.SortByFields>>() {})
            );
        }
        log.info("Request: " + mapper.writeValueAsString(request));
        return mapper.writeValueAsString(request);
    }

    @ParameterizedTest
    @MethodSource("widgetIds")
    public void testWidgetApi(String widgetId) {
        Response response = null;
        try {
            JsonNode widgetNode = widgetsRoot.get(widgetId);

            String requestBody;
            try {
                requestBody = buildFinalRequest(widgetNode);
            } catch (JsonProcessingException e) {
                log.error("Failed to build request for widget " + widgetId, e);
                failedWidgets.add(widgetId);
                fail("Failed to build request for widget " + widgetId + ": " + e.getMessage());
                return;
            }
            String tenantId = widgetNode.has("tenantId") ? widgetNode.get("tenantId").asText() : TENANT_ID;

            String url = IQS_BASE_URL + "/api/v1/tenant/" + tenantId + "/insights/1/widget/" + widgetId;

            response = RestAssured.given()
                    .contentType("application/json")
                    .body(requestBody)
                    .post(url);

            int statusCode = response.getStatusCode();
            if (statusCode != 200) {
                log.error("Non-200 response for widget {}: {}", widgetId, statusCode);
                testFailureByStatusCodeCounter.labels(INSTANCE, widgetId, String.valueOf(statusCode)).inc();
                failedWidgets.add(widgetId);
                fail("API returned status code: " + statusCode);
                return;
            }

            assertEquals(200, response.getStatusCode(), "API should return 200 OK");
            List<String> columns = response.jsonPath().getList("columns");
            List<List<Object>> data = response.jsonPath().getList("data");

            if (columns.contains("aggregate_field") && columns.contains("time_series") && columns.contains("count")) {
                int aggFieldIdx = columns.indexOf("aggregate_field");
                int timeSeriesIdx = columns.indexOf("time_series");

                long totalFlows = 0;
                long totalBytes = 0;

                for (int i = 0; i < data.size(); i++) {
                    List<Object> row = data.get(i);

                    if (row.size() > Math.max(aggFieldIdx, timeSeriesIdx)) {
                        String aggField = (String) row.get(aggFieldIdx);
                        Object timeSeriesData = row.get(timeSeriesIdx);

                        if (timeSeriesData instanceof List) {
                            List<?> timeSeries = (List<?>) timeSeriesData;
                            long rowTotal = 0;

                            for (Object point : timeSeries) {
                                if (point instanceof List && ((List<?>) point).size() >= 2) {
                                    List<?> timePoint = (List<?>) point;
                                    Object value = timePoint.get(1);
                                    if (value instanceof Number) {
                                        rowTotal += ((Number) value).longValue();
                                    }
                                }
                            }

                            if ("Flows".equalsIgnoreCase(aggField)) {
                                totalFlows += rowTotal;
                            } else if ("Bytes".equalsIgnoreCase(aggField)) {
                                totalBytes += rowTotal;
                            }
                        }
                    }
                }
                assertTrue(totalFlows > 0, "Total flow count should be > 0");
                assertTrue(totalBytes > 0, "Total byte count should be > 0");
            } else if (columns.contains("count")) {
                int countIdx = columns.indexOf("count");
                long totalCount = 0;
                for (List<Object> row : data) {
                    if (row.size() > countIdx) {
                        totalCount += ((Number) row.get(countIdx)).longValue();
                    }
                }
                assertTrue(totalCount > 0, "Total count should be > 0");
            } else if (columns.contains("flows") && columns.contains("bytes")) {
                int flowsIdx = columns.indexOf("flows");
                int bytesIdx = columns.indexOf("bytes");
                long totalFlows = 0;
                long totalBytes = 0;
                for (List<Object> row : data) {
                    if (row.size() > Math.max(flowsIdx, bytesIdx)) {
                        totalFlows += ((Number) row.get(flowsIdx)).longValue();
                        totalBytes += ((Number) row.get(bytesIdx)).longValue();
                    }
                }
                assertTrue(totalFlows > 0, "Total flow count should be > 0");
                assertTrue(totalBytes > 0, "Total byte count should be > 0");
            } else if (columns.contains("time_series")) {
                int timeSeriesIdx = columns.indexOf("time_series");
                int totalFlows = 0;
                for (List<Object> row : data) {
                    if (row.size() > timeSeriesIdx) {
                        List<List<Object>> timeSeries = (List<List<Object>>) row.get(timeSeriesIdx);
                        for (List<Object> point : timeSeries) {
                            if (point.size() > 1) {
                                int value = ((Number) point.get(1)).intValue();
                                totalFlows += value;
                            }
                        }
                    }
                }
                assertTrue(totalFlows > 0, "Total flow count should be > 0");
            } else {
                failedWidgets.add(widgetId);
                fail("Response does not contain expected columns: " + columns);
                throw new RuntimeException("Response does not contain expected columns: " + columns);
            }
            log.info("Test passed for widget " + widgetId);
            passedWidgets.add(widgetId);
        }
        catch (Throwable e) {
            if (response != null && response.statusCode() == 200) {
                log.info("Test passed for widget " + widgetId + ", but failed to validate Flow/Byte Count");
                passedWidgets.add(widgetId);
            } else {
                log.error("Test failed for widget " + widgetId, e);
                failedWidgets.add(widgetId);
                throw e;
            }
        }
    }

    static Stream<Arguments> widgetIds() {
        log.info("Starting IQS API test suite");
        return Stream.of(WidgetId.class.getFields())
                .filter(f -> f.getType().equals(String.class))
                .map(f -> {
                    try {
                        return (String) f.get(null);
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                })
                .filter(id -> !"0".equals(id))
                .map(Arguments::of);
    }

    @AfterAll
    public static void afterAll() throws IOException {
        // Push metrics to Pushgateway
        for (String widgetId : passedWidgets) {
            testSuccessCounter.labels(INSTANCE, widgetId).inc();
        }
        // Send metrics for failed widgets
        for (String widgetId : failedWidgets) {
            testFailureCounter.labels(INSTANCE, widgetId).inc();
        }
        try {
            PushGateway pg = new PushGateway(PUSHGATEWAY_URL);
            pg.pushAdd(registry, JOB_NAME);
            log.info("SUCCESS: Pushed test metrics to Pushgateway at " + PUSHGATEWAY_URL);
        } catch (Exception e) {
            log.error("ERROR: Failed to push metrics to Pushgateway: " + e.getMessage());
            e.printStackTrace();
        }
        log.info("IQS API test suite completed");
        //logging endtime of the test suite
        ZonedDateTime endTime = ZonedDateTime.now();
        log.info("Ending Integration Test Suite at " + endTime);
    }
}