package com.illumio.integration.Buiilder;

import java.util.ArrayList;
import java.util.List;

public class IQSRequestBuilder {
    private TimeFrame currentTimeFrame;
    private TimeFrame comparisonTimeFrame;
    private List<Filter> filters = new ArrayList<>();
    private List<SortByFields> sortByFields = List.of(new SortByFields("flows", "desc"));
    private Pagination pagination = new Pagination(1, 5);

    // Getters and setters
    public TimeFrame getCurrentTimeFrame() {
        return currentTimeFrame;
    }
    public void setCurrentTimeFrame(TimeFrame currentTimeFrame) {
        this.currentTimeFrame = currentTimeFrame;
    }
    public TimeFrame getComparisonTimeFrame() {
        return comparisonTimeFrame;
    }
    public void setComparisonTimeFrame(TimeFrame comparisonTimeFrame) {
        this.comparisonTimeFrame = comparisonTimeFrame;
    }
    public List<Filter> getFilters() {
        return filters;
    }
    public void setFilters(List<Filter> filters) {
        this.filters = filters;
    }
    public List<SortByFields> getSortByFields() {
        return sortByFields;
    }
    public void setSortByFields(List<SortByFields> sortByFields) {
        this.sortByFields = sortByFields;
    }
    public Pagination getPagination() {
        return pagination;
    }
    public void setPagination(Pagination pagination) {
        this.pagination = pagination;
    }

    public static class TimeFrame {
        private String startTime;
        private String endTime;

        public TimeFrame() {}
        public TimeFrame(String startTime, String endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
        }

        public String getStartTime() { return startTime; }
        public void setStartTime(String startTime) { this.startTime = startTime; }
        public String getEndTime() { return endTime; }
        public void setEndTime(String endTime) { this.endTime = endTime; }
    }

    public static class Filter {
        private String categoryType;
        private String categoryName;
        private List<Object> categoryValue;

        public String getCategoryType() { return categoryType; }
        public void setCategoryType(String categoryType) { this.categoryType = categoryType; }
        public String getCategoryName() { return categoryName; }
        public void setCategoryName(String categoryName) { this.categoryName = categoryName; }
        public List<Object> getCategoryValue() { return categoryValue; }
        public void setCategoryValue(List<Object> categoryValue) { this.categoryValue = categoryValue; }
    }

    public static class SortByFields {
        private String field;
        private String order;

        public SortByFields() {}
        public SortByFields(String field, String order) {
            this.field = field;
            this.order = order;
        }

        public String getField() { return field; }
        public void setField(String field) { this.field = field; }
        public String getOrder() { return order; }
        public void setOrder(String order) { this.order = order; }
    }

    public static class Pagination {
        private int pageNumber;
        private int rowLimit;

        public Pagination() {}
        public Pagination(int pageNumber, int rowLimit) {
            this.pageNumber = pageNumber;
            this.rowLimit = rowLimit;
        }

        public int getPageNumber() { return pageNumber; }
        public void setPageNumber(int pageNumber) { this.pageNumber = pageNumber; }
        public int getRowLimit() { return rowLimit; }
        public void setRowLimit(int rowLimit) { this.rowLimit = rowLimit; }
    }
}
