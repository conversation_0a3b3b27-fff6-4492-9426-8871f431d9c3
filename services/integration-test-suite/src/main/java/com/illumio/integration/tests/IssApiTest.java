 package com.illumio.integration;
 import io.restassured.RestAssured;
 import com.illumio.integration.applications;
 import io.restassured.response.Response;
 import org.junit.jupiter.api.Test;
 import org.junit.jupiter.api.TestInfo;
 import lombok.extern.slf4j.Slf4j;
 import org.junit.jupiter.api.DisplayName;
 import com.fasterxml.jackson.core.JsonProcessingException;
 import com.illumio.integration.Buiilder.ISSRequestBuilder;

 import io.prometheus.client.CollectorRegistry;
 import io.prometheus.client.Counter;
 import io.prometheus.client.exporter.PushGateway;
 import org.junit.jupiter.api.AfterAll;
 import java.io.IOException;

 import java.time.ZonedDateTime;
 import java.util.HashMap;
 import java.util.List;
 import java.util.Arrays;
 import java.util.Map;

 import static org.junit.jupiter.api.Assertions.fail;
 import static org.junit.jupiter.api.Assertions.assertTrue;
 import static org.junit.jupiter.api.Assertions.assertEquals;
 import static org.junit.jupiter.api.Assertions.assertNotNull;
 import java.util.ArrayList;

 import java.io.BufferedReader;
 import java.io.InputStream;
 import java.io.InputStreamReader;

 @Slf4j
 public class IssApiTest {
     private static final List<String> passedWidgets = new ArrayList<>();
     private static final List<String> failedWidgets = new ArrayList<>();

     private static final String PUSHGATEWAY_URL = System.getenv().getOrDefault("PUSHGATEWAY_URL", "prometheus-pushgateway.prometheus-pushgateway.svc.cluster.local:9091");
     private static final String JOB_NAME = "iss-api-test-suite";
     private static String INSTANCE = "unknown";

     private static final String ISS_BASE_URL = System.getenv().getOrDefault("baseUrl", applications.getValueString("key_sunnyvale"));
     private static final String TENANT_ID = System.getenv().getOrDefault("tenantId", applications.getTenantId("key_sunnyvale"));

     private static final CollectorRegistry registry = new CollectorRegistry();
     private static final Counter testSuccessCounter = Counter.build()
             .name("iss_test_success_total")
             .help("Number of successful ISS tests")
             .labelNames("instance", "test_name")
             .register(registry);
     private static final Counter testFailureCounter = Counter.build()
             .name("iss_test_failure_total")
             .help("Number of failed ISS tests")
             .labelNames("instance", "test_name")
             .register(registry);

     static {
         // logging the starttime of the test suite
         ZonedDateTime startTime = ZonedDateTime.now();
         log.info("Starting Integration Test Suite at " + startTime);
         log.info("Starting ISS API test suite");
         try {
             INSTANCE = java.net.InetAddress.getLocalHost().getHostName();
         } catch (Exception e) {
             INSTANCE = "unknown";
         }
     }

     private static void verifyColumns(List<String> columns, List<String> expectedColumns) {
         for (String column : expectedColumns) {
             assertTrue(columns.contains(column), "Response should contain " + column + " column");
         }
     }

     private static final Counter testFailureByStatusCodeCounter = Counter.build()
             .name("iss_test_failure_by_status_code_total")
             .help("Number of ISS test failures categorized by HTTP status code")
             .labelNames("instance", "test_name", "status_code")
             .register(registry);

     private static Map<String, String> loadPortProtocolToServiceMap() {
         Map<String, String> map = new HashMap<>();
         try (
                 InputStream is = com.illumio.data.model.Filters.class.getClassLoader().getResourceAsStream("risky_services.csv");
                 BufferedReader reader = new BufferedReader(new InputStreamReader(is))) {
             String line;
             boolean first = true;
             while ((line = reader.readLine()) != null) {
                 if (first) { first = false; continue; }
                 String[] parts = line.split(",");
                 if (parts.length < 3) continue;
                 String port = parts[0].trim();
                 String protocol = parts[2].trim().toUpperCase();
                 String service = parts[1].trim();
                 map.put(port + ":" + protocol, service);
             }
         } catch (Exception e) {
             throw new RuntimeException("Failed to load risky_services.csv", e);
         }
         return map;
     }

     @Test
     @DisplayName("decorated_flows_request_vmss")
     public void testDecoratedFlowsVmssSearch(TestInfo testInfo) {
     String testName = testInfo.getDisplayName();
     Response response = null;
     try {
         String url = ISS_BASE_URL + "/api/v1/search/tenant/" + TENANT_ID + "/search";
         String requestBody = ISSRequestBuilder.buildFinalRequest(testName);

         response = RestAssured.given()
                 .contentType("application/json")
                 .body(requestBody)
                 .post(url);

         if (response.getStatusCode() != 200) {
             log.error("Non-200 response for widget {}: {}", testName, response.getStatusCode());
             testFailureByStatusCodeCounter.labels(INSTANCE, testName, String.valueOf(response.getStatusCode())).inc();
             failedWidgets.add(testName);
             fail("API returned status code: " + response.getStatusCode());
             return;
         }

         assertEquals(200, response.getStatusCode(), "Search API should return 200 OK");

         List<Object> data = response.jsonPath().getList("data");
         assertNotNull(data, "Data should not be null");

         int pageNumber = response.jsonPath().getInt("pagination.pageNumber");
         int rowLimit = response.jsonPath().getInt("pagination.rowLimit");
         assertEquals(1, pageNumber, "Page number should be 1");
         assertEquals(1000, rowLimit, "Row limit should be 1000");

         List<String> columns = response.jsonPath().getList("columns");
         List<String> expectedColumns = List.of("source_ip", "destination_ip", "port", "protocol", "sent_bytes", "received_bytes");
         verifyColumns(columns, expectedColumns);

         if (!data.isEmpty()) {
             List<Object> firstResult = (List<Object>) data.get(0);
             assertEquals(8, firstResult.size(), "Result should have 8 fields");
             assertTrue(firstResult.get(0) instanceof String, "source_ip should be a string");
             assertTrue(firstResult.get(1) instanceof String, "destination_ip should be a string");
             assertTrue(firstResult.get(2) instanceof Number, "port should be a number");
             assertTrue(firstResult.get(3) instanceof String, "protocol should be a string");
             assertTrue(firstResult.get(4) instanceof Number, "sent_bytes should be a number");
             assertTrue(firstResult.get(5) instanceof Number, "received_bytes should be a number");

             assertTrue(((Number) firstResult.get(4)).longValue() > 0, "sent_bytes should be greater than 0");
             assertTrue(((Number) firstResult.get(5)).longValue() > 0, "received_bytes should be greater than 0");
         }

         assertTrue(data.size() > 0 && data.size() <= 1000, "Data size should be between 1 and 1000");
         passedWidgets.add(testName);
         log.info("Test completed successfully: " + testName);
     } catch (Throwable e) {
         if (response != null && response.getStatusCode() == 200) {
             log.info("Test completed successfully: " + testName + ", with 200 response and no data");
         }
         else {
             log.error("Test failed: " + testName, e);
             failedWidgets.add(testName);
             throw new RuntimeException(e);
         }
     }
 }

     @Test
     @DisplayName("malicious_ip_traffic_resource_insights_request")
     public void testMaliciousIpTrafficResourceInsightsSearch(TestInfo testInfo) throws JsonProcessingException {
         String testName = testInfo.getDisplayName();
         Response response = null;
         try {
             String url = ISS_BASE_URL + "/api/v1/search/tenant/" + TENANT_ID + "/search";
             String requestBody = ISSRequestBuilder.buildFinalRequest(testName);

             response = RestAssured.given()
                     .contentType("application/json")
                     .body(requestBody)
                     .post(url);

             if (response.getStatusCode() != 200) {
                 log.error("Non-200 response for widget {}: {}", testName, response.getStatusCode());
                 testFailureByStatusCodeCounter.labels(INSTANCE, testName, String.valueOf(response.getStatusCode())).inc();
                 failedWidgets.add(testName);
                 fail("API returned status code: " + response.getStatusCode());
                 return;
             }
             assertEquals(200, response.getStatusCode(), "Search API should return 200 OK");

             List<Object> data = response.jsonPath().getList("data");
             assertNotNull(data, "Data should not be null");

             int pageNumber = response.jsonPath().getInt("pagination.pageNumber");
             int rowLimit = response.jsonPath().getInt("pagination.rowLimit");
             assertEquals(1, pageNumber, "Page number should be 1");
             assertEquals(1000, rowLimit, "Row limit should be 1000");

             List<String> columns = response.jsonPath().getList("columns");
             List<String> expectedColumns = List.of("source_ip", "destination_ip", "port", "protocol", "sent_bytes", "received_bytes");
             verifyColumns(columns, expectedColumns);

             if (!data.isEmpty()) {
                 List<Object> firstResult = (List<Object>) data.get(0);
                 assertEquals(8, firstResult.size(), "Result should have 8 fields");
                 assertTrue(firstResult.get(0) instanceof String, "source_ip should be a string");
                 assertTrue(firstResult.get(1) instanceof String, "destination_ip should be a string");
                 assertTrue(firstResult.get(2) instanceof Number, "port should be a number");
                 assertTrue(firstResult.get(3) instanceof String, "protocol should be a string");
                 assertTrue(firstResult.get(4) instanceof Number, "sent_bytes should be a number");
                 assertTrue(firstResult.get(5) instanceof Number, "received_bytes should be a number");
             }

             assertTrue(data.size() > 0 && data.size() <= 1000, "Data size should be between 1 and 1000");
             passedWidgets.add(testName);
             log.info("Test completed successfully: " + testName);
         }
         catch (Throwable e) {
             if (response != null && response.getStatusCode() == 200) {
                 log.info("Test completed successfully: " + testName + ", with 200 response and no data");
             }
             else {
                 log.error("Test failed: " + testName, e);
                 failedWidgets.add(testName);
                 throw new RuntimeException(e);
             }
         }
     }

     @Test
     @DisplayName("cloud_provider_search_request")
     public void testCloudProviderSearch(TestInfo testInfo) throws JsonProcessingException {
         String testName = testInfo.getDisplayName();
         Response response = null;
         try {
             String url = ISS_BASE_URL + "/api/v1/search/tenant/" + TENANT_ID + "/search";
             String requestBody = ISSRequestBuilder.buildFinalRequest(testName);

             response = RestAssured.given()
                     .contentType("application/json")
                     .body(requestBody)
                     .post(url);

             if (response.getStatusCode() != 200) {
                 log.error("Non-200 response for widget {}: {}", testName, response.getStatusCode());
                 testFailureByStatusCodeCounter.labels(INSTANCE, testName, String.valueOf(response.getStatusCode())).inc();
                 failedWidgets.add(testName);
                 fail("API returned status code: " + response.getStatusCode());
                 return;
             }
             assertEquals(200, response.getStatusCode(), "Search API should return 200 OK");

             List<Object> data = response.jsonPath().getList("data");
             assertNotNull(data, "Data should not be null");

             int pageNumber = response.jsonPath().getInt("pagination.pageNumber");
             int rowLimit = response.jsonPath().getInt("pagination.rowLimit");
             assertEquals(1, pageNumber, "Page number should be 1");
             assertEquals(1000, rowLimit, "Row limit should be 1000");

             List<String> columns = response.jsonPath().getList("columns");
             List<String> expectedColumns = List.of("source_ip", "destination_ip", "port", "protocol", "sent_bytes", "received_bytes");
             verifyColumns(columns, expectedColumns);

             if (!data.isEmpty()) {
                 List<Object> firstResult = (List<Object>) data.get(0);
                 assertEquals(9, firstResult.size(), "Result should have 9 fields");
                 assertTrue(firstResult.get(0) instanceof String, "source_ip should be a string");
                 assertTrue(firstResult.get(1) instanceof String, "destination_ip should be a string");
                 assertTrue(firstResult.get(2) instanceof Number, "port should be a number");
                 assertTrue(firstResult.get(3) instanceof String, "protocol should be a string");
                 assertTrue(firstResult.get(4) instanceof Number, "sent_bytes should be a number");
                 assertTrue(firstResult.get(5) instanceof Number, "received_bytes should be a number");
             }

             assertTrue(data.size() > 0 && data.size() <= 1000, "Data size should be between 1 and 1000");
             passedWidgets.add(testName);
             log.info("Test completed successfully: " + testName);
         }
         catch (Throwable e) {
             if (response != null && response.getStatusCode() == 200) {
                 log.info("Test completed successfully: " + testName + ", with 200 response and no data");
             }
             else {
                 log.error("Test failed: " + testName, e);
                 failedWidgets.add(testName);
                 throw new RuntimeException(e);
             }
         }
     }

     @Test
     @DisplayName("destination_external_label_search_request")
     public void testDestinationExternalLabelSearch(TestInfo testInfo) throws JsonProcessingException {
         String testName = testInfo.getDisplayName();
         Response response = null;
         try {
             String url = ISS_BASE_URL + "/api/v1/search/tenant/" + TENANT_ID + "/search";
             String requestBody = ISSRequestBuilder.buildFinalRequest(testName);

             response = RestAssured.given()
                     .contentType("application/json")
                     .body(requestBody)
                     .post(url);

             if (response.getStatusCode() != 200) {
                 log.error("Non-200 response for widget {}: {}", testName, response.getStatusCode());
                 testFailureByStatusCodeCounter.labels(INSTANCE, testName, String.valueOf(response.getStatusCode())).inc();
                 failedWidgets.add(testName);
                 fail("API returned status code: " + response.getStatusCode());
                 return;
             }
             assertEquals(200, response.getStatusCode(), "Search API should return 200 OK");

             List<Object> data = response.jsonPath().getList("data");
             assertNotNull(data, "Data should not be null");

             int pageNumber = response.jsonPath().getInt("pagination.pageNumber");
             int rowLimit = response.jsonPath().getInt("pagination.rowLimit");
             assertEquals(1, pageNumber, "Page number should be 1");
             assertEquals(1000, rowLimit, "Row limit should be 1000");

             List<String> columns = response.jsonPath().getList("columns");
             List<String> expectedColumns = Arrays.asList("port", "protocol", "source_ip", "destination_ip", "source_resource_id", "source_cloud_provider", "destination_resource_id", "destination_cloud_provider");
             verifyColumns(columns, expectedColumns);

             if (!data.isEmpty()) {
                 List<Object> firstResult = (List<Object>) data.get(0);
                 assertEquals(11, firstResult.size(), "Result should have 11 fields");
                 assertTrue(firstResult.get(0) instanceof Number, "port should be a number");
                 assertTrue(firstResult.get(1) instanceof String, "protocol should be a string");
                 assertTrue(firstResult.get(2) instanceof String, "source_ip should be a string");
                 assertTrue(firstResult.get(3) instanceof String, "destination_ip should be a string");
                 assertTrue(firstResult.get(4) instanceof String, "source_resource_id should be a string");
                 assertTrue(firstResult.get(5) instanceof String, "source_cloud_provider should be a string");
                 assertTrue(firstResult.get(6) instanceof String, "destination_resource_id should be a string");
                 assertTrue(firstResult.get(7) instanceof String, "destination_cloud_provider should be a string");
                 assertTrue(firstResult.get(8) instanceof Number, "sent_bytes should be a number");
                 assertTrue(firstResult.get(9) instanceof String, "ingestion_time should be a string");
                 assertTrue(firstResult.get(10) instanceof Number, "received_bytes should be a number");
             }

             assertTrue(data.size() > 0 && data.size() <= 1000, "Data size should be between 1 and 1000");
             passedWidgets.add(testName);
             log.info("Test completed successfully: " + testName);
         }
         catch (Throwable e) {
             if (response != null && response.getStatusCode() == 200) {
                 log.info("Test completed successfully: " + testName + ", with 200 response and no data");
             }
             else {
                 log.error("Test failed: " + testName, e);
                 failedWidgets.add(testName);
                 throw new RuntimeException(e);
             }
         }
     }

     @Test
     @DisplayName("landing_page_search")
     public void testLandingPageSearch(TestInfo testInfo) throws JsonProcessingException {
         String testName = testInfo.getDisplayName();
         Response response = null;
         try {
             String url = ISS_BASE_URL + "/api/v1/search/tenant/" + TENANT_ID + "/search";
             String requestBody = ISSRequestBuilder.buildFinalRequest(testName);

             response = RestAssured.given()
                     .contentType("application/json")
                     .body(requestBody)
                     .post(url);

             if (response.getStatusCode() != 200) {
                 log.error("Non-200 response for widget {}: {}", testName, response.getStatusCode());
                 testFailureByStatusCodeCounter.labels(INSTANCE, testName, String.valueOf(response.getStatusCode())).inc();
                 failedWidgets.add(testName);
                 fail("API returned status code: " + response.getStatusCode());
                 return;
             }
             assertEquals(200, response.getStatusCode(), "Search API should return 200 OK");

             List<Object> data = response.jsonPath().getList("data");
             assertNotNull(data, "Data should not be null");

             int pageNumber = response.jsonPath().getInt("pagination.pageNumber");
             int rowLimit = response.jsonPath().getInt("pagination.rowLimit");
             assertEquals(1, pageNumber, "Page number should be 1");
             assertEquals(1000, rowLimit, "Row limit should be 1000");

             List<String> columns = response.jsonPath().getList("columns");
             List<String> expectedColumns = Arrays.asList("source_ip", "destination_ip", "port", "protocol", "sent_bytes", "received_bytes", "source_tenant", "destination_tenant", "source_subscription", "destination_subscription", "source_region", "destination_region", "source_resource_id", "destination_resource_id", "source_vnet", "destination_vnet", "source_cloud_tags", "destination_cloud_tags", "traffic_status", "packets_sent", "packets_received", "source_threat_level", "destination_threat_level", "source_well_known", "destination_well_known", "source_domain", "destination_domain", "source_country", "destination_country");
             verifyColumns(columns, expectedColumns);

             if (!data.isEmpty()) {
                 List<Object> firstResult = (List<Object>) data.get(0);
                 assertEquals(54, firstResult.size(), "Result should have 54 fields");
                 assertTrue(firstResult.get(0) instanceof String, "source_ip should be a string");
                 assertTrue(firstResult.get(1) instanceof String, "destination_ip should be a string");
                 assertTrue(firstResult.get(2) instanceof Number, "port should be a number");
                 assertTrue(firstResult.get(3) instanceof String, "protocol should be a string");
                 assertTrue(firstResult.get(4) instanceof Number, "sent_bytes should be a number");
                 assertTrue(firstResult.get(5) instanceof String, "source_tenant should be a string");
             }

             assertTrue(data.size() > 0 && data.size() <= 1000, "Data size should be between 1 and 1000");
             passedWidgets.add(testName);
             log.info("Test completed successfully: " + testName);
         }
         catch (Throwable e) {
             if (response != null && response.getStatusCode() == 200) {
                 log.info("Test completed successfully: " + testName + ", with 200 response and no data");
             }
             else {
                 log.error("Test failed: " + testName, e);
                 failedWidgets.add(testName);
                 throw new RuntimeException(e);
             }
         }
     }

     @Test
     @DisplayName("empty_zone_search")
     public void testEmptyZoneSearch(TestInfo testInfo) {
     String testName = testInfo.getDisplayName();
     Response response = null;
     try {
         String url = ISS_BASE_URL + "/api/v1/search/tenant/" + TENANT_ID + "/search";
         String requestBody = ISSRequestBuilder.buildFinalRequest(testName);

         response = RestAssured.given()
                 .contentType("application/json")
                 .body(requestBody)
                 .post(url);

         if (response.getStatusCode() != 200) {
             log.error("Non-200 response for widget {}: {}", testName, response.getStatusCode());
             testFailureByStatusCodeCounter.labels(INSTANCE, testName, String.valueOf(response.getStatusCode())).inc();
             failedWidgets.add(testName);
             fail("API returned status code: " + response.getStatusCode());
             return;
         }

         assertEquals(200, response.getStatusCode(), "Search API should return 200 OK");

         List<Object> data = response.jsonPath().getList("data");
         assertNotNull(data, "Data should not be null");

         int pageNumber = response.jsonPath().getInt("pagination.pageNumber");
         int rowLimit = response.jsonPath().getInt("pagination.rowLimit");
         assertEquals(1, pageNumber, "Page number should be 1");
         assertEquals(1000, rowLimit, "Row limit should be 1000");

         // Check columns if needed
         List<String> columns = response.jsonPath().getList("columns");
         assertNotNull(columns, "Columns should not be null");

         // Optional: validate structure of first result
         if (!data.isEmpty()) {
             List<Object> firstResult = (List<Object>) data.get(0);
             assertEquals(54, firstResult.size(), "Result should have 54 fields");
         }

         assertTrue(data.size() > 0 && data.size() <= 1000, "Data size should be between 1 and 1000");

         passedWidgets.add(testName);
         log.info("Test completed successfully: " + testName);
     } catch (Throwable e) {
         if (response != null && response.getStatusCode() == 200) {
             log.info("Test completed successfully: " + testName + ", with 200 response and no data");
         }
         else {
             log.error("Test failed: " + testName, e);
             failedWidgets.add(testName);
             throw new RuntimeException(e);
         }
     }
 }

     @Test
     @DisplayName("search_services")
     public void testSearchServices(TestInfo testInfo) throws JsonProcessingException {
         String testName = testInfo.getDisplayName();
         Response response = null;
         try {
             Map<String, String> portProtocolToService = loadPortProtocolToServiceMap();

             String url = ISS_BASE_URL + "/api/v1/search/tenant/" + TENANT_ID + "/search";
             String requestBody = ISSRequestBuilder.buildFinalRequest(testName);

             response = RestAssured.given()
                     .contentType("application/json")
                     .body(requestBody)
                     .post(url);

             if (response.getStatusCode() != 200) {
                 log.error("Non-200 response for widget {}: {}", testName, response.getStatusCode());
                 testFailureByStatusCodeCounter.labels(INSTANCE, testName, String.valueOf(response.getStatusCode())).inc();
                 failedWidgets.add(testName);
                 fail("API returned status code: " + response.getStatusCode());
                 return;
             }
             assertEquals(200, response.getStatusCode(), "Search API should return 200 OK");

             List<Object> data = response.jsonPath().getList("data");
             assertNotNull(data, "Data should not be null");

             int pageNumber = response.jsonPath().getInt("pagination.pageNumber");
             int rowLimit = response.jsonPath().getInt("pagination.rowLimit");
             assertEquals(1, pageNumber, "Page number should be 1");
             assertEquals(1000, rowLimit, "Row limit should be 1000");

             List<String> columns = response.jsonPath().getList("columns");
             List<String> expectedColumns = Arrays.asList("port", "protocol", "source_ip", "destination_ip", "service");
             verifyColumns(columns, expectedColumns);

             if (!data.isEmpty()) {
                 List<Object> firstResult = (List<Object>) data.get(0);
                 assertEquals(7, firstResult.size(), "Result should have 7 fields");
                 assertTrue(firstResult.get(0) instanceof Number, "port should be a number");
                 assertTrue(firstResult.get(1) instanceof String, "source_ip should be a string");
                 assertTrue(firstResult.get(2) instanceof String, "destination_ip should be a string");
                 assertTrue(firstResult.get(3) instanceof String, "service should be a string");
             }

             int portIdx = columns.indexOf("port");
             int protocolIdx = columns.indexOf("protocol");
             int serviceIdx = columns.indexOf("service");

             for (Object rowObj : data) {
                 List<Object> row = (List<Object>) rowObj;
                 String port = String.valueOf(row.get(portIdx));
                 String protocol = String.valueOf(row.get(protocolIdx)).toUpperCase();
                 String service = String.valueOf(row.get(serviceIdx));
                 String expectedService = portProtocolToService.get(port + ":" + protocol);
                 if (expectedService != null) {
                     assertEquals(expectedService, service, "Service mismatch for port " + port + " and protocol " + protocol);
                 } else {
                     log.warn("No mapping found in CSV for port {} and protocol {}", port, protocol);
                 }
             }

             assertTrue(data.size() > 0 && data.size() <= 1000, "Data size should be between 1 and 1000");
             passedWidgets.add(testName);
             log.info("Test completed successfully: " + testName);
         }
         catch (Throwable e) {
             if (response != null && response.getStatusCode() == 200) {
                 log.info("Test completed successfully: " + testName + ", with 200 response and no data");
             }
             else {
                 log.error("Test failed: " + testName, e);
                 failedWidgets.add(testName);
                 throw new RuntimeException(e);
             }
         }
     }

     @AfterAll
     public static void afterAll() throws IOException {
         for (String testName : passedWidgets) {
             testSuccessCounter.labels(INSTANCE, testName).inc();
         }
         // Send metrics for failed widgets
         for (String testName : failedWidgets) {
             testFailureCounter.labels(INSTANCE, testName).inc();
         }
         try {
             PushGateway pg = new PushGateway(PUSHGATEWAY_URL);
             pg.pushAdd(registry, JOB_NAME);
             log.info("SUCCESS: Pushed test metrics to Pushgateway at " + PUSHGATEWAY_URL);
         } catch (Exception e) {
             log.error("ERROR: Failed to push metrics to Pushgateway: " + e.getMessage());
             e.printStackTrace();
         }
         log.info("ISS API test suite completed");
     }
 }