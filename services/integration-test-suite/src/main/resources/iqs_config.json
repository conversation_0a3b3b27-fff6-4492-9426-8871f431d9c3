{"1661": {"tenantId": "39e868b6-fdfc-4118-b664-a7d4b04728e8", "filters": [{"categoryType": "string", "categoryName": "resource_id", "categoryValue": ["/subscriptions/dc80af39-5fb5-4c0a-99fa-4a8b65c4f3d4/resourceGroups/rsa-demo/providers/Microsoft.Compute/virtualMachines/Receiving-Host"]}]}, "5566": {"filters": [{"categoryType": "string", "categoryName": "traffic_direction", "categoryValue": ["INBOUND"]}], "currentTimeFrame": {"startTime": "2025-05-08T00:00:00Z", "endTime": "2025-05-10T23:59:59Z"}, "comparisonTimeFrame": {"startTime": "2025-05-05T00:00:00Z", "endTime": "2025-05-07T23:59:59Z"}}, "1881": {}, "1991": {"filters": [{"categoryType": "string", "categoryName": "category", "categoryValue": ["Subscriptions"]}]}, "1771": {}, "1551": {"filters": [{"categoryType": "string", "categoryName": "resource_id", "categoryValue": ["/subscriptions/f0fe5a5c-096d-42aa-8266-0591c8d9e7ae/resourceGroups/kiran-rg/providers/Microsoft.Compute/virtualMachines/kiran-test-vm1"]}], "pagination": {"pageNumber": 1, "rowLimit": 1000}, "currentTimeFrame": {"startTime": "2025-05-08T00:00:00Z", "endTime": "2025-05-10T23:59:59Z"}, "comparisonTimeFrame": {"startTime": "2025-05-05T00:00:00Z", "endTime": "2025-05-07T23:59:59Z"}}, "1441": {"tenantId": "39e868b6-fdfc-4118-b664-a7d4b04728e8", "filters": [{"categoryType": "string", "categoryName": "resource_id", "categoryValue": ["/subscriptions/6ce15ab6-8bcb-4a01-ac2b-98c21f307df1/resourceGroups/testdrive/providers/Microsoft.Compute/virtualMachines/crm-cfgmgr01-dev"]}]}, "1331": {}, "4321": {"tenantId": "39e868b6-fdfc-4118-b664-a7d4b04728e8", "filters": [{"categoryType": "number", "categoryName": "port", "categoryValue": [443]}, {"categoryType": "string", "categoryName": "protocol", "categoryValue": ["tcp"]}]}, "1234": {}, "5678": {"filters": [{"categoryType": "number", "categoryName": "port", "categoryValue": [8080]}, {"categoryType": "string", "categoryName": "protocol", "categoryValue": ["tcp"]}]}, "9012": {"filters": [{"categoryType": "number", "categoryName": "port", "categoryValue": [8080]}, {"categoryType": "string", "categoryName": "protocol", "categoryValue": ["tcp"]}]}, "3456": {"tenantId": "39e868b6-fdfc-4118-b664-a7d4b04728e8", "filters": [{"categoryType": "number", "categoryName": "port", "categoryValue": [443]}, {"categoryType": "string", "categoryName": "protocol", "categoryValue": ["tcp"]}]}, "1122": {"filters": [{"categoryType": "string", "categoryName": "traffic_direction", "categoryValue": ["INBOUND"]}], "currentTimeFrame": {"startTime": "2025-05-08T00:00:00Z", "endTime": "2025-05-10T23:59:59Z"}, "comparisonTimeFrame": {"startTime": "2025-05-05T00:00:00Z", "endTime": "2025-05-07T23:59:59Z"}}, "2233": {"filters": [{"categoryType": "string", "categoryName": "traffic_direction", "categoryValue": ["INBOUND"]}], "currentTimeFrame": {"startTime": "2025-05-08T00:00:00Z", "endTime": "2025-05-10T23:59:59Z"}, "comparisonTimeFrame": {"startTime": "2025-05-05T00:00:00Z", "endTime": "2025-05-07T23:59:59Z"}}, "3344": {"filters": [{"categoryType": "string", "categoryName": "category", "categoryValue": ["subscriptions"]}, {"categoryType": "string", "categoryName": "traffic_direction", "categoryValue": ["INBOUND"]}], "currentTimeFrame": {"startTime": "2025-05-08T00:00:00Z", "endTime": "2025-05-10T23:59:59Z"}, "comparisonTimeFrame": {"startTime": "2025-05-05T00:00:00Z", "endTime": "2025-05-07T23:59:59Z"}}, "4455": {"filters": [{"categoryType": "string", "categoryName": "traffic_direction", "categoryValue": ["INBOUND"]}], "currentTimeFrame": {"startTime": "2025-05-08T00:00:00Z", "endTime": "2025-05-10T23:59:59Z"}, "comparisonTimeFrame": {"startTime": "2025-05-05T00:00:00Z", "endTime": "2025-05-07T23:59:59Z"}}, "3399": {}, "4499": {}, "5599": {}, "6699": {}, "7799": {}, "9991": {}, "9992": {}, "9876": {}, "8765": {"filters": [{"categoryType": "number", "categoryName": "port", "categoryValue": [8080]}, {"categoryType": "string", "categoryName": "protocol", "categoryValue": ["tcp"]}]}, "7654": {}, "6543": {"filters": [{"categoryType": "string", "categoryName": "traffic_direction", "categoryValue": ["INBOUND"]}], "currentTimeFrame": {"startTime": "2025-05-08T00:00:00Z", "endTime": "2025-05-10T23:59:59Z"}, "comparisonTimeFrame": {"startTime": "2025-05-05T00:00:00Z", "endTime": "2025-05-07T23:59:59Z"}}, "5432": {}, "3210": {}, "9993": {}, "9995": {"filters": [{"categoryType": "string", "categoryName": "category", "categoryValue": ["subscriptions"]}], "currentTimeFrame": {"startTime": "2025-05-08T00:00:00Z", "endTime": "2025-05-10T23:59:59Z"}, "comparisonTimeFrame": {"startTime": "2025-05-05T00:00:00Z", "endTime": "2025-05-07T23:59:59Z"}}, "9994": {}, "ip-top-destination-resources": {"filters": [{"categoryType": "string", "categoryName": "traffic_direction", "categoryValue": ["INBOUND"]}, {"categoryType": "string", "categoryName": "ip", "categoryValue": ["***********"]}]}, "ip-top-source-resources": {"tenantId": "39e868b6-fdfc-4118-b664-a7d4b04728e8", "filters": [{"categoryType": "string", "categoryName": "traffic_direction", "categoryValue": ["OUTBOUND"]}, {"categoryType": "string", "categoryName": "ip", "categoryValue": ["********"]}]}, "ip-top-destination-roles": {"filters": [{"categoryType": "string", "categoryName": "traffic_direction", "categoryValue": ["INBOUND"]}, {"categoryType": "string", "categoryName": "ip", "categoryValue": ["***********"]}]}, "ip-top-source-roles": {"tenantId": "39e868b6-fdfc-4118-b664-a7d4b04728e8", "filters": [{"categoryType": "string", "categoryName": "traffic_direction", "categoryValue": ["OUTBOUND"]}, {"categoryType": "string", "categoryName": "ip", "categoryValue": ["********"]}]}, "ip-data-transfer-inbound": {"filters": [{"categoryType": "string", "categoryName": "traffic_direction", "categoryValue": ["INBOUND"]}, {"categoryType": "string", "categoryName": "ip", "categoryValue": ["***********"]}]}, "ip-data-transfer-outbound": {"tenantId": "39e868b6-fdfc-4118-b664-a7d4b04728e8", "filters": [{"categoryType": "string", "categoryName": "traffic_direction", "categoryValue": ["OUTBOUND"]}, {"categoryType": "string", "categoryName": "ip", "categoryValue": ["********"]}]}, "ip-top-resources": {"filters": [{"categoryType": "string", "categoryName": "traffic_direction", "categoryValue": ["INBOUND"]}, {"categoryType": "string", "categoryName": "ip", "categoryValue": ["***********"]}]}, "ip-top-roles": {"filters": [{"categoryType": "string", "categoryName": "traffic_direction", "categoryValue": ["INBOUND"]}, {"categoryType": "string", "categoryName": "ip", "categoryValue": ["***********"]}]}, "ip-data-transfer": {"filters": [{"categoryType": "string", "categoryName": "traffic_direction", "categoryValue": ["INBOUND"]}, {"categoryType": "string", "categoryName": "ip", "categoryValue": ["***********"]}]}, "get-csp-regions": {}, "traffic-activity-across-countries": {"filters": [{"categoryType": "string", "categoryName": "zone", "categoryValue": ["Internal Azure"]}, {"categoryType": "string", "categoryName": "region", "categoryValue": ["west<PERSON>"]}, {"categoryType": "string", "categoryName": "protocol", "categoryValue": ["tcp"]}]}, "top-cross-region-traffic": {"filters": [{"categoryType": "number", "categoryName": "port", "categoryValue": [8080]}, {"categoryType": "string", "categoryName": "protocol", "categoryValue": ["tcp"]}]}, "top-subs-in-region": {"filters": [{"categoryType": "number", "categoryName": "port", "categoryValue": [8080]}, {"categoryType": "string", "categoryName": "protocol", "categoryValue": ["tcp"]}, {"categoryType": "string", "categoryName": "zone", "categoryValue": ["AWS"]}, {"categoryType": "string", "categoryName": "region", "categoryValue": ["us-east-1"]}]}, "get-risky-service-names": {}}