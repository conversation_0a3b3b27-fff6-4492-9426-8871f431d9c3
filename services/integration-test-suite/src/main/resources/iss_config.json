{"decorated_flows_request_vmss": {"filters": [{"categoryType": "string", "categoryName": "source_resource_id", "categoryValue": ["/subscriptions/87332a70-7c1b-4437-aa3b-ec7c00d72de0/resourceGroups/mc_cloudsecure-data-dev-3-rg_cloudsecure-data-dev-3_westus2/providers/Microsoft.Compute/virtualMachineScaleSets/aks-default-93922338-vmss/virtualMachines/1"]}, {"categoryType": "number", "categoryName": "port", "categoryValue": [135]}, {"categoryType": "string", "categoryName": "protocol", "categoryValue": ["TCP"]}], "sortByFields": [{"field": "sent_bytes", "order": "desc"}], "projectFields": {"projectFields": ["source_ip", "destination_ip", "port", "protocol", "sent_bytes", "received_bytes"]}}, "malicious_ip_traffic_resource_insights_request": {"currentTimeFrame": {"startTime": "2025-05-18T00:00:00Z", "endTime": "2025-06-17T00:00:00Z"}, "baseFilter": {"operand": "logic", "andRelation": false, "filters": [{"operand": "greater", "field": "source_threat_level", "value": "2", "includeEqual": true}, {"operand": "greater", "field": "destination_threat_level", "value": "2", "includeEqual": true}]}, "sortByFields": [{"field": "sent_bytes", "order": "desc"}], "projectFields": {"projectFields": ["source_ip", "destination_ip", "port", "protocol", "sent_bytes", "received_bytes"]}}, "cloud_provider_search_request": {"baseFilter": {"operand": "logic", "andRelation": true, "filters": [{"operand": "logic", "andRelation": true, "filters": [{"operand": "in", "field": "source_resource_id", "values": [""]}, {"operand": "in", "field": "source_cloud_provider", "values": ["aws"], "ignoreCase": true}]}, {"operand": "logic", "andRelation": true, "filters": [{"operand": "in", "field": "destination_resource_id", "values": [""]}, {"operand": "in", "field": "destination_cloud_provider", "values": [""]}]}]}, "sortByFields": [{"field": "flows", "order": "desc"}], "projectFields": {"projectFields": ["source_ip", "destination_ip", "port", "protocol", "sent_bytes", "received_bytes"]}}, "destination_external_label_search_request": {"filters": [{"categoryType": "string", "categoryName": "dest_ext_label_category", "categoryValue": ["CSP"]}, {"categoryType": "string", "categoryName": "dest_ext_label", "categoryValue": ["Azure"]}, {"categoryType": "string", "categoryName": "protocol", "categoryValue": ["tcp"]}], "sortByFields": [{"field": "received_bytes", "order": "desc"}], "projectFields": {"projectFields": ["port", "protocol", "source_ip", "destination_ip", "source_resource_id", "source_cloud_provider", "destination_resource_id", "destination_cloud_provider"]}}, "landing_page_search": {"sortByFields": [{"field": "bytes", "order": "desc"}], "pagination": {"pageNumber": 1, "rowLimit": 100, "topResultHardLimit": 100}}, "empty_zone_search": {"sortByFields": [{"field": "bytes", "order": "desc"}], "pagination": {"pageNumber": 1, "rowLimit": 1000}, "baseFilter": {"operand": "logic", "andRelation": true, "filters": [{"operand": "logic", "andRelation": true, "filters": [{"operand": "in", "field": "source_zone", "values": ["Internal Azure"]}, {"operand": "in", "field": "destination_zone", "values": [""]}]}, {"operand": "logic", "andRelation": true, "filters": [{"operand": "in", "field": "port", "values": [135]}, {"operand": "in", "field": "protocol", "values": ["tcp"]}]}]}}, "search_services": {"filters": [{"categoryType": "string", "categoryName": "service", "categoryValue": ["RustDesk"]}, {"categoryType": "string", "categoryName": "protocol", "categoryValue": ["tcp"]}], "sortByFields": [{"field": "received_bytes", "order": "desc"}], "projectFields": {"projectFields": ["service", "port", "source_ip", "destination_ip"]}, "pagination": {"pageNumber": 1, "rowLimit": 5}}}