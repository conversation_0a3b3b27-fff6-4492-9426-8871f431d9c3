# Integration Test Suite (Stage 1)

This service provides a basic integration test suite for validating the IQS (Insights Query Service) and ISS (Insights Search Service) APIs.

## What it does
- Checks that the APIs return data for a configured tenant, page, and widget.
- Does not generate or inject new data; uses already-settled data in the pipeline.
- For IQS: Asserts that flowCount > 0 and byteCount > 0 in the API responses. This will be overcome and will be tested with the actual data values once the data generation and ingestion takes place.
- For ISS: Asserts that the API returns data with the correct structure and data types.

## How to run

```bash
./gradlew :services:integration-test-suite:test
```
