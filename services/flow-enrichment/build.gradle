plugins {
    id 'org.springframework.boot'
    id 'com.google.cloud.tools.jib'
}

apply from: "${project.rootDir}/gradle/scripts/jib-build.gradle"
apply from: "${project.rootDir}/gradle/scripts/opentelemetry.gradle"

dependencies {
    // implementation
    implementation project(":commons:azure-commons")
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.kafka:spring-kafka'
    implementation "io.projectreactor.kafka:reactor-kafka"
    implementation 'co.elastic.logging:logback-ecs-encoder'
    implementation("com.azure:azure-identity")
    implementation("io.opentelemetry:opentelemetry-api");
    implementation('com.fasterxml.jackson.dataformat:jackson-dataformat-csv')

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // spring
    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"

    // test
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
}

configurations {
    all {
        exclude group: 'org.slf4j', module: 'slf4j-simple'
    }
}

jib {
    container {
        entrypoint = [
                'java', '-cp', '@/app/jib-classpath-file',
                'com.illumio.data.FlowEnrichmentApplication',
                '--spring.config.location=file:/var/resources/application.yml',
                '--spring.profiles.active=prod'
        ]
        environment = [
                "JAVA_TOOL_OPTIONS": "-javaagent:/otel/opentelemetry-javaagent.jar",
                "OTEL_METRICS_EXPORTER": "prometheus",
                "OTEL_TRACES_EXPORTER": "none",
                "OTEL_LOGS_EXPORTER": "none"
        ]
    }
}
