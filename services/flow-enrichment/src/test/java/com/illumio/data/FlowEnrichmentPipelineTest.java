package com.illumio.data;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.EnrichedFlowWriter;
import com.illumio.data.components.DecoratedFlowReader;
import com.illumio.data.components.PortLookup;
import com.illumio.data.configuration.FlowEnrichmentConfig;
import lombok.SneakyThrows;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Answers;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOffset;
import reactor.kafka.receiver.ReceiverRecord;
import reactor.kafka.sender.KafkaSender;
import reactor.test.StepVerifier;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.time.Duration;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FlowEnrichmentPipelineTest {
    @Mock KafkaReceiver<String, String> kafkaReceiver;
    DecoratedFlowReader decoratedFlowReader;
    @Mock PortLookup portLookup;
    @Mock
    EnrichedFlowWriter enrichedFlowWriter;
    @Mock KafkaSender<String, String> kafkaSender;

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    FlowEnrichmentConfig flowEnrichmentConfig;

    FlowEnrichmentPipeline flowEnrichmentPipeline;

    @SneakyThrows
    public static String readFromStream(InputStream inputStream) {
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        for (int length; (length = inputStream.read(buffer)) != -1; ) {
            result.write(buffer, 0, length);
        }
        // StandardCharsets.UTF_8.name() > JDK 7
        return result.toString("UTF-8");
    }

    @BeforeEach
    void setup() {
        ObjectMapper objectMapper = new ObjectMapper();
        decoratedFlowReader = new DecoratedFlowReader(objectMapper);
        when(flowEnrichmentConfig.getKafkaSenderConfig().getSinkTopic()).thenReturn("sink");
        flowEnrichmentPipeline =
                new FlowEnrichmentPipeline(
                        kafkaReceiver,
                        decoratedFlowReader,
                        portLookup,
                        enrichedFlowWriter,
                        kafkaSender,
                        flowEnrichmentConfig);
    }

    /**
     * Expects to drop the record in case of parsing error.
     */
    @Test
    void testBadRecord() {
        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", "value");
        ReceiverOffset receiverOffset = mock(ReceiverOffset.class);
        ReceiverRecord<String, String> receiverRecord =
                new ReceiverRecord<>(consumerRecord, receiverOffset);

        StepVerifier.create(
                        Flux.just(consumerRecord)
                                .flatMap(
                                        receiverRecord1 ->
                                                flowEnrichmentPipeline.processConsumerRecord(
                                                        receiverRecord1)))
                .expectSubscription()
                .thenAwait(Duration.ofSeconds(1))
                .expectNextCount(1)
                .verifyComplete();
    }

    /**
     * Expects the json to be returned without decoration.
     */
    @Test
    void testEmptyJson() {
        JsonNode jsonNode = mock(JsonNode.class);
        when(portLookup.maybeAddDestinationLabel(any())).thenReturn(Mono.just(jsonNode));
        when(enrichedFlowWriter.writeTreeAsString(any())).thenReturn(Mono.just("json"));
        final String value = readFromStream(
                Objects.requireNonNull(this.getClass()
                        .getClassLoader()
                        .getResourceAsStream("unit-test-examples/empty-json.json")));

        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);
        ReceiverOffset receiverOffset = mock(ReceiverOffset.class);
        ReceiverRecord<String, String> receiverRecord = new ReceiverRecord<>(consumerRecord, receiverOffset);

        StepVerifier.create(Flux.just(consumerRecord)
                        .flatMap(receiverRecord1 -> flowEnrichmentPipeline.processConsumerRecord(receiverRecord1))
                )
                .expectSubscription()
                .expectNextCount(1)
                .verifyComplete();
    }

    @Test
    void testOneRecord() {
        JsonNode jsonNode = mock(JsonNode.class);
        when(portLookup.maybeAddDestinationLabel(any())).thenReturn(Mono.just(jsonNode));
        when(enrichedFlowWriter.writeTreeAsString(any())).thenReturn(Mono.just("json"));
        final String value = readFromStream(
                Objects.requireNonNull(this.getClass()
                        .getClassLoader()
                        .getResourceAsStream("unit-test-examples/1-record.json")));

        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);
        ReceiverOffset receiverOffset = mock(ReceiverOffset.class);
        ReceiverRecord<String, String> receiverRecord = new ReceiverRecord<>(consumerRecord, receiverOffset);

        StepVerifier.create(Flux.just(consumerRecord)
                        .flatMap(receiverRecord1 -> flowEnrichmentPipeline.processConsumerRecord(receiverRecord1))
                )
                .expectSubscription()
                .expectNextCount(1)
                .verifyComplete();
    }
}
