package com.illumio.data.components;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

@SpringBootTest
public class DestinationLabelLookupTest {

    private DestinationLabelLookup destinationLabelLookup;

    @BeforeEach
    public void setUp() {
        destinationLabelLookup = new DestinationLabelLookup();
    }

    @Test
    public void testLookupPortExisting() {
        assertEquals("Domain-Controller,LDAP,Mcafee-ePO", destinationLabelLookup.lookupPort(389));
        assertEquals("Domain-Controller", destinationLabelLookup.lookupPort(88));
        assertEquals("Domain-Controller,NetBIOS", destinationLabelLookup.lookupPort(137));
    }

    @Test
    public void testLookupPortNonExisting() {
        assertNull(destinationLabelLookup.lookupPort(12345));
    }

}