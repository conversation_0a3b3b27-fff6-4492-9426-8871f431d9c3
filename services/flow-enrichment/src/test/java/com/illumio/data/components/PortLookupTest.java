package com.illumio.data.components;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class PortLookupTest {
    ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    DestinationLabelLookup destinationLabelLookup;

    PortLookup portLookup;

    @BeforeEach
    public void setup() {
        portLookup = new PortLookup(destinationLabelLookup);
    }

    @SneakyThrows
    @Test
    public void testEmptyObject() {
        String value = "{}";
        JsonNode jsonNode = objectMapper.readTree(value);
        JsonNode result = portLookup.call(jsonNode);

        assertEquals(0, result.size());
    }

    @SneakyThrows
    @Test
    public void testPortWrongType() {
        String value = "{\"Port\":\"stringwtf\"}";
        JsonNode jsonNode = objectMapper.readTree(value);
        JsonNode result = portLookup.call(jsonNode);

        assertEquals(1, result.size());
    }

    @SneakyThrows
    @Test
    public void testPortLookupIsNull() {
        when(destinationLabelLookup.lookupPort(3000)).thenReturn(null);
        String value = "{\"Port\":3000}";
        JsonNode jsonNode = objectMapper.readTree(value);
        JsonNode result = portLookup.call(jsonNode);

        assertEquals(1, result.size());
    }

    @SneakyThrows
    @Test
    public void testPortLookupSucceeds() {
        when(destinationLabelLookup.lookupPort(3000)).thenReturn("App");
        String value = "{\"Port\":3000}";
        JsonNode jsonNode = objectMapper.readTree(value);
        JsonNode result = portLookup.call(jsonNode);

        assertEquals(2, result.size());
        assertEquals("App", result.get("DestinationLabel").asText());
    }
}
