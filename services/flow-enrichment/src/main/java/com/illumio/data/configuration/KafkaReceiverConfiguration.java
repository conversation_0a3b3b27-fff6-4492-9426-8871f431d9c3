package com.illumio.data.configuration;

import lombok.RequiredArgsConstructor;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class KafkaReceiverConfiguration {
    private final FlowEnrichmentConfig flowEnrichmentConfig;

    @Bean
    public KafkaReceiver<String, String> flowsKafkaReceiver() {
        Map<String, Object> consumerProps = new HashMap<>();
        consumerProps.put(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                flowEnrichmentConfig.getKafkaCommonConfig().getBootstrapServers());
        consumerProps.put(
                ConsumerConfig.GROUP_ID_CONFIG,
                flowEnrichmentConfig.getKafkaReceiverConfig().getGroupId());
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(
                CommonClientConfigs.REQUEST_TIMEOUT_MS_CONFIG,
                flowEnrichmentConfig.getKafkaReceiverConfig().getRequestTimeoutMs());
        consumerProps.put(
                ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,
                flowEnrichmentConfig.getKafkaReceiverConfig().getAutoOffsetReset());
        if (null != flowEnrichmentConfig.getKafkaCommonConfig().getIsSasl()
                && flowEnrichmentConfig.getKafkaCommonConfig().getIsSasl()) {
            consumerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            consumerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            consumerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    flowEnrichmentConfig.getKafkaCommonConfig().getSaslJaasConfig());
        }
        consumerProps.put(
                ConsumerConfig.MAX_POLL_RECORDS_CONFIG,
                flowEnrichmentConfig.getKafkaReceiverConfig().getMaxPollRecords());
        consumerProps.put(
                ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG,
                flowEnrichmentConfig.getKafkaReceiverConfig().getMaxPartitionFetchBytes());

        ReceiverOptions<String, String> receiverOptions =
                ReceiverOptions.<String, String>create(consumerProps)
                        .subscription(
                                Collections.singleton(
                                        flowEnrichmentConfig.getKafkaReceiverConfig().getTopic()));

        return KafkaReceiver.create(receiverOptions);
    }
}
