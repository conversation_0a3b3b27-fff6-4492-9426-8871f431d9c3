package com.illumio.data.components;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class DestinationLabelLookup {

    private final ConcurrentHashMap<Integer, String> portLabelMap = new ConcurrentHashMap<>();

    public DestinationLabelLookup() {
        try {
            // read label-port-info JSON file from resources
            Resource resource = new ClassPathResource("label-port-info.json");
            InputStreamReader reader = new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8);

            // Json to map
            ObjectMapper mapper = new ObjectMapper();
            Map<String, String> portMap = mapper.readValue(reader, new TypeReference<Map<String, String>>() {});

            // generate ConcurrentHashMap
            for (Map.Entry<String, String> entry : portMap.entrySet()) {
                portLabelMap.put(Integer.parseInt(entry.getKey()), entry.getValue());
            }
        } catch (IOException e) {
            log.error("Failed to load port labels from JSON", e);
            throw new RuntimeException("Failed to load port labels from JSON", e);

        }
    }

    public String lookupPort(int port) {
        return portLabelMap.get(port);
    }
}