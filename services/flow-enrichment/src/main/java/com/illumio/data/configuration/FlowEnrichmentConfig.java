package com.illumio.data.configuration;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "flow-enrichment")
@Data
public class FlowEnrichmentConfig {
    private final KafkaCommonConfig kafkaCommonConfig;
    private final KafkaReceiverConfig kafkaReceiverConfig;
    private final KafkaSenderConfig kafkaSenderConfig;

    @Configuration
    @Getter
    @Setter
    public static class KafkaCommonConfig {
        private String bootstrapServers;
        private Boolean isSasl = false;
        private String saslJaasConfig;
    }

    @Configuration
    @Getter
    @Setter
    public static class KafkaReceiverConfig {
        private String topic;
        private String groupId;
        private String autoOffsetReset;
        private Integer requestTimeoutMs;
        private Integer maxPollRecords;
        private Integer maxPartitionFetchBytes;
    }

    @Configuration
    @Getter
    @Setter
    public static class KafkaSenderConfig {
        private String sinkTopic;
    }
}
