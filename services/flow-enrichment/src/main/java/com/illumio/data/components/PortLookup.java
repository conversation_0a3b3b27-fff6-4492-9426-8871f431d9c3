package com.illumio.data.components;

import com.fasterxml.jackson.databind.JsonNode;

import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
@Slf4j
public class PortLookup {
    private static final String PORT = "Port";
    private static final String DESTINATION_LABEL = "DestinationLabel";

    private final DestinationLabelLookup destinationLabelLookup;

    public Mono<JsonNode> maybeAddDestinationLabel(JsonNode decoratedFlow) {
        return Mono.fromCallable(() -> call(decoratedFlow));
    }

    public JsonNode call(JsonNode decoratedFlow) {
        if (decoratedFlow.has(PORT)) {
            JsonNode portNode = decoratedFlow.get(PORT);
            if (portNode.isInt()) {
                int port = portNode.asInt();
                String destinationLabel = destinationLabelLookup.lookupPort(port);
                if (null != destinationLabel) {
                    ObjectNode objectNode = (ObjectNode) decoratedFlow;
                    objectNode.put(DESTINATION_LABEL, destinationLabel);
                    return objectNode;
                }
            }
        }
        return decoratedFlow;
    }
}
