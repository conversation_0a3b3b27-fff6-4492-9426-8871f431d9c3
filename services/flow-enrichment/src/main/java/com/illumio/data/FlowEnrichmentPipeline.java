package com.illumio.data;

import com.illumio.data.components.EnrichedFlowWriter;
import com.illumio.data.components.DecoratedFlowReader;
import com.illumio.data.components.PortLookup;
import com.illumio.data.configuration.FlowEnrichmentConfig;
import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.stereotype.Component;

import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.kafka.sender.SenderResult;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.NoSuchElementException;
import java.util.Optional;

@Slf4j
@Component
public class FlowEnrichmentPipeline {
    private final KafkaReceiver<String, String> kafkaReceiver;
    private final DecoratedFlowReader decoratedFlowReader;
    private final PortLookup portLookup;
    private final EnrichedFlowWriter enrichedFlowWriter;
    private final KafkaSender<String, String> kafkaSender;
    private final String sinkTopic;

    private Disposable disposable;

    public FlowEnrichmentPipeline(
            KafkaReceiver<String, String> kafkaReceiver,
            DecoratedFlowReader decoratedFlowReader,
            PortLookup portLookup,
            EnrichedFlowWriter enrichedFlowWriter,
            KafkaSender<String, String> kafkaSender,
            FlowEnrichmentConfig flowEnrichmentConfig) {
        this.kafkaReceiver = kafkaReceiver;
        this.decoratedFlowReader = decoratedFlowReader;
        this.portLookup = portLookup;
        this.enrichedFlowWriter = enrichedFlowWriter;
        this.kafkaSender = kafkaSender;
        this.sinkTopic = getSinkTopic(flowEnrichmentConfig);
    }

    /**
     * Fail early if sink topic is not defined.
     *
     * @param flowEnrichmentConfig
     * @return
     */
    private String getSinkTopic(FlowEnrichmentConfig flowEnrichmentConfig) {
        return Optional.of(flowEnrichmentConfig)
                .map(FlowEnrichmentConfig::getKafkaSenderConfig)
                .map(FlowEnrichmentConfig.KafkaSenderConfig::getSinkTopic)
                .orElseThrow(
                        () ->
                                new NoSuchElementException(
                                        "No flows topic configured. Please make sure "
                                                + "flowEnrichment.kafkaSenderConfig.sinkTopic is set."));
    }

    public void start() {
        this.disposable = startInternal().subscribe();
    }

    public Flux<SenderResult<String>> startInternal() {
        return kafkaSender
                .send(
                        kafkaReceiver
                                .receiveAutoAck()
                                .publishOn(Schedulers.boundedElastic())
                                .retryWhen(
                                        Retry.backoff(5, Duration.ofSeconds(3L))
                                                .doBeforeRetry(
                                                        retrySignal ->
                                                                log.warn(
                                                                        "Error receiving from kafka {}",
                                                                        retrySignal.toString())))
                                .concatMap(r -> r)
                                .flatMap(this::processConsumerRecord))
                .retryWhen(
                        Retry.backoff(5, Duration.ofSeconds(3L))
                                .doBeforeRetry(
                                        retrySignal ->
                                                log.warn(
                                                        "Error sending to kafka {}",
                                                        retrySignal.toString())));
    }

    public Mono<SenderRecord<String, String, String>> processConsumerRecord(
            ConsumerRecord<String, String> consumerRecord) {
        return Mono.just(consumerRecord)
                .doOnNext(__ -> log.debug("Processing record {}", consumerRecord))
                .publishOn(Schedulers.parallel())
                .flatMap(
                        __ ->
                                decoratedFlowReader
                                        .readTree(consumerRecord.value())
                                        .doOnError(
                                                throwable ->
                                                        log.error(
                                                                "Error parsing record {}",
                                                                consumerRecordString(
                                                                        consumerRecord),
                                                                throwable))
                                        .flatMap(portLookup::maybeAddDestinationLabel)
                                        .doOnError(
                                                throwable ->
                                                        log.error(
                                                                "Error enriching flow with port lookup {}: {}",
                                                                consumerRecordString(
                                                                        consumerRecord),
                                                                consumerRecord.value(),
                                                                throwable))
                                        .flatMap(enrichedFlowWriter::writeTreeAsString)
                                        .doOnNext(enrichedFlow -> log.debug("Sending json record: {}", enrichedFlow)))
                .doOnError(throwable -> log.warn("Error parsing or adding label"))
                .onErrorReturn(consumerRecord.value())
                .flatMap(
                        newValue ->
                                Mono.just(
                                        SenderRecord.create(
                                                new ProducerRecord<>(
                                                        sinkTopic, consumerRecord.key(), newValue),
                                                consumerRecordString(consumerRecord))));
    }

    private String consumerRecordString(ConsumerRecord<String, String> consumerRecord) {
        return consumerRecord.topic()
                + "-"
                + consumerRecord.partition()
                + "@"
                + consumerRecord.offset();
    }

    public void stop() {
        this.disposable.dispose();
    }
}
