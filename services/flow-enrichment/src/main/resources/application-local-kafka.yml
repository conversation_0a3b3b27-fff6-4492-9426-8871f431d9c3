logging:
  level:
    ROOT: DEBUG
    org:
      apache:
        kafka: INFO
spring:
  application:
    name: flow-enrichment
  output:
    ansi:
      enabled: ALWAYS

flowEnrichment:
  kafkaCommonConfig:
    bootstrapServers: localhost:9092
    isSasl: false
    saslJaasConfig: _DO_NOT_COMMIT_
  kafkaReceiverConfig:
    topic: illumio-decorated-flows
    groupId: flow-enrichment-group
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880
  kafkaSenderConfig:
    sinkTopic: enriched-flows
