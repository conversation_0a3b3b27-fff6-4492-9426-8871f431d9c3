logging:
  level:
    ROOT: INFO

spring:
  application:
    name: kusto-sink
  output:
    ansi:
      enabled: ALWAYS

# TODO update these to event hub
flowEnrichment:
  kafkaCommonConfig:
    bootstrapServers: arch-eventhub.servicebus.windows.net:9093
    isSasl: true
    saslJaasConfig: _DO_NOT_COMMIT_
  kustoCommonConfig:
    azureClientId: _DO_NOT_COMMIT_
    azureClientSecret: _DO_NOT_COMMIT_
    azureTenantId: _DO_NOT_COMMIT_
    dataIngestionUri: https://ingest-arch-kusto.eastus.kusto.windows.net
  decoratedFlows:
    kafkaReceiverConfig:
      topic: illumio-decorated-flows
      groupId: kusto-decorated-flows-group
      autoOffsetReset: latest
      requestTimeoutMs: 180000
      maxPollRecords: 20000
      maxPartitionFetchBytes: 5242880
    kustoTableConfig:
      database: Illumio
      table: DecoratedFlows
  insights:
    kafkaReceiverConfig:
      topic: illumio-insights
      groupId: kusto-insights-group
      autoOffsetReset: latest
      requestTimeoutMs: 180000
      maxPollRecords: 20000
      maxPartitionFetchBytes: 5242880
    kustoTableConfig:
      database: Illumio
      table: Insights