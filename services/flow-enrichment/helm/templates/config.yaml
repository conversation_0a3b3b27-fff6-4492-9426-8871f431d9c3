apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "FlowEnrichment.fullname" . }}-env-configmap
  labels:
    {{- include "FlowEnrichment.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
        org:
          apache:
            kafka: INFO
    
    spring:
      application:
        name: flow-enrichment
      output:
        ansi:
          enabled: ALWAYS
    
    flowEnrichment:
      kafkaCommonConfig:
        bootstrapServers: "{{.Values.flowEnrichment.kafkaCommonConfig.bootstrapServers}}"
        isSasl: true
      kafkaReceiverConfig:
        topic: "{{.Values.flowEnrichment.kafkaReceiverConfig.topic}}"
        groupId: "{{.Values.flowEnrichment.kafkaReceiverConfig.groupId}}"
        autoOffsetReset: "{{.Values.flowEnrichment.kafkaReceiverConfig.autoOffsetReset}}"
        requestTimeoutMs: "{{.Values.flowEnrichment.kafkaReceiverConfig.requestTimeoutMs}}"
        maxPollRecords: "{{.Values.flowEnrichment.kafkaReceiverConfig.maxPollRecords}}"
        maxPartitionFetchBytes: "{{.Values.flowEnrichment.kafkaReceiverConfig.maxPartitionFetchBytes}}"
      kafkaSenderConfig:
        sinkTopic: "{{.Values.flowEnrichment.kafkaSenderConfig.sinkTopic}}"