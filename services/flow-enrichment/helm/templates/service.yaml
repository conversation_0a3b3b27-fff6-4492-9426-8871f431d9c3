apiVersion: v1
kind: Service
metadata:
  name: {{ include "FlowEnrichment.name" . }}
  labels:
    {{- include "FlowEnrichment.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "FlowEnrichment.selectorLabels" . | nindent 4 }}
