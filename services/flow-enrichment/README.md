# Extended Decoration Connector
A sink data connector that decorates the flow and extends it.

# Running locally
1. Modify application-local-kafka.yaml
    1. kafkaCommonConfig
    2. kafkaReceiverConfig
2. Create src/main/resources/secrets.yml and fill in secrets:
```yaml
flowEnrichment:
   kafkaCommonConfig:
      saslJaasConfig: __DO_NOT_COMMIT__

```
3. Use IntelliJ Run Configuration:
- flow-enrichment/FlowEnrichment