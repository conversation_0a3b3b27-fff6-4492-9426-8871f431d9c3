apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "iqs.fullname" . }}
  labels:
    {{- include "iqs.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "iqs.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
      {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.podMetricsAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "iqs.labels" . | nindent 8 }}
        {{- if .Values.extraLabels }}
          {{ toYaml .Values.extraLabels | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "iqs.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}

      # Init container to create metadata DB before app startup
      initContainers:
        - name: db-init
          image: {{ .Values.image.repositoryBase }}{{ .Values.image.metadataDbRepositoryName }}:{{ .Values.image.metadataDbRepositoryTag }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          envFrom:
            - configMapRef:
                name: {{ include "iqs.fullname" . }}-metadatadb-config
          env:
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "iqs.fullname" . }}-env-secrets
                  key: METADATA_DB_PASSWORD
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repositoryBase }}{{ .Values.image.repositoryName }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          envFrom:
            - secretRef:
                name: {{ include "iqs.fullname" . }}-env-secrets
          env:
            - name: JDK_JAVA_OPTIONS
              value: "{{ .Values.jvmOptions }}"
          ports:
          {{- range .Values.ports }}
            - name: {{ .name }}
              containerPort: {{ .port }}
              protocol: TCP
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: {{ include "iqs.fullname" . }}-env-configmap
              mountPath: /var/resources/

      volumes:
        - name: {{ include "iqs.fullname" . }}-env-configmap
          configMap:
            name: {{ include "iqs.fullname" . }}-env-configmap

      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
