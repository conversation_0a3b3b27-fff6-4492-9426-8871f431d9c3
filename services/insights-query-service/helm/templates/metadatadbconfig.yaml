apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "iqs.fullname" . }}-metadatadb-config
  labels:
    {{- include "iqs.labels" . | nindent 4 }}
data:
  DB_HOST: {{ .Values.metadataDbConfig.host | quote }}
  DB_PORT: {{ .Values.metadataDbConfig.port | quote }}
  DB_NAME: {{ .Values.metadataDbConfig.database | quote }}
  DB_USERNAME: {{ .Values.metadataDbConfig.username | quote }}
  NEWDB_NAME: {{ .Values.metadataDbConfig.metadataDbName | quote }}
  DB_TYPE: {{ .Values.metadataDbConfig.type | quote }}