package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.TrafficDirection;
import com.illumio.data.model.constants.WidgetId;
import com.illumio.data.utils.ApplicationUtils;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;
import static com.illumio.data.model.constants.Fields.ROLE;

@Data
@NoArgsConstructor
@Component
public class IpTopRolesResponse implements ResponseBuilder<IpTopRolesResponse> {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public IpTopRolesResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        if (requestContext.getRequestPayload().isEmpty()) {
            throw new IllegalArgumentException("Request Payload is empty");
        }
        RequestPayload payload = requestContext.getRequestPayload().get();

        IpTopRolesResponse response = this;

        TrafficDirection direction = ApplicationUtils.getTrafficDirection(payload.getFilters());
        Fields roleField = ROLE.resolve(direction);

        //Set Widget Specific Metadata
        response.setWidgetId(WidgetId.IP_TOP_ROLES);
        response.setTitle("Top 15 Roles Communicating With IP");
        response.setColumns(Arrays.asList(
                roleField.getFieldKey(),
                AGGREGATE_FIELD.getFieldKey(),
                PREVIOUS_COUNT.getFieldKey(),
                COUNT.getFieldKey()));
        response.setColumnTypes(Arrays.asList(
                roleField.getFieldType(),
                AGGREGATE_FIELD.getFieldType(),
                PREVIOUS_COUNT.getFieldType(),
                COUNT.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(
                roleField.getFieldDisplayName(),
                AGGREGATE_FIELD.getFieldDisplayName(),
                PREVIOUS_COUNT.getFieldDisplayName(),
                COUNT.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        List<List<Object>> formattedData = new ArrayList<>();
        while (resultSetTable.next()) {
            String role = (String) resultSetTable.getObject(roleField.getTableColumnName());
            String aggregateField = (String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName());
            Long prevValue = getLongValue(resultSetTable, PREVIOUS_COUNT.getTableColumnName());
            Long currentValue = getLongValue(resultSetTable, COUNT.getTableColumnName());

            List<Object> dataRow = new ArrayList<>();
            dataRow.add(role);
            dataRow.add(aggregateField);
            dataRow.add(prevValue);
            dataRow.add(currentValue);
            formattedData.add(dataRow);
        }
        response.setData(formattedData);

        return response;
    }

    @Override
    public IpTopRolesResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : 0L;
    }
}
