package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.service.batch_widget.PivotExtractable;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.ACCOUNT_NAME;
import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.CATEGORY_ID;
import static com.illumio.data.model.constants.Fields.CLOUD_PROVIDER;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;

@Data
@NoArgsConstructor
@Component
public class TopIctResponse implements ResponseBuilder<TopIctResponse>, PivotExtractable {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public TopIctResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        TopIctResponse response = new TopIctResponse();

        //Set Widget Specific Metadata
        response.setWidgetId(requestMetadata.getWidgetId());
        response.setTitle("Top ICTs with Malicious IP flows");
        response.setColumns(Arrays.asList(CATEGORY_ID.getFieldKey(), ACCOUNT_NAME.getFieldKey(), CLOUD_PROVIDER.getFieldKey(), AGGREGATE_FIELD.getFieldKey(), COUNT.getFieldKey(), PREVIOUS_COUNT.getFieldKey()));
        response.setColumnTypes(Arrays.asList(CATEGORY_ID.getFieldType(), ACCOUNT_NAME.getFieldType(), CLOUD_PROVIDER.getFieldType(), AGGREGATE_FIELD.getFieldType(), COUNT.getFieldType(), PREVIOUS_COUNT.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(CATEGORY_ID.getFieldDisplayName(), ACCOUNT_NAME.getFieldDisplayName(), CLOUD_PROVIDER.getFieldDisplayName(), AGGREGATE_FIELD.getFieldDisplayName(), COUNT.getFieldDisplayName(), PREVIOUS_COUNT.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        List<List<Object>> data = new ArrayList<>();

        while (resultSetTable.next()) {
            // Extract the row fields you need
            String categoryId = (String) resultSetTable.getObject(CATEGORY_ID.getTableColumnName());
            String accountName = (String) resultSetTable.getObject(ACCOUNT_NAME.getTableColumnName());
            String cloudProvider = (String) resultSetTable.getObject(CLOUD_PROVIDER.getTableColumnName());
            Long countVal = getLongValue(resultSetTable, COUNT.getTableColumnName());
            Long previousCountVal = getLongValue(resultSetTable, PREVIOUS_COUNT.getTableColumnName());
            String aggregateField = ((String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName())).toUpperCase();

            List<Object> dataRow = new ArrayList<>();
            dataRow.add(categoryId);
            dataRow.add(accountName);
            dataRow.add(cloudProvider);
            dataRow.add(aggregateField);
            dataRow.add(countVal);
            dataRow.add(previousCountVal);
            data.add(dataRow);
        }

        response.setData(data);

        return response;
    }

    @Override
    public TopIctResponse buildAggregatedResponse
            (List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : 0L;
    }
    
    /**
     * Converts the response data to CSV format for efficient consumption.
     * 
     * @return A Map containing the CSV data and metadata
     */
    public Map<String, Object> compress() {
        Map<String, Object> result = new HashMap<>();
        StringBuilder csvData = new StringBuilder();
        
        // Add CSV header based on column display names
        csvData.append("CategoryID,AccountName,CloudProvider,AggregateField,Count,PreviousCount\n");
        
        // Add data rows
        if (data != null && !data.isEmpty()) {
            for (List<Object> row : data) {
                if (row.size() < 6) continue;
                
                String categoryId = (String) row.get(0);
                String accountName = (String) row.get(1);
                String cloudProvider = (String) row.get(2);
                String aggregateField = (String) row.get(3);
                Long count = (row.get(4) != null) ? ((Number)row.get(4)).longValue() : null;
                Long previousCount = (row.get(5) != null) ? ((Number)row.get(5)).longValue() : null;
                
                // Escape fields for CSV format
                String escapedCategoryId = escapeCsvField(categoryId);
                String escapedAccountName = escapeCsvField(accountName);
                String escapedCloudProvider = escapeCsvField(cloudProvider);
                String escapedAggField = escapeCsvField(aggregateField);
                
                // Add row to CSV
                csvData.append(escapedCategoryId).append(",")
                       .append(escapedAccountName).append(",")
                       .append(escapedCloudProvider).append(",")
                       .append(escapedAggField).append(",")
                       .append(count != null ? count : 0).append(",")
                       .append(previousCount != null ? previousCount : 0).append("\n");
            }
        }
        
        // Add metadata
        if (currentTimeFrame != null) {
            result.put("currentTimeFrameStart", currentTimeFrame.getStartTime());
            result.put("currentTimeFrameEnd", currentTimeFrame.getEndTime());
        }
        
        if (comparisonTimeFrame != null) {
            result.put("comparisonTimeFrameStart", comparisonTimeFrame.getStartTime());
            result.put("comparisonTimeFrameEnd", comparisonTimeFrame.getEndTime());
        }
        
        if (pagination != null) {
            result.put("pageNumber", pagination.getPageNumber());
            result.put("totalPages", pagination.getTotalPages());
        }
        
        // Add CSV data and format
        result.put("csvData", csvData.toString());
        result.put("format", "csv");
        result.put("widgetId", widgetId);
        result.put("title", title);
        
        return result;
    }
    
    /**
     * Properly escapes a field value for CSV format according to RFC 4180:
     * - If the field contains commas, newlines, or double quotes, it should be enclosed in double quotes
     * - Double quotes within the field should be escaped by doubling them
     * 
     * @param value The field value to escape
     * @return The properly escaped CSV field
     */
    private String escapeCsvField(String value) {
        if (value == null) {
            return "";
        }
        
        boolean needsQuoting = value.contains(",") || value.contains("\"") || 
                               value.contains("\n") || value.contains("\r");
        
        if (needsQuoting) {
            // Replace any double quotes with two double quotes
            String escapedValue = value.replace("\"", "\"\"");
            return "\"" + escapedValue + "\"";
        } else {
            return value;
        }
    }
}
