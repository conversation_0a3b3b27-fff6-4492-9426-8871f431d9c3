package com.illumio.data.service.batch_widget;

import com.illumio.data.model.Pagination;
import com.illumio.data.model.TimeFrame;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Utility class for robust response compression that can handle dynamic fields and structures.
 * This ensures compress() functions work despite new field additions or structural changes.
 */
public class ResponseCompressor {

    /**
     * Creates a compressed CSV representation of response data that dynamically adapts to any structure.
     * 
     * @param widgetId Widget identifier
     * @param title Widget title
     * @param columns Column names
     * @param columnDisplayNames Display names for columns
     * @param data Table data as List<List<Object>>
     * @param currentTimeFrame Current time frame
     * @param comparisonTimeFrame Comparison time frame
     * @param pagination Pagination info
     * @return Compressed response map
     */
    public static Map<String, Object> compressToCSV(
            String widgetId,
            String title,
            List<String> columns,
            List<String> columnDisplayNames,
            List<List<Object>> data,
            TimeFrame currentTimeFrame,
            TimeFrame comparisonTimeFrame,
            Pagination pagination) {
        
        Map<String, Object> result = new HashMap<>();
        StringBuilder csvData = new StringBuilder();
        
        // Dynamically build CSV header
        List<String> headerNames = getEffectiveColumnHeaders(columns, columnDisplayNames, data);
        String csvHeader = headerNames.stream()
                .map(ResponseCompressor::escapeCsvField)
                .collect(Collectors.joining(","));
        csvData.append(csvHeader).append("\n");
        
        // Add data rows dynamically
        if (data != null && !data.isEmpty()) {
            for (List<Object> row : data) {
                if (row == null || row.isEmpty()) continue;
                
                // Process each field in the row dynamically
                List<String> rowValues = new ArrayList<>();
                for (int i = 0; i < Math.max(row.size(), headerNames.size()); i++) {
                    Object value = (i < row.size()) ? row.get(i) : null;
                    String stringValue = formatValueForCsv(value);
                    rowValues.add(escapeCsvField(stringValue));
                }
                
                // Add row to CSV
                csvData.append(String.join(",", rowValues)).append("\n");
            }
        }
        
        // Add consistent metadata
        result.put("widget_id", widgetId);
        result.put("title", title);
        result.put("format", "csv");
        result.put("data", csvData.toString());
        result.put("row_count", data != null ? data.size() : 0);
        result.put("column_count", headerNames.size());
        
        // Add time frame info
        if (currentTimeFrame != null) {
            result.put("current_period", currentTimeFrame.getStartTime() + " to " + currentTimeFrame.getEndTime());
        }
        if (comparisonTimeFrame != null) {
            result.put("comparison_period", comparisonTimeFrame.getStartTime() + " to " + comparisonTimeFrame.getEndTime());
        }
        
        // Add pagination info if available
        if (pagination != null) {
            result.put("page_number", pagination.getPageNumber());
            result.put("total_pages", pagination.getTotalPages());
            result.put("row_limit", pagination.getRowLimit());
        }
        
        return result;
    }
    
    /**
     * Get effective column headers, with fallback strategies for robustness.
     */
    private static List<String> getEffectiveColumnHeaders(
            List<String> columns, 
            List<String> columnDisplayNames, 
            List<List<Object>> data) {
        
        // Priority 1: Use display names if available
        if (columnDisplayNames != null && !columnDisplayNames.isEmpty()) {
            return columnDisplayNames;
        }
        
        // Priority 2: Use raw column names
        if (columns != null && !columns.isEmpty()) {
            return columns;
        }
        
        // Priority 3: Generate headers from data structure
        if (data != null && !data.isEmpty()) {
            List<Object> firstRow = data.get(0);
            List<String> headers = new ArrayList<>();
            for (int i = 0; i < firstRow.size(); i++) {
                headers.add("Column" + (i + 1));
            }
            return headers;
        }
        
        // Fallback: No data scenario
        return Arrays.asList("No Data");
    }
    
    /**
     * Format a value for CSV output, handling different data types robustly.
     */
    private static String formatValueForCsv(Object value) {
        if (value == null) {
            return "";
        } else if (value instanceof Number) {
            return value.toString();
        } else if (value instanceof Boolean) {
            return value.toString();
        } else if (value instanceof List) {
            // Handle nested lists by joining with semicolons
            List<?> list = (List<?>) value;
            return list.stream()
                    .map(item -> item != null ? item.toString() : "")
                    .collect(Collectors.joining(";"));
        } else if (value instanceof Map) {
            // Handle maps by converting to key=value pairs
            Map<?, ?> map = (Map<?, ?>) value;
            return map.entrySet().stream()
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .collect(Collectors.joining(";"));
        } else {
            return value.toString();
        }
    }
    
    /**
     * Escapes a field value for CSV format according to RFC 4180.
     */
    private static String escapeCsvField(String value) {
        if (value == null) return "";
        
        // If field contains comma, quote, or newline, wrap in quotes and escape quotes
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }
    
    /**
     * Simple compress method for responses that just need basic CSV conversion.
     */
    public static Map<String, Object> simpleCompress(
            String widgetId,
            String title,
            List<String> columns,
            List<List<Object>> data) {
        return compressToCSV(widgetId, title, columns, null, data, null, null, null);
    }
} 