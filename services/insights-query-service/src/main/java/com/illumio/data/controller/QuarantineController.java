package com.illumio.data.controller;

import com.illumio.data.model.RequestPayload;
import com.illumio.data.service.QuarantineKafkaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/inventory/resources/{resourceId}/quarantine")
public class QuarantineController {
    private final QuarantineKafkaService quarantineKafkaService;

    @PutMapping
    public Mono<ResponseEntity<Object>> putQuarantine(
            @RequestHeader MultiValueMap<String, String> headers,
            @RequestParam MultiValueMap<String, String> params,
            @RequestBody RequestPayload payload,
            @PathVariable String resourceId) {

        String illumioTenantId = headers.getFirst("x-tenant-id");
        String csiToken = headers.getFirst("x-csi");

        log.debug("Received request with x-tenant-id: {}", illumioTenantId);

        // Validate mandatory headers
        if (illumioTenantId == null || illumioTenantId.trim().isEmpty()) {
            log.error("Missing required header: x-tenant-id");
            return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", "Missing required header: x-tenant-id."));
        }
        if (csiToken == null || csiToken.trim().isEmpty()) {
            log.error("Missing required header: x-csi");
            return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", "Missing required header: x-csi."));
        }

        //census validations
        //grpc validations

        return quarantineKafkaService.sendQuarantineMessage(headers, params, payload, resourceId)
                .then(Mono.just(ResponseEntity.ok().build()))
                .onErrorResume(e -> {
                    log.error("Error sending quarantine message: {}", e.getMessage(), e);
                    if (e instanceof IllegalArgumentException ) {
                        return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", e.getMessage()));
                    }
                    return Mono.just(ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(null));
                });
    }

    private ResponseEntity<Object> errorResponse(HttpStatus status, String error, String message) {
        return ResponseEntity.status(status).body(Map.of("error", error, "message", message));
    }
}
