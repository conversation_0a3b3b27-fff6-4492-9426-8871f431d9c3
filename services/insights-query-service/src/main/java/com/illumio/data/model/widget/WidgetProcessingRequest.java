package com.illumio.data.model.widget;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * Represents a request to process a specific widget, optionally with direction or category filtering.
 */
@Getter
@Builder
@AllArgsConstructor
@ToString
public class WidgetProcessingRequest {
    private final String widgetId;
    private final String trafficDirection;
    private final String categoryName;
    private final String categoryValue;
    
    // Additional filters for pivot widget scenarios
    private final Map<String, List<String>> additionalFilters;
    
    public WidgetProcessingRequest(String widgetId, String categoryName, String categoryValue) {
        this.widgetId = widgetId;
        this.trafficDirection = null;
        this.categoryName = categoryName;
        this.categoryValue = categoryValue;
        this.additionalFilters = null;
    }
    
    public WidgetProcessingRequest(String widgetId, String trafficDirection, String categoryName, String categoryValue) {
        this.widgetId = widgetId;
        this.trafficDirection = trafficDirection;
        this.categoryName = categoryName;
        this.categoryValue = categoryValue;
        this.additionalFilters = null;
    }
    
    public static WidgetProcessingRequest directional(String widgetId, String direction) {
        return WidgetProcessingRequest.builder()
                .widgetId(widgetId)
                .trafficDirection(direction)
                .build();
    }
    
    public static WidgetProcessingRequest basic(String widgetId) {
        return WidgetProcessingRequest.builder()
                .widgetId(widgetId)
                .build();
    }
}
