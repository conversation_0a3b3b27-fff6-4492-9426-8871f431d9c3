package com.illumio.data.configuration;

import com.illumio.data.repository.RequestMetadataRepository;
import com.illumio.data.repository.iva.ReportSummaryRepository;
import com.illumio.data.repository.iva.ReportTagRepository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.r2dbc.repository.support.R2dbcRepositoryFactory;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import io.r2dbc.spi.ConnectionFactory;

@Configuration
public class RepositoryConfig {

    @Bean
    public RequestMetadataRepository requestMetadataRepository(@Qualifier("metadataConnectionFactory") ConnectionFactory connectionFactory) {
        R2dbcEntityTemplate template = new R2dbcEntityTemplate(connectionFactory);
        R2dbcRepositoryFactory factory = new R2dbcRepositoryFactory(template);
        return factory.getRepository(RequestMetadataRepository.class);
    }

    @Bean
    public ReportSummaryRepository reportSummaryRepository(@Qualifier("ivaConnectionFactory") ConnectionFactory connectionFactory) {
        R2dbcEntityTemplate template = new R2dbcEntityTemplate(connectionFactory);
        R2dbcRepositoryFactory factory = new R2dbcRepositoryFactory(template);
        return factory.getRepository(ReportSummaryRepository.class);
    }

    @Bean
    public ReportTagRepository reportTagRepository(@Qualifier("ivaConnectionFactory") ConnectionFactory connectionFactory) {
        R2dbcEntityTemplate template = new R2dbcEntityTemplate(connectionFactory);
        R2dbcRepositoryFactory factory = new R2dbcRepositoryFactory(template);
        return factory.getRepository(ReportTagRepository.class);
    }
}
