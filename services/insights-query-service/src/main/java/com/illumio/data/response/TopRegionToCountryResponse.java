package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.service.batch_widget.PivotExtractable;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.DESTINATION_CLOUD_PROVIDER;
import static com.illumio.data.model.constants.Fields.DESTINATION_COUNTRY;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;
import static com.illumio.data.model.constants.Fields.SOURCE_CLOUD_PROVIDER;
import static com.illumio.data.model.constants.Fields.SOURCE_REGION;

@Data
@Component
public class TopRegionToCountryResponse implements ResponseBuilder<TopRegionToCountryResponse>, PivotExtractable {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public TopRegionToCountryResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        TopRegionToCountryResponse response = new TopRegionToCountryResponse();

        //Set Widget Specific Metadata
        response.setWidgetId(requestMetadata.getWidgetId());
        response.setTitle("Top Region To Country Traffic");
        response.setColumns(Arrays.asList(SOURCE_REGION.getFieldKey(), SOURCE_CLOUD_PROVIDER.getFieldKey(),  DESTINATION_COUNTRY.getFieldKey(),  DESTINATION_CLOUD_PROVIDER.getFieldKey(), AGGREGATE_FIELD.getFieldKey(), COUNT.getFieldKey(), PREVIOUS_COUNT.getFieldKey()));
        response.setColumnTypes(Arrays.asList(SOURCE_REGION.getFieldType(), SOURCE_CLOUD_PROVIDER.getFieldType(),  DESTINATION_COUNTRY.getFieldType(),  DESTINATION_CLOUD_PROVIDER.getFieldType(), AGGREGATE_FIELD.getFieldType(), COUNT.getFieldType(), PREVIOUS_COUNT.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(SOURCE_REGION.getFieldDisplayName(), SOURCE_CLOUD_PROVIDER.getFieldDisplayName(),  DESTINATION_COUNTRY.getFieldDisplayName(),  DESTINATION_CLOUD_PROVIDER.getFieldDisplayName(), AGGREGATE_FIELD.getFieldDisplayName(), COUNT.getFieldDisplayName(), PREVIOUS_COUNT.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        List<List<Object>> data = new ArrayList<>();
        while (resultSetTable.next()) {
            String srcRegion = (String) resultSetTable.getObject(SOURCE_REGION.getTableColumnName());
            String srcCloudProvider = (String) resultSetTable.getObject(SOURCE_CLOUD_PROVIDER.getTableColumnName());
            String destCountry = (String) resultSetTable.getObject(DESTINATION_COUNTRY.getTableColumnName());
            String destCloudProvider = (String) resultSetTable.getObject(DESTINATION_CLOUD_PROVIDER.getTableColumnName());
            String aggrField = (String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName());
            Long count = getLongValue(resultSetTable, COUNT.getTableColumnName());
            Long prevCount = getLongValue(resultSetTable, PREVIOUS_COUNT.getTableColumnName());

            List<Object> dataRow = new ArrayList<>();
            dataRow.add(srcRegion);
            dataRow.add(srcCloudProvider);
            dataRow.add(destCountry);
            dataRow.add(destCloudProvider);
            dataRow.add(aggrField.toUpperCase());
            dataRow.add(count);
            dataRow.add(prevCount);

            data.add(dataRow);
        }
        response.setData(data);

        return response;
    }

    @Override
    public TopRegionToCountryResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }
    
    /**
     * Compresses the response for LLM consumption to reduce token count.
     * The compressed format maintains all essential information while significantly reducing verbosity.
     *
     * @return A Map containing the compressed response
     */
    public Map<String, Object> compress() {
        Map<String, Object> result = new HashMap<>();
        StringBuilder csvData = new StringBuilder();
        
        // Add CSV header based on column display names
        csvData.append("SourceRegion,SourceCloudProvider,DestinationCountry,DestinationCloudProvider,AggregateField,Count,PreviousCount\n");
        
        // Add data rows
        if (data != null && !data.isEmpty()) {
            for (List<Object> row : data) {
                if (row.size() < 7) continue;
                
                String srcRegion = (String) row.get(0);
                String srcCloud = (String) row.get(1);
                String destCountry = (String) row.get(2);
                String destCloud = (String) row.get(3);
                String aggField = (String) row.get(4);
                Long count = (row.get(5) != null) ? ((Number) row.get(5)).longValue() : null;
                Long prevCount = (row.get(6) != null) ? ((Number) row.get(6)).longValue() : null;
                
                // Escape fields for CSV format
                String escapedSrcRegion = escapeCsvField(srcRegion);
                String escapedSrcCloud = escapeCsvField(srcCloud);
                String escapedDestCountry = escapeCsvField(destCountry);
                String escapedDestCloud = escapeCsvField(destCloud);
                String escapedAggField = escapeCsvField(aggField);
                
                // Add row to CSV
                csvData.append(escapedSrcRegion).append(",")
                       .append(escapedSrcCloud).append(",")
                       .append(escapedDestCountry).append(",")
                       .append(escapedDestCloud).append(",")
                       .append(escapedAggField).append(",")
                       .append(count != null ? count : 0).append(",")
                       .append(prevCount != null ? prevCount : 0).append("\n");
            }
        }
        
        // Add metadata
        if (currentTimeFrame != null) {
            result.put("currentTimeFrameStart", currentTimeFrame.getStartTime());
            result.put("currentTimeFrameEnd", currentTimeFrame.getEndTime());
        }
        
        if (comparisonTimeFrame != null) {
            result.put("comparisonTimeFrameStart", comparisonTimeFrame.getStartTime());
            result.put("comparisonTimeFrameEnd", comparisonTimeFrame.getEndTime());
        }
        
        if (pagination != null) {
            result.put("pageNumber", pagination.getPageNumber());
            result.put("totalPages", pagination.getTotalPages());
        }
        
        // Add CSV data and format
        result.put("csvData", csvData.toString());
        result.put("format", "csv");
        result.put("widgetId", widgetId);
        result.put("title", title);
        
        return result;
    }
    
    /**
     * Properly escapes a field value for CSV format according to RFC 4180:
     * - If the field contains commas, newlines, or double quotes, it should be enclosed in double quotes
     * - Double quotes within the field should be escaped by doubling them
     * 
     * @param value The field value to escape
     * @return The properly escaped CSV field
     */
    private String escapeCsvField(String value) {
        if (value == null) {
            return "";
        }
        
        boolean needsQuoting = value.contains(",") || value.contains("\"") || 
                              value.contains("\n") || value.contains("\r");
        
        if (needsQuoting) {
            // Replace any double quotes with two double quotes
            String escapedValue = value.replace("\"", "\"\"");
            return "\"" + escapedValue + "\"";
        } else {
            return value;
        }
    }
}