package com.illumio.data.configuration;

import io.r2dbc.spi.ConnectionFactory;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.r2dbc.ConnectionFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class PostgresR2dbcConfig {

    private final InsightsServiceConfiguration insightsServiceConfiguration;

    @Bean("ivaConnectionFactory")
    @ConditionalOnProperty(name = "insights-config.ivaJdbcConfig.host")
    public ConnectionFactory ivaConnectionFactory() {
        InsightsServiceConfiguration.IvaJdbcConfig ivaJdbcConfig =
            insightsServiceConfiguration.getIvaJdbcConfig();
        String url = String.format("r2dbc:postgresql://%s:%d/%s?sslMode=%s",
                ivaJdbcConfig.getHost(),
                ivaJdbcConfig.getPort(),
                ivaJdbcConfig.getDatabase(),
                ivaJdbcConfig.getSslMode());

        return ConnectionFactoryBuilder
                .withUrl(url)
                .username(ivaJdbcConfig.getUsername())
                .password(ivaJdbcConfig.getPassword())
                .build();
    }

    @Bean("metadataConnectionFactory")
    @ConditionalOnProperty(name = "insights-config.metadataJdbcConfig.host")
    public ConnectionFactory metadataConnectionFactory() {
        InsightsServiceConfiguration.MetadataJdbcConfig metadataJdbcConfig =
                insightsServiceConfiguration.getMetadataJdbcConfig();
        String url = String.format("r2dbc:postgresql://%s:%d/%s?sslMode=%s",
                metadataJdbcConfig.getHost(),
                metadataJdbcConfig.getPort(),
                metadataJdbcConfig.getDatabase(),
                metadataJdbcConfig.getSslMode());

        return ConnectionFactoryBuilder
                .withUrl(url)
                .username(metadataJdbcConfig.getUsername())
                .password(metadataJdbcConfig.getPassword())
                .build();
    }
}