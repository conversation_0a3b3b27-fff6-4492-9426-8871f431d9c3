package com.illumio.data.configuration;


import com.illumio.data.managedidentity.kafka.KafkaOAuth2AuthenticateCallbackHandler;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Configuration
@RequiredArgsConstructor
public class KafkaProducerConfig {
    private final InsightsServiceConfiguration insightsServiceConfiguration;

    @Bean
    public SenderOptions<String, String> kafkaSenderOptions() {
        Map<String, Object> producerProps = producerOptions();

        return SenderOptions.<String, String>create(producerProps)
                // Non-blocking back-pressure
                .maxInFlight(1024);
    }

    @Bean
    public KafkaSender<String, String> reactiveKafkaSender(
            SenderOptions<String, String> senderOptions) {
        return KafkaSender.create(senderOptions);
    }

//    @Bean
    public KafkaProducer<String, String> kafkaProducer() {
        Map<String, Object> producerProps = producerOptions();
        return new KafkaProducer<>(producerProps);
    }

    private Map<String, Object> producerOptions() {
        Map<String, Object> producerProps = new HashMap<>();
        producerProps.put(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
                insightsServiceConfiguration.getKafkaProducerConfig().getBootstrapServers());
        producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

        if (Optional.of(insightsServiceConfiguration)
                .map(InsightsServiceConfiguration::getKafkaProducerConfig)
                .map(InsightsServiceConfiguration.KafkaProducerConfig::getIsManagedIdentity)
                .orElse(Boolean.FALSE)) {
            producerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            producerProps.put(SaslConfigs.SASL_MECHANISM, "OAUTHBEARER");
            producerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    "org.apache.kafka.common.security.oauthbearer.OAuthBearerLoginModule required;");
            producerProps.put(
                    SaslConfigs.SASL_LOGIN_CALLBACK_HANDLER_CLASS,
                    KafkaOAuth2AuthenticateCallbackHandler.class.getName());
        } else if (Optional.of(insightsServiceConfiguration)
                .map(InsightsServiceConfiguration::getKafkaProducerConfig)
                .map(InsightsServiceConfiguration.KafkaProducerConfig::getIsConnectionString)
                .orElse(Boolean.FALSE)) {
            producerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            producerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            producerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    insightsServiceConfiguration.getKafkaProducerConfig().getSaslJaasConfig());
        }

        if (Optional.of(insightsServiceConfiguration)
                .map(InsightsServiceConfiguration::getKafkaProducerConfig)
                .map(InsightsServiceConfiguration.KafkaProducerConfig::getRequestTimeoutMs)
                .isPresent()) {
            producerProps.put(
                    CommonClientConfigs.REQUEST_TIMEOUT_MS_CONFIG,
                    insightsServiceConfiguration.getKafkaProducerConfig().getRequestTimeoutMs());
        }

        if (Optional.of(insightsServiceConfiguration)
                .map(InsightsServiceConfiguration::getKafkaProducerConfig)
                .map(InsightsServiceConfiguration.KafkaProducerConfig::getDeliveryTimeoutMs)
                .isPresent()) {
            producerProps.put(
                    ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG,
                    insightsServiceConfiguration.getKafkaProducerConfig().getDeliveryTimeoutMs());
        }

        if (Optional.of(insightsServiceConfiguration)
                .map(InsightsServiceConfiguration::getKafkaProducerConfig)
                .map(InsightsServiceConfiguration.KafkaProducerConfig::getLingerMs)
                .isPresent()) {
            producerProps.put(
                    ProducerConfig.LINGER_MS_CONFIG,
                    insightsServiceConfiguration.getKafkaProducerConfig().getLingerMs());
        }

        if (Optional.of(insightsServiceConfiguration)
                .map(InsightsServiceConfiguration::getKafkaProducerConfig)
                .map(InsightsServiceConfiguration.KafkaProducerConfig::getBatchSize)
                .isPresent()) {
            producerProps.put(
                    ProducerConfig.BATCH_SIZE_CONFIG,
                    insightsServiceConfiguration.getKafkaProducerConfig().getBatchSize());
        }

        if (Optional.of(insightsServiceConfiguration)
                .map(InsightsServiceConfiguration::getKafkaProducerConfig)
                .map(InsightsServiceConfiguration.KafkaProducerConfig::getBufferMemory)
                .isPresent()) {
            producerProps.put(
                    ProducerConfig.BUFFER_MEMORY_CONFIG,
                    insightsServiceConfiguration.getKafkaProducerConfig().getBufferMemory());
        }
        if (Optional.of(insightsServiceConfiguration)
                .map(InsightsServiceConfiguration::getKafkaProducerConfig)
                .map(InsightsServiceConfiguration.KafkaProducerConfig::getMaxBlockMs)
                .isPresent()) {
            producerProps.put(
                    ProducerConfig.MAX_BLOCK_MS_CONFIG,
                    insightsServiceConfiguration.getKafkaProducerConfig().getMaxBlockMs());
        }
        return producerProps;
    }
}
