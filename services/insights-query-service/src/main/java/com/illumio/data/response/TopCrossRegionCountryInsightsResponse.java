package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.service.batch_widget.PivotExtractable;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.SOURCE_ZONE;
import static com.illumio.data.model.constants.Fields.SOURCE_REGION;
import static com.illumio.data.model.constants.Fields.DESTINATION_ZONE;
import static com.illumio.data.model.constants.Fields.DESTINATION_REGION;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;
import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;

@Data
@Component
public class TopCrossRegionCountryInsightsResponse implements ResponseBuilder<TopCrossRegionCountryInsightsResponse>, PivotExtractable{
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public TopCrossRegionCountryInsightsResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        TopCrossRegionCountryInsightsResponse response = new TopCrossRegionCountryInsightsResponse();

        //Set Widget Specific Metadata
        response.setWidgetId(requestMetadata.getWidgetId());
        response.setTitle("Top Cross Region Traffic");
        response.setColumns(Arrays.asList(SOURCE_ZONE.getFieldKey(), SOURCE_REGION.getFieldKey(),  DESTINATION_ZONE.getFieldKey(),  DESTINATION_REGION.getFieldKey(),  COUNT.getFieldKey(), PREVIOUS_COUNT.getFieldKey(), AGGREGATE_FIELD.getFieldKey()));
        response.setColumnTypes(Arrays.asList(SOURCE_ZONE.getFieldType(), SOURCE_REGION.getFieldType(),  DESTINATION_ZONE.getFieldType(),  DESTINATION_REGION.getFieldType(),  COUNT.getFieldType(), PREVIOUS_COUNT.getFieldType(), AGGREGATE_FIELD.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(SOURCE_ZONE.getFieldDisplayName(), SOURCE_REGION.getFieldDisplayName(),  DESTINATION_ZONE.getFieldDisplayName(),  DESTINATION_REGION.getFieldDisplayName(),  COUNT.getFieldDisplayName(), PREVIOUS_COUNT.getFieldDisplayName(), AGGREGATE_FIELD.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        List<List<Object>> data = new ArrayList<>();
        while (resultSetTable.next()) {
            String srcZone = (String) resultSetTable.getObject(SOURCE_ZONE.getTableColumnName());
            String srcRegion = (String) resultSetTable.getObject(SOURCE_REGION.getTableColumnName());
            String destZone = (String) resultSetTable.getObject(DESTINATION_ZONE.getTableColumnName());
            String destRegion = (String) resultSetTable.getObject(DESTINATION_REGION.getTableColumnName());
            Long count = getLongValue(resultSetTable, COUNT.getTableColumnName());
            Long prevCount = getLongValue(resultSetTable, PREVIOUS_COUNT.getTableColumnName());
            String aggrField = (String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName());

            List<Object> dataRow = new ArrayList<>();
            dataRow.add(srcZone);
            dataRow.add(srcRegion);
            dataRow.add(destZone);
            dataRow.add(destRegion);
            dataRow.add(count);
            dataRow.add(prevCount);
            dataRow.add(aggrField.toUpperCase());

            data.add(dataRow);
        }
        response.setData(data);

        return response;
    }

    @Override
    public TopCrossRegionCountryInsightsResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }

    /**
     * Converts the response data to CSV format for efficient consumption.
     *
     * @return A Map containing the CSV data and metadata
     */
    public Map<String, Object> compress() {
        Map<String, Object> result = new HashMap<>();
        StringBuilder csvData = new StringBuilder();

        // Add CSV header based on column display names
        csvData.append("SourceZone,SourceRegion,DestinationZone,DestinationRegion,Count,PreviousCount,AggregateField\n");

        // Add data rows
        if (data != null && !data.isEmpty()) {
            for (List<Object> row : data) {
                if (row.size() < 7) continue;

                String srcZone = (String) row.get(0);
                String srcRegion = (String) row.get(1);
                String destZone = (String) row.get(2);
                String destRegion = (String) row.get(3);
                Long count = (row.get(4) != null) ? ((Number)row.get(4)).longValue() : null;
                Long prevCount = (row.get(5) != null) ? ((Number)row.get(5)).longValue() : null;
                String aggField = (String) row.get(6);

                // Escape fields for CSV format
                String escapedSrcZone = escapeCsvField(srcZone);
                String escapedSrcRegion = escapeCsvField(srcRegion);
                String escapedDestZone = escapeCsvField(destZone);
                String escapedDestRegion = escapeCsvField(destRegion);
                String escapedAggField = escapeCsvField(aggField);

                // Add row to CSV
                csvData.append(escapedSrcZone).append(",")
                       .append(escapedSrcRegion).append(",")
                       .append(escapedDestZone).append(",")
                       .append(escapedDestRegion).append(",")
                       .append(count != null ? count : 0).append(",")
                       .append(prevCount != null ? prevCount : 0).append(",")
                       .append(escapedAggField).append("\n");
            }
        }

        // Add metadata
        if (currentTimeFrame != null) {
            result.put("currentTimeFrameStart", currentTimeFrame.getStartTime());
            result.put("currentTimeFrameEnd", currentTimeFrame.getEndTime());
        }

        if (comparisonTimeFrame != null) {
            result.put("comparisonTimeFrameStart", comparisonTimeFrame.getStartTime());
            result.put("comparisonTimeFrameEnd", comparisonTimeFrame.getEndTime());
        }

        if (pagination != null) {
            result.put("pageNumber", pagination.getPageNumber());
            result.put("totalPages", pagination.getTotalPages());
        }

        // Add CSV data and format
        result.put("csvData", csvData.toString());
        result.put("format", "csv");
        result.put("widgetId", widgetId);
        result.put("title", title);

        return result;
    }

    /**
     * Properly escapes a field value for CSV format according to RFC 4180:
     * - If the field contains commas, newlines, or double quotes, it should be enclosed in double quotes
     * - Double quotes within the field should be escaped by doubling them
     *
     * @param value The field value to escape
     * @return The properly escaped CSV field
     */
    private String escapeCsvField(String value) {
        if (value == null) {
            return "";
        }

        boolean needsQuoting = value.contains(",") || value.contains("\"") ||
                               value.contains("\n") || value.contains("\r");

        if (needsQuoting) {
            // Replace any double quotes with two double quotes
            String escapedValue = value.replace("\"", "\"\"");
            return "\"" + escapedValue + "\"";
        } else {
            return value;
        }
    }
}
