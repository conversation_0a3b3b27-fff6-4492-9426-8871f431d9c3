package com.illumio.data.service;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.Set;

@Slf4j
@Getter
@Component
public class LlmServiceInfo {

    private final Set<String> allLLMs = new HashSet<>();
    private String filePath;

    public LlmServiceInfo(InsightsServiceConfiguration config) {
        this.filePath = config.getLlmServiceConfig().getLlmServiceFilePath();
        loadLLMs();
    }

    private void loadLLMs() {
        if (filePath.startsWith("classpath:")) {
            try {
                filePath = ResourceUtils.getFile(filePath).getPath();
            } catch (FileNotFoundException e) {
                throw new RuntimeException("Error loading LLM CSV file. Please verify the path exists.", e);
            }
        }

        try (BufferedReader reader = Files.newBufferedReader(Paths.get(filePath));
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader())) {

            for (CSVRecord record : csvParser) {
                try {
                    String llm = record.get(0).trim();
                    if (!llm.isEmpty()) {
                        allLLMs.add(llm);
                    }
                } catch (Exception e) {
                    log.warn("Malformed row in LLM CSV file. Check line: {}", record);
                }
            }

        } catch (IOException e) {
            throw new RuntimeException("Failed to load LLM CSV file.", e);
        }
    }
}