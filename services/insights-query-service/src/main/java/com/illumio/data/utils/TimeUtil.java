package com.illumio.data.utils;

import com.illumio.data.model.TimeFrame;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class TimeUtil {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ISO_DATE_TIME;

    //updates the timeframe by flooring to 00:00:00Z timestamp and HH:00:00Z timestamp for hourly
    public static TimeFrame getUpdatedTimeFrame(TimeFrame timeFrame, String tableName){
        LocalDateTime startTime = LocalDateTime.parse(timeFrame.getStartTime(), DATE_TIME_FORMATTER);
        LocalDateTime endTime = LocalDateTime.parse(timeFrame.getEndTime(), DATE_TIME_FORMATTER);

        if(tableName.contains("_Hourly")){
            startTime = startTime.withMinute(0).withSecond(0).withNano(0);
            endTime = endTime.withMinute(0).withSecond(0).withNano(0);
        } else {
            startTime = startTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
            endTime = endTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
        }

        return TimeFrame.builder()
                .startTime(startTime.format(DATE_TIME_FORMATTER))
                .endTime(endTime.format(DATE_TIME_FORMATTER))
                .build();
    }
}