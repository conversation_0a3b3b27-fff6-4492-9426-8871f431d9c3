package com.illumio.data.service.batch_widget;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.widget.response.CategoryResponse;
import com.illumio.data.model.widget.result.DirectionalResults;
import com.illumio.data.model.widget.result.CategoryResults;
import com.illumio.data.response.WidgetBatchResponse.WidgetResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * Service responsible for compressing widget results in a generic way.
 * Handles nested structures of categories and directions using a recursive approach.
 */
@Slf4j
@Service
public class WidgetCompressionService {
    
    /**
     * Compresses a widget result if it's successful and contains data that is compressible.
     * For error results, or results where the data cannot be compressed, the original result is returned.
     * 
     * This method recursively handles nested structures including:
     * - Direct ResponseBuilder instances
     * - DirectionalResults with inbound/outbound data
     * - CategoryResults or Maps with category data
     * - Nested combinations of the above
     * 
     * @param result The WidgetResult to potentially compress
     * @return A new WidgetResult with compressed data, or the original if compression is not applicable
     */
    public WidgetResult compressWidgetResult(WidgetResult result) {
        if (!"success".equals(result.getStatus()) || result.getData() == null) {
            return result;
        }
        
        try {
            Object compressedData = compressObject(result.getData());
            
            return WidgetResult.builder()
                    .widgetId(result.getWidgetId())
                    .status(result.getStatus())
                    .data(compressedData)
                    .build();
        } catch (Exception e) {
            log.warn("Failed to compress widget result for ID {}: {}", result.getWidgetId(), e.getMessage());
            // Return the original result if compression fails
            return result;
        }
    }
    
    /**
     * Recursively compresses an object based on its type.
     * 
     * @param obj The object to compress
     * @return The compressed object, or the original if not compressible
     */
    private Object compressObject(Object obj) {
        if (obj == null) {
            return null;
        }
        
        // Direct ResponseBuilder handling
        if (obj instanceof ResponseBuilder) {
            ResponseBuilder<?> responseBuilder = (ResponseBuilder<?>) obj;
            return responseBuilder.compress();
        } 
        // DirectionalResults handling (inbound/outbound)
        else if (obj instanceof DirectionalResults) {
            DirectionalResults dirResults = (DirectionalResults) obj;
            Map<String, Object> compressedResults = new HashMap<>();
            
            // Recursively compress both inbound and outbound data
            compressedResults.put("inbound", compressObject(dirResults.getInbound()));
            compressedResults.put("outbound", compressObject(dirResults.getOutbound()));
            
            return compressedResults;
        }
        // CategoryResults handling
        else if (obj instanceof CategoryResults) {
            CategoryResults catResults = (CategoryResults) obj;
            return compressMap(catResults.getCategories());
        }
        // CategoryResults handling
        else if (obj instanceof CategoryResponse) {
            CategoryResponse categoryResponse = (CategoryResponse) obj;
            return compressObject(categoryResponse.getData());
        }
        // Generic Map handling (including nested maps of categories and directions)
        else if (obj instanceof Map) {
            return compressMap((Map<?, ?>) obj);
        }
        
        // Return the original object if not compressible
        return obj;
    }
    
    /**
     * Compresses each value in a map recursively.
     * 
     * @param map The map whose values need to be compressed
     * @return A new map with compressed values
     */
    private Map<String, Object> compressMap(Map<?, ?> map) {
        if (map == null) {
            return null;
        }
        
        Map<String, Object> compressedMap = new HashMap<>();
        
        for (Map.Entry<?, ?> entry : map.entrySet()) {
            String key = entry.getKey().toString();
            Object value = entry.getValue();
            
            // Recursively compress the value
            compressedMap.put(key, compressObject(value));
        }
        
        return compressedMap;
    }
} 