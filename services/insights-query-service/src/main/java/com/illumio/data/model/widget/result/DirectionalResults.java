package com.illumio.data.model.widget.result;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

/**
 * Helper class for combining inbound and outbound results.
 */
@Setter
@Getter
public class DirectionalResults {
    private Object inbound;
    private Object outbound;

    @JsonIgnore
    public boolean isEmpty() {
        return inbound == null && outbound == null;
    }
} 