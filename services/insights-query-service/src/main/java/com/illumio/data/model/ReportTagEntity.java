package com.illumio.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;
import java.util.UUID;

@Table("report_tag")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ReportTagEntity {
    
    @Id
    @Column("tag_id")
    private UUID tagId;
    
    @Column("display_name")
    private String displayName;
    
    @Column("description")
    private String description;
    
    @Column("system_defined")
    private Boolean systemDefined;
    
    @Column("tenant_id")
    private UUID tenantId;
    
    @Column("user_id")
    private UUID userId;
    
    @Column("created_time")
    private LocalDateTime createdTime;
    
    @Column("updated_time")
    private LocalDateTime updatedTime;
    
    @Column("deleted")
    private Boolean deleted;
    
    @Column("tag_category_id")
    private Integer tagCategoryId;
}
