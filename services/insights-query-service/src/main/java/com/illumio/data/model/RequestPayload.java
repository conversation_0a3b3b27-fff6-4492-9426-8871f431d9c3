package com.illumio.data.model;

import com.illumio.data.service.RiskyServiceInfo;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.illumio.data.model.constants.Fields.PORT;
import static com.illumio.data.model.constants.Fields.PROTOCOL;
import static com.illumio.data.model.constants.Fields.SERVICE;

@Data
@Builder
public class RequestPayload {
    private TimeFrame currentTimeFrame;
    private TimeFrame comparisonTimeFrame;
    private List<Filters> filters;
    private List<SortByFields> sortByFields;
    private Pagination pagination;
    private Boolean quarantine;

    //Currently, we have a static file which has a mapping on Service to Port-Proto
    //If we get service as a filter, we need to translate it to db readable filters
    //There will always be one service selection in the ui
    public void replaceServiceFilters(Map<String, List<RiskyServiceInfo.ServiceInfo>> serviceToServiceInfoMap) {
        List<Filters> newFilters = new ArrayList<>();

        if (filters != null) {
            for (Filters filter : filters) {
                if (filter.getCategoryName().equals(SERVICE.getFieldKey())) {
                    newFilters.add(Filters.builder()
                            .categoryType(PORT.getFieldType())
                            .categoryName(PORT.getFieldKey())
                            .categoryValue(serviceToServiceInfoMap.get(filter.getCategoryValue().get(0)).stream()
                                    .map(RiskyServiceInfo.ServiceInfo::getPort)
                                    .collect(Collectors.toList()))
                            .build());
                    newFilters.add(Filters.builder()
                            .categoryType(PROTOCOL.getFieldType())
                            .categoryName(PROTOCOL.getFieldKey())
                            .categoryValue(serviceToServiceInfoMap.get(filter.getCategoryValue().get(0)).stream()
                                    .map(serviceInfo -> serviceInfo.getProtocol().toLowerCase())
                                    .collect(Collectors.toList()))
                            .build());
                } else if (filter.getCategoryName().equals(PROTOCOL.getFieldKey())) {
                    newFilters.add(Filters.builder()
                            .categoryName(PROTOCOL.getFieldType())
                            .categoryName(PROTOCOL.getFieldKey())
                            .categoryValue(filter.getCategoryValue().stream()
                                    .map(val -> val.toString().toLowerCase())
                                    .collect(Collectors.toList()))
                            .build());
                }
                else {
                    newFilters.add(filter);
                }
            }
        }
        this.filters = newFilters;
    }
}

