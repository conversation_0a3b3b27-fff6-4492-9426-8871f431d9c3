package com.illumio.data.repository.iva;

import com.illumio.data.model.ReportSummaryEntity;

import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Repository
public interface ReportSummaryRepository extends ReactiveCrudRepository<ReportSummaryEntity, Integer> {

    /**
     * Find the most recent automatic daily report for a tenant and persona
     * Only returns reports with summary period < 2 days
     */
    @Query("SELECT * FROM report_summary WHERE tenant_id = :tenantId " +
           "AND persona = :persona " +
           "AND (current_end_time - current_start_time) < INTERVAL '2 days' " +
           "ORDER BY created_at DESC " +
           "LIMIT 1")
    Mono<ReportSummaryEntity> findMostRecentDailyReportByTenantAndPersona(
            UUID tenantId,
            String persona);

    /**
     * Find the most recent automatic weekly report for a tenant and persona
     * Only returns reports with summary period between 6 and 8 days (inclusive)
     */
    @Query("SELECT * FROM report_summary WHERE tenant_id = :tenantId " +
           "AND persona = :persona " +
           "AND (current_end_time - current_start_time) >= INTERVAL '6 days' " +
           "AND (current_end_time - current_start_time) <= INTERVAL '8 days' " +
           "ORDER BY created_at DESC " +
           "LIMIT 1")
    Mono<ReportSummaryEntity> findMostRecentWeeklyReportByTenantAndPersona(
            UUID tenantId,
            String persona);

    /**
     * Find a specific report by ID and tenant for security
     */
    Mono<ReportSummaryEntity> findByReportIdAndTenantId(Integer reportId, UUID tenantId);
}
