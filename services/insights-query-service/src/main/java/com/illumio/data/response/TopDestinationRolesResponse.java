package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.service.batch_widget.PivotExtractable;
import com.illumio.data.service.batch_widget.ResponseCompressor;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.DESTINATION_ROLE;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;

@Data
@NoArgsConstructor
@Component
public class TopDestinationRolesResponse implements ResponseBuilder<TopDestinationRolesResponse>, PivotExtractable {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public TopDestinationRolesResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        TopDestinationRolesResponse response = this;
        RequestPayload payload = requestContext.getRequestPayload().get();

        //Set Widget Specific Metadata
        response.setWidgetId(requestMetadata.getWidgetId());
        response.setTitle("Top Destination Roles");
        response.setColumns(Arrays.asList(DESTINATION_ROLE.getFieldKey(), COUNT.getFieldKey(), PREVIOUS_COUNT.getFieldKey(), AGGREGATE_FIELD.getFieldKey()));
        response.setColumnTypes(Arrays.asList(DESTINATION_ROLE.getFieldType(), COUNT.getFieldType(), PREVIOUS_COUNT.getFieldType(), AGGREGATE_FIELD.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(DESTINATION_ROLE.getFieldDisplayName(), COUNT.getFieldDisplayName(), PREVIOUS_COUNT.getFieldDisplayName(), AGGREGATE_FIELD.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        List<List<Object>> data = new ArrayList<>();
        while (resultSetTable.next()) {
            String destRole = (String) resultSetTable.getObject(DESTINATION_ROLE.getTableColumnName());
            Long count = getLongValue(resultSetTable, COUNT.getTableColumnName());
            Long prevCount = getLongValue(resultSetTable, PREVIOUS_COUNT.getTableColumnName());
            String aggrField = (String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName());

            List<Object> dataRow = new ArrayList<>();
            dataRow.add(destRole);
            dataRow.add(count);
            dataRow.add(prevCount);
            dataRow.add(aggrField);

            data.add(dataRow);
        }
        response.setData(data);

        return response;
    }

    @Override
    public TopDestinationRolesResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }
    
    /**
     * Converts the response data to CSV format for efficient consumption.
     * Uses ResponseCompressor utility for maximum robustness against structural changes.
     * 
     * @return A Map containing the CSV data and metadata
     */
    public Map<String, Object> compress() {
        return ResponseCompressor.compressToCSV(
                widgetId,
                title,
                columns,
                columnDisplayNames,
                data,
                currentTimeFrame,
                comparisonTimeFrame,
                pagination
        );
    }
}
