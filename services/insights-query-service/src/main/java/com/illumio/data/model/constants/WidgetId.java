package com.illumio.data.model.constants;

import java.util.Map;

public final class WidgetId {

    private WidgetId() {
        // prevent instantiation
    }

    public static final String DESTINATION_ROLE_LEVEL_TRAFFIC = "4321";
    public static final String RISKY_SERVICE_TRAFFIC = "1234";
    public static final String ZONE_LEVEL_TRAFFIC = "5678";
    public static final String TOP_DESTINATION_ROLES = "9012";
    public static final String TOP_WORKLOADS = "3456";
    public static final String RESOURCE_INSIGHTS = "0";
    public static final String TOP_MALICIOUS_IPS = "1122";
    public static final String THREAT_MAP = "2233";
    public static final String TOP_CATEGORY_WITH_MALICIOUS_IP = "3344";
    public static final String TOP_ROLES = "4455";
    public static final String TOP_SERVICES = "5566";
    public static final String EXTERNAL_DESTINATION_CATEGORY = "3399";
    public static final String EXTERNAL_SERVICE_TRANSFER = "4499";
    public static final String TOP_SOURCE_ROLE_TRANSFER = "5599";
    public static final String EXTERNAL_GEO_TRANSFER = "6699";
    public static final String TOP_SOURCE_TRANSFER = "7799";
    public static final String LLM_IN_USE = "1881";
    public static final String TOP_CATEGORY_WITH_LLM = "1991";
    public static final String TOP_SOURCES_WITH_LLM = "1771";
    public static final String THIRD_PARTY_DEPENDENCY_INBOUND = "9991";
    public static final String THIRD_PARTY_DEPENDENCY_OUTBOUND = "9992";
    public static final String RISKY_SERVICE_TRAFFIC_INSIGHTS_HUB = "9876";
    public static final String TOP_DESTINATION_ROLES_INSIGHTS_HUB = "8765";
    public static final String TOP_SOURCE_TRANSFER_INSIGHTS_HUB = "7654";
    public static final String TOP_MALICIOUS_IPS_INSIGHTS_HUB = "6543";
    public static final String TOP_CROSS_REGION_TRAFFIC = "5432";
    public static final String TOP_REGION_TO_COUNTRY_TRAFFIC = "3210";
    public static final String UNENCRYPTED_SERVICES = "9993";
    public static final String DORA_TOP_ICT = "9995";
    public static final String DORA_CRITICAL_ICT = "9994";
    public static final String RISKY_TRAFFIC_BY_ROLES_RESOURCE_INSIGHTS = "1661";
    public static final String MALICIOUS_IP_TRAFFIC_RESOURCE_INSIGHTS = "1551";
    public static final String EXTERNAL_DATA_TRANSFER_RESOURCE_INSIGHTS = "1441";
    public static final String RISKY_SERVICES_TRAFFIC_RESOURCE_INSIGHTS = "1331";
    public static final String IP_TOP_DESTINATION_RESOURCES = "ip-top-destination-resources";
    public static final String IP_TOP_SOURCE_RESOURCES = "ip-top-source-resources";
    public static final String IP_TOP_DESTINATION_ROLES = "ip-top-destination-roles";
    public static final String IP_TOP_SOURCE_ROLES = "ip-top-source-roles";
    public static final String IP_DATA_TRANSFER_INBOUND = "ip-data-transfer-inbound";
    public static final String IP_DATA_TRANSFER_OUTBOUND = "ip-data-transfer-outbound";
    public static final String IP_TOP_RESOURCES = "ip-top-resources";
    public static final String IP_TOP_ROLES = "ip-top-roles";
    public static final String IP_DATA_TRANSFER = "ip-data-transfer";
    public static final String GET_CSP_REGIONS = "get-csp-regions";
    public static final String TRAFFIC_ACTIVITY_ACROSS_COUNTRIES_CROSS_COUNTRY = "traffic-activity-across-countries";
    public static final String TOP_CROSS_REGION_TRAFFIC_COUNTRY_INSIGHTS = "top-cross-region-traffic";
    public static final String GET_RISKY_SERVICE_NAMES = "get-risky-service-names";
    public static final String RISKY_TRAFFIC_TOP_SUBSCRIPTIONS = "top-subs-in-region";

    private static final Map<String, Map<TrafficDirection, String>> widgetResolutionMap = Map.of(
            IP_TOP_RESOURCES, Map.of(
                    TrafficDirection.INBOUND, IP_TOP_DESTINATION_RESOURCES,
                    TrafficDirection.OUTBOUND, IP_TOP_SOURCE_RESOURCES
            ),
            IP_TOP_ROLES, Map.of(
                    TrafficDirection.INBOUND, IP_TOP_DESTINATION_ROLES,
                    TrafficDirection.OUTBOUND, IP_TOP_SOURCE_ROLES
            ),
            IP_DATA_TRANSFER, Map.of(
                    TrafficDirection.INBOUND, IP_DATA_TRANSFER_INBOUND,
                    TrafficDirection.OUTBOUND, IP_DATA_TRANSFER_OUTBOUND
            )
    );

    public static String resolve(String widgetId, TrafficDirection direction) {
        // Get the directional map if it exists,
        // Otherwise, fallback to original if no mapping
        return widgetResolutionMap
                .getOrDefault(widgetId, Map.of())
                .getOrDefault(direction, widgetId);
    }
}

