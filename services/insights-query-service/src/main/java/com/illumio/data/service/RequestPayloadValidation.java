package com.illumio.data.service;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.model.Filters;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.RequestMetadata;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.illumio.data.model.constants.Fields.AGGREGATED_BYTES;
import static com.illumio.data.model.constants.Fields.AGGREGATED_FLOWS;
import static com.illumio.data.model.constants.Fields.PREVIOUS_AGGREGATED_BYTES;
import static com.illumio.data.model.constants.Fields.PREVIOUS_AGGREGATED_FLOWS;
import static com.illumio.data.model.constants.Fields.SERVICE;

@Service
@AllArgsConstructor
public class RequestPayloadValidation {
    private final RiskyServiceInfo riskyServiceInfo;
    private final InsightsServiceConfiguration config;

    public String validateRequest(RequestPayload payload, Metadata requestMetadata) {
        // Validate Filter Fields
        if(payload.getFilters() != null && !payload.getFilters().isEmpty()) {

            if(payload.getFilters().stream().anyMatch(Objects::isNull)) {
                return "Invalid filter value, received 'null' ";
            }

            for(Filters filter : payload.getFilters()) {
                // Validate filter name
                if(!Fields.getFieldKeySet().contains(filter.getCategoryName())) {
                    return String.format("Invalid filter value. Category name '%s' is not a valid field." +
                            "Valid field types are: %s", filter.getCategoryName(), Arrays.toString(Fields.getFieldKeySet().toArray()));
                }
                // Validate filter values
                if(filter.getCategoryValue() == null || filter.getCategoryValue().isEmpty() || filter.getCategoryValue().stream().anyMatch(val -> val == null || (val instanceof String && ((String) val).trim().isEmpty()))) {
                    return "Invalid filter value. Category value must have non-null non-empty values.";
                }
                // Validate service if exists
                if(filter.getCategoryName().equals(SERVICE.getFieldKey())) {
                    if(filter.getCategoryValue().size() != 1) {
                        return "Invalid Service Filter. There can only be one service per request";
                    }
                    if(!riskyServiceInfo.getServicetoServiceInfoMap().containsKey(filter.getCategoryValue().get(0))) {
                        return String.format("Invalid Service Filter. Service value '%s' is invalid", filter.getCategoryValue().get(0));
                    }
                }
            }
        }

        // Validate Sort By Fields
        List<String> validSortByFields = new ArrayList<>();
        validSortByFields.addAll(requestMetadata.getGroupByFields().stream().map(Fields::getFieldKey).toList());
        validSortByFields.addAll(Arrays.asList(AGGREGATED_BYTES.getFieldKey(), AGGREGATED_FLOWS.getFieldKey(), PREVIOUS_AGGREGATED_BYTES.getFieldKey(), PREVIOUS_AGGREGATED_FLOWS.getFieldKey()));

        if(payload.getSortByFields() != null && !payload.getSortByFields().isEmpty()) {
            for(SortByFields sortByField : payload.getSortByFields()) {
                if(!validSortByFields.contains(sortByField.getField())) {
                    return String.format("Invalid sort value. Category name '%s' is not a valid field." +
                            "Valid field types are: %s", sortByField.getField(), Arrays.toString(validSortByFields.toArray()));
                }
                if(!RequestMetadata.getValidSortOrder().contains(sortByField.getOrder())) {
                    return String.format("Invalid sort order. '%s' is not a valid sort order." +
                            "Valid sort orders are: %s", sortByField.getOrder(), Arrays.toString(RequestMetadata.getValidSortOrder().toArray()));
                }
            }
        }

        if(payload.getSortByFields() == null || payload.getSortByFields().isEmpty()) {
            return "Failed to set default sort order. Sort order is null or empty";
        }

        // Validate pagination fields
        if (payload.getPagination() == null) {
            return "Expected pagination data, got null";
        }
        if (payload.getPagination().getRowLimit() < -1 || payload.getPagination().getRowLimit() > config.getPaginationConfig().getMaxPageSize()) {
            return String.format("Invalid pagination.rowLimit value %d " +
                    "Valid range is 1 to %d", payload.getPagination().getRowLimit(),config.getPaginationConfig().getMaxPageSize());
        }
        if (payload.getPagination().getPageNumber() < 1) {
            return String.format("Invalid pagination.pageNumber value %d. Value should be >= 1", payload.getPagination().getPageNumber());
        }

        return "";
    }
}