package com.illumio.data.configuration;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;


import java.util.Map;

@Configuration
@ConfigurationProperties(prefix = "service-auth")
@Getter
@Setter
@Slf4j
public class ServiceKeyConfig {
    // serviceName -> authKey
    private Map<String, String> keys;

    public boolean isValidKey(String serviceName, String providedKey) {
        log.info("Service keys available:");
        for (Map.Entry<String, String> entry : keys.entrySet()) {
            log.info("Key: {}, Value: {}", entry.getKey(), entry.getValue());
        }
        return providedKey != null && providedKey.equals(keys.get(serviceName));
    }
}
