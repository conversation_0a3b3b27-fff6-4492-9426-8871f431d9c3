package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.TrafficDirection;
import com.illumio.data.model.constants.WidgetId;
import com.illumio.data.utils.ApplicationUtils;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.RESOURCE_CATEGORY;
import static com.illumio.data.model.constants.Fields.RESOURCE_ID;
import static com.illumio.data.model.constants.Fields.RESOURCE_NAME;
import static com.illumio.data.model.constants.Fields.RESOURCE_TYPE;
import static com.illumio.data.model.constants.Fields.TIME_SERIES;

@Data
@NoArgsConstructor
@Component
public class IpTopResourcesResponse implements ResponseBuilder<IpTopResourcesResponse> {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public IpTopResourcesResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        if (requestContext.getRequestPayload().isEmpty()) {
            throw new IllegalArgumentException("Request Payload is empty");
        }
        RequestPayload payload = requestContext.getRequestPayload().get();

        IpTopResourcesResponse response = this;

        TrafficDirection direction = ApplicationUtils.getTrafficDirection(payload.getFilters());
        Fields resourceIdField = RESOURCE_ID.resolve(direction);
        Fields resourceNameField = RESOURCE_NAME.resolve(direction);
        Fields resourceCategoryField = RESOURCE_CATEGORY.resolve(direction);
        Fields resourceTypeField = RESOURCE_TYPE.resolve(direction);

        //Set Widget Specific Metadata
        response.setWidgetId(WidgetId.IP_TOP_RESOURCES);
        response.setTitle("Top 10 Resources Communicating With IP");
        response.setColumns(Arrays.asList(
                resourceIdField.getFieldKey(),
                resourceNameField.getFieldKey(),
                resourceCategoryField.getFieldKey(),
                resourceTypeField.getFieldKey(),
                AGGREGATE_FIELD.getFieldKey(),
                TIME_SERIES.getFieldKey()));
        response.setColumnTypes(Arrays.asList(
                resourceIdField.getFieldType(),
                resourceNameField.getFieldType(),
                resourceCategoryField.getFieldType(),
                resourceTypeField.getFieldType(),
                AGGREGATE_FIELD.getFieldType(),
                TIME_SERIES.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(
                resourceIdField.getFieldDisplayName(),
                resourceNameField.getFieldDisplayName(),
                resourceCategoryField.getFieldDisplayName(),
                resourceTypeField.getFieldDisplayName(),
                AGGREGATE_FIELD.getFieldDisplayName(),
                TIME_SERIES.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        Map<String, Map<String, List<List<Object>>>> groupedData = new HashMap<>();
        while (resultSetTable.next()) {
            String resourceId = (String) resultSetTable.getObject(resourceIdField.getTableColumnName());
            String resourceName = (String) resultSetTable.getObject(resourceNameField.getTableColumnName());
            String resourceCategory = (String) resultSetTable.getObject(resourceCategoryField.getTableColumnName());
            String resourceType = (String) resultSetTable.getObject(resourceTypeField.getTableColumnName());
            String aggregateField = (String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName());
            String timeSeries = (String) resultSetTable.getObject(TIME_SERIES.getTableColumnName());
            Long count = getLongValue(resultSetTable, COUNT.getTableColumnName());

            String compositeKey = resourceId + "|" + resourceName + "|" + resourceCategory+ "|" + resourceType;

            groupedData
                    .computeIfAbsent(compositeKey, k -> new HashMap<>())
                    .computeIfAbsent(aggregateField, k -> new ArrayList<>())
                    .add(Arrays.asList(timeSeries, count));
        }

        List<List<Object>> formattedData = groupedData.entrySet().stream()
                .flatMap(outer -> {
                    // Split the composite key back into its resource-level parts.
                    String[] parts = outer.getKey().split("\\|", -1);
                    String resId = parts[0];
                    String resName = parts[1];
                    String resCategory = parts[2];
                    String resType = parts[3];

                    return outer.getValue().entrySet().stream()
                            .map(inner -> Arrays.asList(
                                    resId,
                                    resName,
                                    resCategory,
                                    resType,
                                    inner.getKey(),
                                    inner.getValue()
                            ));
                })
                .collect(Collectors.toList());

        response.setData(formattedData);

        return response;
    }

    @Override
    public IpTopResourcesResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : 0L;
    }
}
