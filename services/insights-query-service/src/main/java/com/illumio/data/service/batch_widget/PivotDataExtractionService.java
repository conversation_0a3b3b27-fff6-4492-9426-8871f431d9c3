package com.illumio.data.service.batch_widget;

import com.illumio.data.response.WidgetBatchResponse.WidgetResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service responsible for extracting pivot data from widget results.
 * Uses interface-based approach for better performance and type safety.
 */
@Slf4j
@Service
public class PivotDataExtractionService {

    /**
     * Extract top K values from widget result based on specified field.
     */
    public List<String> extractTopKValues(WidgetResult result, String field, Integer topKSize) {
        try {
            if (result == null || !"success".equals(result.getStatus()) || result.getData() == null) {
                log.warn("Cannot extract top K values from unsuccessful result");
                return Collections.emptyList();
            }

            Object data = result.getData();
            List<String> extractedValues = extractValuesFromResponseObject(data, field);
            
            int limit = topKSize != null ? topKSize : 25;
            return extractedValues.stream()
                    .limit(limit)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("Error extracting top K values from pivot widget result", e);
            return Collections.emptyList();
        }
    }

    /**
     * Extract values from response object using interface-based approach.
     */
    private List<String> extractValuesFromResponseObject(Object responseObject, String field) {
        if (responseObject == null) {
            return Collections.emptyList();
        }

        try {
            // First try interface-based extraction (preferred approach)
            if (responseObject instanceof PivotExtractable) {
                return extractFromPivotExtractable((PivotExtractable) responseObject, field);
            }

            // Fall back to generic extraction for unsupported response types
            log.info("Response type {} does not implement PivotExtractable, falling back to generic extraction",
                    responseObject.getClass().getSimpleName());
            return extractValuesFromGenericData(responseObject, field);

        } catch (Exception e) {
            log.error("Error extracting values from response object of type: {}", 
                    responseObject.getClass().getSimpleName(), e);
            return Collections.emptyList();
        }
    }

    /**
     * Extract values from PivotExtractable response (preferred method).
     */
    private List<String> extractFromPivotExtractable(PivotExtractable extractable, String field) {
        try {
            List<String> columns = extractable.getColumns();
            List<List<Object>> data = extractable.getData();
            
            if (columns == null || data == null) {
                log.warn("PivotExtractable {} has null columns or data", extractable.getResponseTypeName());
                return Collections.emptyList();
            }
            
            log.info("Extracting field '{}' from {} with columns: {}", 
                    field, extractable.getResponseTypeName(), columns);
            
            return extractFromTableStructure(columns, data, field);
            
        } catch (Exception e) {
            log.error("Error extracting from PivotExtractable {}", extractable.getResponseTypeName(), e);
            return Collections.emptyList();
        }
    }

    /**
     * Extract values from generic data structure (fallback method).
     */
    @SuppressWarnings("unchecked")
    private List<String> extractValuesFromGenericData(Object data, String field) {
        if (data == null) {
            return Collections.emptyList();
        }

        try {
            // Handle different data structures
            if (data instanceof Map) {
                return extractFromMap((Map<String, Object>) data, field);
            } else if (data instanceof List) {
                return extractFromList((List<Object>) data, field);
            } else {
                log.warn("Unsupported data type for extraction: {}", data.getClass().getSimpleName());
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("Error extracting values from generic data structure", e);
            return Collections.emptyList();
        }
    }

    /**
     * Extract values from Map data structure.
     */
    @SuppressWarnings("unchecked")
    private List<String> extractFromMap(Map<String, Object> dataMap, String field) {
        List<String> values = new ArrayList<>();

        // Look for data array in common response structures
        Object dataArray = dataMap.get("data");
        if (dataArray instanceof List) {
            values.addAll(extractFromList((List<Object>) dataArray, field));
        }

        // Look for columns and data structure (common in table responses)
        Object columns = dataMap.get("columns");
        if (columns instanceof List && dataArray instanceof List) {
            values.addAll(extractFromTableStructure((List<String>) columns, (List<List<Object>>) dataArray, field));
        }

        // Look for direct field values
        if (dataMap.containsKey(field)) {
            Object fieldValue = dataMap.get(field);
            if (fieldValue instanceof List) {
                ((List<Object>) fieldValue).forEach(val -> {
                    if (val != null) {
                        values.add(val.toString());
                    }
                });
            }
        }

        return values;
    }

    /**
     * Extract values from List data structure.
     */
    @SuppressWarnings("unchecked")
    private List<String> extractFromList(List<Object> dataList, String field) {
        List<String> values = new ArrayList<>();

        for (Object item : dataList) {
            if (item instanceof Map) {
                Map<String, Object> itemMap = (Map<String, Object>) item;
                if (itemMap.containsKey(field)) {
                    Object fieldValue = itemMap.get(field);
                    if (fieldValue != null) {
                        values.add(fieldValue.toString());
                    }
                }
            } else if (item instanceof List) {
                // Handle nested lists
                values.addAll(extractFromList((List<Object>) item, field));
            }
        }

        return values;
    }

    /**
     * Extract values from table structure (columns + data rows).
     */
    private List<String> extractFromTableStructure(List<String> columns, List<List<Object>> dataRows, String field) {
        List<String> values = new ArrayList<>();

        // Find the column index for the specified field
        int fieldIndex = -1;
        for (int i = 0; i < columns.size(); i++) {
            if (field.equalsIgnoreCase(columns.get(i))) {
                fieldIndex = i;
                break;
            }
        }

        if (fieldIndex == -1) {
            log.info("Field '{}' not found in columns: {}", field, columns);
            return values;
        }

        log.info("Found field '{}' at column index {} in columns: {}", field, fieldIndex, columns);

        // Extract values from the data rows
        for (List<Object> row : dataRows) {
            if (row.size() > fieldIndex && row.get(fieldIndex) != null) {
                String value = row.get(fieldIndex).toString();
                values.add(value);
                log.debug("Extracted value: {}", value);
            }
        }

        log.info("Extracted {} values for field '{}': {}", values.size(), field, 
                values.stream().limit(5).collect(Collectors.toList()));
        return values;
    }

    /**
     * Sort and deduplicate extracted values.
     */
    public List<String> sortAndDeduplicate(List<String> values) {
        List<String> result = values.stream()
                .filter(Objects::nonNull)
                .filter(s -> !s.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());
        
        log.info("After deduplication and sorting: {} unique values", result.size());
        return result;
    }
} 