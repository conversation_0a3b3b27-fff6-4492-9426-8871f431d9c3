package com.illumio.data.service;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.configuration.InsightsServiceConfiguration.KustoConfiguration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.function.Supplier;

@Slf4j
@Service
@RequiredArgsConstructor
public class KustoRetryService {

    private final InsightsServiceConfiguration insightsServiceConfiguration;

    /**
     * Execute a Kusto query with retries using the specified KustoConfiguration.
     *
     * @param kustoQueryExecutor The function to execute the Kusto query
     * @param kustoConfig The KustoConfiguration to use for retry settings
     * @return A Mono containing the response or an error after retries
     */
    public Mono<ResponseEntity<Object>> executeWithRetries(
            Supplier<Mono<ResponseEntity<Object>>> kustoQueryExecutor,
            KustoConfiguration kustoConfig) {
        
        Integer maxRetries = kustoConfig.getQueryMaxRetries();
        Duration initialBackoff = Duration.ofSeconds(kustoConfig.getQueryInitialBackoffSeconds());

        return Mono.defer(kustoQueryExecutor)
            .retryWhen(Retry.backoff(maxRetries, initialBackoff)
                .filter(this::isRetryableException)
                .doBeforeRetry(retrySignal -> log.warn("Retrying Kusto query, attempt #{}. Cause: {}",
                                                        retrySignal.totalRetries() + 1,
                                                        retrySignal.failure().getMessage()))
                .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                    log.error("Kusto query failed after {} retries. Last error: {}",
                                maxRetries, retrySignal.failure().getMessage(), retrySignal.failure());
                    // Propagate the original exception after retries are exhausted
                    return retrySignal.failure();
                }));
    }

    private boolean isRetryableException(Throwable throwable) {
        return !(throwable instanceof IllegalArgumentException);
    }
} 