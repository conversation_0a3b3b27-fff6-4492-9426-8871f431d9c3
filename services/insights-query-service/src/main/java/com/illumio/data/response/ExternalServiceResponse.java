package com.illumio.data.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.SeverityLevel;
import com.illumio.data.service.RiskyServiceInfo;
import com.illumio.data.service.batch_widget.PivotExtractable;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.illumio.data.model.constants.Fields.SERVICE;
import static com.illumio.data.model.constants.Fields.PORT;
import static com.illumio.data.model.constants.Fields.PROTOCOL;
import static com.illumio.data.model.constants.Fields.PREVIOUS_AGGREGATED_BYTES;
import static com.illumio.data.model.constants.Fields.PREVIOUS_AGGREGATED_FLOWS;
import static com.illumio.data.model.constants.Fields.AGGREGATED_FLOWS;
import static com.illumio.data.model.constants.Fields.AGGREGATED_BYTES;
import static com.illumio.data.model.constants.Fields.SEVERITY;
import static com.illumio.data.model.constants.Fields.TOTAL_ROWS;

@JsonIgnoreProperties({"riskyServiceInfo"})
@Data
@Component
public class ExternalServiceResponse implements ResponseBuilder<ExternalServiceResponse>, PivotExtractable {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    private final RiskyServiceInfo riskyServiceInfo;

    public ExternalServiceResponse(RiskyServiceInfo riskyServiceInfo) {
        this.riskyServiceInfo = riskyServiceInfo;
    }

    @Override
    public ExternalServiceResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        ExternalServiceResponse response = this;

        //TODO: This should come from API - but for the time being, we are defaulting this to true
        Boolean showAllData = true;

        //Set Widget Specific Metadata
        response.setWidgetId(requestMetadata.getWidgetId());
        response.setTitle("Data Transfers by Services");
        response.setColumns(Arrays.asList(SERVICE.getFieldKey(), PORT.getFieldKey(), PROTOCOL.getFieldKey(), PREVIOUS_AGGREGATED_FLOWS.getFieldKey(), AGGREGATED_FLOWS.getFieldKey(), PREVIOUS_AGGREGATED_BYTES.getFieldKey(), AGGREGATED_BYTES.getFieldKey(), SEVERITY.getFieldKey()));
        response.setColumnTypes(Arrays.asList(SERVICE.getFieldType(), PORT.getFieldType(), PROTOCOL.getFieldType(), PREVIOUS_AGGREGATED_FLOWS.getFieldType(), AGGREGATED_FLOWS.getFieldType(), PREVIOUS_AGGREGATED_BYTES.getFieldType(), AGGREGATED_BYTES.getFieldType(), SEVERITY.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(SERVICE.getFieldDisplayName(), PORT.getFieldDisplayName(), PROTOCOL.getFieldDisplayName(), PREVIOUS_AGGREGATED_FLOWS.getFieldDisplayName(), AGGREGATED_FLOWS.getFieldDisplayName(), PREVIOUS_AGGREGATED_BYTES.getFieldDisplayName(), AGGREGATED_BYTES.getFieldDisplayName(), SEVERITY.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        // Track existing (port, protocol) pairs from resultSetTable
        Set<RiskyServiceInfo.PortProtocolPair> existingPairs = new HashSet<>();
        Set<RiskyServiceInfo.PortProtocolPair> addedPairs = new HashSet<>();

        //Set Response Data
        List<List<Object>> data = new ArrayList<>();
        while (resultSetTable.next()) {
            Integer port = (Integer) resultSetTable.getObject(PORT.getTableColumnName());
            String protocol = (String) resultSetTable.getObject(PROTOCOL.getTableColumnName());
            String service = Optional.ofNullable(port)
                    .flatMap(p -> Optional.ofNullable(protocol)
                            .flatMap(pr -> Optional.ofNullable(
                                            riskyServiceInfo.getPortProtoToServiceInfoMap().get(new RiskyServiceInfo.PortProtocolPair(p, pr.toUpperCase())))
                                    .map(RiskyServiceInfo.ServiceInfo::getService)))
                    .orElse("UNKNOWN");
            Long flowCount = getLongValue(resultSetTable, AGGREGATED_FLOWS.getTableColumnName());
            Long bytes = getLongValue(resultSetTable, AGGREGATED_BYTES.getTableColumnName());
            Long prevFlowCount = getLongValue(resultSetTable, PREVIOUS_AGGREGATED_FLOWS.getTableColumnName());
            Long prevBytes = getLongValue(resultSetTable, PREVIOUS_AGGREGATED_BYTES.getTableColumnName());

            RiskyServiceInfo.PortProtocolPair pair = new RiskyServiceInfo.PortProtocolPair(port, protocol.toUpperCase());
            existingPairs.add(pair);

            List<Object> dataRow = new ArrayList<>();
            dataRow.add(service);
            dataRow.add(port);
            dataRow.add(protocol);
            dataRow.add(prevFlowCount);
            dataRow.add(flowCount);
            dataRow.add(prevBytes);
            dataRow.add(bytes);
            dataRow.add(Optional.ofNullable(port)
                    .flatMap(p -> Optional.ofNullable(protocol)
                            .flatMap(pr -> Optional.ofNullable(
                                            riskyServiceInfo.getPortProtoToServiceInfoMap().get(new RiskyServiceInfo.PortProtocolPair(p, pr.toUpperCase())))
                                    .map(RiskyServiceInfo.ServiceInfo::getSeverity)))
                    .map(SeverityLevel::fromString)
                    .orElse(-1)); // Default value when severity is unknown
            data.add(dataRow);

            //Set total pages = (int) ceil(total_rows / rows per page). Set this only once in the response.
            if (resultSetTable.isLast()) {
                response.pagination.setTotalPages((int) Math.ceil((double) resultSetTable.getInt(TOTAL_ROWS.getTableColumnName()) / response.pagination.getRowLimit()));
            }
        }

        var prevFlowCount = 0;
        var flowCount = 0;
        var prevBytes = 0;
        var bytes = 0;
        if(showAllData){
            // Adding missing service-port-protocol combinations
            for (Map.Entry<RiskyServiceInfo.PortProtocolPair, RiskyServiceInfo.ServiceInfo> entry : riskyServiceInfo.getPortProtoToServiceInfoMap().entrySet()) {
                RiskyServiceInfo.PortProtocolPair pair = entry.getKey();
                RiskyServiceInfo.ServiceInfo serviceInfo = entry.getValue();
                String service = serviceInfo.getService();
                int severity = SeverityLevel.fromString(serviceInfo.getSeverity());
                if (!existingPairs.contains(pair) && !addedPairs.contains(pair)) {
                    List<Object> missingDataRow = new ArrayList<>();
                    missingDataRow.add(service);
                    missingDataRow.add(pair.getPort());
                    missingDataRow.add(pair.getProtocol());
                    missingDataRow.add(prevFlowCount);
                    missingDataRow.add(flowCount);
                    missingDataRow.add(prevBytes);
                    missingDataRow.add(bytes);
                    missingDataRow.add(severity);
                    data.add(missingDataRow);
                    addedPairs.add(pair);
                }
            }
        }

        response.setData(data);
        return response;
    }

    @Override
    public ExternalServiceResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }
    
    /**
     * Converts the response data to CSV format for efficient consumption.
     * 
     * @return A Map containing the CSV data and metadata
     */
    public Map<String, Object> compress() {
        Map<String, Object> result = new HashMap<>();
        StringBuilder csvData = new StringBuilder();
        
        // Add CSV header based on column display names
        csvData.append("Service,Port,Protocol,PreviousFlows,Flows,PreviousBytes,Bytes,Severity\n");
        
        // Add data rows
        if (data != null && !data.isEmpty()) {
            for (List<Object> row : data) {
                if (row.size() < 8) continue;
                
                String service = (String) row.get(0);
                Integer port = (Integer) row.get(1);
                String protocol = (String) row.get(2);
                Long prevFlows = (row.get(3) != null) ? ((Number)row.get(3)).longValue() : null;
                Long flows = (row.get(4) != null) ? ((Number)row.get(4)).longValue() : null;
                Long prevBytes = (row.get(5) != null) ? ((Number)row.get(5)).longValue() : null;
                Long bytes = (row.get(6) != null) ? ((Number)row.get(6)).longValue() : null;
                Integer severity = (Integer) row.get(7);
                
                // Escape fields for CSV format
                String escapedService = escapeCsvField(service);
                String escapedProtocol = escapeCsvField(protocol);
                
                // Add row to CSV
                csvData.append(escapedService).append(",")
                       .append(port != null ? port : 0).append(",")
                       .append(escapedProtocol).append(",")
                       .append(prevFlows != null ? prevFlows : 0).append(",")
                       .append(flows != null ? flows : 0).append(",")
                       .append(prevBytes != null ? prevBytes : 0).append(",")
                       .append(bytes != null ? bytes : 0).append(",")
                       .append(severity != null ? severity : 0).append("\n");
            }
        }
        
        // Add metadata
        if (currentTimeFrame != null) {
            result.put("currentTimeFrameStart", currentTimeFrame.getStartTime());
            result.put("currentTimeFrameEnd", currentTimeFrame.getEndTime());
        }
        
        if (comparisonTimeFrame != null) {
            result.put("comparisonTimeFrameStart", comparisonTimeFrame.getStartTime());
            result.put("comparisonTimeFrameEnd", comparisonTimeFrame.getEndTime());
        }
        
        if (pagination != null) {
            result.put("pageNumber", pagination.getPageNumber());
            result.put("totalPages", pagination.getTotalPages());
        }
        
        // Add CSV data and format
        result.put("csvData", csvData.toString());
        result.put("format", "csv");
        result.put("widgetId", widgetId);
        result.put("title", title);
        
        return result;
    }
    
    /**
     * Properly escapes a field value for CSV format according to RFC 4180:
     * - If the field contains commas, newlines, or double quotes, it should be enclosed in double quotes
     * - Double quotes within the field should be escaped by doubling them
     * 
     * @param value The field value to escape
     * @return The properly escaped CSV field
     */
    private String escapeCsvField(String value) {
        if (value == null) {
            return "";
        }
        
        boolean needsQuoting = value.contains(",") || value.contains("\"") || 
                               value.contains("\n") || value.contains("\r");
        
        if (needsQuoting) {
            // Replace any double quotes with two double quotes
            String escapedValue = value.replace("\"", "\"\"");
            return "\"" + escapedValue + "\"";
        } else {
            return value;
        }
    }
}
