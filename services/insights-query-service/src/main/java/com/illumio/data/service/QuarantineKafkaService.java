package com.illumio.data.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.publisher.KafkaPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
public class QuarantineKafkaService {
    private final KafkaPublisher kafkaPublisher;
    private final ObjectMapper objectMapper;
    private final InsightsServiceConfiguration configuration;

    public QuarantineKafkaService(KafkaPublisher kafkaPublisher, InsightsServiceConfiguration configuration) {
        this.kafkaPublisher = kafkaPublisher;
        this.objectMapper = new ObjectMapper();
        this.configuration = configuration;
    }

    public Mono<Void> sendQuarantineMessage(MultiValueMap<String, String> headers, MultiValueMap<String, String> params, RequestPayload payload, String resourceId) {
        // Extract tenant_id from headers
        String tenantId = headers.getFirst("x-tenant-id");
        if (tenantId == null) {
            return Mono.error(new IllegalArgumentException("Missing tenant_id header"));
        }

        Map<String, Object> labels = new LinkedHashMap<>();
        boolean quarantine = Boolean.TRUE.equals(payload.getQuarantine());
        String labelKey = quarantine ? "set" : "delete";
        labels.put(labelKey, List.of(configuration.getQuarantineConfig().getLabelId()));

        // Build resource object
        Map<String, Object> resource = new LinkedHashMap<>();
        resource.put("resource_type", "workload");
        Map<String, Object> attributes = new LinkedHashMap<>();
        attributes.put("external_data_reference", resourceId);
        attributes.put("labels", labels);
        resource.put("attributes", attributes);
        resource.put("resource_change_type", "update");

        // Build the final message body
        Map<String, Object> messageBody = new LinkedHashMap<>();
        messageBody.put("timestamp", Instant.now().toString()); // RFC 3339 timestamp
        messageBody.put("resources", List.of(resource));

        // Serialize to JSON
        String jsonMessage;
        try {
            jsonMessage = objectMapper.writeValueAsString(messageBody);
        } catch (JsonProcessingException e) {
            return Mono.error(e);
        }

        String partitionKey = tenantId + ":" + resourceId;

        return kafkaPublisher.publishMessage(tenantId, partitionKey, jsonMessage);
    }
}

