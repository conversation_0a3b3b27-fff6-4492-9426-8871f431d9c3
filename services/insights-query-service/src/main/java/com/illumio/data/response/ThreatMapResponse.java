package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.service.batch_widget.PivotExtractable;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.COUNTRY;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;
import static com.illumio.data.model.constants.Fields.PREVIOUS_THREAT_LEVEL;
import static com.illumio.data.model.constants.Fields.THREAT_LEVEL;

@Data
@NoArgsConstructor
@Component
public class ThreatMapResponse implements ResponseBuilder<ThreatMapResponse>, PivotExtractable {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public ThreatMapResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        ThreatMapResponse response = new ThreatMapResponse();

        //Set Widget Specific Metadata
        response.setWidgetId(requestMetadata.getWidgetId());
        response.setTitle("Global Threat Map");
        response.setColumns(Arrays.asList(COUNTRY.getFieldKey(), THREAT_LEVEL.getFieldKey(), PREVIOUS_THREAT_LEVEL.getFieldKey(), COUNT.getFieldKey(), PREVIOUS_COUNT.getFieldKey(), AGGREGATE_FIELD.getFieldKey()));
        response.setColumnTypes(Arrays.asList(COUNTRY.getFieldType(), THREAT_LEVEL.getFieldType(), PREVIOUS_THREAT_LEVEL.getFieldType(), COUNT.getFieldType(), PREVIOUS_COUNT.getFieldType(), AGGREGATE_FIELD.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(COUNTRY.getFieldDisplayName(), THREAT_LEVEL.getFieldDisplayName(), PREVIOUS_THREAT_LEVEL.getFieldDisplayName(), COUNT.getFieldDisplayName(), PREVIOUS_COUNT.getFieldDisplayName(), AGGREGATE_FIELD.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        List<List<Object>> data = new ArrayList<>();
        while (resultSetTable.next()) {
            String country = (String) resultSetTable.getObject(COUNTRY.getTableColumnName());
            String aggregateField = (String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName());
            Integer threatLevel = (Integer) resultSetTable.getObject(THREAT_LEVEL.getTableColumnName());
            Integer previousThreatLevel = (Integer) resultSetTable.getObject(PREVIOUS_THREAT_LEVEL.getTableColumnName());
            Long count = getLongValue(resultSetTable, COUNT.getTableColumnName());
            Long previousCount = getLongValue(resultSetTable, PREVIOUS_COUNT.getTableColumnName());

            List<Object> dataRow = new ArrayList<>();
            dataRow.add(country);
            dataRow.add(threatLevel);
            dataRow.add(previousThreatLevel);
            dataRow.add(count);
            dataRow.add(previousCount);
            dataRow.add(aggregateField.toUpperCase());
            data.add(dataRow);
        }
        response.setData(data);

        return response;
    }

    @Override
    public ThreatMapResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }
    
    /**
     * Converts the response data to CSV format for efficient consumption.
     * 
     * @return A Map containing the CSV data and metadata
     */
    public Map<String, Object> compress() {
        Map<String, Object> result = new HashMap<>();
        StringBuilder csvData = new StringBuilder();
        
        // Add CSV header based on column display names
        csvData.append("Country,ThreatLevel,PreviousThreatLevel,Count,PreviousCount,AggregateField\n");
        
        // Add data rows
        if (data != null && !data.isEmpty()) {
            for (List<Object> row : data) {
                if (row.size() < 6) continue;
                
                String country = (String) row.get(0);
                Integer threatLevel = (Integer) row.get(1);
                Integer prevThreatLevel = (Integer) row.get(2);
                Long count = (row.get(3) != null) ? ((Number)row.get(3)).longValue() : null;
                Long prevCount = (row.get(4) != null) ? ((Number)row.get(4)).longValue() : null;
                String aggField = (String) row.get(5);
                
                // Escape fields for CSV format
                String escapedCountry = escapeCsvField(country);
                String escapedAggField = escapeCsvField(aggField);
                
                // Add row to CSV
                csvData.append(escapedCountry).append(",")
                       .append(threatLevel != null ? threatLevel : 0).append(",")
                       .append(prevThreatLevel != null ? prevThreatLevel : 0).append(",")
                       .append(count != null ? count : 0).append(",")
                       .append(prevCount != null ? prevCount : 0).append(",")
                       .append(escapedAggField).append("\n");
            }
        }
        
        // Add metadata
        if (currentTimeFrame != null) {
            result.put("currentTimeFrameStart", currentTimeFrame.getStartTime());
            result.put("currentTimeFrameEnd", currentTimeFrame.getEndTime());
        }
        
        if (comparisonTimeFrame != null) {
            result.put("comparisonTimeFrameStart", comparisonTimeFrame.getStartTime());
            result.put("comparisonTimeFrameEnd", comparisonTimeFrame.getEndTime());
        }
        
        if (pagination != null) {
            result.put("pageNumber", pagination.getPageNumber());
            result.put("totalPages", pagination.getTotalPages());
        }
        
        // Add CSV data and format
        result.put("csvData", csvData.toString());
        result.put("format", "csv");
        result.put("widgetId", widgetId);
        result.put("title", title);
        
        return result;
    }
    
    /**
     * Properly escapes a field value for CSV format according to RFC 4180:
     * - If the field contains commas, newlines, or double quotes, it should be enclosed in double quotes
     * - Double quotes within the field should be escaped by doubling them
     * 
     * @param value The field value to escape
     * @return The properly escaped CSV field
     */
    private String escapeCsvField(String value) {
        if (value == null) {
            return "";
        }
        
        boolean needsQuoting = value.contains(",") || value.contains("\"") || 
                               value.contains("\n") || value.contains("\r");
        
        if (needsQuoting) {
            // Replace any double quotes with two double quotes
            String escapedValue = value.replace("\"", "\"\"");
            return "\"" + escapedValue + "\"";
        } else {
            return value;
        }
    }
}