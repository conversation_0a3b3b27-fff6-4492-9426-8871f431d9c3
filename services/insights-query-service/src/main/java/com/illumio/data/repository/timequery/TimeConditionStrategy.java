package com.illumio.data.repository.timequery;

public interface TimeConditionStrategy {
    /**
     * Builds a time condition string based on the given start/end times and table name.
     *
     * @param startTime the start time (ISO 8601 format)
     * @param endTime   the end time (ISO 8601 format)
     * @param tableName the target table name, which may affect the condition logic
     * @return a time condition string to be used in a Kusto query
     */
    String buildTimeConditionQuery(String startTime, String endTime, String tableName);
}
