package com.illumio.data.repository.timequery;

public class BaseTableTimeConditionStrategy implements TimeConditionStrategy {
    @Override
    public String buildTimeConditionQuery(String startTime, String endTime, String tableName) {
        // For base tables, simply use the standard operators with an open upper bound.
        return String.format("| where todatetime(StartTime) >= datetime('%s') | where todatetime(EndTime) < datetime('%s')",
                startTime, endTime);
    }
}