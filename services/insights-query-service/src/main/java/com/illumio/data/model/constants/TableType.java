package com.illumio.data.model.constants;

public enum TableType {
    BASE_TABLE,
    AGGREGATED_TABLE_WITH_IN_CLAUSE,
    AGGREGATED_TABLE;

    public static TableType determineTableType(String tableName) {
        if (tableName.contains("_Daily") || tableName.contains("_Hourly")) {
            return AGGREGATED_TABLE;
        } else if (tableName.contains("_Weekly") || tableName.contains("_Monthly")) {
            return AGGREGATED_TABLE_WITH_IN_CLAUSE;
        } else {
            return BASE_TABLE;
        }
    }
}