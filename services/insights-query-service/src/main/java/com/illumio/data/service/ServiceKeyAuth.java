package com.illumio.data.service;

import com.illumio.data.configuration.ServiceKeyConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

@Component
@RequiredArgsConstructor
@Slf4j
public class ServiceKeyAuth {

    private final ServiceKeyConfig serviceKeyConfig;

    public boolean isAuthorized(MultiValueMap<String, String> headers) {
        log.info("Authorizing request");
        String serviceName = headers.getFirst("X-origin-service-name");
        String serviceKey = headers.getFirst("X-service-auth-key");
        log.info("Validating service {} with key {}",serviceName,serviceKey);
        if (serviceName == null || serviceKey == null) {
            return false;
        }

        return serviceKeyConfig.isValidKey(serviceName, serviceKey);
    }
}
