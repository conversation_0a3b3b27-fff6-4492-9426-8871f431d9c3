package com.illumio.data.service.batch_widget;

import com.illumio.data.model.Filters;
import com.illumio.data.model.WidgetBatchRequest;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.widget.WidgetProcessingRequest;
import com.illumio.data.response.WidgetBatchResponse.WidgetResult;
import com.illumio.data.service.PostgresMetadataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Service responsible for handling pivot widget logic.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PivotWidgetService {

    private final PostgresMetadataService postgresMetadataService;
    private final WidgetProcessorService widgetProcessorService;
    private final PivotDataExtractionService pivotDataExtractionService;

    /**
     * Process pivot widgets and generate filtered requests for related widgets.
     */
    public Mono<PivotProcessingResult> processPivotWidgets(
            String tenantId,
            List<WidgetBatchRequest.PivotWidget> pivotWidgets,
            WidgetBatchRequest batchRequest,
            MultiValueMap<String, String> headers,
            MultiValueMap<String, String> params) {

        if (pivotWidgets == null || pivotWidgets.isEmpty()) {
            return Mono.just(PivotProcessingResult.empty());
        }

        log.info("Processing {} pivot widgets", pivotWidgets.size());

        // Process each pivot widget
        return Flux.fromIterable(pivotWidgets)
                .flatMap(pivotWidget -> processSinglePivotWidget(tenantId, pivotWidget, batchRequest, headers, params))
                .collectList()
                .map(this::combineResults);
    }

    private Mono<SinglePivotResult> processSinglePivotWidget(
            String tenantId,
            WidgetBatchRequest.PivotWidget pivotWidget,
            WidgetBatchRequest batchRequest,
            MultiValueMap<String, String> headers,
            MultiValueMap<String, String> params) {

        log.info("Processing pivot widget: {} for page: {}", pivotWidget.getWidgetId(), pivotWidget.getPageId());

        // First, get the pivot widget's data
        return postgresMetadataService.getMetadata(pivotWidget.getWidgetId())
                .flatMap(metadata -> {
                    WidgetProcessingRequest request = WidgetProcessingRequest.basic(pivotWidget.getWidgetId());
                    return widgetProcessorService.processWidgetRequest(
                            tenantId,
                            request,
                            metadata,
                            batchRequest.getCurrentTimeFrame(),
                            batchRequest.getComparisonTimeFrame(),
                            headers,
                            params,
                            batchRequest.getPageSize()
                    ).map(result -> new PivotWidgetResult(pivotWidget, metadata, result));
                })
                .flatMap(pivotResult -> {
                    // Extract top K values from pivot widget result using the dedicated service
                    List<String> extractedValues = pivotDataExtractionService.extractTopKValues(
                            pivotResult.result(),
                            pivotResult.pivotWidget().getField(),
                            pivotResult.pivotWidget().getTopKSize()
                    );

                    // Sort and deduplicate the values
                    final List<String> topKValues = pivotDataExtractionService.sortAndDeduplicate(extractedValues);

                    if (topKValues.isEmpty()) {
                        log.info("No top K values found for pivot widget: {}", pivotResult.pivotWidget().getWidgetId());
                        return Mono.just(new SinglePivotResult(pivotResult.result(), Collections.emptyList()));
                    }

                    log.info("Extracted {} top K values for pivot widget {}: {}",
                            topKValues.size(), pivotResult.pivotWidget().getWidgetId(), topKValues);

                    // Get all widgets for this page
                    return postgresMetadataService.getWidgetsByPageId(pivotResult.pivotWidget().getPageId())
                            .filter(widget -> !widget.getWidgetId().equals(pivotResult.pivotWidget().getWidgetId())) // Exclude the pivot widget itself
                            .filter(widget -> batchRequest.getWidgetIdList().contains(widget.getWidgetId())) // Only include widgets that are in the request
                            .collectList()
                            .flatMap(relatedWidgets -> {
                                if (relatedWidgets.isEmpty()) {
                                    log.info("No related widgets found for page: {}", pivotResult.pivotWidget().getPageId());
                                    return Mono.just(new SinglePivotResult(pivotResult.result(), Collections.emptyList()));
                                }

                                log.info("Found {} related widgets for page: {}", relatedWidgets.size(), pivotResult.pivotWidget().getPageId());

                                // Generate filtered requests for each top K value
                                return generateFilteredRequests(tenantId, relatedWidgets, topKValues, pivotResult.pivotWidget(), batchRequest, headers, params)
                                        .map(filteredResults -> new SinglePivotResult(pivotResult.result(), filteredResults));
                            });
                });
    }

    private Mono<List<WidgetResult>> generateFilteredRequests(
            String tenantId,
            List<Metadata> relatedWidgets,
            List<String> topKValues,
            WidgetBatchRequest.PivotWidget pivotWidget,
            WidgetBatchRequest batchRequest,
            MultiValueMap<String, String> headers,
            MultiValueMap<String, String> params) {

        log.info("Generating {} filtered requests for {} widgets", topKValues.size(), relatedWidgets.size());

        // Create requests for each widget with each top K value as filter
        return Flux.fromIterable(relatedWidgets)
                .flatMap(relatedWidget -> {
                    // Check if this widget is category-dependent
                    boolean isCategoryDependent = relatedWidget.getIsCategoryDependent() != null &&
                                                relatedWidget.getIsCategoryDependent();

                    if (isCategoryDependent) {
                        // For category-dependent widgets, create requests for each category + each top K value
                        List<String> categories = Arrays.asList("subscriptions", "tenants");

                        return Flux.fromIterable(categories)
                                .flatMap(category ->
                                    Flux.fromIterable(topKValues)
                                            .flatMap(filterValue -> {
                                                // Create request with both category and pivot filters
                                                Map<String, List<String>> additionalFilters = new HashMap<>();
                                                additionalFilters.put(pivotWidget.getField(), List.of(filterValue));

                                                WidgetProcessingRequest request = WidgetProcessingRequest.builder()
                                                        .widgetId(relatedWidget.getWidgetId())
                                                        .categoryName("category")
                                                        .categoryValue(category)
                                                        .additionalFilters(additionalFilters)
                                                        .build();

                                                log.info("Creating filtered request for category-dependent widget {} with category={} and pivot filter {}={}",
                                                        relatedWidget.getWidgetId(), category, pivotWidget.getField(), filterValue);

                                                return widgetProcessorService.processWidgetRequest(
                                                        tenantId,
                                                        request,
                                                        relatedWidget,
                                                        batchRequest.getCurrentTimeFrame(),
                                                        batchRequest.getComparisonTimeFrame(),
                                                        headers,
                                                        params,
                                                        batchRequest.getPageSize()
                                                ).map(result -> new FilteredWidgetResult(relatedWidget.getWidgetId(), category + ":" + filterValue, result))
                                                .onErrorResume(error -> {
                                                    log.error("Error processing filtered widget request for {} with category={} and filter {}={}",
                                                            relatedWidget.getWidgetId(), category, pivotWidget.getField(), filterValue, error);
                                                    return Mono.just(new FilteredWidgetResult(
                                                            relatedWidget.getWidgetId(),
                                                            category + ":" + filterValue,
                                                            WidgetResult.error(relatedWidget.getWidgetId(),
                                                                    "Error processing filtered request: " + error.getMessage())
                                                    ));
                                                });
                                            })
                                );
                    } else {
                        // For non-category-dependent widgets, use existing logic
                        return Flux.fromIterable(topKValues)
                                .flatMap(filterValue -> {
                                    // Create a filtered processing request
                                    WidgetProcessingRequest request = WidgetProcessingRequest.builder()
                                            .widgetId(relatedWidget.getWidgetId())
                                            .categoryName(pivotWidget.getField())
                                            .categoryValue(filterValue)
                                            .build();

                                    log.info("Creating filtered request for widget {} with filter {}={}",
                                            relatedWidget.getWidgetId(), pivotWidget.getField(), filterValue);

                                    return widgetProcessorService.processWidgetRequest(
                                            tenantId,
                                            request,
                                            relatedWidget,
                                            batchRequest.getCurrentTimeFrame(),
                                            batchRequest.getComparisonTimeFrame(),
                                            headers,
                                            params,
                                            batchRequest.getPageSize()
                                    ).map(result -> new FilteredWidgetResult(relatedWidget.getWidgetId(), filterValue, result))
                                    .onErrorResume(error -> {
                                        log.error("Error processing filtered widget request for {} with filter {}={}",
                                                relatedWidget.getWidgetId(), pivotWidget.getField(), filterValue, error);
                                        return Mono.just(new FilteredWidgetResult(
                                                relatedWidget.getWidgetId(),
                                                filterValue,
                                                WidgetResult.error(relatedWidget.getWidgetId(),
                                                        "Error processing filtered request: " + error.getMessage())
                                        ));
                                    });
                                });
                    }
                })
                .collectList()
                .map(this::combineFilteredResults);
    }

    private List<WidgetResult> combineFilteredResults(List<FilteredWidgetResult> filteredResults) {
        // Group by widget ID and combine results
        Map<String, List<FilteredWidgetResult>> groupedResults = filteredResults.stream()
                .collect(Collectors.groupingBy(FilteredWidgetResult::widgetId));

        return groupedResults.entrySet().stream()
                .map(entry -> {
                    String widgetId = entry.getKey();
                    List<FilteredWidgetResult> results = entry.getValue();

                    // Separate successful and failed results
                    List<FilteredWidgetResult> successfulResults = results.stream()
                            .filter(result -> "success".equals(result.result().getStatus()))
                            .collect(Collectors.toList());

                    List<FilteredWidgetResult> failedResults = results.stream()
                            .filter(result -> !"success".equals(result.result().getStatus()))
                            .collect(Collectors.toList());

                    if (successfulResults.isEmpty()) {
                        // All requests failed
                        String errorMessage = failedResults.stream()
                                .map(result -> result.result().getMessage())
                                .filter(Objects::nonNull)
                                .collect(Collectors.joining("; "));
                        return WidgetResult.error(widgetId, "All filtered requests failed: " + errorMessage);
                    }

                    // Combine successful results for this widget
                    Map<String, Object> combinedData = new HashMap<>();
                    for (FilteredWidgetResult result : successfulResults) {
                        combinedData.put(result.filterValue(), result.result().getData());
                    }

                    WidgetResult.WidgetResultBuilder resultBuilder = WidgetResult.builder()
                            .widgetId(widgetId)
                            .status("success")
                            .data(combinedData);

                    // Add warning message if some requests failed
                    if (!failedResults.isEmpty()) {
                        String warningMessage = String.format("Some filtered requests failed (%d/%d)",
                                failedResults.size(), results.size());
                        resultBuilder.message(warningMessage);
                    }

                    return resultBuilder.build();
                })
                .collect(Collectors.toList());
    }

    private PivotProcessingResult combineResults(List<SinglePivotResult> results) {
        List<WidgetResult> pivotResults = results.stream()
                .map(SinglePivotResult::pivotResult)
                .collect(Collectors.toList());

        List<WidgetResult> filteredResults = results.stream()
                .flatMap(result -> result.filteredResults().stream())
                .collect(Collectors.toList());

        return new PivotProcessingResult(pivotResults, filteredResults);
    }

    // Helper records
    private record PivotWidgetResult(WidgetBatchRequest.PivotWidget pivotWidget, Metadata metadata, WidgetResult result) {}
    private record FilteredWidgetResult(String widgetId, String filterValue, WidgetResult result) {}
    private record SinglePivotResult(WidgetResult pivotResult, List<WidgetResult> filteredResults) {}

    public record PivotProcessingResult(List<WidgetResult> pivotResults, List<WidgetResult> filteredResults) {
        public static PivotProcessingResult empty() {
            return new PivotProcessingResult(Collections.emptyList(), Collections.emptyList());
        }

        public boolean hasPivotWidgets() {
            return !pivotResults.isEmpty();
        }
    }
}
