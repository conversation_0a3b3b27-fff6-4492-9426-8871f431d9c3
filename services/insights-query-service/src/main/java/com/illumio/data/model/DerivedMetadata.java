package com.illumio.data.model;

import com.illumio.data.model.constants.TableType;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Slf4j
@Getter
@RequiredArgsConstructor
public class DerivedMetadata {
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ISO_DATE_TIME;

    private final String currentTimeFrameTable;
    private final String comparedTimeFrameTable;
    private final TimeFrame updatedCurrentTimeFrame;
    private final TimeFrame updatedComparisonTimeFrame;
    private final TableType currentTableType;
    private final TableType comparisonTableType;

    public DerivedMetadata fallbackToDaily(String fallbackTable) {
        TimeFrame updatedCurrentTimeFrame = normalizeTimeFrameToDaily(this.updatedCurrentTimeFrame);
        TimeFrame updatedComparisionTimeFrame = normalizeTimeFrameToDaily(this.updatedComparisonTimeFrame);

        return new DerivedMetadata(
                fallbackTable,
                fallbackTable,
                updatedCurrentTimeFrame,
                updatedComparisionTimeFrame,
                TableType.AGGREGATED_TABLE,
                TableType.AGGREGATED_TABLE
        );
    }

    private TimeFrame normalizeTimeFrameToDaily(TimeFrame original) {
        LocalDateTime startTime = LocalDateTime.parse(original.getStartTime(), DATE_TIME_FORMATTER).withHour(0);
        LocalDateTime endTime = LocalDateTime.parse(original.getEndTime(), DATE_TIME_FORMATTER).withHour(0);
        return new TimeFrame(startTime.format(DATE_TIME_FORMATTER), endTime.format(DATE_TIME_FORMATTER));
    }
}