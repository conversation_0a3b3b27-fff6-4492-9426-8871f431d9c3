package com.illumio.data.service.batch_widget;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.model.Filters;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.widget.WidgetProcessingRequest;
import com.illumio.data.model.widget.response.CategoryResponse;
import com.illumio.data.model.widget.response.CombinedResponse;
import com.illumio.data.model.widget.response.DirectionalResponse;
import com.illumio.data.response.WidgetBatchResponse.WidgetResult;
import com.illumio.data.service.DefaultValues;
import com.illumio.data.service.KustoRetryService;
import com.illumio.data.service.PostgresMetadataService;
import com.illumio.data.service.RequestPayloadValidation;
import com.illumio.data.service.RequestRouter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service responsible for processing individual widget requests.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WidgetProcessorService {
    private final RequestRouter requestRouter;
    private final KustoRetryService kustoRetryService;
    private final RequestPayloadValidation requestPayloadValidation;
    private final DefaultValues defaultValues;
    private final InsightsServiceConfiguration configuration;
    private final PostgresMetadataService postgresMetadataService;

    /**
     * Process a single widget request.
     */
    public Mono<WidgetResult> processWidgetRequest(
            String tenantId,
            WidgetProcessingRequest request,
            Metadata requestMetadata,
            com.illumio.data.model.TimeFrame currentTimeFrame,
            com.illumio.data.model.TimeFrame comparisonTimeFrame,
            MultiValueMap<String, String> headers,
            MultiValueMap<String, String> params,
            Integer pageSize) {

        String widgetId = request.getWidgetId();
        String trafficDirection = request.getTrafficDirection();
        String categoryName = request.getCategoryName();
        String categoryValue = request.getCategoryValue();

        log.debug("Processing widget ID: {} ({}{})", widgetId,
                trafficDirection != null ? "direction: " + trafficDirection : "",
                categoryValue != null ? "category: " + categoryValue : "default");

        // Create payload for this specific widget
        RequestPayload widgetPayload = RequestPayload.builder()
                .currentTimeFrame(currentTimeFrame)
                .comparisonTimeFrame(comparisonTimeFrame)
                .build();

        // Add filters based on request type
        List<Filters> filters = new ArrayList<>();

        // If this is a traffic direction-specific request, add the direction filter
        if (trafficDirection != null) {
            filters.add(Filters.builder()
                    .categoryName("traffic_direction")
                    .categoryType("string")
                    .categoryValue(List.of(trafficDirection))
                    .build());
        }

        // If this is a category-specific request, add the category filter
        if (categoryName != null && categoryValue != null) {
            filters.add(Filters.builder()
                    .categoryName(categoryName)
                    .categoryType("string")
                    .categoryValue(List.of(categoryValue))
                    .build());
        }

        // Add additional filters if present (for pivot widget scenarios)
        if (request.getAdditionalFilters() != null) {
            request.getAdditionalFilters().forEach((filterName, filterValues) -> {
                filters.add(Filters.builder()
                        .categoryName(filterName)
                        .categoryType("string")
                        .categoryValue(List.of(filterValues.toArray()))
                        .build());
            });
        }

        // Set filters if we have any
        if (!filters.isEmpty()) {
            widgetPayload.setFilters(filters);
        }

        // Apply default values and validate
        defaultValues.setPayloadDefaults(configuration, widgetPayload);

        // Set custom page size if provided
        if (pageSize != null && pageSize > 0) {
            widgetPayload.setPagination(Pagination.builder()
                            .pageNumber(1)
                            .rowLimit(pageSize)
                            .build());
        }

        RequestContext requestContext = RequestContext.builder()
                .tenantId(Optional.of(tenantId))
                .widgetId(Optional.of(widgetId))
                .requestPayload(Optional.of(widgetPayload))
                .headers(Optional.ofNullable(headers))
                .queryParams(Optional.ofNullable(params))
                .build();

        InsightsServiceConfiguration.KustoConfiguration kustoConfig = configuration.getKustoInsightsConfig();

        // Process this widget and map response to WidgetResult with Retry Logic
        return kustoRetryService.executeWithRetries(
                () -> requestRouter.routeRequest(requestMetadata, requestContext),
                        kustoConfig)
                .map(response -> {
                    if (trafficDirection != null && categoryValue != null) {
                        // Format responses from combined category and direction queries
                        return WidgetResult.builder()
                                .widgetId(widgetId)
                                .status("success")
                                .data(new CombinedResponse(categoryValue, trafficDirection, response.getBody()))
                                .build();
                    } else if (trafficDirection != null) {
                        // Format responses from direction-specific queries
                        return WidgetResult.builder()
                                .widgetId(widgetId)
                                .status("success")
                                .data(new DirectionalResponse(trafficDirection, response.getBody()))
                                .build();
                    } else if (categoryValue != null) {
                        // Format responses from category-specific queries
                        return WidgetResult.builder()
                                .widgetId(widgetId)
                                .status("success")
                                .data(new CategoryResponse(categoryValue, response.getBody()))
                                .build();
                    } else {
                        return WidgetResult.success(widgetId, response.getBody());
                    }
                })
                .onErrorResume(error -> {
                    log.error("Error processing widget ID: {} ({}{}{})", widgetId,
                            trafficDirection != null ? "direction: " + trafficDirection : "",
                            categoryValue != null ? ", category: " + categoryValue : "",
                            trafficDirection == null && categoryValue == null ? "default" : "",
                            error);

                    if (trafficDirection != null && categoryValue != null) {
                        // Format error for combined query
                        return Mono.just(WidgetResult.builder()
                                .widgetId(widgetId)
                                .status("error")
                                .data(new CombinedResponse(categoryValue, trafficDirection, null))
                                .message("Error processing " + categoryValue + ", " + trafficDirection + ": " + error.getMessage())
                                .build());
                    } else if (trafficDirection != null) {
                        // Format error for directional query
                        return Mono.just(WidgetResult.builder()
                                .widgetId(widgetId)
                                .status("error")
                                .data(new DirectionalResponse(trafficDirection, null))
                                .message("Error processing " + trafficDirection + ": " + error.getMessage())
                                .build());
                    } else if (categoryValue != null) {
                        // Format error for category query
                        return Mono.just(WidgetResult.builder()
                                .widgetId(widgetId)
                                .status("error")
                                .data(new CategoryResponse(categoryValue, null))
                                .message("Error processing " + categoryValue + ": " + error.getMessage())
                                .build());
                    } else {
                        return Mono.just(WidgetResult.error(widgetId, error.getMessage()));
                    }
                })
                // Run each widget request on a scheduler to control concurrency
                .subscribeOn(Schedulers.boundedElastic());
    }
}