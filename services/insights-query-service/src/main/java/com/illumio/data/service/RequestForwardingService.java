package com.illumio.data.service;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;

import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class RequestForwardingService {

    private final WebClient webClient;
    private final InsightsServiceConfiguration configuration;

    public Mono<ResponseEntity<Object>> forwardRequest(
            MultiValueMap<String, String> headers,
            MultiValueMap<String, String> params) {

        if (!configuration.getProxyConfig().isEnableProxy()) {
            log.debug("Proxy is disabled, skipping request forwarding");
            return Mono.empty();
        }

        String targetUrl = configuration.getProxyConfig().getTargetFqdn() + 
                          configuration.getProxyConfig().getTargetEndpoint();

        log.info("Forwarding request to: {}", targetUrl);

        return webClient.get()
                .uri(targetUrl, uriBuilder -> {
                    params.forEach((key, values) -> {
                        if (values != null && !values.isEmpty()) {
                            uriBuilder.queryParam(key, values.toArray());
                        }
                    });
                    return uriBuilder.build();
                })
                .headers(httpHeaders -> {
                    headers.forEach((key, values) -> {
                        if (values != null && !values.isEmpty()) {
                            httpHeaders.addAll(key, values);
                        }
                    });
                })
                .retrieve()
                .toEntity(Object.class)
                .doOnSuccess(response -> {
                    log.debug("Successfully forwarded request to target FQDN. Response status: {}", 
                             response.getStatusCode());
                })
                .onErrorResume(WebClientResponseException.class, ex -> {
                    log.error("Error forwarding request to target FQDN: {} - {}", 
                             ex.getStatusCode(), ex.getResponseBodyAsString());
                    ResponseEntity<Object> errorResponse = ResponseEntity.status(ex.getStatusCode())
                            .body(Map.of("error", "Upstream Error", 
                                        "message", "Error from target service: " + ex.getMessage()));
                    return Mono.just(errorResponse);
                })
                .onErrorResume(Exception.class, ex -> {
                    log.error("Unexpected error forwarding request to target FQDN: ", ex);
                    ResponseEntity<Object> errorResponse = ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                            .body(Map.of("error", "Service Unavailable", 
                                        "message", "Target service is currently unavailable"));
                    return Mono.just(errorResponse);
                });
    }
} 