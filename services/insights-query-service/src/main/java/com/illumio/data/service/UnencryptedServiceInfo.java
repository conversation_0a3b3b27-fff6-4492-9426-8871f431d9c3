package com.illumio.data.service;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Getter
@Component
public class UnencryptedServiceInfo {
    private final Map<UnencryptedServiceInfo.PortProtocolPair, UnencryptedServiceInfo.ServiceInfo> portProtoToServiceInfoMap = new HashMap<>();
    private final Map<String, List<UnencryptedServiceInfo.ServiceInfo>> servicetoServiceInfoMap = new HashMap<>();
    private String filePath;

    public UnencryptedServiceInfo(InsightsServiceConfiguration config) {
        filePath = config.getUnencryptedServiceConfig().getUnencryptedServiceFilePath();
        loadUnencryptedServices();
    }

    private void loadUnencryptedServices() {
        if (filePath.startsWith("classpath:")) {
            try {
                filePath = ResourceUtils.getFile(filePath).getPath();
            } catch (FileNotFoundException e) {
                throw new RuntimeException("Error in loading the unencrypted service file. Please verify that the file exists.", e);
            }
        }
        try (BufferedReader reader = Files.newBufferedReader(Paths.get(filePath));
             CSVParser csvParser = new CSVParser(reader, CSVFormat.DEFAULT.withFirstRecordAsHeader())) {

            for (CSVRecord record : csvParser) {
                try {
                    int port = Integer.parseInt(record.get(0));
                    String serviceName = record.get(1).trim();  // Preserves "Metasploit, Customapps"
                    String protocol = record.get(2).toUpperCase().trim();
                    String severity = record.get(3).toUpperCase().trim();
                    String category = record.get(4).toUpperCase().trim();
                    portProtoToServiceInfoMap.put(new UnencryptedServiceInfo.PortProtocolPair(port, protocol),
                            new UnencryptedServiceInfo.ServiceInfo(port, serviceName, protocol, severity, category));

                    servicetoServiceInfoMap.putIfAbsent(serviceName, new ArrayList<>());
                    servicetoServiceInfoMap.get(serviceName)
                            .add(new UnencryptedServiceInfo.ServiceInfo(port, serviceName, protocol, severity, category));

                } catch (NumberFormatException | NullPointerException e) {
                    log.warn("Poorly formatted unencrypted service file. Check line: {}", record);
                }
            }
        } catch (IOException | NullPointerException e) {
            throw new RuntimeException("Error in loading the unencrypted service lookup file", e);
        }
    }

    @Getter
    public static class ServiceInfo {
        private final int port;
        private final String service;
        private final String protocol;
        private final String severity;
        private final String category;

        public ServiceInfo(int port, String service, String protocol, String severity, String category) {
            this.port = port;
            this.service = service;
            this.protocol = protocol;
            this.severity = severity;
            this.category = category;
        }
    }

    @Getter
    @AllArgsConstructor
    @EqualsAndHashCode
    public static class PortProtocolPair {
        int port;
        String protocol;
    }
}
