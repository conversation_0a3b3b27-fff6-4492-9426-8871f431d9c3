package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.service.batch_widget.PivotExtractable;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_CATEGORY;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_ID;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_NAME;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_TYPE;
import static com.illumio.data.model.constants.Fields.TIME_SERIES;

@Data
@NoArgsConstructor
@Component
public class TopWorkloadsResponse implements ResponseBuilder<TopWorkloadsResponse>, PivotExtractable {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public TopWorkloadsResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        TopWorkloadsResponse response = this;
        RequestPayload payload = requestContext.getRequestPayload().get();

        //Set Widget Specific Metadata
        response.setWidgetId(requestMetadata.getWidgetId());
        response.setTitle("Top Workloads");
        response.setColumns(Arrays.asList(SOURCE_RESOURCE_ID.getFieldKey(), SOURCE_RESOURCE_NAME.getFieldKey(), SOURCE_RESOURCE_CATEGORY.getFieldKey(), SOURCE_RESOURCE_TYPE.getFieldKey(), AGGREGATE_FIELD.getFieldKey(), TIME_SERIES.getFieldKey()));
        response.setColumnTypes(Arrays.asList(SOURCE_RESOURCE_ID.getFieldType(), SOURCE_RESOURCE_NAME.getFieldType(), SOURCE_RESOURCE_CATEGORY.getFieldType(), SOURCE_RESOURCE_TYPE.getFieldType(), AGGREGATE_FIELD.getFieldType(), TIME_SERIES.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(SOURCE_RESOURCE_ID.getFieldDisplayName(), SOURCE_RESOURCE_NAME.getFieldDisplayName(), SOURCE_RESOURCE_CATEGORY.getFieldDisplayName(), SOURCE_RESOURCE_TYPE.getFieldDisplayName(), AGGREGATE_FIELD.getFieldDisplayName(), TIME_SERIES.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        Map<String, Map<String, List<List<Object>>>> groupedData = new HashMap<>();

        while (resultSetTable.next()) {
            String srcResId = (String) resultSetTable.getObject(SOURCE_RESOURCE_ID.getTableColumnName());
            String srcResName = (String) resultSetTable.getObject(SOURCE_RESOURCE_NAME.getTableColumnName());
            String srcResCategory = (String) resultSetTable.getObject(SOURCE_RESOURCE_CATEGORY.getTableColumnName());
            String srcResType = (String) resultSetTable.getObject(SOURCE_RESOURCE_TYPE.getTableColumnName());
            // Composite key for grouping
            String compositeKey = srcResId + "|" + srcResName + "|" + srcResCategory + "|" + srcResType;

            // Extract row values
            String aggregateField = (String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName());
            String timestamp = (String) resultSetTable.getObject(TIME_SERIES.getTableColumnName());
            Long count = getLongValue(resultSetTable, COUNT.getTableColumnName());

            // Group by composite key and then by aggregateField.
            groupedData
                    .computeIfAbsent(compositeKey, k -> new HashMap<>())
                    .computeIfAbsent(aggregateField, k -> new ArrayList<>())
                    .add(Arrays.asList(timestamp, count));
        }

        // Build final formatted data: for each composite key and then each aggregate group
        List<List<Object>> formattedData = groupedData.entrySet().stream()
                .flatMap(outer -> {
                    // Split the composite key back into its resource-level parts.
                    String[] parts = outer.getKey().split("\\|", -1);
                    String srcResId = parts[0];
                    String srcResName = parts[1];
                    String srcResCategory = parts[2];
                    String srcResType = parts[3];

                    return outer.getValue().entrySet().stream()
                            .map(inner -> Arrays.asList(
                                    srcResId,
                                    srcResName,
                                    srcResCategory,
                                    srcResType,
                                    inner.getKey(),
                                    inner.getValue()
                            ));
                })
                .collect(Collectors.toList());
        response.setData(formattedData);

        return response;

    }

    @Override
    public TopWorkloadsResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }
    
    /**
     * Converts the response data to CSV format for efficient consumption.
     * 
     * @return A Map containing the CSV data and metadata
     */
    public Map<String, Object> compress() {
        Map<String, Object> result = new HashMap<>();
        StringBuilder csvData = new StringBuilder();
        
        // Add CSV header based on column display names
        csvData.append("ResourceID,ResourceName,ResourceCategory,ResourceType,AggregateField,Timestamp,Count\n");
        
        // Add data rows
        if (data != null && !data.isEmpty()) {
            for (List<Object> row : data) {
                if (row.size() < 6) continue;
                
                String resId = (String) row.get(0);
                String resName = (String) row.get(1);
                String resCat = (String) row.get(2);
                String resType = (String) row.get(3);
                String aggField = (String) row.get(4);
                List<List<Object>> timeSeries = (List<List<Object>>) row.get(5);
                
                // Escape fields for CSV format
                String escapedResId = escapeCsvField(resId);
                String escapedResName = escapeCsvField(resName);
                String escapedResCat = escapeCsvField(resCat);
                String escapedResType = escapeCsvField(resType);
                String escapedAggField = escapeCsvField(aggField);
                
                // If timeSeries is empty, add a single row with empty timestamp and zero count
                if (timeSeries == null || timeSeries.isEmpty()) {
                    csvData.append(escapedResId).append(",")
                           .append(escapedResName).append(",")
                           .append(escapedResCat).append(",")
                           .append(escapedResType).append(",")
                           .append(escapedAggField).append(",")
                           .append(",")  // Empty timestamp
                           .append("0\n"); // Zero count
                } else {
                    // Add a row for each timestamp-count pair
                    for (List<Object> dataPoint : timeSeries) {
                        String timestamp = (String) dataPoint.get(0);
                        Long count = (dataPoint.get(1) != null) ? ((Number)dataPoint.get(1)).longValue() : null;
                        
                        String escapedTimestamp = escapeCsvField(timestamp);
                        
                        csvData.append(escapedResId).append(",")
                               .append(escapedResName).append(",")
                               .append(escapedResCat).append(",")
                               .append(escapedResType).append(",")
                               .append(escapedAggField).append(",")
                               .append(escapedTimestamp).append(",")
                               .append(count != null ? count : 0).append("\n");
                    }
                }
            }
        }
        
        // Add metadata
        if (currentTimeFrame != null) {
            result.put("currentTimeFrameStart", currentTimeFrame.getStartTime());
            result.put("currentTimeFrameEnd", currentTimeFrame.getEndTime());
        }
        
        if (comparisonTimeFrame != null) {
            result.put("comparisonTimeFrameStart", comparisonTimeFrame.getStartTime());
            result.put("comparisonTimeFrameEnd", comparisonTimeFrame.getEndTime());
        }
        
        if (pagination != null) {
            result.put("pageNumber", pagination.getPageNumber());
            result.put("totalPages", pagination.getTotalPages());
        }
        
        // Add CSV data and format
        result.put("csvData", csvData.toString());
        result.put("format", "csv");
        result.put("widgetId", widgetId);
        result.put("title", title);
        
        return result;
    }
    
    /**
     * Properly escapes a field value for CSV format according to RFC 4180:
     * - If the field contains commas, newlines, or double quotes, it should be enclosed in double quotes
     * - Double quotes within the field should be escaped by doubling them
     * 
     * @param value The field value to escape
     * @return The properly escaped CSV field
     */
    private String escapeCsvField(String value) {
        if (value == null) {
            return "";
        }
        
        boolean needsQuoting = value.contains(",") || value.contains("\"") || 
                               value.contains("\n") || value.contains("\r");
        
        if (needsQuoting) {
            // Replace any double quotes with two double quotes
            String escapedValue = value.replace("\"", "\"\"");
            return "\"" + escapedValue + "\"";
        } else {
            return value;
        }
    }
}