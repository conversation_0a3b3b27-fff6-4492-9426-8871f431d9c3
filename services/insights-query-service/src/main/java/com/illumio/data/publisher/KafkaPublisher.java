package com.illumio.data.publisher;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class KafkaPublisher {

    private final KafkaSender<String, String> kafkaSender;
    private final InsightsServiceConfiguration insightsServiceConfiguration;

    public Mono<Void> publishMessage(String tenantId, String partitionKey, String message) {
        // TODO: Bypassing Kafka Publish for RSA. Long term fix TBD once quarantine is productized
        return Mono.empty()
                .then()
                .doOnSuccess(unused -> log.info("Bypassing Kafka Publish for RSA. Label WILL NOT be quarantined "));
                
        /*
        List<Header> headers = Arrays.asList(new RecordHeader("tenant_id", tenantId.getBytes()),
                new RecordHeader("dataplane_id", "dataplane_1".getBytes()));

        ProducerRecord<String, String> producerRecord = new ProducerRecord<>(insightsServiceConfiguration.getKafkaProducerConfig().getTopic(),
                null, partitionKey, message, headers);

        return kafkaSender.createOutbound()
                .send(Mono.just(SenderRecord.create(producerRecord, partitionKey)))
                .then()
                .doOnSuccess(unused -> log.info("Message published successfully to topic '{}': {}", insightsServiceConfiguration.getKafkaProducerConfig().getTopic(), producerRecord))
                .retryWhen(Retry.fixedDelay(3, Duration.ofSeconds(2))
                        .onRetryExhaustedThrow((retrySpec, signal) ->
                                new RuntimeException("Kafka message send failed after retries")))
                .doOnError(error -> log.error("Failed to send Kafka message: {}", error.getMessage()));
        */
    }
}
