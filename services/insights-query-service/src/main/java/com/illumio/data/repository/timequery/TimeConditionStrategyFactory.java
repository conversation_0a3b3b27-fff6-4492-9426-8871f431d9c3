package com.illumio.data.repository.timequery;

import com.illumio.data.model.constants.TableType;

public class TimeConditionStrategyFactory {

    public static TimeConditionStrategy getStrategy(TableType tableType) {
        return switch (tableType) {
            case BASE_TABLE -> new BaseTableTimeConditionStrategy();
            case AGGREGATED_TABLE_WITH_IN_CLAUSE -> new WeeklyMonthlyTimeConditionStrategy();
            case AGGREGATED_TABLE -> new DailyHourlyTimeConditionStrategy();
        };
    }
}