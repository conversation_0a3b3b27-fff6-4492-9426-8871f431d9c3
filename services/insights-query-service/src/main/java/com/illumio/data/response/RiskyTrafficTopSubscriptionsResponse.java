package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.WidgetId;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.ACCOUNT_NAME;
import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.COUNTRY;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;
import static com.illumio.data.model.constants.Fields.SUBSCRIPTION_ID;

@Data
@NoArgsConstructor
@Component
public class RiskyTrafficTopSubscriptionsResponse implements ResponseBuilder<RiskyTrafficTopSubscriptionsResponse> {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public RiskyTrafficTopSubscriptionsResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        if (requestContext.getRequestPayload().isEmpty()) {
            throw new IllegalArgumentException("Request Payload is empty");
        }
        RequestPayload payload = requestContext.getRequestPayload().get();

        RiskyTrafficTopSubscriptionsResponse response = this;

        //Set Widget Specific Metadata
        response.setWidgetId(WidgetId.RISKY_TRAFFIC_TOP_SUBSCRIPTIONS);
        response.setTitle("Top 5 Subscriptions in CSP Regions");
        response.setColumns(Arrays.asList(
                SUBSCRIPTION_ID.getFieldKey(),
                ACCOUNT_NAME.getFieldKey(),
                COUNTRY.getFieldKey(),
                COUNT.getFieldKey(),
                PREVIOUS_COUNT.getFieldKey(),
                AGGREGATE_FIELD.getFieldKey()));
        response.setColumnTypes(Arrays.asList(
                SUBSCRIPTION_ID.getFieldType(),
                ACCOUNT_NAME.getFieldType(),
                COUNTRY.getFieldType(),
                COUNT.getFieldType(),
                PREVIOUS_COUNT.getFieldType(),
                AGGREGATE_FIELD.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(
                SUBSCRIPTION_ID.getFieldDisplayName(),
                ACCOUNT_NAME.getFieldDisplayName(),
                COUNTRY.getFieldDisplayName(),
                COUNT.getFieldDisplayName(),
                PREVIOUS_COUNT.getFieldDisplayName(),
                AGGREGATE_FIELD.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        List<List<Object>> data = new ArrayList<>();

        while (resultSetTable.next()) {
            // Extract the row fields you need
            String subId = (String) resultSetTable.getObject(SUBSCRIPTION_ID.getTableColumnName());
            String accountName = (String) resultSetTable.getObject(ACCOUNT_NAME.getTableColumnName());
            String country = (String) resultSetTable.getObject(COUNTRY.getTableColumnName());
            Long countVal = getLongValue(resultSetTable, COUNT.getTableColumnName());
            Long previousCountVal = getLongValue(resultSetTable, PREVIOUS_COUNT.getTableColumnName());
            String aggregateField = ((String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName())).toUpperCase();

            List<Object> dataRow = new ArrayList<>();
            dataRow.add(subId);
            dataRow.add(accountName);
            dataRow.add(country);
            dataRow.add(countVal);
            dataRow.add(previousCountVal);
            dataRow.add(aggregateField);
            data.add(dataRow);
        }

        response.setData(data);

        return response;
    }

    @Override
    public RiskyTrafficTopSubscriptionsResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : 0L;
    }
}
