package com.illumio.data.service;

import com.illumio.data.model.WidgetBatchRequest;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.WidgetResultWithMetadata;
import com.illumio.data.model.widget.WidgetProcessingRequest;
import com.illumio.data.response.WidgetBatchResponse;
import com.illumio.data.response.WidgetBatchResponse.WidgetResult;
import com.illumio.data.service.batch_widget.PivotWidgetService;
import com.illumio.data.service.batch_widget.WidgetCompressionService;
import com.illumio.data.service.batch_widget.WidgetProcessorService;
import com.illumio.data.service.batch_widget.WidgetRequestCreationService;
import com.illumio.data.service.batch_widget.WidgetResultCombinerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Service for processing batches of widget requests.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WidgetBatchService {
    private final WidgetProcessorService widgetProcessorService;
    private final WidgetRequestCreationService widgetRequestCreationService;
    private final WidgetResultCombinerService widgetResultCombinerService;
    private final PostgresMetadataService postgresMetadataService;
    private final WidgetCompressionService widgetCompressionService;
    private final PivotWidgetService pivotWidgetService;

    // Max number of concurrent widget requests to process
    private static final int MAX_CONCURRENT_WIDGETS = 10;

    /**
     * Process a batch of widget requests with pivot widget support
     *
     * @param tenantId      The tenant ID
     * @param batchRequest  The batch request containing widget IDs and payload
     * @param headers       Request headers
     * @param params        Request parameters
     * @return A Mono emitting the batch response
     */
    public Mono<WidgetBatchResponse> processBatchRequest(
            String tenantId,
            WidgetBatchRequest batchRequest,
            MultiValueMap<String, String> headers,
            MultiValueMap<String, String> params) {

        List<String> widgetIds = batchRequest.getWidgetIdList();
        boolean isCompressed = batchRequest.getCompressed() != null && batchRequest.getCompressed();
        Integer pageSize = batchRequest.getPageSize();
        List<WidgetBatchRequest.PivotWidget> pivotWidgets = batchRequest.getPivotWidget();

        if (widgetIds == null || widgetIds.isEmpty()) {
            return Mono.just(WidgetBatchResponse.builder()
                    .results(List.of(WidgetResult.error("batch", "Widget IDs list cannot be empty")))
                    .build());
        }

        // Check if we have pivot widgets
        if (pivotWidgets != null && !pivotWidgets.isEmpty()) {
            log.info("Processing batch request with {} pivot widgets", pivotWidgets.size());
            return processBatchWithPivotWidgets(tenantId, batchRequest, headers, params, isCompressed);
        } else {
            log.info("Processing batch request without pivot widgets");
            return processRegularBatch(tenantId, batchRequest, headers, params, isCompressed);
        }
    }

    /**
     * Process batch request with pivot widgets
     */
    private Mono<WidgetBatchResponse> processBatchWithPivotWidgets(
            String tenantId,
            WidgetBatchRequest batchRequest,
            MultiValueMap<String, String> headers,
            MultiValueMap<String, String> params,
            boolean isCompressed) {

        // Process pivot widgets first
        return pivotWidgetService.processPivotWidgets(tenantId, batchRequest.getPivotWidget(), batchRequest, headers, params)
                .flatMap(pivotResult -> {
                    // Get widget IDs that are affected by pivot widgets
                    Set<String> pivotAffectedWidgetIds = extractPivotAffectedWidgetIds(pivotResult);
                    
                    // Get remaining widgets that are not affected by pivot widgets
                    List<String> remainingWidgetIds = batchRequest.getWidgetIdList().stream()
                            .filter(widgetId -> !isPivotWidget(widgetId, batchRequest.getPivotWidget()) && !pivotAffectedWidgetIds.contains(widgetId))
                            .collect(Collectors.toList());
                    
                    log.info("Processing {} remaining widgets not affected by pivot widgets", remainingWidgetIds.size());
                    
                    // Process remaining widgets normally
                    Mono<List<WidgetResult>> remainingResults = remainingWidgetIds.isEmpty() 
                            ? Mono.just(List.of())
                            : processWidgetList(tenantId, remainingWidgetIds, batchRequest, headers, params);
                    
                    return remainingResults.map(remaining -> {
                        // Combine all results
                        List<WidgetResult> allResults = List.of(
                                pivotResult.pivotResults(),
                                pivotResult.filteredResults(),
                                remaining
                        ).stream().flatMap(List::stream).collect(Collectors.toList());
                        
                        // Apply compression if requested
                        if (isCompressed) {
                            allResults = allResults.stream()
                                    .map(widgetCompressionService::compressWidgetResult)
                                    .collect(Collectors.toList());
                            log.info("Compressed responses for {} widgets", allResults.size());
                        }
                        
                        return WidgetBatchResponse.builder().results(allResults).build();
                    });
                });
    }

    /**
     * Process regular batch request (without pivot widgets)
     */
    private Mono<WidgetBatchResponse> processRegularBatch(
            String tenantId,
            WidgetBatchRequest batchRequest,
            MultiValueMap<String, String> headers,
            MultiValueMap<String, String> params,
            boolean isCompressed) {

        return processWidgetList(tenantId, batchRequest.getWidgetIdList(), batchRequest, headers, params)
                .map(results -> {
                    // Apply compression if requested
                    if (isCompressed) {
                        results = results.stream()
                                .map(widgetCompressionService::compressWidgetResult)
                                .collect(Collectors.toList());
                        log.info("Compressed responses for {} widgets", results.size());
                    }
                    
                    return WidgetBatchResponse.builder().results(results).build();
                });
    }

    /**
     * Process a list of widget IDs
     */
    private Mono<List<WidgetResult>> processWidgetList(
            String tenantId,
            List<String> widgetIds,
            WidgetBatchRequest batchRequest,
            MultiValueMap<String, String> headers,
            MultiValueMap<String, String> params) {

        if (widgetIds.isEmpty()) {
            return Mono.just(List.of());
        }

        // Process each widget in parallel with bounded concurrency
        return Flux.fromIterable(widgetIds)
                .flatMap(widgetId ->
                        postgresMetadataService.getMetadata(widgetId)
                                .flatMapMany(metadata -> {
                                    List<WidgetProcessingRequest> processingRequests = widgetRequestCreationService.createProcessingRequests(metadata);
                                    log.info("Processing {} widget requests (including direction/category variants)", processingRequests.size());

                                    return Flux.fromIterable(processingRequests)
                                            .flatMap(request ->
                                                            widgetProcessorService.processWidgetRequest(
                                                                    tenantId,
                                                                    request,
                                                                    metadata,
                                                                    batchRequest.getCurrentTimeFrame(),
                                                                    batchRequest.getComparisonTimeFrame(),
                                                                    headers,
                                                                    params,
                                                                    batchRequest.getPageSize()
                                                            ).map(result -> new WidgetResultWithMetadata(metadata, result)),
                                                    MAX_CONCURRENT_WIDGETS
                                            );
                                })
                )
                .collectList()
                .map(results -> {
                    // Combine direction-specific and category-specific results for the same widget ID
                    return widgetResultCombinerService.combineResults(results);
                });
    }

    /**
     * Extract widget IDs that are affected by pivot widgets
     */
    private Set<String> extractPivotAffectedWidgetIds(PivotWidgetService.PivotProcessingResult pivotResult) {
        return pivotResult.filteredResults().stream()
                .map(WidgetResult::getWidgetId)
                .collect(Collectors.toSet());
    }

    /**
     * Check if a widget ID is a pivot widget
     */
    private boolean isPivotWidget(String widgetId, List<WidgetBatchRequest.PivotWidget> pivotWidgets) {
        if (pivotWidgets == null) {
            return false;
        }
        return pivotWidgets.stream()
                .anyMatch(pivot -> pivot.getWidgetId().equals(widgetId));
    }
}