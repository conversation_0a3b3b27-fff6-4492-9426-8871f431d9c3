package com.illumio.data.controller;


import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.constants.RequestMetadata;
import com.illumio.data.model.constants.Template;
import com.illumio.data.service.JWTService;
import com.illumio.data.service.MetricRecordService;
import com.illumio.data.service.RequestRouter;
import com.illumio.data.service.RequestForwardingService;
import com.illumio.data.utils.MetadataHelper;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Optional;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/resource-insights")
public class SentinelController {

    private final RequestRouter requestRouter;
    private final InsightsServiceConfiguration configuration;
    private final MeterRegistry meterRegistry;
    private final MetricRecordService metricRecordService;
    private final RequestForwardingService requestForwardingService;
    private final JWTService jwtService;


    @GetMapping
    public Mono<ResponseEntity<Object>> getResourceInsights(
            @RequestHeader MultiValueMap<String, String> headers,
            @RequestParam MultiValueMap<String, String> params) {
        String authKey = configuration.getApiConfig().getAuthKey();

        Timer.Sample timer = Timer.start(meterRegistry);

        String illumioTenantId = headers.getFirst("x-illumio-tenant-id");
        String requestAuthKey = headers.getFirst("x-auth-key");
        String jwt = headers.getFirst("X-GW");

        String startTime = params.getFirst("after");

        log.debug("Received request with x-illumio-tenant-id: {}, after: {}", illumioTenantId, startTime);
        
        String targetFqdn = configuration.getProxyConfig().getTargetFqdn();

        // Check if request should be forwarded to target FQDN
        if (configuration.getProxyConfig().isEnableProxy()) {
            log.info("Proxy is enabled, forwarding request to target FQDN: {}", targetFqdn);
            return requestForwardingService.forwardRequest(headers, params)
                    .doOnSubscribe(subscription -> log.info("Forwarding Sentinel Request to target FQDN: {}, with Headers: {}, Params: {}", targetFqdn, headers, params))
                    .doOnSuccess(result -> {
                        log.info("Successfully forwarded request for illumio tenant ID: {}", illumioTenantId);
                        // recording metrics for successful forwarded response
                        metricRecordService.recordTimeAndCountMetrics(meterRegistry, timer, MetadataHelper.toMetadataObject(RequestMetadata.RESOURCE_INSIGHTS, Template.TYPE_ONE), "forwarded_success","Time taken to forward Resource Insights request", "resourceInsightsTenant");
                        metricRecordService.recordSizeMetrics(meterRegistry, result, MetadataHelper.toMetadataObject(RequestMetadata.RESOURCE_INSIGHTS, Template.TYPE_ONE), "resourceInsightsTenant", "forwarded_success");
                    })
                    .onErrorResume(error -> {
                        log.error("Error forwarding request: ", error);
                        // recording metrics for forwarded error response
                        metricRecordService.recordTimeAndCountMetrics(meterRegistry, timer, MetadataHelper.toMetadataObject(RequestMetadata.RESOURCE_INSIGHTS, Template.TYPE_ONE), "forwarded_error","Error forwarding request duration per tenantId", "resourceInsightsTenant");
                        
                        ResponseEntity<Object> serverErrorResponse = errorResponse(HttpStatus.BAD_GATEWAY, "Bad Gateway", "Failed to forward request: " + error.getMessage());
                        metricRecordService.recordSizeMetrics(meterRegistry, serverErrorResponse, MetadataHelper.toMetadataObject(RequestMetadata.RESOURCE_INSIGHTS, Template.TYPE_ONE), "resourceInsightsTenant", "forwarded_error");
                        return Mono.just(serverErrorResponse);
                    });
        }

        // Validate mandatory headers
        if (illumioTenantId == null || illumioTenantId.trim().isEmpty()) {
            log.error("Missing required header: x-illumio-tenant-id");
            return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", "Missing required header: x-illumio-tenant-id."));
        }

        //Validate StartTime
        if (params.containsKey("after") && (startTime == null || startTime.trim().isEmpty())) {
            log.error("Missing param value: after (timestamp unix millis)");
            return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", "Missing 'after' param value."));
        }

        // Validate auth, either x-auth-key or X-GW which is a jwt
        if (requestAuthKey != null && !requestAuthKey.trim().isEmpty()) {
            log.info("Auth key present, proceeding with authkey validation");
            
            // Check if auth key is configured
            if (authKey == null || authKey.trim().isEmpty()) {
                log.error("Auth key header provided but auth key is not configured in service");
                return Mono.just(errorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error", 
                    "Service authentication configuration error: auth key not configured"));
            }
            
            // Validate auth key against configured value
            if (!authKey.equals(requestAuthKey)) {
                log.error("Invalid auth key provided");
                return Mono.just(errorResponse(HttpStatus.UNAUTHORIZED, "Unauthorized", "Invalid auth key."));
            }
            log.info("Auth key validation successful");
            
        } else {
            // No auth key, proceed with jwt validation
            if(jwt == null || jwt.trim().isEmpty()) {
                log.error("Missing required header: X-GW");
                return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", "Missing required header: X-GW and no auth key present."));
            } else {
                // Validate jwt
                try {
                    // TODO: Once X-Illumio-Tenant-Id is unified with the JWT tenant id, we can pass the illumioTenantId as a parameter to the validateJWT method
                    Pair<Boolean, String> jwtValidationResult = jwtService.validateJWT(headers, null, "X-GW", "tenant", false);
                    if(!jwtValidationResult.getLeft()) {
                        log.error("Invalid JWT provided, message: " + jwtValidationResult.getRight());
                        return Mono.just(errorResponse(HttpStatus.UNAUTHORIZED, "Unauthorized", "Invalid JWT. Error: " + jwtValidationResult.getRight()));
                    }
                    log.info("JWT validation successful");
                    log.info("JWT validation result: {}", jwtValidationResult.getRight());
                } catch (IllegalArgumentException e) {
                    log.error("Failed to base64 decode illumio tenant ID: {}", e.getMessage());
                    return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", "Invalid base64 encoded illumio tenant ID."));
                }

            }
        }


        RequestContext requestContext = RequestContext.builder()
                .tenantId(Optional.empty())
                .pageId(Optional.empty())
                .widgetId(Optional.empty())
                .requestPayload(Optional.empty())
                .headers(Optional.of(headers))
                .queryParams(Optional.of(params))
                .build();

        // Route request
        return requestRouter.routeGetRequest(RequestMetadata.RESOURCE_INSIGHTS, requestContext)
                .doOnSubscribe(subscription -> log.info("Received Msg Insights Request with Headers: {}, Params: {}", headers, params))
                .doOnSuccess(result -> {
                    log.debug("Successfully processed request for illumio tenant ID: {}", illumioTenantId);
                    // recording metrics for successful response
                    metricRecordService.recordTimeAndCountMetrics(meterRegistry, timer, MetadataHelper.toMetadataObject(RequestMetadata.RESOURCE_INSIGHTS, Template.TYPE_ONE), "success","Time taken to process Resource Insights request", "resourceInsightsTenant");
                    metricRecordService.recordSizeMetrics(meterRegistry, result, MetadataHelper.toMetadataObject(RequestMetadata.RESOURCE_INSIGHTS, Template.TYPE_ONE), "resourceInsightsTenant", "success");
                })
                .onErrorResume(error -> {
                    log.error("Error processing request: ", error);
                    // recording metrics for error response
                    metricRecordService.recordTimeAndCountMetrics(meterRegistry, timer, MetadataHelper.toMetadataObject(RequestMetadata.RESOURCE_INSIGHTS, Template.TYPE_ONE), "error","Error request duration per tenantId, pageId and widgetId", "resourceInsightsTenant");

                    if (error instanceof IllegalArgumentException) {
                        ResponseEntity<Object> badRequestResponse = errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", error.getMessage());
                        metricRecordService.recordSizeMetrics(meterRegistry, badRequestResponse, MetadataHelper.toMetadataObject(RequestMetadata.RESOURCE_INSIGHTS, Template.TYPE_ONE), "resourceInsightsTenant", "error");
                        return Mono.just(badRequestResponse);
                    }

                    ResponseEntity<Object> serverErrorResponse = errorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error", "Something went wrong: " + error.getMessage());
                    metricRecordService.recordSizeMetrics(meterRegistry, serverErrorResponse, MetadataHelper.toMetadataObject(RequestMetadata.RESOURCE_INSIGHTS, Template.TYPE_ONE), "resourceInsightsTenant", "error");
                    return Mono.just(serverErrorResponse);
                });
    }

    private ResponseEntity<Object> errorResponse(HttpStatus status, String error, String message) {
        return ResponseEntity.status(status).body(Map.of("error", error, "message", message));
    }
}
