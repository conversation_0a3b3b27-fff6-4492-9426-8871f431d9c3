package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.service.batch_widget.PivotExtractable;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.AGGREGATED_BYTES;
import static com.illumio.data.model.constants.Fields.AGGREGATED_FLOWS;
import static com.illumio.data.model.constants.Fields.DESTINATION_ROLE;
import static com.illumio.data.model.constants.Fields.PREVIOUS_AGGREGATED_BYTES;
import static com.illumio.data.model.constants.Fields.PREVIOUS_AGGREGATED_FLOWS;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_CATEGORY;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_ID;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_NAME;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_TYPE;
import static com.illumio.data.model.constants.Fields.TOTAL_ROWS;

@Data
@NoArgsConstructor
@Component
public class DestinationRoleLevelTrafficResponse implements ResponseBuilder<DestinationRoleLevelTrafficResponse>, PivotExtractable {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public DestinationRoleLevelTrafficResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        DestinationRoleLevelTrafficResponse response = new DestinationRoleLevelTrafficResponse();

        //Set Widget Specific Metadata
        response.setWidgetId(requestMetadata.getWidgetId());
        response.setTitle("Destination Role Level Traffic");
        response.setColumns(Arrays.asList(SOURCE_RESOURCE_ID.getFieldKey(), SOURCE_RESOURCE_NAME.getFieldKey(), SOURCE_RESOURCE_CATEGORY.getFieldKey(), SOURCE_RESOURCE_TYPE.getFieldKey(), DESTINATION_ROLE.getFieldKey(), PREVIOUS_AGGREGATED_FLOWS.getFieldKey(), AGGREGATED_FLOWS.getFieldKey(), PREVIOUS_AGGREGATED_BYTES.getFieldKey(), AGGREGATED_BYTES.getFieldKey()));
        response.setColumnTypes(Arrays.asList(SOURCE_RESOURCE_ID.getFieldType(), SOURCE_RESOURCE_NAME.getFieldType(), SOURCE_RESOURCE_CATEGORY.getFieldType(), SOURCE_RESOURCE_TYPE.getFieldType(), DESTINATION_ROLE.getFieldType(), PREVIOUS_AGGREGATED_FLOWS.getFieldType(), AGGREGATED_FLOWS.getFieldType(), PREVIOUS_AGGREGATED_BYTES.getFieldType(), AGGREGATED_BYTES.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(SOURCE_RESOURCE_ID.getFieldDisplayName(), SOURCE_RESOURCE_NAME.getFieldDisplayName(), SOURCE_RESOURCE_CATEGORY.getFieldDisplayName(), SOURCE_RESOURCE_TYPE.getFieldDisplayName(), DESTINATION_ROLE.getFieldDisplayName(), PREVIOUS_AGGREGATED_FLOWS.getFieldDisplayName(), AGGREGATED_FLOWS.getFieldDisplayName(), PREVIOUS_AGGREGATED_BYTES.getFieldDisplayName(), AGGREGATED_BYTES.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        List<List<Object>> data = new ArrayList<>();
        while (resultSetTable.next()) {
            String sourceResId = (String) resultSetTable.getObject(SOURCE_RESOURCE_ID.getTableColumnName());
            String sourceResName = (String) resultSetTable.getObject(SOURCE_RESOURCE_NAME.getTableColumnName());
            String sourceResCat = (String) resultSetTable.getObject(SOURCE_RESOURCE_CATEGORY.getTableColumnName());
            String srcResType = (String) resultSetTable.getObject(SOURCE_RESOURCE_TYPE.getTableColumnName());;
            String destRole = (String) resultSetTable.getObject(DESTINATION_ROLE.getTableColumnName());
            Long flowCount = getLongValue(resultSetTable, AGGREGATED_FLOWS.getTableColumnName());
            Long bytes = getLongValue(resultSetTable, AGGREGATED_BYTES.getTableColumnName());
            Long prevFlowCount = getLongValue(resultSetTable, PREVIOUS_AGGREGATED_FLOWS.getTableColumnName());
            Long prevBytes = getLongValue(resultSetTable, PREVIOUS_AGGREGATED_BYTES.getTableColumnName());

            List<Object> dataRow = new ArrayList<>();
            dataRow.add(sourceResId);
            dataRow.add(sourceResName);
            dataRow.add(sourceResCat);
            dataRow.add(srcResType);
            dataRow.add(destRole);
            dataRow.add(prevFlowCount);
            dataRow.add(flowCount);
            dataRow.add(prevBytes);
            dataRow.add(bytes);
            data.add(dataRow);

            //Set total pages = (int) ceil(total_rows / rows per page). Set this only once in the response.
            if (resultSetTable.isLast()) {
                response.pagination.setTotalPages((int) Math.ceil((double)resultSetTable.getInt(TOTAL_ROWS.getTableColumnName()) / response.pagination.getRowLimit()));
            }
        }
        response.setData(data);

        return response;
    }

    @Override
    public DestinationRoleLevelTrafficResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }
    
    /**
     * Converts the response data to CSV format for efficient consumption.
     * 
     * @return A Map containing the CSV data and metadata
     */
    public Map<String, Object> compress() {
        Map<String, Object> result = new HashMap<>();
        StringBuilder csvData = new StringBuilder();
        
        // Add CSV header based on column display names
        csvData.append("SourceResourceID,SourceResourceName,SourceResourceCategory,SourceResourceType,DestinationRole,PreviousFlows,Flows,PreviousBytes,Bytes\n");
        
        // Add data rows
        if (data != null && !data.isEmpty()) {
            for (List<Object> row : data) {
                if (row.size() < 9) continue;
                
                String resId = (String) row.get(0);
                String resName = (String) row.get(1);
                String resCat = (String) row.get(2);
                String resType = (String) row.get(3);
                String role = (String) row.get(4);
                Long prevFlows = (row.get(5) != null) ? ((Number) row.get(5)).longValue() : null;
                Long flows = (row.get(6) != null) ? ((Number) row.get(6)).longValue() : null;
                Long prevBytes = (row.get(7) != null) ? ((Number) row.get(7)).longValue() : null;
                Long bytes = (row.get(8) != null) ? ((Number) row.get(8)).longValue() : null;
                
                // Escape fields for CSV format
                String escapedResId = escapeCsvField(resId);
                String escapedResName = escapeCsvField(resName);
                String escapedResCat = escapeCsvField(resCat);
                String escapedResType = escapeCsvField(resType);
                String escapedRole = escapeCsvField(role);
                
                // Add row to CSV
                csvData.append(escapedResId).append(",")
                       .append(escapedResName).append(",")
                       .append(escapedResCat).append(",")
                       .append(escapedResType).append(",")
                       .append(escapedRole).append(",")
                       .append(prevFlows != null ? prevFlows : 0).append(",")
                       .append(flows != null ? flows : 0).append(",")
                       .append(prevBytes != null ? prevBytes : 0).append(",")
                       .append(bytes != null ? bytes : 0).append("\n");
            }
        }
        
        // Add metadata
        if (currentTimeFrame != null) {
            result.put("currentTimeFrameStart", currentTimeFrame.getStartTime());
            result.put("currentTimeFrameEnd", currentTimeFrame.getEndTime());
        }
        
        if (comparisonTimeFrame != null) {
            result.put("comparisonTimeFrameStart", comparisonTimeFrame.getStartTime());
            result.put("comparisonTimeFrameEnd", comparisonTimeFrame.getEndTime());
        }
        
        if (pagination != null) {
            result.put("pageNumber", pagination.getPageNumber());
            result.put("totalPages", pagination.getTotalPages());
        }
        
        // Add CSV data and format
        result.put("csvData", csvData.toString());
        result.put("format", "csv");
        result.put("widgetId", widgetId);
        result.put("title", title);
        
        return result;
    }
    
    /**
     * Properly escapes a field value for CSV format according to RFC 4180:
     * - If the field contains commas, newlines, or double quotes, it should be enclosed in double quotes
     * - Double quotes within the field should be escaped by doubling them
     * 
     * @param value The field value to escape
     * @return The properly escaped CSV field
     */
    private String escapeCsvField(String value) {
        if (value == null) {
            return "";
        }
        
        boolean needsQuoting = value.contains(",") || value.contains("\"") || 
                               value.contains("\n") || value.contains("\r");
        
        if (needsQuoting) {
            // Replace any double quotes with two double quotes
            String escapedValue = value.replace("\"", "\"\"");
            return "\"" + escapedValue + "\"";
        } else {
            return value;
        }
    }
}