package com.illumio.data.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InsightSummaryResponse {
    
    @JsonProperty("ReportId")
    private String reportId;
    
    @JsonProperty("SummaryStartDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime summaryStartDate;
    
    @JsonProperty("SummaryEndDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime summaryEndDate;
    
    @JsonProperty("ComparisonStartDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime comparisonStartDate;
    
    @JsonProperty("ComparisonEndDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime comparisonEndDate;
    
    @JsonProperty("CreatedAt")
    private Long createdAt;
    
    @JsonProperty("Persona")
    private String persona;
    
    @JsonProperty("DataSources")
    private List<String> dataSources;
    
    @JsonProperty("ReportPayload")
    private String reportPayload;
    
    @JsonProperty("IllumioTenantId")
    private String illumioTenantId;
    
    @JsonProperty("Tags")
    private List<String> tags;
}
