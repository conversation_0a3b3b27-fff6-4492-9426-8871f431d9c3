package com.illumio.data.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WidgetBatchResponse {
    @Builder.Default
    private List<WidgetResult> results = new ArrayList<>();

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WidgetResult {
        private String widgetId;
        private String status;
        private Object data;
        private String message;

        public static WidgetResult success(String widgetId, Object data) {
            return WidgetResult.builder()
                    .widgetId(widgetId)
                    .status("success")
                    .data(data)
                    .build();
        }

        public static WidgetResult error(String widgetId, String message) {
            return WidgetResult.builder()
                    .widgetId(widgetId)
                    .status("error")
                    .message(message)
                    .build();
        }
    }
} 