package com.illumio.data.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.service.RiskyServiceInfo;
import com.illumio.data.service.batch_widget.PivotExtractable;
import com.illumio.data.service.batch_widget.ResponseCompressor;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.COMPOUND_SERVICE;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.IP;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;
import static com.illumio.data.model.constants.Fields.PREVIOUS_THREAT_LEVEL;
import static com.illumio.data.model.constants.Fields.SOURCE_IP;
import static com.illumio.data.model.constants.Fields.THREAT_LEVEL;

@JsonIgnoreProperties({"riskyServiceInfo"})
@Data
@Component
public class TopMaliciousIpsResponse implements ResponseBuilder<TopMaliciousIpsResponse>, PivotExtractable {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    private final RiskyServiceInfo riskyServiceInfo;

    public TopMaliciousIpsResponse(RiskyServiceInfo riskyServiceInfo) {
        this.riskyServiceInfo = riskyServiceInfo;
    }

    @Override
    public TopMaliciousIpsResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        TopMaliciousIpsResponse response = new TopMaliciousIpsResponse(riskyServiceInfo);

        //Set Widget Specific Metadata
        response.setWidgetId(requestMetadata.getWidgetId());
        response.setTitle("Top Malicious IPs");
        response.setColumns(Arrays.asList(SOURCE_IP.getFieldKey(), AGGREGATE_FIELD.getFieldKey(), COUNT.getFieldKey(), PREVIOUS_COUNT.getFieldKey(), THREAT_LEVEL.getFieldKey(), PREVIOUS_THREAT_LEVEL.getFieldKey(), COMPOUND_SERVICE.getFieldKey()));
        response.setColumnTypes(Arrays.asList(SOURCE_IP.getFieldType(), AGGREGATE_FIELD.getFieldType(), COUNT.getFieldType(), PREVIOUS_COUNT.getFieldType(), THREAT_LEVEL.getFieldType(), PREVIOUS_THREAT_LEVEL.getFieldType(), COMPOUND_SERVICE.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(SOURCE_IP.getFieldDisplayName(), AGGREGATE_FIELD.getFieldDisplayName(), COUNT.getFieldDisplayName(), PREVIOUS_COUNT.getFieldDisplayName(), THREAT_LEVEL.getFieldDisplayName(), PREVIOUS_THREAT_LEVEL.getFieldDisplayName(), COMPOUND_SERVICE.getFieldDisplayName()));
        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        Map<List<Object>, Map<String, Object>> groupedData = new LinkedHashMap<>();

        while (resultSetTable.next()) {
            String ip = (String) resultSetTable.getObject(IP.getTableColumnName());
            String aggregateField = ((String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName())).toUpperCase();
            Integer threatLevel = (Integer) resultSetTable.getObject(THREAT_LEVEL.getTableColumnName());
            Integer previousThreatLevel = (Integer) resultSetTable.getObject(THREAT_LEVEL.getTableColumnName());

            Long count = getLongValue(resultSetTable, COUNT.getTableColumnName());
            Long prevCount = getLongValue(resultSetTable, PREVIOUS_COUNT.getTableColumnName());

            String proto = (String) resultSetTable.getObject("Proto");
            Integer port = (Integer) resultSetTable.getObject("Port");
            String service = Optional.ofNullable(port)
                    .flatMap(p -> Optional.ofNullable(proto)
                            .flatMap(pr -> Optional.ofNullable(
                                            riskyServiceInfo.getPortProtoToServiceInfoMap().get(new RiskyServiceInfo.PortProtocolPair(p, pr.toUpperCase())))
                                    .map(RiskyServiceInfo.ServiceInfo::getService)))
                    .orElse("UNKNOWN");

            // Group only by IP, aggregateField, and threat levels (not by count)
            List<Object> key = Arrays.asList(
                    ip,
                    aggregateField,
                    threatLevel,
                    previousThreatLevel
            );

            Map<String, Object> ipData = groupedData.computeIfAbsent(key, k -> {
                Map<String, Object> data = new LinkedHashMap<>();
                data.put("totalCount", 0L);
                data.put("totalPrevCount", 0L);
                data.put("services", new ArrayList<List<Object>>());
                return data;
            });

            // Sum up counts
            long totalCount = (Long) ipData.get("totalCount") + (count != null ? count : 0L);
            long totalPrevCount = (Long) ipData.get("totalPrevCount") + (prevCount != null ? prevCount : 0L);
            ipData.put("totalCount", totalCount);
            ipData.put("totalPrevCount", totalPrevCount);

            // Add service to list
            ((List<List<Object>>) ipData.get("services")).add(Arrays.asList(
                    service,
                    proto != null ? proto : "",
                    port != null ? port : 0L
            ));
        }

        List<List<Object>> data = new ArrayList<>();
        for (Map.Entry<List<Object>, Map<String, Object>> entry : groupedData.entrySet()) {
            List<Object> groupKey = entry.getKey();
            Map<String, Object> ipData = entry.getValue();
            
            // Create row with the IP data: IP, aggregateField, totalCount, totalPrevCount, threatLevel, prevThreatLevel
            List<Object> row = new ArrayList<>();
            row.add(groupKey.get(0)); // IP
            row.add(groupKey.get(1)); // aggregateField
            row.add(ipData.get("totalCount")); // summed count
            row.add(ipData.get("totalPrevCount")); // summed prev count
            row.add(groupKey.get(2)); // threatLevel
            row.add(groupKey.get(3)); // prevThreatLevel
            
            // Add all services for this IP
            List<List<Object>> services = (List<List<Object>>) ipData.get("services");
            row.addAll(services);
            
            data.add(row);
        }
        
        // Sort by totalCount (index 2) in descending order
        data.sort((row1, row2) -> {
            Long count1 = (Long) row1.get(2);
            Long count2 = (Long) row2.get(2);
            return Long.compare(count2, count1); // Descending order
        });
        
        response.setData(data);
        return response;
    }

    @Override
    public TopMaliciousIpsResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }
    
    /**
     * Converts the response data to CSV format for efficient consumption.
     * Uses ResponseCompressor utility for maximum robustness against structural changes.
     * 
     * @return A Map containing the CSV data and metadata
     */
    public Map<String, Object> compress() {
        return ResponseCompressor.compressToCSV(
                widgetId,
                title,
                columns,
                columnDisplayNames,
                data,
                currentTimeFrame,
                comparisonTimeFrame,
                pagination
        );
    }
}