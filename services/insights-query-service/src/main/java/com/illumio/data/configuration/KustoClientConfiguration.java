package com.illumio.data.configuration;

import com.microsoft.azure.kusto.data.Client;
import com.microsoft.azure.kusto.data.ClientFactory;
import com.microsoft.azure.kusto.data.StringUtils;
import com.microsoft.azure.kusto.data.auth.ConnectionStringBuilder;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.net.URISyntaxException;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class KustoClientConfiguration {
    private final InsightsServiceConfiguration insightsConfig;

    @Bean(name = "kustoClient")
    @SneakyThrows
    public Client kustoClient() {
        return createKustoClient(insightsConfig.getKustoConfig());
    }

    @Bean(name = "kustoInsightsClient")
    @SneakyThrows
    public Client kustoInsightsClient() {
        return createKustoClient(insightsConfig.getKustoInsightsConfig());
    }

    private Client createKustoClient(InsightsServiceConfiguration.KustoConfiguration kustoConfig) throws URISyntaxException {
        ConnectionStringBuilder connectionStringBuilder;
        if (Optional.ofNullable(kustoConfig)
                .map(InsightsServiceConfiguration.KustoConfiguration::getIsManagedIdentity)
                .orElse(false)) {
            if (StringUtils.isBlank(
                    kustoConfig.getManagedIdentityClientId())) {
                connectionStringBuilder =
                        ConnectionStringBuilder.createWithAadManagedIdentity(
                                kustoConfig.getClusterUri());
            } else {
                connectionStringBuilder =
                        ConnectionStringBuilder.createWithAadManagedIdentity(
                                kustoConfig.getClusterUri(),
                                kustoConfig.getManagedIdentityClientId());
            }
        } else {
            connectionStringBuilder =
                    ConnectionStringBuilder.createWithAadApplicationCredentials(
                            kustoConfig.getClusterUri(),
                            kustoConfig.getAzureClientId(),
                            kustoConfig.getAzureClientSecret(),
                            kustoConfig.getAzureTenantId());
        }
        return ClientFactory.createClient(connectionStringBuilder);
    }
}
