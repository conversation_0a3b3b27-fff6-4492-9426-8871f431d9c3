package com.illumio.data.repository.iva;

import com.illumio.data.model.ReportTagEntity;
import org.springframework.data.r2dbc.repository.Query;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

import java.util.UUID;

@Repository
public interface ReportTagRepository extends ReactiveCrudRepository<ReportTagEntity, UUID> {

    /**
     * Find all tags associated with a specific report
     */
    @Query("SELECT rt.* FROM report_tag rt " +
           "INNER JOIN report_tag_map rtm ON rt.tag_id = rtm.tag_id " +
           "WHERE rtm.report_id = :reportId " +
           "AND rt.deleted = false")
    Flux<ReportTagEntity> findTagsByReportId(Integer reportId);

    /**
     * Find all tags for a tenant (both system-defined and tenant-specific)
     */
    @Query("SELECT * FROM report_tag WHERE " +
           "(tenant_id = :tenantId OR system_defined = true) " +
           "AND deleted = false " +
           "ORDER BY created_time DESC")
    Flux<ReportTagEntity> findTagsByTenantId(UUID tenantId);
}
