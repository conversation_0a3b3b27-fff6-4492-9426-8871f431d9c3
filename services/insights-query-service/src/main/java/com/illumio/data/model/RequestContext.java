package com.illumio.data.model;

import lombok.Builder;
import lombok.Data;
import org.springframework.util.MultiValueMap;

import java.util.Optional;

@Data
@Builder(toBuilder = true)
public class RequestContext {
    @Builder.Default
    private final Optional<String> tenantId = Optional.empty();

    @Builder.Default
    private final Optional<String> pageId = Optional.empty();

    @Builder.Default
    private final Optional<String> widgetId = Optional.empty();

    @Builder.Default
    private final Optional<RequestPayload> requestPayload = Optional.empty();

    @Builder.Default
    private final Optional<MultiValueMap<String, String>> headers = Optional.empty();

    @Builder.Default
    private final Optional<MultiValueMap<String, String>> queryParams = Optional.empty();

    @Builder.Default
    private final Optional<DerivedMetadata> derivedMetadata = Optional.empty();
}
