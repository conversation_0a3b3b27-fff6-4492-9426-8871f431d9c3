package com.illumio.data.service.batch_widget;

import java.util.List;

/**
 * Interface for response objects that support pivot data extraction.
 * Response classes implementing this interface can be used as pivot widgets
 * for extracting top K values from specific fields.
 */
public interface PivotExtractable {
    
    /**
     * Get the column names for this response.
     * 
     * @return List of column names in order
     */
    List<String> getColumns();
    
    /**
     * Get the data rows for this response.
     * 
     * @return List of data rows, where each row is a list of objects corresponding to the columns
     */
    List<List<Object>> getData();
    
    /**
     * Get a human-readable name for this response type (used for logging).
     * 
     * @return Response type name
     */
    default String getResponseTypeName() {
        return this.getClass().getSimpleName();
    }
} 