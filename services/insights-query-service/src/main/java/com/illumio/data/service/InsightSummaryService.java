package com.illumio.data.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.illumio.data.response.InsightSummaryResponse;
import com.illumio.data.model.ReportSummaryEntity;
import com.illumio.data.model.ReportTagEntity;
import com.illumio.data.repository.iva.ReportSummaryRepository;
import com.illumio.data.repository.iva.ReportTagRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.ZoneOffset;
import java.util.List;
import java.util.UUID;


@Slf4j
@Service
@RequiredArgsConstructor
public class InsightSummaryService {

    private final ReportSummaryRepository reportSummaryRepository;
    private final ReportTagRepository reportTagRepository;
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Retrieve the most recent insight summary with optional filtering
     * Returns single report as per API specification
     */
    public Mono<InsightSummaryResponse> getInsightSummary(
            String tenantId,
            String persona,
            String reportInterval) {

        log.debug("Getting insight summary for tenant: {}, persona: {}, report_interval: {}", 
                  tenantId, persona, reportInterval);

        UUID tenantUuid;
        try {
            tenantUuid = UUID.fromString(tenantId);
        } catch (IllegalArgumentException e) {
            log.error("Invalid tenant ID format: {}", tenantId);
            return Mono.error(new IllegalArgumentException("Invalid tenant ID format"));
        }

        // Choose appropriate repository method based on report interval
        Mono<ReportSummaryEntity> reportMono;
        
        if ("weekly".equals(reportInterval)) {
            log.debug("Processing weekly report request");
            reportMono = reportSummaryRepository.findMostRecentWeeklyReportByTenantAndPersona(tenantUuid, persona);
        } else {
            // Default to daily reports (includes null, empty, or "daily")
            log.debug("Processing daily report request");
            reportMono = reportSummaryRepository.findMostRecentDailyReportByTenantAndPersona(tenantUuid, persona);
        }

        return reportMono
                .flatMap(this::convertToResponse)
                .doOnNext(response -> log.debug("Retrieved insight summary: {}", response.getReportId()))
                .switchIfEmpty(Mono.empty())
                .onErrorResume(error -> {
                    log.error("Error retrieving insight summary for tenant {}: {}", tenantId, error.getMessage());
                    return Mono.error(error);
                });
    }

    /**
     * Get a specific insight summary by report ID
     */
    public Mono<InsightSummaryResponse> getInsightSummaryById(String tenantId, Integer reportId) {
        UUID tenantUuid;
        try {
            tenantUuid = UUID.fromString(tenantId);
        } catch (IllegalArgumentException e) {
            return Mono.error(new IllegalArgumentException("Invalid tenant ID format"));
        }

        return reportSummaryRepository
                .findByReportIdAndTenantId(reportId, tenantUuid)
                .flatMap(this::convertToResponse)
                .switchIfEmpty(Mono.error(new RuntimeException("Report not found or access denied")));
    }

    /**
     * Convert entity to response DTO
     */
    private Mono<InsightSummaryResponse> convertToResponse(ReportSummaryEntity entity) {
        return reportTagRepository
                .findTagsByReportId(entity.getReportId())
                .map(ReportTagEntity::getDisplayName)
                .collectList()
                .map(tagNames -> buildResponse(entity, tagNames))
                .onErrorResume(error -> {
                    log.warn("Error retrieving tags for report {}: {}", entity.getReportId(), error.getMessage());
                    // Return response without tags if tag retrieval fails
                    return Mono.just(buildResponse(entity, List.of()));
                });
    }

    /**
     * Build the response DTO from entity and tags
     */
    private InsightSummaryResponse buildResponse(ReportSummaryEntity entity, List<String> tagNames) {
        return InsightSummaryResponse.builder()
                .reportId(entity.getReportId().toString())
                .summaryStartDate(entity.getCurrentStartTime().toLocalDateTime())
                .summaryEndDate(entity.getCurrentEndTime().toLocalDateTime())
                .comparisonStartDate(entity.getComparisonStartTime().toLocalDateTime())
                .comparisonEndDate(entity.getComparisonEndTime().toLocalDateTime())
                .createdAt(entity.getCreatedAt().toEpochSecond() * 1000) // Unix millis
                .persona(entity.getPersona())
                .dataSources(extractDataSources(entity.getReportPayload()))
                .reportPayload(cleanReportPayload(entity.getReportPayload(), entity))
                .illumioTenantId(entity.getTenantId().toString())
                .tags(tagNames)
                .build();
    }

    /**
     * Extract data sources from report payload JSONB
     */
    private List<String> extractDataSources(String reportPayloadJson) {
        try {
            JsonNode payloadNode = objectMapper.readTree(reportPayloadJson);
            JsonNode dataSourcesNode = payloadNode.get("data_sources");
            
            if (dataSourcesNode != null && dataSourcesNode.isArray()) {
                return objectMapper.convertValue(dataSourcesNode, objectMapper.getTypeFactory().constructCollectionType(List.class, String.class));
            }
            
            // Default data sources if not specified in payload
            return List.of("DecoratedFlows", "ThreatIntelligence");
            
        } catch (Exception e) {
            log.warn("Error parsing report payload for data sources: {}", e.getMessage());
            return List.of("DecoratedFlows"); // Fallback
        }
    }

    /**
     * Clean report payload by removing widgets from indicators_of_compromise and adding database fields
     */
    private String cleanReportPayload(String reportPayloadJson, ReportSummaryEntity entity) {
        try {
            JsonNode payloadNode = objectMapper.readTree(reportPayloadJson);
            ObjectNode payloadObject = (ObjectNode) payloadNode;
            
            // Remove widgets from indicators_of_compromise
            JsonNode iocArray = payloadNode.get("indicators_of_compromise");
            if (iocArray != null && iocArray.isArray()) {
                for (JsonNode iocNode : iocArray) {
                    if (iocNode.isObject()) {
                        ((ObjectNode) iocNode).remove("widgets");
                        ((ObjectNode) iocNode).remove("widget_ids");
                    }
                }
            }
            
            // Add database fields to the payload (convert to UTC to match database values)
            payloadObject.put("comparison_start_time", entity.getComparisonStartTime().atZoneSameInstant(ZoneOffset.UTC).toString());
            payloadObject.put("comparison_end_time", entity.getComparisonEndTime().atZoneSameInstant(ZoneOffset.UTC).toString());
            payloadObject.put("current_start_time", entity.getCurrentStartTime().atZoneSameInstant(ZoneOffset.UTC).toString());
            payloadObject.put("current_end_time", entity.getCurrentEndTime().atZoneSameInstant(ZoneOffset.UTC).toString());
            payloadObject.put("persona", entity.getPersona());
            payloadObject.put("generated_time", entity.getCreatedAt().atZoneSameInstant(ZoneOffset.UTC).toString());
            if (entity.getReportGenerateMethod() != null) {
                payloadObject.put("report_generate_method", entity.getReportGenerateMethod());
            }
            
            return objectMapper.writeValueAsString(payloadObject);
            
        } catch (Exception e) {
            log.warn("Error cleaning report payload, returning original: {}", e.getMessage());
            return reportPayloadJson; // Return original if parsing fails
        }
    }
}
