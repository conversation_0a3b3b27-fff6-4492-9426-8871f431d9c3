package com.illumio.data.service;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.illumio.data.model.constants.Fields.AGGREGATED_BYTES;

@Service
public class DefaultValues {

    // Sets default values for a request payload
    public void setPayloadDefaults(InsightsServiceConfiguration config, RequestPayload payload){
        // Pagination default values
        if (payload.getPagination() == null) {
            payload.setPagination(Pagination.builder().build());
        }

        if (payload.getPagination().getRowLimit() < -1) {
            payload.getPagination().setRowLimit(config.getPaginationConfig().getDefaultPageSize());
        }
        if (payload.getPagination().getRowLimit() > config.getPaginationConfig().getMaxPageSize()) {
            payload.getPagination().setRowLimit(config.getPaginationConfig().getMaxPageSize());
        }
        if (payload.getPagination().getPageNumber() < 1) {
            payload.getPagination().setPageNumber(config.getPaginationConfig().getDefaultPageNumber());
        }

        // Sort order default values
        if (payload.getSortByFields() == null || payload.getSortByFields().isEmpty()) {
            payload.setSortByFields(List.of(SortByFields.builder().field(AGGREGATED_BYTES.getFieldKey()).order("desc").build()));
        }
    }
}
