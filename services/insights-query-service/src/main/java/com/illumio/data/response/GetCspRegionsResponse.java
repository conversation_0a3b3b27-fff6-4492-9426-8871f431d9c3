package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.constants.Metadata;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
public class GetCspRegionsResponse implements ResponseBuilder<GetCspRegionsResponse.BasicResponse> {
    @Override
    public BasicResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        List<ZoneRegion> data = new java.util.ArrayList<>();
        while (resultSetTable.next()) {
            Object region = resultSetTable.getObject("Region");
            Object zone = resultSetTable.getObject("Zone");
            if (region != null && zone != null) {
                String regionStr = region.toString();
                String zoneStr = zone.toString();
                if (!regionStr.isEmpty() && !zoneStr.isEmpty()) {
                    data.add(new ZoneRegion(zoneStr, regionStr));
                }
            }
        }
        return new BasicResponse(data);
    }

    @Override
    public BasicResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        // For now, just return an empty structure
        return new BasicResponse(Collections.emptyList());
    }

    public static class BasicResponse {
        public List<?> data;
        public BasicResponse(List<?> data) {
            this.data = data;
        }
        public List<?> getData() { return data; }
        public void setData(List<?> data) { this.data = data; }
    }

    public static class ZoneRegion {
        public String zone;
        public String region;
        public ZoneRegion(String zone, String region) {
            this.zone = zone;
            this.region = region;
        }
        public String getZone() { return zone; }
        public String getRegion() { return region; }
        public void setZone(String zone) { this.zone = zone; }
        public void setRegion(String region) { this.region = region; }
    }
} 