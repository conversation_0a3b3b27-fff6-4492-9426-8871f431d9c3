package com.illumio.data.repository;

import com.illumio.data.model.RequestMetadataTableEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface RequestMetadataRepository extends ReactiveCrudRepository<RequestMetadataTableEntity, String> {

    Mono<RequestMetadataTableEntity> findByWidgetId(String widgetId);
    
    Flux<RequestMetadataTableEntity> findByPageId(String pageId);
}
