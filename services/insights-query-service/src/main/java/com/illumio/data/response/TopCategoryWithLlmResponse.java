package com.illumio.data.response;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.service.batch_widget.PivotExtractable;
import lombok.extern.slf4j.Slf4j;
import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.util.LinkedHashMap;
import java.util.HashMap;
import java.lang.StringBuilder;

import static com.illumio.data.model.constants.Fields.CATEGORY_ID;
import static com.illumio.data.model.constants.Fields.ACCOUNT_NAME;
import static com.illumio.data.model.constants.Fields.CLOUD_PROVIDER;
import static com.illumio.data.model.constants.Fields.DESTINATION_EXTERNAL_LABEL;
import static com.illumio.data.model.constants.Fields.LLM;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;
import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;

@Data
@Component
@Slf4j
public class TopCategoryWithLlmResponse implements ResponseBuilder<TopCategoryWithLlmResponse>, PivotExtractable {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public TopCategoryWithLlmResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        TopCategoryWithLlmResponse response = new TopCategoryWithLlmResponse();

        //Set Widget Specific Metadata
        response.setWidgetId(requestMetadata.getWidgetId());
        response.setTitle("Top Category With LLM Usage");
        response.setColumns(Arrays.asList(CATEGORY_ID.getFieldKey(), ACCOUNT_NAME.getFieldKey(), CLOUD_PROVIDER.getFieldKey(), LLM.getFieldKey(), COUNT.getFieldKey(), PREVIOUS_COUNT.getFieldKey(), AGGREGATE_FIELD.getFieldKey()));
        response.setColumnTypes(Arrays.asList(CATEGORY_ID.getFieldType(), ACCOUNT_NAME.getFieldType(), CLOUD_PROVIDER.getFieldType(), LLM.getFieldType(), COUNT.getFieldType(), PREVIOUS_COUNT.getFieldType(), AGGREGATE_FIELD.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(CATEGORY_ID.getFieldDisplayName(), ACCOUNT_NAME.getFieldDisplayName(), CLOUD_PROVIDER.getFieldDisplayName(), LLM.getFieldDisplayName(), COUNT.getFieldDisplayName(), PREVIOUS_COUNT.getFieldDisplayName(), AGGREGATE_FIELD.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        Map<List<Object>, List<List<Object>>> groupedData = new LinkedHashMap<>();
        Map<String, List<Long>> categoryCount = new LinkedHashMap<>();
        while (resultSetTable.next()) {
            String categoryId = (String) resultSetTable.getObject(CATEGORY_ID.getTableColumnName());
            String accountName = ((String) resultSetTable.getObject(ACCOUNT_NAME.getTableColumnName())).toLowerCase();
            String cloudProvider = ((String) resultSetTable.getObject(CLOUD_PROVIDER.getTableColumnName())).toLowerCase();
            String llm = ((String) resultSetTable.getObject(DESTINATION_EXTERNAL_LABEL.getTableColumnName())).toLowerCase();
            Long previousCount = getLongValue(resultSetTable, PREVIOUS_COUNT.getTableColumnName());
            Long count = getLongValue(resultSetTable, COUNT.getTableColumnName());
            String aggregateField = (String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName());

            // Build composite key
            String key = categoryId + "|" + accountName + "|" + cloudProvider + "|" + llm + "|" + aggregateField + "|";
            if (!categoryCount.containsKey(key)) {
                categoryCount.put(key, Arrays.asList(0L, 0L));
            }
            Long curr = categoryCount.get(key).get(0) + count;
            Long prev = categoryCount.get(key).get(1) + previousCount;
            categoryCount.put(key, Arrays.asList(curr, prev));

            //Grouping
            List<Object> groupKey = Arrays.asList(categoryId, accountName, cloudProvider, llm, aggregateField);
            groupedData.computeIfAbsent(groupKey, k -> new ArrayList<>());
        }

        // Build final rows
        List<List<Object>> finalData = new ArrayList<>();
        for (Map.Entry<List<Object>, List<List<Object>>> entry : groupedData.entrySet()) {
            List<Object> groupKey1 = new ArrayList<>(entry.getKey());

            String compositeKey = "";
            for (Object k : groupKey1) {
                compositeKey += k + "|";
            }
            List<Object> finalRow = new ArrayList<>();
            finalRow.add(groupKey1.get(0)); // category_id
            finalRow.add(groupKey1.get(1)); // account_name
            finalRow.add(groupKey1.get(2)); // cloud_provider
            finalRow.add(groupKey1.get(3)); // llm
            finalRow.add(categoryCount.get(compositeKey).get(0)); // count
            finalRow.add(categoryCount.get(compositeKey).get(1)); // previous_count
            finalRow.add(groupKey1.get(4)); // aggregate_field
            finalData.add(finalRow);
        }
        response.setData(finalData);
        return response;
    }

    @Override
    public TopCategoryWithLlmResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String column) {
        Object value = resultSetTable.getObject(column);
        return (value != null) ? ((Number) value).longValue() : 0L;
    }
    
    /**
     * Converts the response data to CSV format for efficient consumption.
     * 
     * @return A Map containing the CSV data and metadata
     */
    public Map<String, Object> compress() {
        Map<String, Object> result = new HashMap<>();
        StringBuilder csvData = new StringBuilder();
        
        // Add CSV header based on column display names
        csvData.append("CategoryID,AccountName,CloudProvider,LLM,Count,PreviousCount,AggregateField\n");
        
        // Add data rows
        if (data != null && !data.isEmpty()) {
            for (List<Object> row : data) {
                if (row.size() < 7) continue;
                
                String categoryId = (String) row.get(0);
                String accountName = (String) row.get(1);
                String cloudProvider = (String) row.get(2);
                String llm = (String) row.get(3);
                Long count = (row.get(4) != null) ? ((Number)row.get(4)).longValue() : null;
                Long previousCount = (row.get(5) != null) ? ((Number)row.get(5)).longValue() : null;
                String aggregateField = (String) row.get(6);
                
                // Escape fields for CSV format
                String escapedCategoryId = escapeCsvField(categoryId);
                String escapedAccountName = escapeCsvField(accountName);
                String escapedCloudProvider = escapeCsvField(cloudProvider);
                String escapedLlm = escapeCsvField(llm);
                String escapedAggField = escapeCsvField(aggregateField);
                
                // Add row to CSV
                csvData.append(escapedCategoryId).append(",")
                       .append(escapedAccountName).append(",")
                       .append(escapedCloudProvider).append(",")
                       .append(escapedLlm).append(",")
                       .append(count != null ? count : 0).append(",")
                       .append(previousCount != null ? previousCount : 0).append(",")
                       .append(escapedAggField).append("\n");
            }
        }
        
        // Add metadata
        if (currentTimeFrame != null) {
            result.put("currentTimeFrameStart", currentTimeFrame.getStartTime());
            result.put("currentTimeFrameEnd", currentTimeFrame.getEndTime());
        }
        
        if (comparisonTimeFrame != null) {
            result.put("comparisonTimeFrameStart", comparisonTimeFrame.getStartTime());
            result.put("comparisonTimeFrameEnd", comparisonTimeFrame.getEndTime());
        }
        
        if (pagination != null) {
            result.put("pageNumber", pagination.getPageNumber());
            result.put("totalPages", pagination.getTotalPages());
        }
        
        // Add CSV data and format
        result.put("csvData", csvData.toString());
        result.put("format", "csv");
        result.put("widgetId", widgetId);
        result.put("title", title);
        
        return result;
    }
    
    /**
     * Properly escapes a field value for CSV format according to RFC 4180:
     * - If the field contains commas, newlines, or double quotes, it should be enclosed in double quotes
     * - Double quotes within the field should be escaped by doubling them
     * 
     * @param value The field value to escape
     * @return The properly escaped CSV field
     */
    private String escapeCsvField(String value) {
        if (value == null) {
            return "";
        }
        
        boolean needsQuoting = value.contains(",") || value.contains("\"") || 
                               value.contains("\n") || value.contains("\r");
        
        if (needsQuoting) {
            // Replace any double quotes with two double quotes
            String escapedValue = value.replace("\"", "\"\"");
            return "\"" + escapedValue + "\"";
        } else {
            return value;
        }
    }
}