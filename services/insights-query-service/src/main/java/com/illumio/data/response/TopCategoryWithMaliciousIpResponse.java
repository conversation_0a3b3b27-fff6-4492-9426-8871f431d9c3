package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.service.batch_widget.PivotExtractable;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.ACCOUNT_NAME;
import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.CATEGORY_ID;
import static com.illumio.data.model.constants.Fields.CLOUD_PROVIDER;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.IP;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;

@Data
@NoArgsConstructor
@Component
public class TopCategoryWithMaliciousIpResponse implements ResponseBuilder<TopCategoryWithMaliciousIpResponse>, PivotExtractable {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;


    Map<String, List<Long>> categoryCount;

    @Override
    public TopCategoryWithMaliciousIpResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        TopCategoryWithMaliciousIpResponse response = new TopCategoryWithMaliciousIpResponse();

        //Set Widget Specific Metadata
        response.setWidgetId(requestMetadata.getWidgetId());
        response.setTitle("Top Category With Malicious IP");
        response.setColumns(Arrays.asList(CATEGORY_ID.getFieldKey(), ACCOUNT_NAME.getFieldKey(), CLOUD_PROVIDER.getFieldKey(), AGGREGATE_FIELD.getFieldKey(), COUNT.getFieldKey(), PREVIOUS_COUNT.getFieldKey()));
        response.setColumnTypes(Arrays.asList(CATEGORY_ID.getFieldType(), ACCOUNT_NAME.getFieldType(), CLOUD_PROVIDER.getFieldType(), AGGREGATE_FIELD.getFieldType(), COUNT.getFieldType(), PREVIOUS_COUNT.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(CATEGORY_ID.getFieldDisplayName(), ACCOUNT_NAME.getFieldDisplayName(), CLOUD_PROVIDER.getFieldDisplayName(), AGGREGATE_FIELD.getFieldDisplayName(), COUNT.getFieldDisplayName(), PREVIOUS_COUNT.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        Map<List<Object>, List<List<Object>>> groupedData = new LinkedHashMap<>();
        Long totalCount = 0L;
        Long previousTotalCount = 0L;
        categoryCount = new LinkedHashMap<>();


        while (resultSetTable.next()) {
            // Extract the row fields you need
            String categoryId = (String) resultSetTable.getObject(CATEGORY_ID.getTableColumnName());
            String accountName = (String) resultSetTable.getObject(ACCOUNT_NAME.getTableColumnName());
            String cloudProvider = (String) resultSetTable.getObject(CLOUD_PROVIDER.getTableColumnName());
            Long countVal = getLongValue(resultSetTable, COUNT.getTableColumnName());
            Long previousCountVal = getLongValue(resultSetTable, PREVIOUS_COUNT.getTableColumnName());
            String aggregateField = ((String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName())).toUpperCase();

            totalCount += countVal;
            previousTotalCount += previousCountVal;

            String key = categoryId + "|" + accountName + "|" + cloudProvider + "|" + aggregateField + "|";

            if (!categoryCount.containsKey(key)) {
                categoryCount.put(key, List.of(0L, 0L));
            }

            Long curr = categoryCount.get(key).get(0) + countVal;
            Long prev = categoryCount.get(key).get(1) + previousCountVal;
            categoryCount.put(key, List.of(curr, prev));

            String srcIp = (String) resultSetTable.getObject(IP.getTableColumnName());

            List<Object> groupKey = Arrays.asList(
                    categoryId,
                    accountName,
                    cloudProvider,
                    aggregateField
            );
            groupedData
                    .computeIfAbsent(groupKey, k -> new ArrayList<>())
                    .add(Arrays.asList(srcIp, countVal, previousCountVal));
        }
        List<List<Object>> finalData = new ArrayList<>();
        for (Map.Entry<List<Object>, List<List<Object>>> entry : groupedData.entrySet()) {
            List<Object> groupKey1 = new ArrayList<>(entry.getKey());
            List<List<Object>> ipArray = entry.getValue();

            String key = "";
            for (Object k : groupKey1) {
                key += k + "|";
            }
            groupKey1.add(categoryCount.get(key).get(0));
            groupKey1.add(categoryCount.get(key).get(1));

            // Build one row from groupKey plus the IP array
            List<Object> row = new ArrayList<>(groupKey1);
            row.add(ipArray);
            finalData.add(row);
        }

        response.setData(finalData);

        return response;
    }

    @Override
    public TopCategoryWithMaliciousIpResponse buildAggregatedResponse
            (List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : 0L;
    }
    
    /**
     * Converts the response data to CSV format for efficient consumption.
     * 
     * @return A Map containing the CSV data and metadata
     */
    public Map<String, Object> compress() {
        Map<String, Object> result = new HashMap<>();
        StringBuilder csvData = new StringBuilder();
        
        // Add CSV header based on column display names
        csvData.append("CategoryID,AccountName,CloudProvider,AggregateField,CategoryCount,CategoryPreviousCount,IP,IPCount,IPPreviousCount\n");
        
        // Add data rows
        if (data != null && !data.isEmpty()) {
            for (List<Object> row : data) {
                if (row.size() < 7) continue;
                
                String categoryId = (String) row.get(0);
                String accountName = (String) row.get(1);
                String cloudProvider = (String) row.get(2);
                String aggField = (String) row.get(3);
                Long count = (row.get(4) != null) ? ((Number)row.get(4)).longValue() : null;
                Long prevCount = (row.get(5) != null) ? ((Number)row.get(5)).longValue() : null;
                List<List<Object>> ipArray = (List<List<Object>>) row.get(6);
                
                // Escape fields for CSV format
                String escapedCategoryId = escapeCsvField(categoryId);
                String escapedAccountName = escapeCsvField(accountName);
                String escapedCloudProvider = escapeCsvField(cloudProvider);
                String escapedAggField = escapeCsvField(aggField);
                
                // If no IPs, add a single row with empty IP fields
                if (ipArray == null || ipArray.isEmpty()) {
                    csvData.append(escapedCategoryId).append(",")
                           .append(escapedAccountName).append(",")
                           .append(escapedCloudProvider).append(",")
                           .append(escapedAggField).append(",")
                           .append(count != null ? count : 0).append(",")
                           .append(prevCount != null ? prevCount : 0).append(",")
                           .append(",")  // Empty IP
                           .append("0,")  // Zero IP count
                           .append("0\n");  // Zero IP previous count
                } else {
                    // Add a row for each IP
                    for (List<Object> ipEntry : ipArray) {
                        if (ipEntry.size() >= 3) {
                            String ip = (String) ipEntry.get(0);
                            Long ipCount = (ipEntry.get(1) != null) ? ((Number)ipEntry.get(1)).longValue() : null;
                            Long ipPrevCount = (ipEntry.get(2) != null) ? ((Number)ipEntry.get(2)).longValue() : null;
                            
                            String escapedIp = escapeCsvField(ip);
                            
                            csvData.append(escapedCategoryId).append(",")
                                   .append(escapedAccountName).append(",")
                                   .append(escapedCloudProvider).append(",")
                                   .append(escapedAggField).append(",")
                                   .append(count != null ? count : 0).append(",")
                                   .append(prevCount != null ? prevCount : 0).append(",")
                                   .append(escapedIp).append(",")
                                   .append(ipCount != null ? ipCount : 0).append(",")
                                   .append(ipPrevCount != null ? ipPrevCount : 0).append("\n");
                        }
                    }
                }
            }
        }
        
        // Add metadata
        if (currentTimeFrame != null) {
            result.put("currentTimeFrameStart", currentTimeFrame.getStartTime());
            result.put("currentTimeFrameEnd", currentTimeFrame.getEndTime());
        }
        
        if (comparisonTimeFrame != null) {
            result.put("comparisonTimeFrameStart", comparisonTimeFrame.getStartTime());
            result.put("comparisonTimeFrameEnd", comparisonTimeFrame.getEndTime());
        }
        
        if (pagination != null) {
            result.put("pageNumber", pagination.getPageNumber());
            result.put("totalPages", pagination.getTotalPages());
        }
        
        // Add CSV data and format
        result.put("csvData", csvData.toString());
        result.put("format", "csv");
        result.put("widgetId", widgetId);
        result.put("title", title);
        
        return result;
    }
    
    /**
     * Properly escapes a field value for CSV format according to RFC 4180:
     * - If the field contains commas, newlines, or double quotes, it should be enclosed in double quotes
     * - Double quotes within the field should be escaped by doubling them
     * 
     * @param value The field value to escape
     * @return The properly escaped CSV field
     */
    private String escapeCsvField(String value) {
        if (value == null) {
            return "";
        }
        
        boolean needsQuoting = value.contains(",") || value.contains("\"") || 
                               value.contains("\n") || value.contains("\r");
        
        if (needsQuoting) {
            // Replace any double quotes with two double quotes
            String escapedValue = value.replace("\"", "\"\"");
            return "\"" + escapedValue + "\"";
        } else {
            return value;
        }
    }
}