package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.service.batch_widget.PivotExtractable;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.DESTINATION_EXTERNAL_LABEL;
import static com.illumio.data.model.constants.Fields.DESTINATION_EXTERNAL_LABEL_CATEGORY;
import static com.illumio.data.model.constants.Fields.AGGREGATED_BYTES;
import static com.illumio.data.model.constants.Fields.AGGREGATED_FLOWS;
import static com.illumio.data.model.constants.Fields.PREVIOUS_AGGREGATED_BYTES;
import static com.illumio.data.model.constants.Fields.PREVIOUS_AGGREGATED_FLOWS;
import static com.illumio.data.model.constants.Fields.TOTAL_ROWS;

@Data
@NoArgsConstructor
@Component
public class ExternalDestinationCategoryResponse implements ResponseBuilder<ExternalDestinationCategoryResponse>, PivotExtractable {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public ExternalDestinationCategoryResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        ExternalDestinationCategoryResponse response = new ExternalDestinationCategoryResponse();

        //Set Widget Specific Metadata
        response.setWidgetId(requestMetadata.getWidgetId());
        response.setTitle("Data Transfers by Destination Category");
        response.setColumns(Arrays.asList(DESTINATION_EXTERNAL_LABEL_CATEGORY.getFieldKey(), PREVIOUS_AGGREGATED_FLOWS.getFieldKey(), AGGREGATED_FLOWS.getFieldKey(), PREVIOUS_AGGREGATED_BYTES.getFieldKey(), AGGREGATED_BYTES.getFieldKey()));
        response.setColumnTypes(Arrays.asList(DESTINATION_EXTERNAL_LABEL_CATEGORY.getFieldType(), PREVIOUS_AGGREGATED_FLOWS.getFieldType(), AGGREGATED_FLOWS.getFieldType(), PREVIOUS_AGGREGATED_BYTES.getFieldType(), AGGREGATED_BYTES.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(DESTINATION_EXTERNAL_LABEL_CATEGORY.getFieldDisplayName(),  PREVIOUS_AGGREGATED_FLOWS.getFieldDisplayName(), AGGREGATED_FLOWS.getFieldDisplayName(), PREVIOUS_AGGREGATED_BYTES.getFieldDisplayName(), AGGREGATED_BYTES.getFieldDisplayName()));

        //Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getComparisonTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        List<List<Object>> data = new ArrayList<>();
        while (resultSetTable.next()) {
            String externalCategory = (String) resultSetTable.getObject(DESTINATION_EXTERNAL_LABEL_CATEGORY.getTableColumnName());
            String label = ((String) resultSetTable.getObject(DESTINATION_EXTERNAL_LABEL.getTableColumnName())).toLowerCase().replace(" ", "_");
            Long flowCount = getLongValue(resultSetTable, AGGREGATED_FLOWS.getTableColumnName());
            Long bytes = getLongValue(resultSetTable, AGGREGATED_BYTES.getTableColumnName());
            Long prevFlowCount = getLongValue(resultSetTable, PREVIOUS_AGGREGATED_FLOWS.getTableColumnName());
            Long prevBytes = getLongValue(resultSetTable, PREVIOUS_AGGREGATED_BYTES.getTableColumnName());

            List<Object> dataRow = new ArrayList<>();
            dataRow.add(externalCategory+":"+label);
            dataRow.add(prevFlowCount);
            dataRow.add(flowCount);
            dataRow.add(prevBytes);
            dataRow.add(bytes);
            data.add(dataRow);

            //Set total pages = (int) ceil(total_rows / rows per page). Set this only once in the response.
            if (resultSetTable.isLast()) {
                response.pagination.setTotalPages((int) Math.ceil((double)resultSetTable.getInt(TOTAL_ROWS.getTableColumnName()) / response.pagination.getRowLimit()));
            }
        }
        response.setData(data);

        return response;
    }

    @Override
    public ExternalDestinationCategoryResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }
    
    /**
     * Converts the response data to CSV format for efficient consumption.
     * 
     * @return A Map containing the CSV data and metadata
     */
    public Map<String, Object> compress() {
        Map<String, Object> result = new HashMap<>();
        StringBuilder csvData = new StringBuilder();
        
        // Add CSV header based on column display names
        csvData.append("DestinationCategory,PreviousFlows,Flows,PreviousBytes,Bytes\n");
        
        // Add data rows
        if (data != null && !data.isEmpty()) {
            for (List<Object> row : data) {
                if (row.size() < 5) continue;
                
                String category = (String) row.get(0);
                Long prevFlows = (row.get(1) != null) ? ((Number)row.get(1)).longValue() : null;
                Long flows = (row.get(2) != null) ? ((Number)row.get(2)).longValue() : null;
                Long prevBytes = (row.get(3) != null) ? ((Number)row.get(3)).longValue() : null;
                Long bytes = (row.get(4) != null) ? ((Number)row.get(4)).longValue() : null;
                
                // Escape fields for CSV format
                String escapedCategory = escapeCsvField(category);
                
                // Add row to CSV
                csvData.append(escapedCategory).append(",")
                       .append(prevFlows != null ? prevFlows : 0).append(",")
                       .append(flows != null ? flows : 0).append(",")
                       .append(prevBytes != null ? prevBytes : 0).append(",")
                       .append(bytes != null ? bytes : 0).append("\n");
            }
        }
        
        // Add metadata
        if (currentTimeFrame != null) {
            result.put("currentTimeFrameStart", currentTimeFrame.getStartTime());
            result.put("currentTimeFrameEnd", currentTimeFrame.getEndTime());
        }
        
        if (comparisonTimeFrame != null) {
            result.put("comparisonTimeFrameStart", comparisonTimeFrame.getStartTime());
            result.put("comparisonTimeFrameEnd", comparisonTimeFrame.getEndTime());
        }
        
        if (pagination != null) {
            result.put("pageNumber", pagination.getPageNumber());
            result.put("totalPages", pagination.getTotalPages());
        }
        
        // Add CSV data and format
        result.put("csvData", csvData.toString());
        result.put("format", "csv");
        result.put("widgetId", widgetId);
        result.put("title", title);
        
        return result;
    }
    
    /**
     * Properly escapes a field value for CSV format according to RFC 4180:
     * - If the field contains commas, newlines, or double quotes, it should be enclosed in double quotes
     * - Double quotes within the field should be escaped by doubling them
     * 
     * @param value The field value to escape
     * @return The properly escaped CSV field
     */
    private String escapeCsvField(String value) {
        if (value == null) {
            return "";
        }
        
        boolean needsQuoting = value.contains(",") || value.contains("\"") || 
                               value.contains("\n") || value.contains("\r");
        
        if (needsQuoting) {
            // Replace any double quotes with two double quotes
            String escapedValue = value.replace("\"", "\"\"");
            return "\"" + escapedValue + "\"";
        } else {
            return value;
        }
    }
}
