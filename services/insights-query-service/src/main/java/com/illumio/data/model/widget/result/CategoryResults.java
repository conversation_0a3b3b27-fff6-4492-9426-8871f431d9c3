package com.illumio.data.model.widget.result;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * Helper class for combining category-specific results.
 */
@Setter
@Getter
public class CategoryResults {
    private Map<String, Object> categories = new HashMap<>();
    
    @JsonIgnore
    public boolean isEmpty() {
        return categories.isEmpty() || categories.values().stream().allMatch(Objects::isNull);
    }
    
    public void addCategory(String category, Object data) {
        categories.put(category, data);
    }
    
    public Object get(String category) {
        return categories.get(category);
    }
} 