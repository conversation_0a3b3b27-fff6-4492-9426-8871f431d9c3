package com.illumio.data.model.widget.result;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
public class CombinedResults {
    private Map<String, DirectionalResults> categoryDirectionalResults = new HashMap<>();
    
    @JsonIgnore
    public boolean isEmpty() {
        return categoryDirectionalResults.isEmpty() || 
               categoryDirectionalResults.values().stream().allMatch(DirectionalResults::isEmpty);
    }
    
    public void addResult(String category, String direction, Object data) {
        DirectionalResults dirResults = categoryDirectionalResults.computeIfAbsent(
                category, k -> new DirectionalResults());
        
        if ("inbound".equals(direction)) {
            dirResults.setInbound(data);
        } else if ("outbound".equals(direction)) {
            dirResults.setOutbound(data);
        }
    }
} 