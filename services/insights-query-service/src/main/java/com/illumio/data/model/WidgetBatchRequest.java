package com.illumio.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WidgetBatchRequest {
    private List<String> widgetIdList;
    private TimeFrame currentTimeFrame;
    private TimeFrame comparisonTimeFrame;
    
    @Builder.Default
    private Boolean compressed = false;
    
    // Optional page size for pagination control
    private Integer pageSize;
    
    // Pivot widgets configuration
    private List<PivotWidget> pivotWidget;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PivotWidget {
        private String widgetId;
        private String pageId;
        private String field;
        private Integer topKSize;
    }
} 