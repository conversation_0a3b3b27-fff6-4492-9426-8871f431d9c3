package com.illumio.data.configuration;

import lombok.Data;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration
@ConfigurationProperties(prefix = "resource-config")
public class ResourceMapConfig {
    private boolean enabled;
    private List<Resource> resourceMap;

    @Data
    @Getter
    public static class Resource {
        private String resourceId;
        private String severityLevel;
        private double VEScore;
        private long sentBytes;
        private long receivedBytes;
        private long flowCount;
    }
}
