package com.illumio.data.repository.timequery;

public class DailyHourlyTimeConditionStrategy implements TimeConditionStrategy {
    @Override
    public String buildTimeConditionQuery(String startTime, String endTime, String tableName) {
        // For daily/hourly aggregates, use the standard operators >= and <= operators.
        return String.format("| where todatetime(StartTime) >= datetime('%s') | where todatetime(EndTime) <= datetime('%s')",
                startTime, endTime);
    }
}