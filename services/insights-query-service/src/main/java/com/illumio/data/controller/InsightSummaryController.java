package com.illumio.data.controller;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.service.InsightSummaryService;
import com.illumio.data.service.JWTService;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.Base64;
import java.util.Collections;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1")
public class InsightSummaryController {

    private final InsightSummaryService insightSummaryService;
    private final MeterRegistry meterRegistry;
    private final InsightsServiceConfiguration configuration;
    private final JWTService jwtService;

    @GetMapping("/insights-summary")
    public Mono<ResponseEntity<Object>> getInsightSummary(
            @RequestHeader MultiValueMap<String, String> headers,
            @RequestParam Map<String, String> requestParams) {
        
        // Extract parameters case-insensitively with defaults
        String rawPersona = getCaseInsensitiveParam(requestParams, "persona", "threat_hunter");
        String rawReportInterval = getCaseInsensitiveParam(requestParams, "report_interval", "daily");
        
        String persona = rawPersona.toLowerCase();
        String reportInterval = rawReportInterval.toLowerCase();
        
        Timer.Sample timer = Timer.start(meterRegistry);

        log.info("Received insight summary request with persona: {} and report_interval: {}", persona, reportInterval);

        // Validate report_interval parameter
        if (!reportInterval.equals("daily") && !reportInterval.equals("weekly")) {
            log.error("Invalid report_interval parameter: {}. Must be 'daily' or 'weekly'", reportInterval);
            return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", 
                "Invalid report_interval parameter. Must be 'daily' or 'weekly'"));
        }

        String authKey = configuration.getApiConfig().getAuthKey();
        
        // Validate required headers
        String illumioTenantIdHeader = headers.getFirst("x-illumio-tenant-id");
        String requestAuthKey = headers.getFirst("x-auth-key");
        String jwt = headers.getFirst("X-GW");

        if (illumioTenantIdHeader == null || illumioTenantIdHeader.trim().isEmpty()) {
            log.error("Missing required header: x-illumio-tenant-id");
            return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", 
                "Missing required header: x-illumio-tenant-id"));
        }

        // Validate auth, either x-auth-key or X-GW which is a jwt
        if (requestAuthKey != null && !requestAuthKey.trim().isEmpty()) {
            log.info("Auth key present, proceeding with authkey validation");
            
            // Check if auth key is configured
            if (authKey == null || authKey.trim().isEmpty()) {
                log.error("Auth key header provided but auth key is not configured in service");
                return Mono.just(errorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error", 
                    "Service authentication configuration error: auth key not configured"));
            }
            
            // Validate auth key against configured value
            if (!authKey.equals(requestAuthKey)) {
                log.error("Invalid auth key provided");
                return Mono.just(errorResponse(HttpStatus.UNAUTHORIZED, "Unauthorized", "Invalid auth key"));
            }
            log.info("Auth key validation successful");
            
        } else {
            // No auth key, proceed with jwt validation
            if(jwt == null || jwt.trim().isEmpty()) {
                log.error("Missing required header: X-GW");
                return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", 
                    "Missing required header: X-GW and no auth key present"));
            } else {
                // Validate jwt
                try {
                    // TODO: Once X-Illumio-Tenant-Id is unified with the JWT tenant id, we can pass the illumioTenantId as a parameter to the validateJWT method
                    Pair<Boolean, String> jwtValidationResult = jwtService.validateJWT(headers, null, "X-GW", "tenant", false);
                    if(!jwtValidationResult.getLeft()) {
                        log.error("Invalid JWT provided, message: " + jwtValidationResult.getRight());
                        return Mono.just(errorResponse(HttpStatus.UNAUTHORIZED, "Unauthorized", 
                            "Invalid JWT. Error: " + jwtValidationResult.getRight()));
                    }
                    log.info("JWT validation successful");
                    log.info("JWT validation result: {}", jwtValidationResult.getRight());
                } catch (IllegalArgumentException e) {
                    log.error("Failed to base64 decode illumio tenant ID: {}", e.getMessage());
                    return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", 
                        "Invalid base64 encoded illumio tenant ID"));
                }
            }
        }

        // Decode base64 encoded tenant ID
        String tenantId;
        try {
            byte[] decodedBytes = Base64.getDecoder().decode(illumioTenantIdHeader);
            tenantId = new String(decodedBytes);
            log.debug("Decoded tenant ID: {}", tenantId);
        } catch (IllegalArgumentException e) {
            log.error("Invalid base64 encoded tenant ID: {}", illumioTenantIdHeader);
            return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", 
                "Invalid base64 encoded tenant ID"));
        }

        return insightSummaryService
                .getInsightSummary(
                    tenantId, 
                    persona, 
                    reportInterval)
                .map(summary -> ResponseEntity.<Object>ok(summary))
                .switchIfEmpty(Mono.just(ResponseEntity.ok(Collections.emptyMap())))
                .doOnSuccess(result -> {
                    timer.stop(Timer.builder("insight_summary_request")
                        .tag("status", "success")
                        .tag("persona", persona)
                        .register(meterRegistry));
                    log.info("Successfully processed insight summary request for persona: {}", persona);
                })
                .onErrorResume(error -> {
                    timer.stop(Timer.builder("insight_summary_request")
                        .tag("status", "error")
                        .tag("persona", persona)
                        .register(meterRegistry));
                    
                    log.error("Error processing insight summary request: ", error);
                    
                    if (error instanceof IllegalArgumentException) {
                        return Mono.just(errorResponse(HttpStatus.BAD_REQUEST, "Bad Request", error.getMessage()));
                    }
                    
                    if (error instanceof UnsupportedOperationException) {
                        return Mono.just(errorResponse(HttpStatus.NOT_IMPLEMENTED, "Not Implemented", error.getMessage()));
                    }
                    
                    return Mono.just(errorResponse(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error", 
                        "An unexpected error occurred"));
                });
    }

    
    private ResponseEntity<Object> errorResponse(HttpStatus status, String error, String message) {
        return ResponseEntity.status(status).body(Map.of(
            "error", error,
            "message", message,
            "timestamp", System.currentTimeMillis()
        ));
    }

    private String getCaseInsensitiveParam(Map<String, String> params, String paramName, String defaultValue) {
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (entry.getKey().equalsIgnoreCase(paramName)) {
                return entry.getValue();
            }
        }
        return defaultValue;
    }
}
