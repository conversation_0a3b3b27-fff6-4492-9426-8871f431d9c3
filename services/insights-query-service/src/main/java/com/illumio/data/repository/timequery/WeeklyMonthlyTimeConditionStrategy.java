package com.illumio.data.repository.timequery;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class WeeklyMonthlyTimeConditionStrategy implements TimeConditionStrategy {
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ISO_DATE_TIME;

    @Override
    public String buildTimeConditionQuery(String startTime, String endTime, String tableName) {
        LocalDateTime start = LocalDateTime.parse(startTime, FORMATTER);
        LocalDateTime end = LocalDateTime.parse(endTime, FORMATTER);
        StringBuilder conditions = new StringBuilder();
        LocalDateTime current = start;

        // Use a weekly increment if the table name indicates weekly aggregation;
        // otherwise, use monthly increments.
        boolean isWeekly = tableName.contains("Weekly");
        while (current.isBefore(end)) {
            conditions.append(String.format("datetime('%s')", current.format(FORMATTER)));
            current = isWeekly ? current.plusWeeks(1) : current.plusMonths(1);
            if (current.isBefore(end)) {
                conditions.append(", ");
            }
        }
        return "| where StartTime in (" + conditions.toString() + ")";
    }
}
