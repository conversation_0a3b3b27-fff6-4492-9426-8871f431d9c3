package com.illumio.data.response;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.SeverityLevel;
import com.illumio.data.service.RiskyServiceInfo;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Component
public class GetRiskyServiceNamesResponse implements ResponseBuilder<GetRiskyServiceNamesResponse.BasicResponse> {
    
    @Autowired
    private RiskyServiceInfo riskyServiceInfo;
    
    @Override
    public BasicResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext, Metadata requestMetadata) {
        List<Map<String, Object>> data = new ArrayList<>();
        while (resultSetTable.next()) {
            Object serviceName = resultSetTable.getObject("ServiceName");
            Object port = resultSetTable.getObject("Port");
            Object proto = resultSetTable.getObject("Proto");
            if (serviceName != null && port != null && proto != null) {
                Integer portInt = Integer.valueOf(port.toString());
                String protocolStr = proto.toString().toUpperCase();
                
                // Calculate severity using the same pattern as RiskyServicesTrafficResourceInsightsResponse
                int severity = Optional.ofNullable(portInt)
                        .flatMap(p -> Optional.ofNullable(protocolStr)
                                .flatMap(pr -> Optional.ofNullable(
                                                riskyServiceInfo.getPortProtoToServiceInfoMap().get(new RiskyServiceInfo.PortProtocolPair(p, pr)))
                                        .map(RiskyServiceInfo.ServiceInfo::getSeverity)))
                        .map(SeverityLevel::fromString)
                        .orElse(-1); // Default value when severity is unknown
                
                data.add(Map.of(
                    "ServiceName", serviceName.toString(),
                    "Port", port.toString(),
                    "Proto", proto.toString(),
                    "Severity", severity
                ));
            }
        }
        return new BasicResponse(data);
    }

    @Override
    public BasicResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return new BasicResponse(new ArrayList<>());
    }

    public static class BasicResponse {
        public List<Map<String, Object>> data;
        public BasicResponse(List<Map<String, Object>> data) {
            this.data = data;
        }
        public List<Map<String, Object>> getData() { return data; }
        public void setData(List<Map<String, Object>> data) { this.data = data; }
    }
} 