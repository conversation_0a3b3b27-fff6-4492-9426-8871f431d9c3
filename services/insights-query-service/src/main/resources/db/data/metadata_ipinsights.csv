widget_id,request_type,api_version,page_id,metadata,description,persona
ip-top-destination-resources,topResourcesCommunicatingWithIpInbound,v2,IpSlideOuts,"{""templateType"": ""TYPE_ONE"", ""tableName"": ""InfiltrationTraffic"", ""summarizeFields"": [""flows"", ""bytes""], ""groupByFields"": [""destination_resource_id"",""dest_resource_name"",""dest_resource_cat"",""dest_resource_type""],""isCategoryDependent"":false,""isDirectionDependent"":false}","Top resources receiving traffic from IP widget","Threat Hunter"
ip-top-source-resources,topResourcesCommunicatingWithIpOutbound,v2,IpSlideOuts,"{""templateType"": ""TYPE_ONE"", ""tableName"": ""ExfiltrationTraffic"", ""summarizeFields"": [""flows"", ""bytes""], ""groupByFields"": [""source_resource_id"",""source_resource_name"",""source_resource_cat"",""source_resource_type""],""isCategoryDependent"":false,""isDirectionDependent"":false}","Top resources sending traffic to IP widget","Threat Hunter"
ip-top-destination-roles,topRolesCommunicatingWithIpInbound,v2,IpSlideOuts,"{""templateType"": ""TYPE_ONE"", ""tableName"": ""InfiltrationTraffic"", ""summarizeFields"": [""flows"", ""bytes""], ""groupByFields"": [""destination_role""],""isCategoryDependent"":false,""isDirectionDependent"":false}","Top roles receiving traffic from IP widget","Threat Hunter"
ip-top-source-roles,topRolesCommunicatingWithIpOutbound,v2,IpSlideOuts,"{""templateType"": ""TYPE_ONE"", ""tableName"": ""ExfiltrationTraffic"", ""summarizeFields"": [""flows"", ""bytes""], ""groupByFields"": [""source_role""],""isCategoryDependent"":false,""isDirectionDependent"":false}","Top roles sending traffic to IP widget","Threat Hunter"
ip-data-transfer-inbound,ipDataTransferInbound,v2,IpSlideOuts,"{""templateType"": ""TYPE_ONE"", ""tableName"": ""InfiltrationTraffic"", ""summarizeFields"": [""flows"", ""bytes""], ""groupByFields"": [""source_ip""],""isCategoryDependent"":false,""isDirectionDependent"":false}","Data Transfer from IP widget","Threat Hunter"
ip-data-transfer-outbound,ipDataTransferOutbound,v2,IpSlideOuts,"{""templateType"": ""TYPE_ONE"", ""tableName"": ""ExfiltrationTraffic"", ""summarizeFields"": [""flows"", ""bytes""], ""groupByFields"": [""destination_ip""],""isCategoryDependent"":false,""isDirectionDependent"":false}","Data Transfer to IP widget","Threat Hunter"