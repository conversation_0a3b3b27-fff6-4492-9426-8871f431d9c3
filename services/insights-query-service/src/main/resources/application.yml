logging:
  level:
    ROOT: INFO

server:
  port:
    8081

jwt:
  secret: _DO_NOT_COMMIT_

spring:
  application:
    name: insights-query-service
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive
  r2dbc:
    url: _DO_NOT_COMMIT_
    username: _DO_NOT_COMMIT_
    password: _DO_NOT_COMMIT_
  liquibase:
    enabled: true
    change-log: classpath:db/changelog/db.changelog-master.yaml

insights-config:
  kustoConfig:
    clusterUri: https://arch-kusto.eastus.kusto.windows.net
    database: DecoratedFlows
    isManagedIdentity: false
    azureClientId: _DO_NOT_COMMIT_
    azureClientSecret: _DO_NOT_COMMIT_
    azureTenantId: _DO_NOT_COMMIT_
  kustoInsightsConfig:
    clusterUri: https://arch-kusto.eastus.kusto.windows.net
    database: DecoratedFlows
    isManagedIdentity: false
    azureClientId: _DO_NOT_COMMIT_
    azureClientSecret: _DO_NOT_COMMIT_
    azureTenantId: _DO_NOT_COMMIT_
  riskyServiceConfig:
    riskyServiceFilePath: "classpath:risky_services.csv"
  llmServiceConfig:
    llmServiceFilePath: "classpath:llm_services.csv"
  unencryptedServiceConfig:
    unencryptedServiceFilePath: "classpath:dora_unencrypted_services.csv"
  paginationConfig:
    maxPageSize: 100
    defaultPageSize: 25
    defaultPageNumber: 1
  jwtConfig:
    enableJwt: false
    isSymmetricKey: true
  kafkaProducerConfig:
    bootstrapServers: sunnyvale-enf-kafka-eventhub-ns.servicebus.windows.net:9093
    isConnectionString: true
    isManagedIdentity: false
    topic: cs-sync-inventory2pce-v1
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="_CONNECTION_STRING_DO_NOT_COMMIT_";
  quarantineConfig:
    labelId: "18577348462903458"
  urlConfig:
    uiUrl: https://graph.illum.io
  metadataJdbcConfig:
    driverClassName: org.postgresql.Driver
    host: _DO_NOT_COMMIT_
    port: 5432
    database: metadata
    username: _DO_NOT_COMMIT_
    password: _DO_NOT_COMMIT_
    sslMode: require # See the enums io.r2dbc.postgresql.client.SSLMode
  ivaJdbcConfig:
    driverClassName: org.postgresql.Driver
    host: _DO_NOT_COMMIT_
    port: 5432
    database: iss
    username: _DO_NOT_COMMIT_
    password: _DO_NOT_COMMIT_
    sslMode: require # See the enums io.r2dbc.postgresql.client.SSLMode
  proxyConfig:
    # Set to true to enable request forwarding to target FQDN
    enableProxy: false
    # Target FQDN to forward requests to (including protocol)
    targetFqdn: https://other-cluster.example.com
    # Target endpoint path on the remote service
    targetEndpoint: /api/v1/resource-insights
    # HTTP timeout in seconds for forwarded requests
    timeoutSeconds: 30
  liquibaseConfig:
    changelogPath: classpath:db/changelog/db.changelog-master.yaml

resource-config:
  enabled: false
  resourceMap:
    - resourceId: /subscriptions/d74212c4-cc55-406e-85b1-ea45fb89ea03/resourceGroups/rg_sauto2_1/providers/Microsoft.Network/networkInterfaces/vnet_sauto2-nic01-5b8734c6
      severityLevel: HIGH
      VEScore: 8.6
      sentBytes: 3100
      receivedBytes: 57708104
      flowCount: 58
    - resourceId: /subscriptions/87332a70-7c1b-4437-aa3b-ec7c00d72de0/resourceGroups/nebula-inventory-test-vm-1-rg/providers/Microsoft.Compute/virtualMachines/nebula-inventory-test-vm-1-myVM
      severityLevel: MEDIUM
      VEScore: 6.3
      sentBytes: 100112
      receivedBytes: 810410
      flowCount: 8990

service-auth:
  keys:
    iss: dummy_key
