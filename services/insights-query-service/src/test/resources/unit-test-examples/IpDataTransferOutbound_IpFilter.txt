let byBytes = Insights_ExfiltrationTraffic_Hourly
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where DestIP in ('********')
    | summarize Count = sum(AggByteCount) by DestIP
    | sort by Count desc
    | take 15;
let byFlows = Insights_ExfiltrationTraffic_Hourly
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where DestIP in ('********')
    | summarize Count = sum(AggFlowCount) by DestIP
    | sort by Count desc
    | take 15;
let FlowsTimeSeries = Insights_ExfiltrationTraffic_Hourly
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where DestIP in ('********')
    | summarize Count = sum(AggFlowCount) by DestIP, TimeSeries = bin(StartTime, 1h)
    | make-series Count  = sum(Count) default = 0 on TimeSeries in range(datetime('2025-01-01T00:00:00Z'), datetime('2025-01-02T00:00:00Z'), 1h) by DestIP
    | mv-expand TimeSeries, Count
    | extend AggregateField = 'FLOWS';
let BytesTimeSeries = Insights_ExfiltrationTraffic_Hourly
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where DestIP in ('********')
    | summarize Count = sum(AggByteCount) by DestIP, TimeSeries = bin(StartTime, 1h)
    | make-series Count  = sum(Count) default = 0 on TimeSeries in range(datetime('2025-01-01T00:00:00Z'), datetime('2025-01-02T00:00:00Z'), 1h) by DestIP
    | mv-expand TimeSeries, Count
    | extend AggregateField = 'BYTES';
union FlowsTimeSeries, BytesTimeSeries;