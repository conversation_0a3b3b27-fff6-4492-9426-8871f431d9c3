let byFlows = Insights_InfiltrationTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where SrcIP in ('********')
    | summarize Count = sum(AggFlowCount) by DestinationLabel
    | sort by Count desc
    | take 15;
let byFlowsPrev = Insights_InfiltrationTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where SrcIP in ('********')
    | where DestinationLabel in(byFlows)
    | summarize Count = sum(AggFlowCount) by DestinationLabel;
let byBytes = Insights_InfiltrationTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where SrcIP in ('********')
    | summarize Count = sum(AggByteCount) by DestinationLabel
    | sort by Count desc
    | take 15;
let byBytesPrev = Insights_InfiltrationTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where SrcIP in ('********')
    | where DestinationLabel in(byBytes)
    | summarize Count = sum(AggByteCount) by DestinationLabel;
let finalFlows = byFlows
    | join kind=leftouter(byFlowsPrev) on DestinationLabel
    | extend AggregateField="FLOWS"
    | sort by Count;
let finalBytes = byBytes
    | join kind=leftouter(byBytesPrev) on DestinationLabel
    | extend AggregateField="BYTES"
    | sort by Count;
union finalFlows, finalBytes;