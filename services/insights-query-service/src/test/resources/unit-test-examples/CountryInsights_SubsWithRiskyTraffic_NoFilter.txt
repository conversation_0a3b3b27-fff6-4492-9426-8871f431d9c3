let subIdsInByFlows = Insights_RiskyTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-02T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-03T00:00:00Z')
    | where isempty(SrcSubId) and isnotempty(DestSubId)
    | summarize CountINBOUND=sum(AggFlowCount) by SubId = DestSubId;
let subIdsOutByFlows = Insights_RiskyTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-02T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-03T00:00:00Z')
    | where isnotempty(SrcSubId) and isempty(DestSubId)
    | summarize CountOUTBOUND=sum(AggFlowCount) by SubId = SrcSubId;
let topSubIdsByFlows = subIdsOutByFlows
    | join kind=fullouter (subIdsInByFlows) on SubId
    | extend TotalFlowCount = coalesce(CountINBOUND, 0) + coalesce(CountOUTBOUND, 0)
    | top 10 by TotalFlowCount
    | project SubId = coalesce(SubId, SubId1);
let bySubIdsInCurrFlows = Insights_RiskyTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-02T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-03T00:00:00Z')
    | where DestSubId in (topSubIdsByFlows | project SubId) and isempty(SrcSubId)
    | summarize TotalFlowCountIn=sum(AggFlowCount) by SubId = DestSubId, AccountName = DestAccountName, Country = SrcCountry;
let bySubIdsOutCurrFlows = Insights_RiskyTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-02T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-03T00:00:00Z')
    | where SrcSubId in (topSubIdsByFlows | project SubId) and isempty(DestSubId)
    | summarize TotalFlowCountOut=sum(AggFlowCount) by SubId = SrcSubId, AccountName = SrcAccountName, Country = DestCountry;
let bySubIdsCurrFlows = bySubIdsOutCurrFlows
    | join kind=fullouter (bySubIdsInCurrFlows) on SubId, AccountName, Country
    | extend TotalFlowCount = coalesce(TotalFlowCountOut, 0) + coalesce(TotalFlowCountIn, 0)
    | project
        SubId = coalesce(SubId, SubId1),
        AccountName = coalesce(AccountName, AccountName1),
        Country = coalesce(Country, Country1),
        TotalFlowCount;
let bySubIdsInPrevFlows = Insights_RiskyTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where DestSubId in (topSubIdsByFlows | project SubId) and isempty(SrcSubId)
    | summarize TotalFlowCountInPrev=sum(AggFlowCount) by SubId = DestSubId, AccountName = DestAccountName, Country = SrcCountry;
let bySubIdsOutPrevFlows = Insights_RiskyTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where SrcSubId in (topSubIdsByFlows | project SubId) and isempty(DestSubId)
    | summarize TotalFlowCountOutPrev=sum(AggFlowCount) by SubId = SrcSubId, AccountName = SrcAccountName, Country = DestCountry;
let bySubIdsPrevFlows = bySubIdsOutPrevFlows
    | join kind=fullouter (bySubIdsInPrevFlows) on SubId, Country
    | extend TotalFlowCountPrev = coalesce(TotalFlowCountOutPrev, 0) + coalesce(TotalFlowCountInPrev, 0)
    | project
        SubId = coalesce(SubId, SubId1),
        Country = coalesce(Country, Country1),
        TotalFlowCountPrev;
let subIdsFlows = bySubIdsCurrFlows
    | join kind=leftouter(bySubIdsPrevFlows) on SubId, Country
    | project
        SubId,
        AccountName,
        Country,
        Count=TotalFlowCount,
        Count1=TotalFlowCountPrev,
        AggregateField='FLOWS';
let subIdsInByBytes = Insights_RiskyTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-02T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-03T00:00:00Z')
    | where isempty(SrcSubId) and isnotempty(DestSubId)
    | summarize CountINBOUND=sum(AggByteCount) by SubId = DestSubId;
let subIdsOutByBytes = Insights_RiskyTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-02T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-03T00:00:00Z')
    | where isnotempty(SrcSubId) and isempty(DestSubId)
    | summarize CountOUTBOUND=sum(AggByteCount) by SubId = SrcSubId;
let topSubIdsByBytes = subIdsOutByBytes
    | join kind=fullouter (subIdsInByBytes) on SubId
    | extend TotalBytesCount = coalesce(CountINBOUND, 0) + coalesce(CountOUTBOUND, 0)
    | top 10 by TotalBytesCount
    | project SubId = coalesce(SubId, SubId1);
let bySubIdsInCurrBytes = Insights_RiskyTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-02T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-03T00:00:00Z')
    | where DestSubId in (topSubIdsByBytes | project SubId) and isempty(SrcSubId)
    | summarize TotalBytesCountIn=sum(AggByteCount) by SubId = DestSubId, AccountName = DestAccountName, Country = SrcCountry;
let bySubIdsOutCurrBytes = Insights_RiskyTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-02T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-03T00:00:00Z')
    | where SrcSubId in (topSubIdsByBytes | project SubId) and isempty(DestSubId)
    | summarize TotalBytesCountOut=sum(AggByteCount) by SubId = SrcSubId, AccountName = SrcAccountName, Country = DestCountry;
let bySubIdsCurrBytes = bySubIdsOutCurrBytes
    | join kind=fullouter (bySubIdsInCurrBytes) on SubId, AccountName, Country
    | extend TotalBytesCount = coalesce(TotalBytesCountOut, 0) + coalesce(TotalBytesCountIn, 0)
    | project
        SubId = coalesce(SubId, SubId1),
        AccountName = coalesce(AccountName, AccountName1),
        Country = coalesce(Country, Country1),
        TotalBytesCount;
let bySubIdsInPrevBytes = Insights_RiskyTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where DestSubId in (topSubIdsByBytes | project SubId) and isempty(SrcSubId)
    | summarize TotalBytesCountInPrev=sum(AggByteCount) by SubId = DestSubId, AccountName = DestAccountName, Country = SrcCountry;
let bySubIdsOutPrevBytes = Insights_RiskyTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where SrcSubId in (topSubIdsByBytes | project SubId) and isempty(DestSubId)
    | summarize TotalBytesCountOutPrev=sum(AggByteCount) by SubId = SrcSubId, AccountName = SrcAccountName, Country = DestCountry;
let bySubIdsPrevBytes = bySubIdsOutPrevBytes
    | join kind=fullouter (bySubIdsInPrevBytes) on SubId, Country
    | extend TotalBytesCountPrev = coalesce(TotalBytesCountOutPrev, 0) + coalesce(TotalBytesCountInPrev, 0)
    | project
        SubId = coalesce(SubId, SubId1),
        Country = coalesce(Country, Country1),
        TotalBytesCountPrev;
let subIdsBytes = bySubIdsCurrBytes
    | join kind=leftouter(bySubIdsPrevBytes) on SubId, Country
    | project
        SubId,
        AccountName,
        Country,
        Count=TotalBytesCount,
        Count1=TotalBytesCountPrev,
        AggregateField='BYTES';
union subIdsFlows, subIdsBytes