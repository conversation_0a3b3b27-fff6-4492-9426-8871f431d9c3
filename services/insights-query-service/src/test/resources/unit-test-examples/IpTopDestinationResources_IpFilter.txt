let byBytes = Insights_InfiltrationTraffic_Hourly
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where SrcIP in ('********')
    | summarize Count = sum(AggByteCount) by DestResId, DestResourceName, DestResourceCategory, DestResourceType
    | sort by Count desc
    | take 15;
let byFlows = Insights_InfiltrationTraffic_Hourly
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where SrcIP in ('********')
    | summarize Count = sum(AggFlowCount) by DestResId, DestResourceName, DestResourceCategory, DestResourceType
    | sort by Count desc
    | take 15;
let FlowsTimeSeries = Insights_InfiltrationTraffic_Hourly
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where SrcIP in ('********')
    | where DestResId in(byFlows | project DestResId)
    | summarize Count = sum(AggFlowCount)
        by
        DestResId,
        DestResourceName,
        DestResourceCategory,
        DestResourceType,
        TimeSeries = bin(StartTime, 1h)
    | make-series Count  = sum(Count) default = 0 on TimeSeries in range(datetime('2025-01-01T00:00:00Z'), datetime('2025-01-02T00:00:00Z'), 1h) by DestResId, DestResourceName, DestResourceCategory, DestResourceType
    | mv-expand TimeSeries, Count
    | extend AggregateField = 'FLOWS';
let BytesTimeSeries = Insights_InfiltrationTraffic_Hourly
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where SrcIP in ('********')
    | where DestResId in(byBytes | project DestResId)
    | summarize Count = sum(AggByteCount)
        by
        DestResId,
        DestResourceName,
        DestResourceCategory,
        DestResourceType,
        TimeSeries = bin(StartTime, 1h)
    | make-series Count  = sum(Count) default = 0 on TimeSeries in range(datetime('2025-01-01T00:00:00Z'), datetime('2025-01-02T00:00:00Z'), 1h) by DestResId, DestResourceName, DestResourceCategory, DestResourceType
    | mv-expand TimeSeries, Count
    | extend AggregateField = 'BYTES';
union FlowsTimeSeries, BytesTimeSeries;