let byFlows = Insights_ExfiltrationTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where DestIP in ('********')
    | summarize Count = sum(AggFlowCount) by SourceLabel
    | sort by Count desc
    | take 15;
let byFlowsPrev = Insights_ExfiltrationTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where DestIP in ('********')
    | where SourceLabel in(byFlows)
    | summarize Count = sum(AggFlowCount) by SourceLabel;
let byBytes = Insights_ExfiltrationTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where DestIP in ('********')
    | summarize Count = sum(AggByteCount) by SourceLabel
    | sort by Count desc
    | take 15;
let byBytesPrev = Insights_ExfiltrationTraffic_Daily
    | where IllumioTenantId == 'tenant_test'
    | where todatetime(StartTime) >= datetime('2025-01-01T00:00:00Z')
    | where todatetime(EndTime) <= datetime('2025-01-02T00:00:00Z')
    | where DestIP in ('********')
    | where SourceLabel in(byBytes)
    | summarize Count = sum(AggByteCount) by SourceLabel;
let finalFlows = byFlows
    | join kind=leftouter(byFlowsPrev) on SourceLabel
    | extend AggregateField="FLOWS"
    | sort by Count;
let finalBytes = byBytes
    | join kind=leftouter(byBytesPrev) on SourceLabel
    | extend AggregateField="BYTES"
    | sort by Count;
union finalFlows, finalBytes;