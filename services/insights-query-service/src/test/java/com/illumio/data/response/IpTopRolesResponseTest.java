package com.illumio.data.response;

import com.illumio.data.model.Filters;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.WidgetId;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.DESTINATION_ROLE;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;
import static com.illumio.data.model.constants.Fields.SOURCE_ROLE;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class IpTopRolesResponseTest {
    private IpTopRolesResponse ipTopRolesResponse;

    @BeforeEach
    void setUp() {
        ipTopRolesResponse = new IpTopRolesResponse();
    }

    @Test
    void ipTopSourceRolesResponseTest() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.IP_TOP_SOURCE_ROLES)
                .build();
        KustoResultSetTable resultSetTable = mock(KustoResultSetTable.class);
        when(resultSetTable.next()).thenReturn(true, false);

        when(resultSetTable.getObject(SOURCE_ROLE.getTableColumnName())).thenReturn("LDAP");
        when(resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName())).thenReturn("Flows");
        when(resultSetTable.getObject(PREVIOUS_COUNT.getTableColumnName())).thenReturn(100);
        when(resultSetTable.getObject(COUNT.getTableColumnName())).thenReturn(200);

        RequestPayload payload = RequestPayload.builder().build();
        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);
        Pagination pagination = Pagination.builder().build();
        pagination.setRowLimit(15);
        payload.setPagination(pagination);
        payload.setSortByFields(Collections.emptyList());
        Filters filter = Filters.builder()
                .categoryName("traffic_direction")
                .categoryType("string")
                .categoryValue(List.of("OUTBOUND"))
                .build();
        List<Filters> filters = List.of(filter);
        payload.setFilters(filters);

        RequestContext requestContext = RequestContext.builder()
                .requestPayload(Optional.of(payload))
                .build();

        IpTopRolesResponse response = ipTopRolesResponse.buildResponse(resultSetTable,  requestContext, metadata);

        assertEquals(WidgetId.IP_TOP_ROLES, response.getWidgetId());
        assertEquals("Top 15 Roles Communicating With IP", response.getTitle());
        assertEquals(List.of("source_role", "aggregate_field", "previous_count", "count"), response.getColumns());
        assertEquals(List.of("Source Role", "Aggregate Field", "Previous Count", "Count"), response.getColumnDisplayNames());
        assertEquals(currentTimeFrame, response.getCurrentTimeFrame());
        assertEquals(comparisonTimeFrame, response.getComparisonTimeFrame());
        assertEquals(pagination, response.getPagination());
        assertEquals(Collections.emptyList(), response.getSortByFields());

        List<List<Object>> data = response.getData();
        assertNotNull(data);
        assertEquals(1, data.size());
        List<Object> row = data.get(0);
        assertEquals(4, row.size());
        assertEquals("LDAP", row.get(0));
        assertEquals("Flows", row.get(1));
        assertEquals(100L, row.get(2));
        assertEquals(200L, row.get(3));
    }

    @Test
    void ipTopDestinationRolesResponseTest() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.IP_TOP_DESTINATION_ROLES)
                .build();
        KustoResultSetTable resultSetTable = mock(KustoResultSetTable.class);
        when(resultSetTable.next()).thenReturn(true, false);

        when(resultSetTable.getObject(DESTINATION_ROLE.getTableColumnName())).thenReturn("LDAP");
        when(resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName())).thenReturn("Flows");
        when(resultSetTable.getObject(PREVIOUS_COUNT.getTableColumnName())).thenReturn(100);
        when(resultSetTable.getObject(COUNT.getTableColumnName())).thenReturn(200);

        RequestPayload payload = RequestPayload.builder().build();
        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);
        Pagination pagination = Pagination.builder().build();
        pagination.setRowLimit(15);
        payload.setPagination(pagination);
        payload.setSortByFields(Collections.emptyList());
        Filters filter = Filters.builder()
                .categoryName("traffic_direction")
                .categoryType("string")
                .categoryValue(List.of("INBOUND"))
                .build();
        List<Filters> filters = List.of(filter);
        payload.setFilters(filters);

        RequestContext requestContext = RequestContext.builder()
                .requestPayload(Optional.of(payload))
                .build();

        IpTopRolesResponse response = ipTopRolesResponse.buildResponse(resultSetTable,  requestContext, metadata);

        assertEquals(WidgetId.IP_TOP_ROLES, response.getWidgetId());
        assertEquals("Top 15 Roles Communicating With IP", response.getTitle());
        assertEquals(List.of("destination_role", "aggregate_field", "previous_count", "count"), response.getColumns());
        assertEquals(List.of("Destination Role", "Aggregate Field", "Previous Count", "Count"), response.getColumnDisplayNames());
        assertEquals(currentTimeFrame, response.getCurrentTimeFrame());
        assertEquals(comparisonTimeFrame, response.getComparisonTimeFrame());
        assertEquals(pagination, response.getPagination());
        assertEquals(Collections.emptyList(), response.getSortByFields());

        List<List<Object>> data = response.getData();
        assertNotNull(data);
        assertEquals(1, data.size());
        List<Object> row = data.get(0);
        assertEquals(4, row.size());
        assertEquals("LDAP", row.get(0));
        assertEquals("Flows", row.get(1));
        assertEquals(100L, row.get(2));
        assertEquals(200L, row.get(3));
    }
}