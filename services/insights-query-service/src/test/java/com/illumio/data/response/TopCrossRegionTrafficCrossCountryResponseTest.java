package com.illumio.data.response;

import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.WidgetId;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.SOURCE_ZONE;
import static com.illumio.data.model.constants.Fields.SOURCE_REGION;
import static com.illumio.data.model.constants.Fields.DESTINATION_ZONE;
import static com.illumio.data.model.constants.Fields.DESTINATION_REGION;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.PREVIOUS_COUNT;
import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TopCrossRegionTrafficCrossCountryResponseTest {

    private TopCrossRegionCountryInsightsResponse responseBuilder;
    private KustoResultSetTable resultSetTable;
    private RequestContext requestContext;
    private Metadata metadata = Metadata.builder()
            .widgetId(WidgetId.TOP_CROSS_REGION_TRAFFIC_COUNTRY_INSIGHTS)
            .build();

    @BeforeEach
    void setUp() {
        responseBuilder = new TopCrossRegionCountryInsightsResponse();
        resultSetTable = mock(KustoResultSetTable.class);
        RequestPayload payload = RequestPayload.builder().build();
        TimeFrame currentTimeFrame = TimeFrame.builder()
                .startTime("2025-01-01T00:00:00Z")
                .endTime("2025-01-02T00:00:00Z")
                .build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder()
                .startTime("2025-01-01T00:00:00Z")
                .endTime("2025-01-02T00:00:00Z")
                .build();
        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);

        Pagination pagination = Pagination.builder().rowLimit(10).build();
        payload.setPagination(pagination);

        List<SortByFields> sortByFields = List.of(SortByFields.builder()
                .field("flows")
                .order("desc")
                .build());
        payload.setSortByFields(sortByFields);

        requestContext = RequestContext.builder()
                .requestPayload(Optional.of(payload))
                .build();
    }

    @Test
    void testBuildResponse() {
        when(resultSetTable.next()).thenReturn(true, false);

        when(resultSetTable.getObject(SOURCE_ZONE.getTableColumnName())).thenReturn("US-West");
        when(resultSetTable.getObject(SOURCE_REGION.getTableColumnName())).thenReturn("Azure");
        when(resultSetTable.getObject(DESTINATION_ZONE.getTableColumnName())).thenReturn("EU-Central");
        when(resultSetTable.getObject(DESTINATION_REGION.getTableColumnName())).thenReturn("AWS");
        when(resultSetTable.getObject(COUNT.getTableColumnName())).thenReturn(1000);
        when(resultSetTable.getObject(PREVIOUS_COUNT.getTableColumnName())).thenReturn(900);;
        when(resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName())).thenReturn("FLOWS");

        TopCrossRegionCountryInsightsResponse response = responseBuilder.buildResponse(resultSetTable, requestContext, metadata);

        assertEquals(WidgetId.TOP_CROSS_REGION_TRAFFIC_COUNTRY_INSIGHTS, response.getWidgetId());
        assertEquals("Top Cross Region Traffic", response.getTitle());

        assertEquals(requestContext.getRequestPayload().get().getCurrentTimeFrame(), response.getCurrentTimeFrame());
        assertEquals(requestContext.getRequestPayload().get().getComparisonTimeFrame(), response.getComparisonTimeFrame());
        assertEquals(requestContext.getRequestPayload().get().getPagination(), response.getPagination());
        assertEquals(requestContext.getRequestPayload().get().getSortByFields(), response.getSortByFields());

        List<List<Object>> data = response.getData();
        assertNotNull(data);
        assertEquals(1, data.size());
        List<Object> row = data.get(0);
        assertEquals(7, row.size());
        assertEquals("US-West", row.get(0));
        assertEquals("Azure", row.get(1));
        assertEquals("EU-Central", row.get(2));
        assertEquals("AWS", row.get(3));
        assertEquals(1000L, row.get(4));
        assertEquals(900L, row.get(5));
        assertEquals("FLOWS", row.get(6));
    }
}
