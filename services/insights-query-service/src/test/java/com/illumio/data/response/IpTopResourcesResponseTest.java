package com.illumio.data.response;

import com.illumio.data.model.Filters;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.WidgetId;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.DESTINATION_RESOURCE_CATEGORY;
import static com.illumio.data.model.constants.Fields.DESTINATION_RESOURCE_ID;
import static com.illumio.data.model.constants.Fields.DESTINATION_RESOURCE_NAME;
import static com.illumio.data.model.constants.Fields.DESTINATION_RESOURCE_TYPE;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_CATEGORY;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_ID;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_NAME;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_TYPE;
import static com.illumio.data.model.constants.Fields.TIME_SERIES;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class IpTopResourcesResponseTest {

    private IpTopResourcesResponse ipTopResourcesResponse;

    @BeforeEach
    void setUp() {
        ipTopResourcesResponse = new IpTopResourcesResponse();
    }

    @Test
    void ipTopSourceResourcesResponseTest() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.IP_TOP_SOURCE_RESOURCES)
                .build();
        KustoResultSetTable resultSetTable = mock(KustoResultSetTable.class);
        when(resultSetTable.next()).thenReturn(true, false);

        when(resultSetTable.getObject(SOURCE_RESOURCE_ID.getTableColumnName())).thenReturn("sample_id");
        when(resultSetTable.getObject(SOURCE_RESOURCE_NAME.getTableColumnName())).thenReturn("sample_name");
        when(resultSetTable.getObject(SOURCE_RESOURCE_CATEGORY.getTableColumnName())).thenReturn("sample_category");
        when(resultSetTable.getObject(SOURCE_RESOURCE_TYPE.getTableColumnName())).thenReturn("sample_type");
        when(resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName())).thenReturn("FLOWS");
        when(resultSetTable.getObject(TIME_SERIES.getTableColumnName())).thenReturn("2025-01-01T00:00:00Z");
        when(resultSetTable.getObject(COUNT.getTableColumnName())).thenReturn(321);

        RequestPayload payload = RequestPayload.builder().build();
        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);
        Pagination pagination = Pagination.builder().build();
        pagination.setRowLimit(10);
        payload.setPagination(pagination);
        payload.setSortByFields(Collections.emptyList());
        Filters filter = Filters.builder()
                .categoryName("traffic_direction")
                .categoryType("string")
                .categoryValue(List.of("OUTBOUND"))
                .build();
        List<Filters> filters = List.of(filter);
        payload.setFilters(filters);

        RequestContext requestContext = RequestContext.builder()
                .requestPayload(Optional.of(payload))
                .build();

        IpTopResourcesResponse response = ipTopResourcesResponse.buildResponse(resultSetTable, requestContext, metadata);

        assertEquals(WidgetId.IP_TOP_RESOURCES, response.getWidgetId());
        assertEquals("Top 10 Resources Communicating With IP", response.getTitle());
        assertEquals(List.of(
                "source_resource_id",
                "source_resource_name",
                "source_resource_cat",
                "source_resource_type",
                "aggregate_field",
                "time_series"), response.getColumns());
        assertEquals(List.of(
                "Source Resource Id",
                "Source Resource Name",
                "Source Resource Category",
                "Source Resource Type",
                "Aggregate Field",
                "Time Series"), response.getColumnDisplayNames());
        assertEquals(currentTimeFrame, response.getCurrentTimeFrame());
        assertEquals(comparisonTimeFrame, response.getComparisonTimeFrame());
        assertEquals(pagination, response.getPagination());
        assertEquals(Collections.emptyList(), response.getSortByFields());

        List<List<Object>> data = response.getData();
        List<List<Object>> timeSeriesData = new ArrayList<>();
        timeSeriesData.add(List.of("2025-01-01T00:00:00Z", 321L));

        assertNotNull(data);
        assertEquals(1, data.size());
        List<Object> row = data.get(0);
        assertEquals(6, row.size());
        assertEquals("sample_id", row.get(0));
        assertEquals("sample_name", row.get(1));
        assertEquals("sample_category", row.get(2));
        assertEquals("sample_type", row.get(3));
        assertEquals("FLOWS", row.get(4));
        assertEquals(timeSeriesData, row.get(5));
    }

    @Test
    void ipTopDestinationResourcesResponseTest() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.IP_TOP_DESTINATION_RESOURCES)
                .build();
        KustoResultSetTable resultSetTable = mock(KustoResultSetTable.class);
        when(resultSetTable.next()).thenReturn(true, false);

        when(resultSetTable.getObject(DESTINATION_RESOURCE_ID.getTableColumnName())).thenReturn("sample_id");
        when(resultSetTable.getObject(DESTINATION_RESOURCE_NAME.getTableColumnName())).thenReturn("sample_name");
        when(resultSetTable.getObject(DESTINATION_RESOURCE_CATEGORY.getTableColumnName())).thenReturn("sample_category");
        when(resultSetTable.getObject(DESTINATION_RESOURCE_TYPE.getTableColumnName())).thenReturn("sample_type");
        when(resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName())).thenReturn("FLOWS");
        when(resultSetTable.getObject(TIME_SERIES.getTableColumnName())).thenReturn("2025-01-01T00:00:00Z");
        when(resultSetTable.getObject(COUNT.getTableColumnName())).thenReturn(321);

        RequestPayload payload = RequestPayload.builder().build();
        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);
        Pagination pagination = Pagination.builder().build();
        pagination.setRowLimit(10);
        payload.setPagination(pagination);
        payload.setSortByFields(Collections.emptyList());
        Filters filter = Filters.builder()
                .categoryName("traffic_direction")
                .categoryType("string")
                .categoryValue(List.of("INBOUND"))
                .build();
        List<Filters> filters = List.of(filter);
        payload.setFilters(filters);

        RequestContext requestContext = RequestContext.builder()
                .requestPayload(Optional.of(payload))
                .build();

        IpTopResourcesResponse response = ipTopResourcesResponse.buildResponse(resultSetTable, requestContext, metadata);

        assertEquals(WidgetId.IP_TOP_RESOURCES, response.getWidgetId());
        assertEquals("Top 10 Resources Communicating With IP", response.getTitle());
        assertEquals(List.of(
                "destination_resource_id",
                "dest_resource_name",
                "dest_resource_cat",
                "dest_resource_type",
                "aggregate_field",
                "time_series"), response.getColumns());
        assertEquals(List.of(
                "Destination Resource Id",
                "Destination Resource Name",
                "Destination Resource Category",
                "Destination Resource Type",
                "Aggregate Field",
                "Time Series"), response.getColumnDisplayNames());
        assertEquals(currentTimeFrame, response.getCurrentTimeFrame());
        assertEquals(comparisonTimeFrame, response.getComparisonTimeFrame());
        assertEquals(pagination, response.getPagination());
        assertEquals(Collections.emptyList(), response.getSortByFields());

        List<List<Object>> data = response.getData();
        List<List<Object>> timeSeriesData = new ArrayList<>();
        timeSeriesData.add(List.of("2025-01-01T00:00:00Z", 321L));

        assertNotNull(data);
        assertEquals(1, data.size());
        List<Object> row = data.get(0);
        assertEquals(6, row.size());
        assertEquals("sample_id", row.get(0));
        assertEquals("sample_name", row.get(1));
        assertEquals("sample_category", row.get(2));
        assertEquals("sample_type", row.get(3));
        assertEquals("FLOWS", row.get(4));
        assertEquals(timeSeriesData, row.get(5));
    }
}