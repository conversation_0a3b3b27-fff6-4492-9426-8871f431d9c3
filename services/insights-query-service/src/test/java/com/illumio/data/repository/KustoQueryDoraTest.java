package com.illumio.data.repository;

import com.illumio.data.model.DerivedMetadata;
import com.illumio.data.model.Filters;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.TableType;
import com.illumio.data.model.constants.WidgetId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.AGGREGATED_BYTES;
import static com.illumio.data.model.constants.Fields.AGGREGATED_FLOWS;
import static com.illumio.data.model.constants.Fields.SOURCE_EXTERNAL_LABEL;
import static com.illumio.data.model.constants.Fields.SOURCE_EXTERNAL_LABEL_CATEGORY;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;

public class KustoQueryDoraTest {
    private KustoQueryRepository repository;
    private RequestPayload payload;
    private final String tenantId = "testTenant";
    private RequestContext requestContext;


    @BeforeEach
    void setUp() {
        repository = new KustoQueryRepository();

        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();

        Pagination pagination = Pagination.builder().build();
        pagination.setPageNumber(1);
        pagination.setRowLimit(10);

        payload = RequestPayload.builder().build();
        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);
        payload.setPagination(pagination);
        payload.setSortByFields(Collections.emptyList());
        DerivedMetadata derivedMetadata = new DerivedMetadata(
                "Insights_InfiltrationTraffic_Hourly",
                "Insights_InfiltrationTraffic_Hourly",
                currentTimeFrame,
                comparisonTimeFrame,
                TableType.AGGREGATED_TABLE,
                TableType.AGGREGATED_TABLE
        );

        requestContext = RequestContext.builder()
                .tenantId(Optional.of(tenantId))
                .requestPayload(Optional.of(payload))
                .derivedMetadata(Optional.of(derivedMetadata))
                .build();
    }

    @Test
    void testGetQueryString_ThirdPartyInbound() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.THIRD_PARTY_DEPENDENCY_INBOUND)
                .groupByFields(List.of(SOURCE_EXTERNAL_LABEL_CATEGORY, SOURCE_EXTERNAL_LABEL))
                .summarizeFields(List.of(AGGREGATED_FLOWS, AGGREGATED_BYTES))
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);
        assertTrue(query.contains("Insights_InfiltrationTraffic_Hourly"), "Query should contain the base table");
        assertTrue(query.contains(String.format("| where IllumioTenantId == '%s'", tenantId)), "Query should contain tenant condition");
        assertTrue(query.contains("| summarize"), "Query should include grouping logic");
        assertTrue(query.contains("|sort by"), "Query should include sorting logic");
        assertTrue(query.contains("let curr = "), "Query should include current and previous logic");
        assertTrue(query.contains("let prev = "), "Query should include current and previous logic");
        assertTrue(query.contains("let totalRows = toscalar(rows | count);"), "Query should include pagination logic");
        assertFalse(query.contains("TrafficStatus in ('ALLOWED')"), "Query should not contain traffic status filter");

        //adding the traffic status filter
        payload.setFilters(List.of(
                Filters.builder()
                        .categoryName("traffic_status")
                        .categoryValue(List.of("ALLOWED"))
                        .categoryType("string")
                        .build()
        ));
        String trafficQuery = repository.getQueryString(requestContext, metadata);
        assertTrue(trafficQuery.contains("TrafficStatus in ('ALLOWED')"), "Query should contain traffic status filter");
    }
}
