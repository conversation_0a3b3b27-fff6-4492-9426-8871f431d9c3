package com.illumio.data.service;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import org.mockito.Mockito;

@ExtendWith(MockitoExtension.class)
class RequestForwardingServiceTest {

    private RequestForwardingService requestForwardingService;

    @Mock
    private InsightsServiceConfiguration configuration;

    @Mock
    private InsightsServiceConfiguration.ProxyConfig proxyConfig;

    @Mock
    private WebClient webClient;

    @Mock
    @SuppressWarnings("rawtypes")
    private WebClient.RequestHeadersUriSpec requestHeadersUriSpec;

    @Mock
    @SuppressWarnings("rawtypes")
    private WebClient.RequestHeadersSpec requestHeadersSpec;

    @Mock
    private WebClient.ResponseSpec responseSpec;

    private static final String TARGET_FQDN = "https://target.example.com";
    private static final String TARGET_ENDPOINT = "/api/v1/target-endpoint";

    @BeforeEach
    void setUp() {
        requestForwardingService = new RequestForwardingService(webClient, configuration);

        // Default configuration setup
        when(configuration.getProxyConfig()).thenReturn(proxyConfig);
        // Use lenient stubbing for optional configuration that may not be used in all tests
        Mockito.lenient().when(proxyConfig.getTargetFqdn()).thenReturn(TARGET_FQDN);
        Mockito.lenient().when(proxyConfig.getTargetEndpoint()).thenReturn(TARGET_ENDPOINT);
    }

    @SuppressWarnings({"unchecked"})
    private void setupWebClientMocks() {
        // WebClient method chaining setup
        when(webClient.get()).thenReturn(requestHeadersUriSpec);
        when(requestHeadersUriSpec.uri(anyString(), any(Function.class))).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.headers(any())).thenReturn(requestHeadersSpec);
        when(requestHeadersSpec.retrieve()).thenReturn(responseSpec);
    }

    @Test
    void testForwardRequest_ProxyDisabled_ReturnsEmpty() {
        // Arrange
        when(proxyConfig.isEnableProxy()).thenReturn(false);

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

        // Act
        Mono<ResponseEntity<Object>> result = requestForwardingService.forwardRequest(headers, params);

        // Assert
        StepVerifier.create(result)
                .verifyComplete(); // Should complete without emitting any item (empty)
    }

    @Test
    void testForwardRequest_Success() {
        // Arrange
        when(proxyConfig.isEnableProxy()).thenReturn(true);
        setupWebClientMocks();
        
        ResponseEntity<Object> expectedResponse = ResponseEntity.ok().body(Map.of("data", "test response"));
        when(responseSpec.toEntity(Object.class)).thenReturn(Mono.just(expectedResponse));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("x-auth-key", "test-key");
        headers.add("x-tenant-id", "test-tenant");

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("after", "1640995200000");
        params.add("limit", "100");

        // Act
        Mono<ResponseEntity<Object>> result = requestForwardingService.forwardRequest(headers, params);

        // Assert
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(HttpStatus.OK, response.getStatusCode());
                    assertNotNull(response.getBody());
                    @SuppressWarnings("unchecked")
                    Map<String, Object> body = (Map<String, Object>) response.getBody();
                    assertEquals("test response", body.get("data"));
                })
                .verifyComplete();
    }

    @Test
    void testForwardRequest_WithMultipleValueHeaders() {
        // Arrange
        when(proxyConfig.isEnableProxy()).thenReturn(true);
        setupWebClientMocks();
        
        ResponseEntity<Object> expectedResponse = ResponseEntity.ok().body(Map.of("success", true));
        when(responseSpec.toEntity(Object.class)).thenReturn(Mono.just(expectedResponse));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.addAll("x-custom-header", List.of("value1", "value2"));

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.addAll("tags", List.of("tag1", "tag2"));

        // Act
        Mono<ResponseEntity<Object>> result = requestForwardingService.forwardRequest(headers, params);

        // Assert
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(HttpStatus.OK, response.getStatusCode());
                    assertNotNull(response.getBody());
                })
                .verifyComplete();
    }

    @Test
    void testForwardRequest_WithNullAndEmptyValues() {
        // Arrange
        when(proxyConfig.isEnableProxy()).thenReturn(true);
        setupWebClientMocks();
        
        ResponseEntity<Object> expectedResponse = ResponseEntity.ok().body(Map.of("success", true));
        when(responseSpec.toEntity(Object.class)).thenReturn(Mono.just(expectedResponse));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("valid-header", "valid-value");
        headers.put("null-header", null);
        headers.put("empty-header", List.of());

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("valid-param", "valid-value");
        params.put("null-param", null);
        params.put("empty-param", List.of());

        // Act
        Mono<ResponseEntity<Object>> result = requestForwardingService.forwardRequest(headers, params);

        // Assert
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(HttpStatus.OK, response.getStatusCode());
                    assertNotNull(response.getBody());
                })
                .verifyComplete();
    }

    @Test
    void testForwardRequest_WebClientResponseException() {
        // Arrange
        when(proxyConfig.isEnableProxy()).thenReturn(true);
        setupWebClientMocks();
        
        WebClientResponseException webClientException = WebClientResponseException.create(
                404, "Not Found", null, "{\"error\": \"Not Found\"}".getBytes(), null);
        when(responseSpec.toEntity(Object.class)).thenReturn(Mono.error(webClientException));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

        // Act
        Mono<ResponseEntity<Object>> result = requestForwardingService.forwardRequest(headers, params);

        // Assert
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
                    @SuppressWarnings("unchecked")
                    Map<String, Object> body = (Map<String, Object>) response.getBody();
                    assertNotNull(body);
                    assertEquals("Upstream Error", body.get("error"));
                    assertTrue(body.get("message").toString().contains("Error from target service"));
                })
                .verifyComplete();
    }

    @Test
    void testForwardRequest_WebClientResponseException_BadGateway() {
        // Arrange
        when(proxyConfig.isEnableProxy()).thenReturn(true);
        setupWebClientMocks();
        
        WebClientResponseException webClientException = WebClientResponseException.create(
                502, "Bad Gateway", null, "Bad Gateway".getBytes(), null);
        when(responseSpec.toEntity(Object.class)).thenReturn(Mono.error(webClientException));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

        // Act
        Mono<ResponseEntity<Object>> result = requestForwardingService.forwardRequest(headers, params);

        // Assert
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(HttpStatus.BAD_GATEWAY, response.getStatusCode());
                    @SuppressWarnings("unchecked")
                    Map<String, Object> body = (Map<String, Object>) response.getBody();
                    assertNotNull(body);
                    assertEquals("Upstream Error", body.get("error"));
                })
                .verifyComplete();
    }

    @Test
    void testForwardRequest_NetworkException() {
        // Arrange
        when(proxyConfig.isEnableProxy()).thenReturn(true);
        setupWebClientMocks();
        
        RuntimeException networkException = new RuntimeException("Connection timeout");
        when(responseSpec.toEntity(Object.class)).thenReturn(Mono.error(networkException));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();

        // Act
        Mono<ResponseEntity<Object>> result = requestForwardingService.forwardRequest(headers, params);

        // Assert
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(HttpStatus.SERVICE_UNAVAILABLE, response.getStatusCode());
                    @SuppressWarnings("unchecked")
                    Map<String, Object> body = (Map<String, Object>) response.getBody();
                    assertNotNull(body);
                    assertEquals("Service Unavailable", body.get("error"));
                    assertEquals("Target service is currently unavailable", body.get("message"));
                })
                .verifyComplete();
    }

    @Test
    void testForwardRequest_NoQueryParams() {
        // Arrange
        when(proxyConfig.isEnableProxy()).thenReturn(true);
        setupWebClientMocks();
        
        ResponseEntity<Object> expectedResponse = ResponseEntity.ok().body(Map.of("success", true));
        when(responseSpec.toEntity(Object.class)).thenReturn(Mono.just(expectedResponse));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("x-auth-key", "test-key");

        MultiValueMap<String, String> params = new LinkedMultiValueMap<>(); // Empty params

        // Act
        Mono<ResponseEntity<Object>> result = requestForwardingService.forwardRequest(headers, params);

        // Assert
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(HttpStatus.OK, response.getStatusCode());
                    assertNotNull(response.getBody());
                })
                .verifyComplete();
    }

    @Test
    void testForwardRequest_NoHeaders() {
        // Arrange
        when(proxyConfig.isEnableProxy()).thenReturn(true);
        setupWebClientMocks();
        
        ResponseEntity<Object> expectedResponse = ResponseEntity.ok().body(Map.of("success", true));
        when(responseSpec.toEntity(Object.class)).thenReturn(Mono.just(expectedResponse));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>(); // Empty headers
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("test", "value");

        // Act
        Mono<ResponseEntity<Object>> result = requestForwardingService.forwardRequest(headers, params);

        // Assert
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(HttpStatus.OK, response.getStatusCode());
                    assertNotNull(response.getBody());
                })
                .verifyComplete();
    }

    @Test 
    void testForwardRequest_SuccessfulRetryAfterError() {
        // Arrange
        when(proxyConfig.isEnableProxy()).thenReturn(true);
        setupWebClientMocks();
        
        ResponseEntity<Object> expectedResponse = ResponseEntity.ok().body(Map.of("data", "success after retry"));
        when(responseSpec.toEntity(Object.class)).thenReturn(Mono.just(expectedResponse));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("x-retry-header", "retry-value");
        
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("retry", "true");

        // Act
        Mono<ResponseEntity<Object>> result = requestForwardingService.forwardRequest(headers, params);

        // Assert
        StepVerifier.create(result)
                .assertNext(response -> {
                    assertEquals(HttpStatus.OK, response.getStatusCode());
                    assertNotNull(response.getBody());
                    @SuppressWarnings("unchecked")
                    Map<String, Object> body = (Map<String, Object>) response.getBody();
                    assertEquals("success after retry", body.get("data"));
                })
                .verifyComplete();
    }
}