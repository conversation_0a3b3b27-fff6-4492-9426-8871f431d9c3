package com.illumio.data.response;

import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.WidgetId;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Optional;

import static com.illumio.data.model.constants.Fields.ZONE;
import static com.illumio.data.model.constants.Fields.REGION;
import static com.illumio.data.model.constants.Fields.COUNTRY;
import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TrafficActivityAcrossCountriesCrossCountryResponseTest {
    private TrafficActivityAcrossCountriesCrossCountryResponse responseBuilder;
    private Metadata metadata = Metadata.builder()
            .widgetId(WidgetId.TRAFFIC_ACTIVITY_ACROSS_COUNTRIES_CROSS_COUNTRY)
            .build();

    @BeforeEach
    void setUp() {
        responseBuilder = new TrafficActivityAcrossCountriesCrossCountryResponse();
    }

    @Test
    void testBuildResponse() {
        KustoResultSetTable resultSetTable = mock(KustoResultSetTable.class);
        when(resultSetTable.next()).thenReturn(true, false);

        when(resultSetTable.getObject(ZONE.getTableColumnName())).thenReturn("AWS");
        when(resultSetTable.getObject(REGION.getTableColumnName())).thenReturn("us-east-1");
        when(resultSetTable.getObject(COUNTRY.getTableColumnName())).thenReturn("US");
        when(resultSetTable.getObject(COUNT.getTableColumnName())).thenReturn(123L);
        when(resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName())).thenReturn("FLOWS");

        RequestPayload payload = RequestPayload.builder().build();
        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-04-30T00:00:00Z").endTime("2025-05-01T23:59:59Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-04-27T00:00:00Z").endTime("2025-04-29T23:59:59Z").build();
        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);
        Pagination pagination = Pagination.builder().build();
        pagination.setRowLimit(10);
        payload.setPagination(pagination);

        RequestContext requestContext = RequestContext.builder()
                .requestPayload(Optional.of(payload))
                .build();

        TrafficActivityAcrossCountriesCrossCountryResponse response = responseBuilder.buildResponse(resultSetTable, requestContext, metadata);

        assertEquals(WidgetId.TRAFFIC_ACTIVITY_ACROSS_COUNTRIES_CROSS_COUNTRY, response.getWidgetId());
        assertEquals("Traffic Activity Across Countries", response.getTitle());
        assertEquals(currentTimeFrame, response.getCurrentTimeFrame());
        assertEquals(comparisonTimeFrame, response.getComparisonTimeFrame());
        assertEquals(pagination, response.getPagination());

        List<List<Object>> data = response.getData();
        assertNotNull(data);
        assertEquals(1, data.size());
        List<Object> row = data.get(0);
        assertEquals(5, row.size());
        assertEquals("AWS", row.get(0));
        assertEquals("us-east-1", row.get(1));
        assertEquals("US", row.get(2));
        assertEquals(123L, row.get(3));
        assertEquals("FLOWS", row.get(4));
    }
}
