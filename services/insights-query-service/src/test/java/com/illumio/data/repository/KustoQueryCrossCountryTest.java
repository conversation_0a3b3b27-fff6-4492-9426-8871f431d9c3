package com.illumio.data.repository;

import com.illumio.data.model.DerivedMetadata;
import com.illumio.data.model.Filters;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.WidgetId;
import com.illumio.data.model.constants.TableType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.illumio.data.model.constants.WidgetId.TRAFFIC_ACTIVITY_ACROSS_COUNTRIES_CROSS_COUNTRY;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;

public class KustoQueryCrossCountryTest {

    private KustoQueryRepository repository;
    private RequestPayload payload;
    private final String tenantId = "testTenant";
    private RequestContext requestContext;

    @BeforeEach
    void setUp() {
        repository = new KustoQueryRepository();

        TimeFrame currentTimeFrame = TimeFrame.builder()
                .startTime("2025-04-30T00:00:00Z")
                .endTime("2025-05-01T23:59:59Z")
                .build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder()
                .startTime("2025-04-27T00:00:00Z")
                .endTime("2025-04-29T23:59:59Z")
                .build();

        Pagination pagination = Pagination.builder().build();
        pagination.setPageNumber(1);
        pagination.setRowLimit(10);

        payload = RequestPayload.builder().build();
        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);
        payload.setPagination(pagination);
        payload.setFilters(new ArrayList<>(List.of(
                Filters.builder().categoryType("string").categoryName("zone").categoryValue(List.of("AWS")).build(),
                Filters.builder().categoryType("string").categoryName("region").categoryValue(List.of("us-east-1")).build(),
                Filters.builder().categoryType("string").categoryName("service").categoryValue(List.of("HTTPS")).build()
        )));
        payload.setSortByFields(new ArrayList<>());

        DerivedMetadata derivedMetadata = new DerivedMetadata(
                "Insights_RiskyTraffic_Daily",
                "Insights_RiskyTraffic_Daily",
                currentTimeFrame,
                comparisonTimeFrame,
                TableType.AGGREGATED_TABLE,
                TableType.AGGREGATED_TABLE
        );

        requestContext = RequestContext.builder()
                .tenantId(Optional.of(tenantId))
                .requestPayload(Optional.of(payload))
                .derivedMetadata(Optional.of(derivedMetadata))
                .build();
    }

    @Test
    void testGetQueryString_TopCrossRegionTrafficCountryInsights() {
        Metadata metadata = Metadata.builder()
                .widgetId("top-cross-region-traffic")
                .groupByFields(List.of(
                        Fields.SOURCE_ZONE,
                        Fields.SOURCE_REGION,
                        Fields.DESTINATION_ZONE,
                        Fields.DESTINATION_REGION
                ))
                .summarizeFields(List.of(Fields.AGGREGATED_FLOWS))
                .tableName("RiskyTraffic")
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);
        assertTrue(query.contains("RiskyTraffic"), "Query should contain risky traffic database name");
        assertTrue(query.contains("SourceZone in~"), "Query should contain SourceZone");
        assertTrue(query.contains("DestinationZone in~"), "Query should contain DestinationZone");
        assertTrue(query.contains("SrcRegion in~"), "Query should contain SrcRegion");
        assertTrue(query.contains("DestRegion in~"), "Query should contain DestRegion");
        assertTrue(query.contains("AggFlowCount"), "Query should contain AggFlowCount");
        assertFalse(query.contains("TrafficStatus in ('ALLOWED')"), "Query should not contain traffic status filter");

        //adding the traffic status filter
        payload.setFilters(List.of(
                Filters.builder()
                        .categoryName("traffic_status")
                        .categoryValue(List.of("ALLOWED"))
                        .categoryType("string")
                        .build()
        ));
        String trafficQuery = repository.getQueryString(requestContext, metadata);
        assertTrue(trafficQuery.contains("TrafficStatus in ('ALLOWED')"), "Query should contain traffic status filter");
    }

    @Test
    void testGetQueryString_TrafficActivityAcrossCountries() {
        Metadata metadata = Metadata.builder()
                .widgetId(TRAFFIC_ACTIVITY_ACROSS_COUNTRIES_CROSS_COUNTRY)
                .groupByFields(List.of(
                        Fields.SOURCE_ZONE,
                        Fields.SOURCE_REGION,
                        Fields.DESTINATION_ZONE,
                        Fields.DESTINATION_REGION
                ))
                .summarizeFields(List.of(Fields.AGGREGATED_FLOWS, Fields.AGGREGATED_BYTES))
                .tableName("RiskyTraffic")
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);
        // Check that both source and destination zone/region are present
        assertTrue(query.contains("SourceZone in~"), "Query should contain SourceZone");
        assertTrue(query.contains("DestinationZone in~"), "Query should contain DestinationZone");
        assertTrue(query.contains("SrcRegion in~"), "Query should contain SrcRegion");
        assertTrue(query.contains("DestRegion in~"), "Query should contain DestRegion");
        assertTrue(query.contains("AggFlowCount"), "Query should contain AggFlowCount");
        assertTrue(query.contains("AggByteCount"), "Query should contain AggByteCount");
        assertTrue(query.contains("let topCountriesOutFLOWS"), "Query should contain let topCountriesOutFLOWS");
        assertTrue(query.contains("let byRegionCountriesOutFLOWS"), "Query should contain let byRegionCountriesOutFLOWS");
        assertTrue(query.contains("union byRegionCountriesFLOWS, byRegionCountriesBYTES;"), "Query should contain the final union");
        assertFalse(query.contains("TrafficStatus in ('ALLOWED')"), "Query should not contain traffic status filter");
    }
}
