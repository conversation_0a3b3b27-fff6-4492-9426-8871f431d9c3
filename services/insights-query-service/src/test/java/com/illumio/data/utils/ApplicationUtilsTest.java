package com.illumio.data.utils;

import com.illumio.data.model.Filters;
import com.illumio.data.model.constants.TrafficDirection;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class ApplicationUtilsTest {
    @Test
    void testNullFilters() {
        TrafficDirection result = ApplicationUtils.getTrafficDirection(null);
        assertEquals(TrafficDirection.NONE, result);
    }

    @Test
    void testEmptyFilters() {
        TrafficDirection result = ApplicationUtils.getTrafficDirection(List.of());
        assertEquals(TrafficDirection.NONE, result);
    }

    @Test
    void testNoTrafficDirectionCategory() {
        Filters filter = Filters.builder()
                .categoryName("ip")
                .categoryType("string")
                .categoryValue(List.of("***********", "***********"))
                .build();
        List<Filters> filters = List.of(filter);
        TrafficDirection result = ApplicationUtils.getTrafficDirection(filters);
        assertEquals(TrafficDirection.NONE, result);
    }

    @Test
    void testInboundTrafficDirection() {
        Filters filter = Filters.builder()
                .categoryName("traffic_direction")
                .categoryType("string")
                .categoryValue(List.of("INBOUND"))
                .build();
        List<Filters> filters = List.of(filter);
        TrafficDirection result = ApplicationUtils.getTrafficDirection(filters);
        assertEquals(TrafficDirection.INBOUND, result);
    }

    @Test
    void testOutboundTrafficDirection() {
        Filters filter = Filters.builder()
                .categoryName("traffic_direction")
                .categoryType("string")
                .categoryValue(List.of("OUTBOUND"))
                .build();
        List<Filters> filters = List.of(filter);
        TrafficDirection result = ApplicationUtils.getTrafficDirection(filters);
        assertEquals(TrafficDirection.OUTBOUND, result);
    }
}