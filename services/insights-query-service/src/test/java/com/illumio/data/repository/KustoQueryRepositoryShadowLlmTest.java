package com.illumio.data.repository;
import com.illumio.data.model.Filters;
import com.illumio.data.model.DerivedMetadata;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.TableType;
import com.illumio.data.model.constants.WidgetId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.Optional;
import java.util.List;

import static com.illumio.data.model.constants.Fields.DESTINATION_SUBSCRIPTION_ID;
import static com.illumio.data.model.constants.Fields.SOURCE_IP;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;

public class KustoQueryRepositoryShadowLlmTest {
    private KustoQueryRepository repository;
    private RequestPayload payload;
    private final String tenantId = "testTenant";
    private RequestContext requestContext;

    @BeforeEach
    void setUp() {
        repository = new KustoQueryRepository();

        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-03-05T00:00:00Z").endTime("2025-04-03T23:59:59Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-02-01T00:00:00Z").endTime("2025-02-28T00:00:00Z").build();

        Pagination pagination = Pagination.builder().build();
        pagination.setPageNumber(1);
        pagination.setRowLimit(10);



        payload = RequestPayload.builder().build();
        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);
        payload.setPagination(pagination);
        payload.setFilters(List.of(
                        Filters.builder()
                        .categoryName("category")
                        .categoryValue(List.of("subscriptions"))
                        .categoryType("string")
                        .build()));
        payload.setSortByFields(Collections.emptyList());

        DerivedMetadata derivedMetadata = new DerivedMetadata(
                "Insights_ExfiltrationTraffic_Hourly",
                "Insights_ExfiltrationTraffic_Hourly",
                currentTimeFrame,
                comparisonTimeFrame,
                TableType.AGGREGATED_TABLE,
                TableType.AGGREGATED_TABLE);
        requestContext = RequestContext.builder()
                .tenantId(Optional.of(tenantId))
                .requestPayload(Optional.of(payload))
                .derivedMetadata(Optional.of(derivedMetadata))
                .build();
    }

    @Test
    void testGetQueryString_LlmInUse() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.LLM_IN_USE)
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);
        assertTrue(query.contains("ExfiltrationTraffic"), "Query should contain external filtration traffic database name");
        assertTrue(query.contains("let llmsInUseCurr = "), "Query should contain current timeframe data");
        assertTrue(query.contains("let llmsInUsePrev = "), "Query should contain previous timeframe data");
        assertTrue(query.contains("join kind=leftouter (llmsInUsePrev)"), "Query should join curr and prev data");
        assertTrue(query.contains("|sort by"), "Query should include sorting logic");
        assertTrue(query.contains("let totalRows = toscalar(rows | count);"), "Query should include pagination logic");
        assertFalse(query.contains("TrafficStatus in ('ALLOWED')"), "Query should not contain traffic status filter");

        //adding the traffic status filter
        payload.setFilters(List.of(
                Filters.builder()
                        .categoryName("traffic_status")
                        .categoryValue(List.of("ALLOWED"))
                        .categoryType("string")
                        .build()
        ));
        String trafficQuery = repository.getQueryString(requestContext, metadata);
        assertTrue(trafficQuery.contains("TrafficStatus in ('ALLOWED')"), "Query should contain traffic status filter");
    }

    @Test
    void testGetQueryString_TopCategoryWithLlm() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.TOP_CATEGORY_WITH_LLM)
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);
        assertTrue(query.contains("ExfiltrationTraffic"), "Query should contain external filtration traffic database name");
        assertTrue(query.contains("let topCategory_FLOWS = "), "Query should contain top category data for FLOWS");
        assertTrue(query.contains("let topLlm_FLOWS = "), "Query should contain current timeframe data for FLOWS");
        assertTrue(query.contains("let prevLlm_FLOWS = "), "Query should contain previous timeframe data for FLOWS");
        assertTrue(query.contains("let topCategory_BYTES = "), "Query should contain top category data for BYTES");
        assertTrue(query.contains("let topLlm_BYTES = "), "Query should contain current timeframe data for BYTES");
        assertTrue(query.contains("let prevLlm_BYTES = "), "Query should contain previous timeframe data for BYTES");
        assertTrue(query.contains("join kind=leftouter (prevLlm_FLOWS)"), "Query should join curr and prev data for FLOWS");
        assertTrue(query.contains("join kind=leftouter (prevLlm_BYTES)"), "Query should join curr and prev data for BYTES");
        assertTrue(query.contains("union summaryJoinFLOWS, summaryJoinBYTES, flowsResult, bytesResult"), "Query should union the top workloads queries");
        assertFalse(query.contains("TrafficStatus in ('ALLOWED')"), "Query should not contain traffic status filter");
    }

    @Test
    void testGetQueryString_TopSourcesWithLlm() {
        Metadata metadata = Metadata.builder()
                .widgetId(WidgetId.TOP_SOURCES_WITH_LLM)
                .groupByFields(List.of(DESTINATION_SUBSCRIPTION_ID, SOURCE_IP))
                .tableName("ExfiltrationTraffic")
                .build();
        String query = repository.getQueryString(requestContext, metadata);
        assertNotNull(query);
        assertTrue(query.contains("ExfiltrationTraffic"), "Query should contain external filtration traffic database name");
        assertTrue(query.contains("let topSourcesByFlows = "), "Query should contain top sources data");
        assertTrue(query.contains("let flowSeries = "), "Query should contain current timeframe data for FLOWS");
        assertTrue(query.contains("let byteSeries = "), "Query should contain current timeframe data for BYTES");
        assertTrue(query.contains(" TimeSeries = bin("+ Fields.START_TIME.getTableColumnName()+", 1h)"), "Query should create a timeseries on start time");
        assertTrue(query.contains("union flowSeries, byteSeries"), "Query should union the top workloads queries");
        assertFalse(query.contains("TrafficStatus in ('ALLOWED')"), "Query should not contain traffic status filter");

        //adding the traffic status filter
        payload.setFilters(List.of(
                Filters.builder()
                        .categoryName("traffic_status")
                        .categoryValue(List.of("ALLOWED"))
                        .categoryType("string")
                        .build()
        ));
        String trafficQuery = repository.getQueryString(requestContext, metadata);
        assertTrue(trafficQuery.contains("TrafficStatus in ('ALLOWED')"), "Query should contain traffic status filter");
    }
}
