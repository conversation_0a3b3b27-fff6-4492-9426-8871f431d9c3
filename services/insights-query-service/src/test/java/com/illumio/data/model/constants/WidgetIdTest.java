package com.illumio.data.model.constants;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
class WidgetIdTest {
    @Test
    void testIpTopResourcesWidgetId_NullFilters() {
        assertEquals("ip-top-resources", WidgetId.resolve(WidgetId.IP_TOP_RESOURCES, TrafficDirection.NONE));
    }

    @Test
    void testIpTopResourcesWidgetId_InboundFilters() {
        assertEquals("ip-top-destination-resources", WidgetId.resolve(WidgetId.IP_TOP_RESOURCES, TrafficDirection.INBOUND));
    }

    @Test
    void testIpTopResourcesWidgetId_OutboundFilters() {
        assertEquals("ip-top-source-resources", WidgetId.resolve(WidgetId.IP_TOP_RESOURCES, TrafficDirection.OUTBOUND));
    }

    @Test
    void testIpTopRolesWidgetId_NullFilters() {
        assertEquals("ip-top-roles", WidgetId.resolve(WidgetId.IP_TOP_ROLES, TrafficDirection.NONE));
    }

    @Test
    void testIpTopRolesWidgetId_InboundFilters() {
        assertEquals("ip-top-destination-roles", WidgetId.resolve(WidgetId.IP_TOP_ROLES, TrafficDirection.INBOUND));
    }

    @Test
    void testIpTopRolesWidgetId_OutboundFilters() {
        assertEquals("ip-top-source-roles", WidgetId.resolve(WidgetId.IP_TOP_ROLES, TrafficDirection.OUTBOUND));
    }

    @Test
    void testIpDataTransferWidgetId_NullFilters() {
        assertEquals("ip-data-transfer", WidgetId.resolve(WidgetId.IP_DATA_TRANSFER, TrafficDirection.NONE));
    }

    @Test
    void testIpDataTransferWidgetId_InboundFilters() {
        assertEquals("ip-data-transfer-inbound", WidgetId.resolve(WidgetId.IP_DATA_TRANSFER, TrafficDirection.INBOUND));
    }

    @Test
    void testIpDataTransferWidgetId_OutboundFilters() {
        assertEquals("ip-data-transfer-outbound", WidgetId.resolve(WidgetId.IP_DATA_TRANSFER, TrafficDirection.OUTBOUND));
    }

    @Test
    void testGenericWidgetId_EmptyFilters() {
        assertEquals("4321", WidgetId.resolve(WidgetId.DESTINATION_ROLE_LEVEL_TRAFFIC, TrafficDirection.NONE));
    }

    @Test
    void testGenericWidgetId_InboundFilters() {
        assertEquals("4321", WidgetId.resolve(WidgetId.DESTINATION_ROLE_LEVEL_TRAFFIC, TrafficDirection.INBOUND));
    }

    @Test
    void testGenericWidgetId_OutboundFilters() {
        assertEquals("4321", WidgetId.resolve(WidgetId.DESTINATION_ROLE_LEVEL_TRAFFIC, TrafficDirection.OUTBOUND));
    }
}