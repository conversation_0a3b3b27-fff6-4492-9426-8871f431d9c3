package com.illumio.data.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.configuration.InsightsServiceConfiguration.UnencryptedServiceConfig;
import com.illumio.data.service.UnencryptedServiceInfo.PortProtocolPair;
import com.illumio.data.service.UnencryptedServiceInfo.ServiceInfo;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;

class UnencryptedServiceInfoTest {

    private Path tempFile;
    private InsightsServiceConfiguration config;
    private UnencryptedServiceInfo unencryptedServiceInfo;

    private UnencryptedServiceInfo getService(String filePath) {
        InsightsServiceConfiguration config = mock(InsightsServiceConfiguration.class);
        UnencryptedServiceConfig serviceConfig = mock(UnencryptedServiceConfig.class);
        when(serviceConfig.getUnencryptedServiceFilePath()).thenReturn(filePath);
        when(config.getUnencryptedServiceConfig()).thenReturn(serviceConfig);
        return new UnencryptedServiceInfo(config);
    }

    @BeforeEach
    void setUp() throws IOException {
        String content = """
                Port,Service,Proto,Severity,Category,OS\n
                20,FTPData,TCP,Medium,DataTransfer,"Linux, Windows\n"
                23,TELNET,TCP,Medium,RemoteManagement,"Linux, Windows\n"
                23,TELNET,UDP,Medium,RemoteManagement,"Linux, Windows\n"
                71,NETRJS-1,TCP,Low,Legacy,
                """;
        tempFile = Files.createTempFile("dora_unencrypted_services", ".csv");
        Files.writeString(tempFile, content);

        config = mock(InsightsServiceConfiguration.class);
        InsightsServiceConfiguration.UnencryptedServiceConfig unencryptedServiceConfig = mock(InsightsServiceConfiguration.UnencryptedServiceConfig.class);
        when(unencryptedServiceConfig.getUnencryptedServiceFilePath()).thenReturn(tempFile.toAbsolutePath().toString());
        when(config.getUnencryptedServiceConfig()).thenReturn(unencryptedServiceConfig);

        unencryptedServiceInfo = new UnencryptedServiceInfo(config);
    }

    @AfterEach
    void tearDown() throws IOException {
        if (tempFile != null && Files.exists(tempFile)) {
            Files.delete(tempFile);
        }
    }

    @Test
    void testLoadValidFile() throws IOException {
        Map<UnencryptedServiceInfo.PortProtocolPair, UnencryptedServiceInfo.ServiceInfo> portProtoMap = unencryptedServiceInfo.getPortProtoToServiceInfoMap();
        Map<String, List<UnencryptedServiceInfo.ServiceInfo>> serviceMap = unencryptedServiceInfo.getServicetoServiceInfoMap();

        assertEquals(4, portProtoMap.size());
        assertEquals(3, serviceMap.size());

        UnencryptedServiceInfo.PortProtocolPair key20 = new UnencryptedServiceInfo.PortProtocolPair(20, "TCP");
        UnencryptedServiceInfo.ServiceInfo httpInfo = portProtoMap.get(key20);
        assertNotNull(httpInfo, "Mapping for port 20/TCP should exist");
        assertEquals("FTPData", httpInfo.getService());
    }

    @Test
    void testMalformedLine() throws IOException {
        String csv = """
                Port,Service,Proto,Severity,Category,OS
                20,FTPData,TCP,Medium,DataTransfer,"Linux, Windows"
                BAD_LINE
                25,SMTP,TCP,Medium,Legacy,
                """;
        tempFile = Files.createTempFile("unencrypted_bad", ".csv");
        Files.writeString(tempFile, csv);

        UnencryptedServiceInfo info = getService(tempFile.toString());

        Map<PortProtocolPair, ServiceInfo> map = info.getPortProtoToServiceInfoMap();
        assertEquals(2, map.size());
        assertTrue(map.containsKey(new PortProtocolPair(20, "TCP")));
        assertTrue(map.containsKey(new PortProtocolPair(25, "TCP")));
    }

    @Test
    void testMissingFile() {
        String fakePath = "fake/path/to/file.csv";
        RuntimeException ex = assertThrows(RuntimeException.class, () -> getService(fakePath));
        assertTrue(ex.getMessage().contains("Error in loading the unencrypted service lookup file"));
    }
}
