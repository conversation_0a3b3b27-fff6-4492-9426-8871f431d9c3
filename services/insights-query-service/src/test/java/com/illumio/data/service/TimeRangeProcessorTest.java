package com.illumio.data.service;

import org.junit.jupiter.api.Test;
import reactor.test.StepVerifier;

import java.time.format.DateTimeParseException;

class TimeRangeProcessorTest {

    private final TimeRangeProcessor timeRangeProcessor = new TimeRangeProcessor();

    @Test
    void calculateDaysInRange_returnsCorrectDifference() {
        String start = "2025-02-15T08:00:00Z";
        String end = "2025-02-18T00:00:00";

        StepVerifier.create(timeRangeProcessor.calculateDaysInRange(start, end))
                .expectNext(2L)
                .verifyComplete();
    }

    @Test
    void calculateDaysInRange_returnsZeroWhenSameDay() {
        String start = "2025-02-15T08:00:00Z";
        String end = "2025-02-15T23:59:59";

        StepVerifier.create(timeRangeProcessor.calculateDaysInRange(start, end))
                .expectNext(0L)
                .verifyComplete();
    }

    @Test
    void calculateDaysInRange_throwsExceptionForInvalidDateFormat() {
        String start = "2025-01-01";
        String end = "2025-01-02";

        StepVerifier.create(timeRangeProcessor.calculateDaysInRange(start, end))
                .expectError(DateTimeParseException.class)
                .verify();
    }
}
