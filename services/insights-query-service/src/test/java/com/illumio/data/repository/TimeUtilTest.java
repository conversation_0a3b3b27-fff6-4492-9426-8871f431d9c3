package com.illumio.data.repository;

import com.illumio.data.model.TimeFrame;
import com.illumio.data.utils.TimeUtil;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class TimeUtilTest {

    @Test
    void testGetUpdatedTimeFrame_HourlyTable() {
        TimeFrame input = TimeFrame.builder()
                .startTime("2025-04-01T12:34:56")
                .endTime("2025-04-01T14:45:59")
                .build();

        TimeFrame updated = TimeUtil.getUpdatedTimeFrame(input, "Table_Hourly");

        assertEquals("2025-04-01T12:00:00", updated.getStartTime());
        assertEquals("2025-04-01T14:00:00", updated.getEndTime());
    }

    @Test
    void testGetUpdatedTimeFrame_DailyTable() {
        TimeFrame input = TimeFrame.builder()
                .startTime("2025-04-01T12:34:56")
                .endTime("2025-04-02T08:45:59")
                .build();

        TimeFrame updated = TimeUtil.getUpdatedTimeFrame(input, "Table_Daily");

        assertEquals("2025-04-01T00:00:00", updated.getStartTime());
        assertEquals("2025-04-02T00:00:00", updated.getEndTime());
    }

    @Test
    void testGetUpdatedTimeFrame_MonthlyTable() {
        TimeFrame input = TimeFrame.builder()
                .startTime("2025-01-31T22:45:10")
                .endTime("2025-03-01T05:20:00")
                .build();

        TimeFrame updated = TimeUtil.getUpdatedTimeFrame(input, "Table_Monthly");

        assertEquals("2025-01-31T00:00:00", updated.getStartTime());
        assertEquals("2025-03-01T00:00:00", updated.getEndTime());
    }

    @Test
    void testGetUpdatedTimeFrame_WeeklyTable() {
        TimeFrame input = TimeFrame.builder()
                .startTime("2025-03-10T09:30:15")
                .endTime("2025-03-17T17:45:30")
                .build();

        TimeFrame updated = TimeUtil.getUpdatedTimeFrame(input, "Table_Weekly");

        assertEquals("2025-03-10T00:00:00", updated.getStartTime());
        assertEquals("2025-03-17T00:00:00", updated.getEndTime());
    }
}
