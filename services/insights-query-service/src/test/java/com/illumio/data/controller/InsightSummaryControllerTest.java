package com.illumio.data.controller;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.response.InsightSummaryResponse;
import com.illumio.data.service.InsightSummaryService;
import com.illumio.data.service.JWTService;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.http.HttpStatus;

import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.Map;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class InsightSummaryControllerTest {

    private InsightSummaryController controller;
    private MeterRegistry meterRegistry;

    @Mock
    private InsightSummaryService insightSummaryService;

    @Mock
    private InsightsServiceConfiguration configuration;

    @Mock
    private InsightsServiceConfiguration.ApiConfig apiConfig;

    @Mock
    private JWTService jwtService;

    private static final String TENANT_ID = "test-tenant-id";
    private static final String ENCODED_TENANT_ID = Base64.getEncoder()
            .encodeToString(TENANT_ID.getBytes(StandardCharsets.UTF_8));
    private static final String AUTH_KEY = "test-auth-key";

    @BeforeEach
    void setUp() {
        meterRegistry = new SimpleMeterRegistry();
        controller = new InsightSummaryController(insightSummaryService, meterRegistry, configuration, jwtService);
        
        // Global mock setup - LENIENT allows unused stubs
        when(configuration.getApiConfig()).thenReturn(apiConfig);
        when(apiConfig.getAuthKey()).thenReturn(AUTH_KEY);
    }

    @Test
    void testGetInsightSummary_ValidRequestWithAuthKey_ReturnsSuccess() {
        // Arrange

        MultiValueMap<String, String> headers = createValidHeaders();
        headers.add("x-auth-key", AUTH_KEY);
        
        InsightSummaryResponse expectedResponse = createMockResponse();
        when(insightSummaryService.getInsightSummary(eq(TENANT_ID), eq("threat_hunter"), eq("daily")))
                .thenReturn(Mono.just(expectedResponse));

        // Act & Assert
        Map<String, String> requestParams = Map.of("persona", "threat_hunter", "report_interval", "daily");
        StepVerifier.create(controller.getInsightSummary(headers, requestParams))
                .assertNext(response -> {
                    assertEquals(HttpStatus.OK, response.getStatusCode());
                    assertNotNull(response.getBody());
                    assertEquals(expectedResponse, response.getBody());
                })
                .verifyComplete();
    }

    @Test
    void testGetInsightSummary_ValidRequestWithJWT_ReturnsSuccess() {
        // Arrange
        MultiValueMap<String, String> headers = createValidHeaders();
        headers.add("X-GW", "valid-jwt-token");
        
        when(jwtService.validateJWT(any(), eq(null), eq("X-GW"), eq("tenant"), eq(false)))
                .thenReturn(Pair.of(true, null));
        
        InsightSummaryResponse expectedResponse = createMockResponse();
        when(insightSummaryService.getInsightSummary(eq(TENANT_ID), eq("threat_hunter"), eq("daily")))
                .thenReturn(Mono.just(expectedResponse));

        // Act & Assert
        Map<String, String> requestParams = Map.of("persona", "threat_hunter", "report_interval", "daily");
        StepVerifier.create(controller.getInsightSummary(headers, requestParams))
                .assertNext(response -> {
                    assertEquals(HttpStatus.OK, response.getStatusCode());
                    assertNotNull(response.getBody());
                    assertEquals(expectedResponse, response.getBody());
                })
                .verifyComplete();
    }

    @Test
    void testGetInsightSummary_InvalidReportInterval_ReturnsBadRequest() {
        // Arrange

        MultiValueMap<String, String> headers = createValidHeaders();
        headers.add("x-auth-key", AUTH_KEY);
        // No service mock needed - controller validates before calling service

        // Act & Assert
        Map<String, String> requestParams = Map.of("persona", "threat_hunter", "report_interval", "monthly");
        StepVerifier.create(controller.getInsightSummary(headers, requestParams))
                .assertNext(response -> {
                    assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
                })
                .verifyComplete();
    }

    @Test
    void testGetInsightSummary_WeeklyReportInterval_ReturnsNotImplemented() {
        // Arrange

        MultiValueMap<String, String> headers = createValidHeaders();
        headers.add("x-auth-key", AUTH_KEY);
        
        when(insightSummaryService.getInsightSummary(eq(TENANT_ID), eq("threat_hunter"), eq("weekly")))
                .thenReturn(Mono.error(new UnsupportedOperationException("Weekly reports are not yet implemented")));

        // Act & Assert
        Map<String, String> requestParams = Map.of("persona", "threat_hunter", "report_interval", "weekly");
        StepVerifier.create(controller.getInsightSummary(headers, requestParams))
                .assertNext(response -> {
                    assertEquals(HttpStatus.NOT_IMPLEMENTED, response.getStatusCode());
                })
                .verifyComplete();
    }

    @Test
    void testGetInsightSummary_MissingTenantIdHeader_ReturnsBadRequest() {
        // Arrange

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("x-auth-key", AUTH_KEY);

        // Act & Assert
        Map<String, String> requestParams = Map.of("persona", "threat_hunter", "report_interval", "daily");
        StepVerifier.create(controller.getInsightSummary(headers, requestParams))
                .assertNext(response -> {
                    assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
                })
                .verifyComplete();
    }

    @Test
    void testGetInsightSummary_InvalidBase64TenantId_ReturnsBadRequest() {
        // Arrange

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("x-illumio-tenant-id", "invalid-base64!");
        headers.add("x-auth-key", AUTH_KEY);

        // Act & Assert
        Map<String, String> requestParams = Map.of("persona", "threat_hunter", "report_interval", "daily");
        StepVerifier.create(controller.getInsightSummary(headers, requestParams))
                .assertNext(response -> {
                    assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
                })
                .verifyComplete();
    }

    @Test
    void testGetInsightSummary_InvalidAuthKey_ReturnsUnauthorized() {
        // Arrange

        MultiValueMap<String, String> headers = createValidHeaders();
        headers.add("x-auth-key", "invalid-auth-key");

        // Act & Assert
        Map<String, String> requestParams = Map.of("persona", "threat_hunter", "report_interval", "daily");
        StepVerifier.create(controller.getInsightSummary(headers, requestParams))
                .assertNext(response -> {
                    assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
                })
                .verifyComplete();
    }

    @Test
    void testGetInsightSummary_InvalidJWT_ReturnsUnauthorized() {
        // Arrange
        MultiValueMap<String, String> headers = createValidHeaders();
        headers.add("X-GW", "invalid-jwt-token");
        
        when(jwtService.validateJWT(any(), eq(null), eq("X-GW"), eq("tenant"), eq(false)))
                .thenReturn(Pair.of(false, "Invalid JWT"));

        // Act & Assert
        Map<String, String> requestParams = Map.of("persona", "threat_hunter", "report_interval", "daily");
        StepVerifier.create(controller.getInsightSummary(headers, requestParams))
                .assertNext(response -> {
                    assertEquals(HttpStatus.UNAUTHORIZED, response.getStatusCode());
                })
                .verifyComplete();
    }

    @Test
    void testGetInsightSummary_MissingAuthCredentials_ReturnsBadRequest() {
        // Arrange
        MultiValueMap<String, String> headers = createValidHeaders();
        // No auth key or JWT token provided

        // Act & Assert
        Map<String, String> requestParams = Map.of("persona", "threat_hunter", "report_interval", "daily");
        StepVerifier.create(controller.getInsightSummary(headers, requestParams))
                .assertNext(response -> {
                    assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
                })
                .verifyComplete();
    }

    @Test
    void testGetInsightSummary_ServiceException_ReturnsInternalServerError() {
        // Arrange

        MultiValueMap<String, String> headers = createValidHeaders();
        headers.add("x-auth-key", AUTH_KEY);
        
        when(insightSummaryService.getInsightSummary(eq(TENANT_ID), eq("threat_hunter"), eq("daily")))
                .thenReturn(Mono.error(new RuntimeException("Database connection failed")));

        // Act & Assert
        Map<String, String> requestParams = Map.of("persona", "threat_hunter", "report_interval", "daily");
        StepVerifier.create(controller.getInsightSummary(headers, requestParams))
                .assertNext(response -> {
                    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
                })
                .verifyComplete();
    }

    private MultiValueMap<String, String> createValidHeaders() {
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("x-illumio-tenant-id", ENCODED_TENANT_ID);
        return headers;
    }

    private InsightSummaryResponse createMockResponse() {
        return InsightSummaryResponse.builder()
                .reportId("test-report-id")
                .summaryStartDate(LocalDateTime.now().minusDays(1))
                .summaryEndDate(LocalDateTime.now())
                .comparisonStartDate(LocalDateTime.now().minusDays(8))
                .comparisonEndDate(LocalDateTime.now().minusDays(7))
                .createdAt(System.currentTimeMillis())
                .persona("threat_hunter")
                .dataSources(List.of("kusto", "logs"))
                .reportPayload("{\"summary\": \"test data\"}")
                .illumioTenantId(TENANT_ID)
                .tags(List.of("daily", "automatic"))
                .build();
    }
}
