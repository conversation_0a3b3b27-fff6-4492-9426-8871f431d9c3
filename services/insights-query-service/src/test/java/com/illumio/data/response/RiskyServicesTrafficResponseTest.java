package com.illumio.data.response;

import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.constants.Metadata;
import com.illumio.data.model.constants.SeverityLevel;
import com.illumio.data.model.constants.WidgetId;
import com.illumio.data.service.RiskyServiceInfo;
import com.illumio.data.model.TimeFrame;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class RiskyServicesTrafficResponseTest {
    private RiskyServiceInfo riskyServiceInfo;
    private RiskyServicesTrafficResponse responseBuilder;
    private Metadata metadata = Metadata.builder()
            .widgetId(WidgetId.RISKY_SERVICE_TRAFFIC)
            .build();

    @BeforeEach
    void setUp() {
        Map<RiskyServiceInfo.PortProtocolPair, RiskyServiceInfo.ServiceInfo> portProtoMap = new HashMap<>();
        portProtoMap.put(new RiskyServiceInfo.PortProtocolPair(80, "TCP"), new RiskyServiceInfo.ServiceInfo(80, "HTTP", "TCP", "HIGH", "NETWORK"));

        riskyServiceInfo = mock(RiskyServiceInfo.class);
        when(riskyServiceInfo.getPortProtoToServiceInfoMap()).thenReturn(portProtoMap);

        responseBuilder = new RiskyServicesTrafficResponse(riskyServiceInfo);
    }

    @Test
    void testBuildResponse() {
        KustoResultSetTable resultSetTable = mock(KustoResultSetTable.class);
        when(resultSetTable.next()).thenReturn(true, false);

        when(resultSetTable.getObject("Port")).thenReturn(80);
        when(resultSetTable.getObject("Proto")).thenReturn("TCP");
        when(resultSetTable.getObject("AggFlowCount")).thenReturn(123);
        when(resultSetTable.getObject("AggByteCount")).thenReturn(456);
        when(resultSetTable.getObject("AggFlowCount1")).thenReturn(321);
        when(resultSetTable.getObject("AggByteCount1")).thenReturn(654);

        RequestPayload payload = RequestPayload.builder().build();
        TimeFrame currentTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder().startTime("2025-01-01T00:00:00Z").endTime("2025-01-02T00:00:00Z").build();
        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);
        Pagination pagination = Pagination.builder().build();
        pagination.setRowLimit(10);
        pagination.setTotalPages(1);
        payload.setPagination(pagination);
        payload.setSortByFields(Collections.emptyList());
        payload.setFilters(Collections.emptyList());

        RequestContext requestContext = RequestContext.builder()
                .requestPayload(Optional.of(payload))
                .build();

        RiskyServicesTrafficResponse response = responseBuilder.buildResponse(resultSetTable,  requestContext, metadata);

        assertEquals(WidgetId.RISKY_SERVICE_TRAFFIC, response.getWidgetId());
        assertEquals("Risky Services Traffic", response.getTitle());

        assertEquals(currentTimeFrame, response.getCurrentTimeFrame());
        assertEquals(comparisonTimeFrame, response.getComparisonTimeFrame());
        assertEquals(pagination, response.getPagination());
        assertEquals(10, response.getPagination().getRowLimit());
        assertEquals(1, response.getPagination().getTotalPages());
        assertEquals(Collections.emptyList(), response.getSortByFields());

        List<List<Object>> data = response.getData();
        assertNotNull(data);
        assertEquals(1, data.size());
        List<Object> row = data.get(0);
        // Expected row format: [port, protocol, service, "20%", flowCount, "15%", sentBytes, severity]
        // In the stub: [80, "TCP", "HTTP", "20%", 123, "15%", 456, 2]
        assertEquals(8, row.size());
        assertEquals(80, row.get(0));
        assertEquals("TCP", row.get(1));
        assertEquals("HTTP", row.get(2));
        assertEquals(321L, row.get(3));
        assertEquals(123L, row.get(4));
        assertEquals(654L, row.get(5));
        assertEquals(456L, row.get(6));
        assertEquals(2, row.get(7));
    }

    @Test
    void testBuildResponseWithShowAllData() {
        KustoResultSetTable resultSetTable = mock(KustoResultSetTable.class);
        when(resultSetTable.next()).thenReturn(false); // No data in resultSetTable

        RequestPayload payload = RequestPayload.builder().build();
        TimeFrame currentTimeFrame = TimeFrame.builder()
                .startTime("2025-01-01T00:00:00Z")
                .endTime("2025-01-02T00:00:00Z")
                .build();
        TimeFrame comparisonTimeFrame = TimeFrame.builder()
                .startTime("2025-01-01T00:00:00Z")
                .endTime("2025-01-02T00:00:00Z")
                .build();

        payload.setCurrentTimeFrame(currentTimeFrame);
        payload.setComparisonTimeFrame(comparisonTimeFrame);

        Pagination pagination = Pagination.builder().build();
        pagination.setRowLimit(10);
        pagination.setTotalPages(1);
        payload.setPagination(pagination);
        payload.setSortByFields(Collections.emptyList());
        payload.setFilters(Collections.emptyList());

        // Mock risky service info with multiple missing entries
        Map<RiskyServiceInfo.PortProtocolPair, RiskyServiceInfo.ServiceInfo> portProtoMap = new HashMap<>();
        portProtoMap.put(new RiskyServiceInfo.PortProtocolPair(80, "TCP"),
                new RiskyServiceInfo.ServiceInfo(80, "HTTP", "TCP", "HIGH", "NETWORK"));
        portProtoMap.put(new RiskyServiceInfo.PortProtocolPair(80, "UDP"),
                new RiskyServiceInfo.ServiceInfo(80, "HTTP", "UDP", "HIGH", "NETWORK"));
        portProtoMap.put(new RiskyServiceInfo.PortProtocolPair(443, "TCP"),
                new RiskyServiceInfo.ServiceInfo(443, "HTTPS", "TCP", "MEDIUM", "NETWORK"));
        portProtoMap.put(new RiskyServiceInfo.PortProtocolPair(22, "TCP"),
                new RiskyServiceInfo.ServiceInfo(22, "SSH", "TCP", "LOW", "SECURITY"));

        when(riskyServiceInfo.getPortProtoToServiceInfoMap()).thenReturn(portProtoMap);

        MultiValueMap<String, String> map = new LinkedMultiValueMap<>();
        map.add("showAllData", "true");

        Optional<MultiValueMap<String, String>> queryParams = Optional.of(map);
        RequestContext requestContext = RequestContext.builder()
                .requestPayload(Optional.of(payload))
                .queryParams(queryParams)
                .build();


        RiskyServicesTrafficResponse response = responseBuilder.buildResponse(resultSetTable,  requestContext, metadata);

        assertEquals(WidgetId.RISKY_SERVICE_TRAFFIC, response.getWidgetId());
        assertEquals("Risky Services Traffic", response.getTitle());
        assertEquals(currentTimeFrame, response.getCurrentTimeFrame());
        assertEquals(comparisonTimeFrame, response.getComparisonTimeFrame());
        assertEquals(pagination, response.getPagination());
        assertEquals(10, response.getPagination().getRowLimit());
        assertEquals(1, response.getPagination().getTotalPages());
        assertEquals(Collections.emptyList(), response.getSortByFields());

        List<List<Object>> data = response.getData();
        assertNotNull(data);
        assertEquals(4, data.size()); // Expecting four missing port-protocol combinations to be added

        // Verify each expected row exists, order-independent
        Set<List<Object>> expectedDataSet = new HashSet<>(List.of(
                List.of(80, "TCP", "HTTP", 0, 0, 0, 0, SeverityLevel.fromString("HIGH")),
                List.of(80, "UDP", "HTTP", 0, 0, 0, 0, SeverityLevel.fromString("HIGH")),
                List.of(443, "TCP", "HTTPS", 0, 0, 0, 0, SeverityLevel.fromString("MEDIUM")),
                List.of(22, "TCP", "SSH", 0, 0, 0, 0, SeverityLevel.fromString("LOW"))
        ));

        Set<List<Object>> actualDataSet = new HashSet<>(data);
        assertEquals(expectedDataSet, actualDataSet);
    }
}
