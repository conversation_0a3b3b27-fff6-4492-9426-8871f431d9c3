package com.illumio.data.service;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.configuration.InsightsServiceConfiguration.KustoConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class KustoRetryServiceTest {

    @Mock
    private InsightsServiceConfiguration insightsServiceConfiguration;

    @Mock
    private KustoConfiguration kustoConfiguration;

    private KustoRetryService kustoRetryService;

    @BeforeEach
    void setUp() {
        // Configure mock to return default retry values
        when(kustoConfiguration.getQueryMaxRetries()).thenReturn(3);
        when(kustoConfiguration.getQueryInitialBackoffSeconds()).thenReturn(1L);
        kustoRetryService = new KustoRetryService(insightsServiceConfiguration);
    }

    @Test
    void testExecuteWithRetries_SuccessOnFirstAttempt() {
        Supplier<Mono<ResponseEntity<Object>>> mockSupplier = () -> Mono.just(ResponseEntity.ok("Success"));

        StepVerifier.create(kustoRetryService.executeWithRetries(mockSupplier, kustoConfiguration))
            .expectNextMatches(response -> response.getStatusCode() == HttpStatus.OK && "Success".equals(response.getBody()))
            .verifyComplete();
    }

    @Test
    void testExecuteWithRetries_SuccessAfterRetries() {
        AtomicInteger attempts = new AtomicInteger(0);
        Supplier<Mono<ResponseEntity<Object>>> mockSupplier = () -> {
            if (attempts.incrementAndGet() < 3) {
                return Mono.error(new RuntimeException("Simulated Kusto Error"));
            }
            return Mono.just(ResponseEntity.ok("Success after retries"));
        };

        StepVerifier.create(kustoRetryService.executeWithRetries(mockSupplier, kustoConfiguration))
            .expectNextMatches(response -> response.getStatusCode() == HttpStatus.OK && "Success after retries".equals(response.getBody()))
            .verifyComplete();
        
        assertEquals(3, attempts.get());
    }

    @Test
    void testExecuteWithRetries_FailureAfterExhaustingRetries() {
        Supplier<Mono<ResponseEntity<Object>>> mockSupplier = () -> Mono.error(new RuntimeException("Persistent Kusto Error"));

        // Configure for 2 retries to speed up test
        when(kustoConfiguration.getQueryMaxRetries()).thenReturn(2);
        
        StepVerifier.create(kustoRetryService.executeWithRetries(mockSupplier, kustoConfiguration))
            .expectErrorMessage("Persistent Kusto Error")
            .verify();
    }

    @Test
    void testExecuteWithRetries_NonRetryableException() {
        Supplier<Mono<ResponseEntity<Object>>> mockSupplier = () -> Mono.error(new IllegalArgumentException("Non-retryable error"));

        StepVerifier.create(kustoRetryService.executeWithRetries(mockSupplier, kustoConfiguration))
            .expectError(IllegalArgumentException.class)
            .verify();
    }
    
    @Test
    void testExecuteWithRetries_UsesConfigurationValues() {
        // Configure mock for specific retry values
        when(kustoConfiguration.getQueryMaxRetries()).thenReturn(1); // Test with 1 retry
        when(kustoConfiguration.getQueryInitialBackoffSeconds()).thenReturn(0L); // No backoff for faster test
        
        AtomicInteger attempts = new AtomicInteger(0);
        Supplier<Mono<ResponseEntity<Object>>> mockSupplier = () -> {
            attempts.incrementAndGet();
            return Mono.error(new RuntimeException("Simulated Kusto Error"));
        };

        StepVerifier.create(kustoRetryService.executeWithRetries(mockSupplier, kustoConfiguration))
            .expectErrorMessage("Simulated Kusto Error")
            .verify(Duration.ofSeconds(5)); // Add a timeout for safety

        // Max retries = 1, so total attempts = 1 (initial) + 1 (retry) = 2
        assertEquals(2, attempts.get(), "Should attempt query 2 times (1 initial + 1 retry)");
    }
} 