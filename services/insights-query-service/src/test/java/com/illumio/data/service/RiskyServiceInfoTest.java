package com.illumio.data.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.configuration.InsightsServiceConfiguration.RiskyServiceConfig;
import com.illumio.data.service.RiskyServiceInfo.PortProtocolPair;
import com.illumio.data.service.RiskyServiceInfo.ServiceInfo;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;

class RiskyServiceInfoTest {

    private Path tempFile;
    private InsightsServiceConfiguration config;
    private RiskyServiceInfo riskyServiceInfo;

    @BeforeEach
    void setUp() throws IOException {
        String content = "port,service,protocol,severity,category\n"
                + "80,HTTP,TCP,HIGH,NETWORK\n"
                + "443,HTTPS,TCP,MEDIUM,NETWORK\n";
        tempFile = Files.createTempFile("risky_services", ".csv");
        Files.writeString(tempFile, content);

        config = mock(InsightsServiceConfiguration.class);
        RiskyServiceConfig riskyConfig = mock(RiskyServiceConfig.class);
        when(riskyConfig.getRiskyServiceFilePath()).thenReturn(tempFile.toAbsolutePath().toString());
        when(config.getRiskyServiceConfig()).thenReturn(riskyConfig);

        riskyServiceInfo = new RiskyServiceInfo(config);
    }

    @AfterEach
    void tearDown() throws IOException {
        Files.deleteIfExists(tempFile);
    }

    @Test
    void testLoadRiskyServices_ValidFile() {
        Map<PortProtocolPair, ServiceInfo> portProtoMap = riskyServiceInfo.getPortProtoToServiceInfoMap();
        Map<String, List<ServiceInfo>> serviceMap = riskyServiceInfo.getServicetoServiceInfoMap();

        assertEquals(2, portProtoMap.size());
        assertEquals(2, serviceMap.size());

        PortProtocolPair key80 = new PortProtocolPair(80, "TCP");
        ServiceInfo httpInfo = portProtoMap.get(key80);
        assertNotNull(httpInfo, "Mapping for port 80/TCP should exist");
        assertEquals("HTTP", httpInfo.getService());

        List<ServiceInfo> httpList = serviceMap.get("HTTP");
        assertNotNull(httpList, "Service 'HTTP' should exist in service map");
        assertEquals(1, httpList.size(), "Expected one entry for service 'HTTP'");
    }

    @Test
    void testLoadRiskyServices_SkipsMalformedLines() throws IOException {
        String content = "port,service,protocol,severity,category\n"
                + "80,HTTP,TCP,HIGH,NETWORK\n"
                + "invalid,line,that,should,fail\n";
        Path malformedFile = Files.createTempFile("risky_services_malformed", ".csv");
        Files.writeString(malformedFile, content);

        InsightsServiceConfiguration configMalformed = mock(InsightsServiceConfiguration.class);
        RiskyServiceConfig riskyConfigMalformed = mock(RiskyServiceConfig.class);
        when(riskyConfigMalformed.getRiskyServiceFilePath()).thenReturn(malformedFile.toAbsolutePath().toString());
        when(configMalformed.getRiskyServiceConfig()).thenReturn(riskyConfigMalformed);

        RiskyServiceInfo infoMalformed = new RiskyServiceInfo(configMalformed);

        // Verify that only the valid line was loaded.
        Map<PortProtocolPair, ServiceInfo> portProtoMap = infoMalformed.getPortProtoToServiceInfoMap();
        assertEquals(1, portProtoMap.size(), "Expected only one valid mapping from malformed file");

        Files.deleteIfExists(malformedFile);
    }

    @Test
    void testLoadRiskyServices_FileNotFound() {
        InsightsServiceConfiguration configNotFound = mock(InsightsServiceConfiguration.class);
        RiskyServiceConfig riskyConfigNotFound = mock(RiskyServiceConfig.class);
        when(riskyConfigNotFound.getRiskyServiceFilePath()).thenReturn("classpath:nonexistent.csv");
        when(configNotFound.getRiskyServiceConfig()).thenReturn(riskyConfigNotFound);

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            new RiskyServiceInfo(configNotFound);
        });
        assertTrue(exception.getMessage().contains("Error in loading the risky ports lookup table"), "Expected error message for missing file");
    }
}
