package com.illumio.data.controller;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.constants.RequestMetadata;
import com.illumio.data.service.JWTService;
import com.illumio.data.service.MetricRecordService;
import com.illumio.data.service.RequestRouter;
import com.illumio.data.service.RequestForwardingService;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import reactor.core.publisher.Mono;

import java.util.Map;
import org.apache.commons.lang3.tuple.Pair;
import java.util.Base64;
import java.nio.charset.StandardCharsets;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class SentinelControllerTest {

    private WebTestClient webTestClient;
    private SentinelController sentinelController;

    @Mock
    private RequestRouter requestRouter;

    @Mock
    private InsightsServiceConfiguration configuration;

    @Mock
    private InsightsServiceConfiguration.ProxyConfig proxyConfig;

    @Mock
    private InsightsServiceConfiguration.ApiConfig apiConfig;

    @Mock
    private MetricRecordService metricRecordService;

    @Mock
    private RequestForwardingService requestForwardingService;

    @Mock
    private JWTService jwtService;

    private MeterRegistry meterRegistry;

    private static final String BASE_URL = "/api/v1/resource-insights";
    private static final String AZURE_TENANT_ID = "test-azure-tenant";
    private static final String ILLUMIO_TENANT_ID = "test-illumio-tenant";
    private static final String AUTH_KEY = "test-auth-key";
    private static final String AFTER_PARAM = "1640995200000";
    // JWT claim key used by controller when validating tenant id
    private static final String JWT_TENANT_KEY = "tenant";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        meterRegistry = new SimpleMeterRegistry();
        
        sentinelController = new SentinelController(
                requestRouter,
                configuration,
                meterRegistry,
                metricRecordService,
                requestForwardingService,
                jwtService
        );
        
        webTestClient = WebTestClient.bindToController(sentinelController).build();
        
        // Default configuration setup
        when(configuration.getProxyConfig()).thenReturn(proxyConfig);
        when(configuration.getApiConfig()).thenReturn(apiConfig);
        when(apiConfig.getAuthKey()).thenReturn(AUTH_KEY);
    }

    @Test
    void testGetResourceInsights_ProxyEnabled_Success() {
        // Arrange
        when(proxyConfig.isEnableProxy()).thenReturn(true);
        ResponseEntity<Object> expectedResponse = ResponseEntity.ok().body(Map.of("data", "forwarded"));
        when(requestForwardingService.forwardRequest(any(), any()))
                .thenReturn(Mono.just(expectedResponse));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("x-azure-tenant-id", AZURE_TENANT_ID);
        headers.add("x-illumio-tenant-id", ILLUMIO_TENANT_ID);
        headers.add("x-auth-key", AUTH_KEY);

        // Act & Assert
        webTestClient.get()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL)
                        .queryParam("after", AFTER_PARAM)
                        .build())
                .headers(httpHeaders -> headers.forEach(httpHeaders::addAll))
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.data").isEqualTo("forwarded");

        // Verify forwarding service was called and router was not
        verify(requestForwardingService).forwardRequest(any(), any());
        verifyNoInteractions(requestRouter);
    }

    @Test
    void testGetResourceInsights_ProxyEnabled_ForwardingError() {
        // Arrange
        when(proxyConfig.isEnableProxy()).thenReturn(true);
        when(requestForwardingService.forwardRequest(any(), any()))
                .thenReturn(Mono.error(new RuntimeException("Forwarding failed")));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("x-azure-tenant-id", AZURE_TENANT_ID);
        headers.add("x-illumio-tenant-id", ILLUMIO_TENANT_ID);
        headers.add("x-auth-key", AUTH_KEY);

        // Act & Assert
        webTestClient.get()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL)
                        .queryParam("after", AFTER_PARAM)
                        .build())
                .headers(httpHeaders -> headers.forEach(httpHeaders::addAll))
                .exchange()
                .expectStatus().isEqualTo(HttpStatus.BAD_GATEWAY)
                .expectBody()
                .jsonPath("$.error").isEqualTo("Bad Gateway")
                .jsonPath("$.message").value(message -> 
                    ((String) message).contains("Failed to forward request"));

        verify(requestForwardingService).forwardRequest(any(), any());
        verifyNoInteractions(requestRouter);
    }

    @Test
    void testGetResourceInsights_ProxyDisabled_Success() {
        // Arrange
        when(proxyConfig.isEnableProxy()).thenReturn(false);
        ResponseEntity<Object> expectedResponse = ResponseEntity.ok().body(Map.of("data", "local"));
        when(requestRouter.routeGetRequest(eq(RequestMetadata.RESOURCE_INSIGHTS), any(RequestContext.class)))
                .thenReturn(Mono.just(expectedResponse));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("x-azure-tenant-id", AZURE_TENANT_ID);
        headers.add("x-illumio-tenant-id", ILLUMIO_TENANT_ID);
        headers.add("x-auth-key", AUTH_KEY);

        // Act & Assert
        webTestClient.get()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL)
                        .queryParam("after", AFTER_PARAM)
                        .build())
                .headers(httpHeaders -> headers.forEach(httpHeaders::addAll))
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.data").isEqualTo("local");

        // Verify router was called and forwarding service was not
        verify(requestRouter).routeGetRequest(eq(RequestMetadata.RESOURCE_INSIGHTS), any(RequestContext.class));
        verifyNoInteractions(requestForwardingService);
    }

    @Test
    void testGetResourceInsights_ProxyEnabledBypassesValidation() {
        // Arrange - When proxy is enabled, it should bypass local validation and forward immediately
        when(proxyConfig.isEnableProxy()).thenReturn(true);
        ResponseEntity<Object> expectedResponse = ResponseEntity.ok().body(Map.of("data", "forwarded"));
        when(requestForwardingService.forwardRequest(any(), any()))
                .thenReturn(Mono.just(expectedResponse));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        // Note: We're deliberately missing some headers that would normally be required
        headers.add("x-azure-tenant-id", AZURE_TENANT_ID);
        // Missing x-illumio-tenant-id and x-auth-key

        // Act & Assert - Should succeed because proxy bypasses validation
        webTestClient.get()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL)
                        .queryParam("after", AFTER_PARAM)
                        .build())
                .headers(httpHeaders -> headers.forEach(httpHeaders::addAll))
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.data").isEqualTo("forwarded");

        // Verify forwarding service was called and router was not
        verify(requestForwardingService).forwardRequest(any(), any());
        verifyNoInteractions(requestRouter);
    }

    // New tests for auth key and JWT flows

    @Test
    void testGetResourceInsights_WithValidAuthKey_Succeeds() {
        when(proxyConfig.isEnableProxy()).thenReturn(false);
        ResponseEntity<Object> expectedResponse = ResponseEntity.ok().body(Map.of("data", "local"));
        when(requestRouter.routeGetRequest(eq(RequestMetadata.RESOURCE_INSIGHTS), any(RequestContext.class)))
                .thenReturn(Mono.just(expectedResponse));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("x-azure-tenant-id", AZURE_TENANT_ID);
        headers.add("x-illumio-tenant-id", ILLUMIO_TENANT_ID);
        headers.add("x-auth-key", AUTH_KEY);

        webTestClient.get()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL).build())
                .headers(httpHeaders -> headers.forEach(httpHeaders::addAll))
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.data").isEqualTo("local");

        verify(requestRouter).routeGetRequest(eq(RequestMetadata.RESOURCE_INSIGHTS), any(RequestContext.class));
        verifyNoInteractions(jwtService);
    }

    @Test
    void testGetResourceInsights_WithWrongAuthKey_Returns401() {
        when(proxyConfig.isEnableProxy()).thenReturn(false);

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("x-illumio-tenant-id", ILLUMIO_TENANT_ID);
        headers.add("x-auth-key", "wrong-key");

        webTestClient.get()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL).build())
                .headers(httpHeaders -> headers.forEach(httpHeaders::addAll))
                .exchange()
                .expectStatus().isEqualTo(HttpStatus.UNAUTHORIZED)
                .expectBody()
                .jsonPath("$.error").isEqualTo("Unauthorized");

        verifyNoInteractions(requestRouter);
        verifyNoInteractions(jwtService);
    }

    @Test
    void testGetResourceInsights_WithValidJwt_Succeeds() {
        when(proxyConfig.isEnableProxy()).thenReturn(false);
        ResponseEntity<Object> expectedResponse = ResponseEntity.ok().body(Map.of("data", "local"));
        when(requestRouter.routeGetRequest(eq(RequestMetadata.RESOURCE_INSIGHTS), any(RequestContext.class)))
                .thenReturn(Mono.just(expectedResponse));

        String decodedTenant = "tenant-123";
        String encodedTenant = Base64.getEncoder().encodeToString(decodedTenant.getBytes(StandardCharsets.UTF_8));

        when(jwtService.validateJWT(
                org.mockito.ArgumentMatchers.<org.springframework.util.MultiValueMap<String, String>>any(),
                org.mockito.ArgumentMatchers.isNull(),
                eq("X-GW"),
                eq(JWT_TENANT_KEY),
                eq(false)))
                .thenReturn(Pair.of(true, null));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("x-illumio-tenant-id", encodedTenant);
        headers.add("X-GW", "dummy.jwt.token");

        webTestClient.get()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL).build())
                .headers(httpHeaders -> headers.forEach(httpHeaders::addAll))
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.data").isEqualTo("local");

        verify(jwtService).validateJWT(
                org.mockito.ArgumentMatchers.<org.springframework.util.MultiValueMap<String, String>>any(),
                org.mockito.ArgumentMatchers.isNull(),
                eq("X-GW"),
                eq(JWT_TENANT_KEY),
                eq(false));
        verify(requestRouter).routeGetRequest(eq(RequestMetadata.RESOURCE_INSIGHTS), any(RequestContext.class));
    }

    @Test
    void testGetResourceInsights_WithInvalidJwt_Returns401() {
        when(proxyConfig.isEnableProxy()).thenReturn(false);

        String decodedTenant = "tenant-123";
        String encodedTenant = Base64.getEncoder().encodeToString(decodedTenant.getBytes(StandardCharsets.UTF_8));

        when(jwtService.validateJWT(
                org.mockito.ArgumentMatchers.<org.springframework.util.MultiValueMap<String, String>>any(),
                org.mockito.ArgumentMatchers.isNull(),
                eq("X-GW"),
                eq(JWT_TENANT_KEY),
                eq(false)))
                .thenReturn(Pair.of(false, "Invalid signature"));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("x-illumio-tenant-id", encodedTenant);
        headers.add("X-GW", "invalid.jwt.token");

        webTestClient.get()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL).build())
                .headers(httpHeaders -> headers.forEach(httpHeaders::addAll))
                .exchange()
                .expectStatus().isEqualTo(HttpStatus.UNAUTHORIZED)
                .expectBody()
                .jsonPath("$.error").isEqualTo("Unauthorized")
                .jsonPath("$.message").value(message -> ((String) message).contains("Invalid JWT"));

        verify(jwtService).validateJWT(
                org.mockito.ArgumentMatchers.<org.springframework.util.MultiValueMap<String, String>>any(),
                org.mockito.ArgumentMatchers.isNull(),
                eq("X-GW"),
                eq(JWT_TENANT_KEY),
                eq(false));
        verifyNoInteractions(requestRouter);
    }

    @Test
    void testGetResourceInsights_MissingAuthAndJwt_Returns400() {
        when(proxyConfig.isEnableProxy()).thenReturn(false);

        String decodedTenant = "tenant-123";
        String encodedTenant = Base64.getEncoder().encodeToString(decodedTenant.getBytes(StandardCharsets.UTF_8));

        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("x-illumio-tenant-id", encodedTenant);
        // No x-auth-key and no X-GW

        webTestClient.get()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL).build())
                .headers(httpHeaders -> headers.forEach(httpHeaders::addAll))
                .exchange()
                .expectStatus().isEqualTo(HttpStatus.BAD_REQUEST)
                .expectBody()
                .jsonPath("$.error").isEqualTo("Bad Request")
                .jsonPath("$.message").value(message -> ((String) message).contains("Missing required header: X-GW"));

        verifyNoInteractions(requestRouter);
        verifyNoInteractions(jwtService);
    }
}