package com.illumio.data.response;

import com.illumio.data.model.RequestContext;
import com.illumio.data.model.constants.Metadata;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class GetCspRegionsResponseTest {
    private GetCspRegionsResponse responseBuilder;
    private KustoResultSetTable resultSetTable;
    private RequestContext requestContext;
    private Metadata metadata;

    @BeforeEach
    void setUp() {
        responseBuilder = new GetCspRegionsResponse();
        resultSetTable = mock(KustoResultSetTable.class);
        requestContext = mock(RequestContext.class);
        metadata = mock(Metadata.class);
    }

    @Test
    void testBuildResponse_withValidData() {
        when(resultSetTable.next()).thenReturn(true, true, false);
        when(resultSetTable.getObject("Region")).thenReturn("us-east-1", "eu-west-1");
        when(resultSetTable.getObject("Zone")).thenReturn("zone-a", "zone-b");

        GetCspRegionsResponse.BasicResponse response = responseBuilder.buildResponse(resultSetTable, requestContext, metadata);
        assertNotNull(response);
        List<?> data = response.getData();
        assertEquals(2, data.size());
        GetCspRegionsResponse.ZoneRegion zr1 = (GetCspRegionsResponse.ZoneRegion) data.get(0);
        GetCspRegionsResponse.ZoneRegion zr2 = (GetCspRegionsResponse.ZoneRegion) data.get(1);
        assertEquals("zone-a", zr1.getZone());
        assertEquals("us-east-1", zr1.getRegion());
        assertEquals("zone-b", zr2.getZone());
        assertEquals("eu-west-1", zr2.getRegion());
    }

    @Test
    void testBuildResponse_withNullsAndEmptyStrings() {
        when(resultSetTable.next()).thenReturn(true, true, true, false);
        when(resultSetTable.getObject("Region")).thenReturn(null, "", "us-west-2");
        when(resultSetTable.getObject("Zone")).thenReturn("zone-x", "zone-y", "");

        GetCspRegionsResponse.BasicResponse response = responseBuilder.buildResponse(resultSetTable, requestContext, metadata);
        assertNotNull(response);
        List<?> data = response.getData();
        // Only the first row is valid (zone-x, null region is skipped; zone-y, empty region is skipped; zone-"", region us-west-2 is skipped)
        assertEquals(0, data.size());
    }

    @Test
    void testBuildAggregatedResponse_returnsEmptyList() {
        GetCspRegionsResponse.BasicResponse response = responseBuilder.buildAggregatedResponse(Collections.emptyList(), Optional.empty());
        assertNotNull(response);
        assertTrue(response.getData().isEmpty());
    }
} 