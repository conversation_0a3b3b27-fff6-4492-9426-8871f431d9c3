package com.illumio.data.service;

import com.illumio.data.configuration.ServiceKeyConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

public class ServiceAuthTest {
    @Mock
    private ServiceKeyConfig serviceKeyConfig;

    private ServiceKeyAuth serviceKeyAuth;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        serviceKeyAuth = new ServiceKeyAuth(serviceKeyConfig);
    }

    @Test
    void isAuthorized_returnsFalse_ifHeadersMissingServiceName() {
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("X-service-auth-key", "somekey");

        boolean authorized = serviceKeyAuth.isAuthorized(headers);

        assertFalse(authorized);
        verifyNoInteractions(serviceKeyConfig);
    }

    @Test
    void isAuthorized_returnsFalse_ifHeadersMissingServiceKey() {
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("X-origin-service-name", "user-service");

        boolean authorized = serviceKeyAuth.isAuthorized(headers);

        assertFalse(authorized);
        verifyNoInteractions(serviceKeyConfig);
    }

    @Test
    void isAuthorized_returnsTrue_ifValidKey() {
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("X-origin-service-name", "user-service");
        headers.add("X-service-auth-key", "validkey");

        when(serviceKeyConfig.isValidKey("user-service", "validkey")).thenReturn(true);

        boolean authorized = serviceKeyAuth.isAuthorized(headers);

        assertTrue(authorized);
        verify(serviceKeyConfig).isValidKey("user-service", "validkey");
    }

    @Test
    void isAuthorized_returnsFalse_ifInvalidKey() {
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
        headers.add("X-origin-service-name", "user-service");
        headers.add("X-service-auth-key", "invalidkey");

        when(serviceKeyConfig.isValidKey("user-service", "invalidkey")).thenReturn(false);

        boolean authorized = serviceKeyAuth.isAuthorized(headers);

        assertFalse(authorized);
        verify(serviceKeyConfig).isValidKey("user-service", "invalidkey");
    }

    @Test
    void isAuthorized_returnsFalse_ifHeadersAreEmpty() {
        MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();

        boolean authorized = serviceKeyAuth.isAuthorized(headers);

        assertFalse(authorized);
        verifyNoInteractions(serviceKeyConfig);
    }
}
