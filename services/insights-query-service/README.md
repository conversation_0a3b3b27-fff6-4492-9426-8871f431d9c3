Insights Query Service: Service to power backend for insights widgets

Build Locally with Test Coverage: 
    `./gradlew :services:insights-query-service:test \
    :services:insights-query-service:jacocoTestReport \
    :services:insights-query-service:check \
    :services:insights-query-service:jibDockerBuild `

Run Locally: 
1. Update application.yml with Kusto credentials
2. Run `InsightsQueryService+Local` from runconfigs