plugins {
    id 'org.springframework.boot'
    id 'com.google.cloud.tools.jib'
    id 'com.github.johnrengelman.shadow'
    id 'jacoco'
}

apply from: "${project.rootDir}/gradle/scripts/jib-build.gradle"
apply from: "${project.rootDir}/gradle/scripts/opentelemetry.gradle"

repositories {
    maven {
        url  = uri("https://packages.confluent.io/maven/")
    }
}

dependencies {
    // implementation
    implementation project(":commons:azure-commons")

    implementation 'org.springframework.boot:spring-boot-starter-data-r2dbc'
    implementation 'io.r2dbc:r2dbc-postgresql'
    implementation 'org.springframework.boot:spring-boot-starter-jdbc'
    implementation 'org.liquibase:liquibase-core:4.24.0'
    implementation 'org.postgresql:postgresql'

    implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.5' // for JSON parsing

    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.mapstruct:mapstruct:1.4.2.Final'
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.4.2.Final'
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    implementation 'org.springframework.kafka:spring-kafka'
    implementation "io.projectreactor.kafka:reactor-kafka"
    implementation 'org.apache.commons:commons-csv:1.9.0'


    implementation('com.microsoft.azure.kusto:kusto-ingest:5.1.0') {
        exclude group: 'org.slf4j', module: 'slf4j-simple'
    }

    implementation 'com.azure:azure-core-metrics-opentelemetry:1.0.0-beta.25'
    implementation 'io.micrometer:micrometer-core'
    implementation 'io.projectreactor:reactor-core-micrometer:1.1.8'
    implementation("io.opentelemetry.instrumentation:opentelemetry-micrometer-1.5:2.7.0-alpha")


    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // spring
    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"

    // test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.boot:spring-boot-starter-web'
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'io.projectreactor:reactor-test'
}


jib {
    container {
        entrypoint = [
                'java', '-cp', '@/app/jib-classpath-file',
                'com.illumio.data.InsightsQueryServiceApp',
                '--spring.config.location=file:/var/resources/application.yml',
                '--spring.profiles.active=prod'
        ]
        environment = [
                "JAVA_TOOL_OPTIONS": "-javaagent:/otel/opentelemetry-javaagent.jar",
                "OTEL_METRICS_EXPORTER": "prometheus",
                "OTEL_TRACES_EXPORTER": "none",
                "OTEL_LOGS_EXPORTER": "none"
        ]
    }
}

def testExclusions = [
        "**/com/illumio/data/builder/**",
        "**/com/illumio/data/configuration/**",
        "**/com/illumio/data/datavisitor/**",
        "**/com/illumio/data/model/**",

        "**/com/illumio/data/service/DataService.class",
        "**/com/illumio/data/service/KustoDataService.class",
        "**/com/illumio/data/InsightsQueryServiceApp.class"
]


jacoco {
    toolVersion = "0.8.8"
}

tasks.jacocoTestReport {
    reports {
        xml.required.set(true)
        html.required.set(true)
    }
    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: testExclusions)
        }))
    }

}

tasks.jacocoTestCoverageVerification {
    violationRules {
        rule {
            element = 'CLASS'

            limit {
                counter = 'LINE'
                value = 'COVEREDRATIO'
                minimum = 0.00
            }
        }
    }
    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: testExclusions)
        }))
    }
}

test.finalizedBy jacocoTestReport
check.dependsOn jacocoTestCoverageVerification
