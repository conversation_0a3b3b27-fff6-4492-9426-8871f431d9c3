apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "NetworkDeviceSync.fullname" . }}-env-configmap
  labels:
    {{- include "NetworkDeviceSync.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
        org:
          apache:
            kafka: {{.Values.logging.level.kafka}}
    spring:
      application:
        name: "network-device-sync"
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: reactive
    server:
      port: {{.Values.server.port}}
    retry-config:
      parameters:
        min-backoff: "{{.Values.retryConfig.parameters.minBackoff}}"
        max-retries: {{.Values.retryConfig.parameters.maxRetries}}

    network-device-sync:
      kafka-flow-consumer-config:
        bootstrapServers: "{{.Values.networkDeviceSync.kafkaFlowConsumerConfig.bootstrapServers}}"
        topic: "{{.Values.networkDeviceSync.kafkaFlowConsumerConfig.topic}}"
        groupId: "{{.Values.networkDeviceSync.kafkaFlowConsumerConfig.groupId}}"
        autoOffsetReset: "{{.Values.networkDeviceSync.kafkaFlowConsumerConfig.autoOffsetReset}}"
        requestTimeoutMs: "{{.Values.networkDeviceSync.kafkaFlowConsumerConfig.requestTimeoutMs}}"
        maxPollRecords: "{{.Values.networkDeviceSync.kafkaFlowConsumerConfig.maxPollRecords}}"
        maxPartitionFetchBytes: "{{.Values.networkDeviceSync.kafkaFlowConsumerConfig.maxPartitionFetchBytes}}"
      kafka-inventory-producer-config:
        bootstrapServers: "{{.Values.networkDeviceSync.kafkaInventoryProducerConfig.bootstrapServers}}"
        topic: "{{.Values.networkDeviceSync.kafkaInventoryProducerConfig.topic}}"
        maxInFlight: "{{.Values.networkDeviceSync.kafkaInventoryProducerConfig.maxInFlight}}"
        requestTimeoutMs: "{{.Values.networkDeviceSync.kafkaInventoryProducerConfig.requestTimeoutMs}}"
        deliveryTimeoutMs: "{{.Values.networkDeviceSync.kafkaInventoryProducerConfig.deliveryTimeoutMs}}"
        maxBlockMs: "{{.Values.networkDeviceSync.kafkaInventoryProducerConfig.maxBlockMs}}"
        lingerMs: "{{.Values.networkDeviceSync.kafkaInventoryProducerConfig.lingerMs}}"
        batchSize: "{{.Values.networkDeviceSync.kafkaInventoryProducerConfig.batchSize}}"
        bufferMemory: "{{.Values.networkDeviceSync.kafkaInventoryProducerConfig.bufferMemory}}"