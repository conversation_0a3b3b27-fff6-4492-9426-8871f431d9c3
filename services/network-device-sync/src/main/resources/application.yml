logging:
  level:
    ROOT: INFO
spring:
  application:
    name: network-device-sync
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive
server:
  port: 8081

retry-config:
  parameters:
    min-backoff: 30s
    max-retries: 2

network-device-sync:
  kafka-flow-consumer-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";
    topic: nw-aggregated-flows-v1
    groupId: network-device-sync-1
    auto-offset-reset: latest

  kafka-inventory-producer-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    topic: inventory-sync-v1
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";