package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.annotations.retry.RetryReactiveOnError;
import com.illumio.data.model.FlowValue;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.receiver.KafkaReceiver;

@Slf4j
@Service
@RequiredArgsConstructor
public class FlowReceiverService {
    private final KafkaReceiver<String, String> kafkaFlowReceiver;
    private final ObjectMapper objectMapper;
    private final FlowInventoryExtractionService flowInventoryExtractionService;
    private final InventorySenderService inventorySenderService;

    @PostConstruct
    @RetryReactiveOnError
    public void consumeFlows() {
        kafkaFlowReceiver.receiveAutoAck()
                         .publishOn(Schedulers.boundedElastic())
                         .flatMap(flowRecords -> flowRecords)
                         .doOnNext(flowRecord -> log.debug("Received flow record: key={}, value={}",
                                 flowRecord.key(), flowRecord.value()))
                         .flatMap(flowRecord -> {
                             final FlowValue flowValue = flowValueFromKafkaRecord(flowRecord);
                             return Mono.just(flowInventoryExtractionService.extractInventoryFromFlow(flowValue));
                         })
                         .flatMap(inventorySenderService::sendInventoryObjects)
                         .onErrorContinue((throwable, o) ->
                                 log.error("Error occurred while consuming flow. Continuing flow consumption.", throwable))
                         .subscribe();
    }

    @SneakyThrows
    private FlowValue flowValueFromKafkaRecord(final ConsumerRecord<String, String> record) {
        return objectMapper.readValue(record.value(), FlowValue.class);
    }

}
