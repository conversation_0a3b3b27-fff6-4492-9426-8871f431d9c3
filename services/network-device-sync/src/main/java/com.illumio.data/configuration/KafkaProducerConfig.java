package com.illumio.data.configuration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class KafkaProducerConfig {
    private final NetworkDeviceSyncConfig networkDeviceSyncConfig;

    @Bean
    public KafkaSender<String, String> kafkaInventorySender() {
        final Map<String, Object> producerProps = producerOptions();
        final SenderOptions<String, String> senderOptions = SenderOptions.<String, String>create(producerProps)
                                                                   .maxInFlight(networkDeviceSyncConfig
                                                                           .getKafkaInventoryProducerConfig()
                                                                           .getMaxInFlight());
        return KafkaSender.create(senderOptions);
    }

    private Map<String, Object> producerOptions() {
        final Map<String, Object> producerProps = new HashMap<>();
        producerProps.put(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
                networkDeviceSyncConfig.getKafkaInventoryProducerConfig().getBootstrapServers());
        producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
        producerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
        producerProps.put(
                SaslConfigs.SASL_JAAS_CONFIG,
                networkDeviceSyncConfig.getKafkaInventoryProducerConfig().getSaslJaasConfig());

        Optional.of(networkDeviceSyncConfig)
                .map(NetworkDeviceSyncConfig::getKafkaInventoryProducerConfig)
                .map(NetworkDeviceSyncConfig.KafkaProducerConfig::getRequestTimeoutMs)
                .ifPresent(requestTimeoutMs ->
                        producerProps.put(
                                CommonClientConfigs.REQUEST_TIMEOUT_MS_CONFIG,
                                requestTimeoutMs));
        Optional.of(networkDeviceSyncConfig)
                .map(NetworkDeviceSyncConfig::getKafkaInventoryProducerConfig)
                .map(NetworkDeviceSyncConfig.KafkaProducerConfig::getDeliveryTimeoutMs)
                .ifPresent(deliveryTimeoutMs ->
                        producerProps.put(
                                ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG,
                                deliveryTimeoutMs));
        Optional.of(networkDeviceSyncConfig)
                .map(NetworkDeviceSyncConfig::getKafkaInventoryProducerConfig)
                .map(NetworkDeviceSyncConfig.KafkaProducerConfig::getLingerMs)
                .ifPresent(lingerMs ->
                        producerProps.put(
                                ProducerConfig.LINGER_MS_CONFIG,
                                lingerMs));
        Optional.of(networkDeviceSyncConfig)
                .map(NetworkDeviceSyncConfig::getKafkaInventoryProducerConfig)
                .map(NetworkDeviceSyncConfig.KafkaProducerConfig::getBatchSize)
                .ifPresent(batchSize ->
                        producerProps.put(
                                ProducerConfig.BATCH_SIZE_CONFIG,
                                batchSize));
        Optional.of(networkDeviceSyncConfig)
                .map(NetworkDeviceSyncConfig::getKafkaInventoryProducerConfig)
                .map(NetworkDeviceSyncConfig.KafkaProducerConfig::getBufferMemory)
                .ifPresent(bufferMemory ->
                        producerProps.put(
                                ProducerConfig.BUFFER_MEMORY_CONFIG,
                                bufferMemory));
        Optional.of(networkDeviceSyncConfig)
                .map(NetworkDeviceSyncConfig::getKafkaInventoryProducerConfig)
                .map(NetworkDeviceSyncConfig.KafkaProducerConfig::getMaxBlockMs)
                .ifPresent(maxBlockMs ->
                        producerProps.put(
                                ProducerConfig.MAX_BLOCK_MS_CONFIG,
                                maxBlockMs));

        return producerProps;
    }
}
