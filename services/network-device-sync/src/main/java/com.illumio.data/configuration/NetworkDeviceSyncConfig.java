package com.illumio.data.configuration;

import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "network-device-sync")
@Getter
@Setter
public class NetworkDeviceSyncConfig {

    private final KafkaConsumerConfig kafkaFlowConsumerConfig = new KafkaConsumerConfig();
    private final KafkaProducerConfig kafkaInventoryProducerConfig = new KafkaProducerConfig();

    @Getter
    @Setter
    public static class KafkaConsumerConfig {
        private String bootstrapServers;
        private String saslJaasConfig;
        private String topic;
        private String groupId;
        private String autoOffsetReset;
        private Integer requestTimeoutMs = 30000;
        private Integer maxPollRecords = 500;
        private Integer maxPartitionFetchBytes;
    }

    @Getter
    @Setter
    public static class KafkaProducerConfig {
        private String bootstrapServers;
        private String saslJaasConfig;
        private String topic;
        private Integer requestTimeoutMs;
        private Integer deliveryTimeoutMs;
        private Integer lingerMs;
        private Integer batchSize;
        private Integer bufferMemory;
        private Integer maxBlockMs;
        private Integer maxInFlight = 1024;
    }

}
