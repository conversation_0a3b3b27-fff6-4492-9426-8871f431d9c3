package com.illumio.data.configuration;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;

@Configuration
@RequiredArgsConstructor
public class KafkaConsumerConfig {
    private final NetworkDeviceSyncConfig networkDeviceSyncConfig;

    @Bean
    public KafkaReceiver<String, String> kafkaReceiverOptions() {
        final Map<String, Object> consumerProps = new HashMap<>();
        consumerProps.put(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                networkDeviceSyncConfig.getKafkaFlowConsumerConfig().getBootstrapServers());
        consumerProps.put(
                ConsumerConfig.GROUP_ID_CONFIG,
                networkDeviceSyncConfig.getKafkaFlowConsumerConfig().getGroupId());
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
        consumerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
        consumerProps.put(
                SaslConfigs.SASL_JAAS_CONFIG,
                networkDeviceSyncConfig.getKafkaFlowConsumerConfig().getSaslJaasConfig());

        Optional.of(networkDeviceSyncConfig)
                .map(NetworkDeviceSyncConfig::getKafkaFlowConsumerConfig)
                .map(NetworkDeviceSyncConfig.KafkaConsumerConfig::getAutoOffsetReset)
                .ifPresent(autoOffsetReset ->
                        consumerProps.put(
                                ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,
                                autoOffsetReset));
        Optional.of(networkDeviceSyncConfig)
                .map(NetworkDeviceSyncConfig::getKafkaFlowConsumerConfig)
                .map(NetworkDeviceSyncConfig.KafkaConsumerConfig::getRequestTimeoutMs)
                .ifPresent(requestTimeoutMs ->
                        consumerProps.put(
                                ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG,
                                requestTimeoutMs));
        Optional.of(networkDeviceSyncConfig)
                .map(NetworkDeviceSyncConfig::getKafkaFlowConsumerConfig)
                .map(NetworkDeviceSyncConfig.KafkaConsumerConfig::getMaxPollRecords)
                .ifPresent(maxPollRecords ->
                        consumerProps.put(
                                ConsumerConfig.MAX_POLL_RECORDS_CONFIG,
                                maxPollRecords));
        Optional.of(networkDeviceSyncConfig)
                .map(NetworkDeviceSyncConfig::getKafkaFlowConsumerConfig)
                .map(NetworkDeviceSyncConfig.KafkaConsumerConfig::getMaxPartitionFetchBytes)
                .ifPresent(maxPartitionFetchBytes ->
                        consumerProps.put(
                                ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG,
                                maxPartitionFetchBytes));

        final ReceiverOptions<String, String> receiverOptions =
                ReceiverOptions.<String, String>create(consumerProps)
                               .subscription(
                                       Collections.singleton(
                                               networkDeviceSyncConfig.getKafkaFlowConsumerConfig().getTopic()));
        return KafkaReceiver.create(receiverOptions);
    }
}
