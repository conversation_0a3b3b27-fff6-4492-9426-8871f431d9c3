package com.illumio.data.components;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.model.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import static com.illumio.data.model.InventoryObjectType.DEVICE;
import static com.illumio.data.model.InventoryObjectType.IDENTITY;
import static com.illumio.data.model.InventoryItemType.FIREWALL;
import static com.illumio.data.model.InventoryItemType.USER;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class FlowInventoryExtractionServiceTest {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final FlowInventoryExtractionService flowInventoryExtractionService =
            new FlowInventoryExtractionService(objectMapper);

    private final String tenantId = "6f1d0df6-3a70-4cf3-b3c1-33d9b9b0a2af";
    private final String srcIp = "************";
    private final String sourceHostName = "src-host-01";
    private final String sourceMacAddress = "00-14-22-01-23-45";
    private final String destIp = "*********";
    private final String firewallDeviceName = "firewall-01";
    private final String srcUserName = "<EMAIL>";
    private final String destUserName = "<EMAIL>";
    private final String deviceVendor = "Palo Alto Networks";
    private final String userPrivileges = "admin";

    private InventoryItemComparable toComparable(InventoryItem item) {
        return new InventoryItemComparable(
                item.getTenantId(),
                item.getEntityId(),
                item.getSuperType(),
                item.getType(),
                item.getData()
        );
    }

    private record InventoryItemComparable(UUID tenantId, String entityId,
                                           InventoryObjectType superType,
                                           InventoryItemType type,
                                           String data) {}

    @Test
    void extractInventoryFromFlow_shouldExtractInventoryAndRelationships_AllPresent() throws JsonProcessingException {
        FlowValue flow = generateFlowValue(firewallDeviceName, srcUserName, destUserName);

        FlowInventory result = flowInventoryExtractionService.extractInventoryFromFlow(flow);

        List<InventoryItemComparable> expectedItems = List.of(
                toComparable(new InventoryItem(tenantId, srcIp, DEVICE, InventoryItemType.DEVICE,
                        objectMapper.writeValueAsString(DeviceMetadata.builder().ip(srcIp).hostName(sourceHostName).macAddress(sourceMacAddress).build()))),
                toComparable(new InventoryItem(tenantId, destIp, DEVICE, InventoryItemType.DEVICE,
                        objectMapper.writeValueAsString(DeviceMetadata.builder().ip(destIp).hostName("dest-host-02").macAddress("00-16-17-29-45-67").build()))),
                toComparable(new InventoryItem(tenantId, firewallDeviceName, DEVICE, FIREWALL,
                        objectMapper.writeValueAsString(FirewallDeviceMetadata.builder().hostName(firewallDeviceName).ip("**********").macAddress("AA-BB-CC-DD-EE-FF")
                                                                              .externalId("ext-device-001").version("9.1.0").vendor(deviceVendor).product("Palo Alto NGFW").build()))),
                toComparable(new InventoryItem(tenantId, srcUserName, IDENTITY, USER,
                        objectMapper.writeValueAsString(IdentityMetadata.builder().userId("user-src-abc").userName(srcUserName).userPrivileges(userPrivileges).build()))),
                toComparable(new InventoryItem(tenantId, destUserName, IDENTITY, USER,
                        objectMapper.writeValueAsString(IdentityMetadata.builder().userId("user-dest-def").userName(destUserName).userPrivileges(userPrivileges).build())))
        );

        List<InventoryItemComparable> actualItems = result.getInventoryItems().stream().map(this::toComparable).toList();

        assertThat(actualItems).containsExactlyInAnyOrderElementsOf(expectedItems);
        assertThat(result.getDeviceToDeviceRelationships()).hasSize(3);
        assertThat(result.getIdentityToDeviceRelationships()).hasSize(2);
    }

    @Test
    void extractInventoryFromFlow_shouldSkipFirewallItemAndRelationship_IfDeviceNameMissing() throws JsonProcessingException {
        FlowValue flow = generateFlowValue(null, srcUserName, destUserName);

        FlowInventory result = flowInventoryExtractionService.extractInventoryFromFlow(flow);

        List<InventoryItemComparable> expectedItems = List.of(
                toComparable(new InventoryItem(tenantId, srcIp, DEVICE, InventoryItemType.DEVICE,
                        objectMapper.writeValueAsString(DeviceMetadata.builder().ip(srcIp).hostName(sourceHostName).macAddress(sourceMacAddress).build()))),
                toComparable(new InventoryItem(tenantId, destIp, DEVICE, InventoryItemType.DEVICE,
                        objectMapper.writeValueAsString(DeviceMetadata.builder().ip(destIp).hostName("dest-host-02").macAddress("00-16-17-29-45-67").build()))),
                toComparable(new InventoryItem(tenantId, srcUserName, IDENTITY, USER,
                        objectMapper.writeValueAsString(IdentityMetadata.builder().userId("user-src-abc").userName(srcUserName).userPrivileges(userPrivileges).build()))),
                toComparable(new InventoryItem(tenantId, destUserName, IDENTITY, USER,
                        objectMapper.writeValueAsString(IdentityMetadata.builder().userId("user-dest-def").userName(destUserName).userPrivileges(userPrivileges).build())))
        );

        List<InventoryItemComparable> actualItems = result.getInventoryItems().stream().map(this::toComparable).toList();

        assertThat(actualItems).containsExactlyInAnyOrderElementsOf(expectedItems);
        assertThat(result.getDeviceToDeviceRelationships()).hasSize(1);
        assertThat(result.getIdentityToDeviceRelationships()).hasSize(2);
    }

    @Test
    void extractInventoryFromFlow_shouldSkipUserItemsAndRelationships_IfUsernamesMissing() throws JsonProcessingException {
        FlowValue flow = generateFlowValue(firewallDeviceName, null, null);

        FlowInventory result = flowInventoryExtractionService.extractInventoryFromFlow(flow);

        List<InventoryItemComparable> expectedItems = List.of(
                toComparable(new InventoryItem(tenantId, srcIp, DEVICE, InventoryItemType.DEVICE,
                        objectMapper.writeValueAsString(DeviceMetadata.builder().ip(srcIp).hostName(sourceHostName).macAddress(sourceMacAddress).build()))),
                toComparable(new InventoryItem(tenantId, destIp, DEVICE, InventoryItemType.DEVICE,
                        objectMapper.writeValueAsString(DeviceMetadata.builder().ip(destIp).hostName("dest-host-02").macAddress("00-16-17-29-45-67").build()))),
                toComparable(new InventoryItem(tenantId, firewallDeviceName, DEVICE, FIREWALL,
                        objectMapper.writeValueAsString(FirewallDeviceMetadata.builder().hostName(firewallDeviceName).ip("**********").macAddress("AA-BB-CC-DD-EE-FF")
                                                                              .externalId("ext-device-001").version("9.1.0").vendor(deviceVendor).product("Palo Alto NGFW").build())))
        );

        List<InventoryItemComparable> actualItems = result.getInventoryItems().stream().map(this::toComparable).toList();

        assertThat(actualItems).containsExactlyInAnyOrderElementsOf(expectedItems);
        assertThat(result.getDeviceToDeviceRelationships()).hasSize(3);
        assertThat(result.getIdentityToDeviceRelationships()).isEmpty();
    }

    @Test
    void extractInventoryFromFlow_shouldExtractBasicDeviceItems_IfMinimalDataProvided() throws JsonProcessingException {
        FlowValue flow = generateFlowValue(null, null, null);

        FlowInventory result = flowInventoryExtractionService.extractInventoryFromFlow(flow);

        List<InventoryItemComparable> expectedItems = List.of(
                toComparable(new InventoryItem(tenantId, srcIp, DEVICE, InventoryItemType.DEVICE,
                        objectMapper.writeValueAsString(DeviceMetadata.builder().ip(srcIp).hostName(sourceHostName).macAddress(sourceMacAddress).build()))),
                toComparable(new InventoryItem(tenantId, destIp, DEVICE, InventoryItemType.DEVICE,
                        objectMapper.writeValueAsString(DeviceMetadata.builder().ip(destIp).hostName("dest-host-02").macAddress("00-16-17-29-45-67").build())))
        );

        List<InventoryItemComparable> actualItems = result.getInventoryItems().stream().map(this::toComparable).toList();

        assertThat(actualItems).containsExactlyInAnyOrderElementsOf(expectedItems);
        assertThat(result.getDeviceToDeviceRelationships()).hasSize(1);
        assertThat(result.getIdentityToDeviceRelationships()).isEmpty();
    }

    private FlowValue generateFlowValue(final String deviceName, final String srcUserName, final String destUserName) {
        return FlowValue.builder()
                .SrcIP(srcIp)
                .SrcId("src-1234")
                .CSSrcId("cs-src-1234")
                .DestIP(destIp)
                .DestId("dest-567")
                .CSDestId("cs-dest-5678")
                .Port(443)
                .Proto("TCP")
                .SentBytes(1024L)
                .ReceivedBytes(2048L)
                .IllumioTenantId("6f1d0df6-3a70-4cf3-b3c1-33d9b9b0a2af")
                .SrcTenantId("src-tenant-xyz")
                .SrcSubId("src-sub-001")
                .SrcRegion("us-west")
                .SrcResId("vm-src-abc")
                .SrcVnetId("vnet-src-001")
                .SourceUserName(srcUserName)
                .DestTenantId("dest-tenant-xyz")
                .DestSubId("dest-sub-002")
                .DestRegion("us-east")
                .DestResId("vm-dest-xyz")
                .DestVnetId("vnet-dest-002")
                .DestinationUserName(destUserName)
                .SrcFlowType("inbound")
                .DestFlowType("outbound")
                .SrcDeviceId("device-src-123")
                .SrcFirewallId("fw-src-1")
                .SourceUserId("user-src-abc")
                .DestDeviceId("device-dest-456")
                .DestFirewallId("fw-dest-2")
                .DestinationUserId("user-dest-def")
                .SrcResourceType("VirtualMachine")
                .DestResourceType("Database")
                .SrcThreatLevel("low")
                .DestThreatLevel("medium")
                .SrcIsWellknown(true)
                .DestIsWellknown(false)
                .SrcDomain("source.local")
                .DestDomain("destination.local")
                .SrcCountry("USA")
                .DestCountry("Canada")
                .SrcCity("San Francisco")
                .DestCity("Toronto")
                .SrcCloudProvider("Azure")
                .DestCloudProvider("AWS")
                .SourceHostName(sourceHostName)
                .SourceMACAddress(sourceMacAddress)
                .SourceNTDomain("SRCDOMAIN")
                .SourceProcessId("1234")
                .SourceProcessName("java.exe")
                .SourceUserPrivileges(userPrivileges)
                .DeviceAction("Allow")
                .DeviceAddress("**********")
                .DestinationDnsDomain("example.com")
                .DestinationHostName("dest-host-02")
                .DestinationMACAddress("00-16-17-29-45-67")
                .DestinationNTDomain("DESTDOMAIN")
                .DestinationProcessId("5678")
                .DestinationProcessName("mysqld")
                .DestinationServiceName("MySQL")
                .DestinationTranslatedAddress("**********")
                .DestinationUserPrivileges(userPrivileges)
                .LogSeverity("INFO")
                .MaliciousIP("************")
                .MaliciousIPCountry("Russia")
                .MaliciousIPLatitude("55.7558")
                .MaliciousIPLongitude("37.6176")
                .LawTenantId("law-tenant-001")
                .ThreatConfidence("high")
                .ThreatDescription("Suspicious outbound traffic detected")
                .ThreatSeverity("critical")
                .StartTime(Instant.parse("2025-06-13T08:00:00Z"))
                .EndTime(Instant.parse("2025-06-13T08:05:00Z"))
                .SourceDnsDomain("source.internal")
                .SourceServiceName("Apache")
                .SourceSystem("ubuntu-20.04")
                .DeviceMacAddress("AA-BB-CC-DD-EE-FF")
                .DeviceName(deviceName)
                .DeviceOutboundInterface("eth0")
                .DeviceProduct("Palo Alto NGFW")
                .DeviceTranslatedAddress("**************")
                .DeviceVersion("9.1.0")
                .DeviceTimeZone("UTC")
                .DeviceExternalId("ext-device-001")
                .DeviceCustomerNumber3("cust-789")
                .DeviceVendor(deviceVendor)
                .ReceiptTime(Instant.parse("2025-06-13T08:05:01Z"))
                .Activity("ConnectionAttempt")
                .AdditionalExtensions("none")
                .SourceZone("internal")
                .DestinationZone("external")
                .RequestUrl("https://example.com/login")
                .SrcCloudTags("env=dev;team=network")
                .DestCloudTags("env=prod;team=db")
                .FlowCount(1L)
                .PacketsSent(10L)
                .PacketsReceived(10L)
                .TrafficStatus("success")
                .build();
    }

}

