package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.model.FlowInventory;
import com.illumio.data.model.FlowValue;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.receiver.KafkaReceiver;

import java.time.Duration;

import static org.awaitility.Awaitility.await;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class FlowReceiverServiceTest {

    @Mock
    private KafkaReceiver<String, String> kafkaFlowReceiver;
    @Mock
    private FlowInventoryExtractionService flowInventoryExtractionService;
    @Mock
    private InventorySenderService inventorySenderService;
    @Mock
    private ObjectMapper objectMapper;

    private ConsumerRecord<String, String> consumerRecord;
    private FlowReceiverService flowReceiverService;


    @BeforeEach
    public void setUp() {
        flowReceiverService = new FlowReceiverService(kafkaFlowReceiver, objectMapper, flowInventoryExtractionService, inventorySenderService);
        consumerRecord = new ConsumerRecord<>("aggregated-flows", 0, 0L, "key", "{\"SrcIP\":\"************\",\"SrcId\":\"src-1234\",\"CSSrcId\":\"cs-src-1234\",\"DestIP\": \"*********\"}");
    }

    @Test
    public void testConsumeFlows_successfulFlow() throws Exception {
        FlowValue flowValue = new FlowValue();
        FlowInventory flowInventory = new FlowInventory();

        when(kafkaFlowReceiver.receiveAutoAck()).thenReturn(Flux.just(Flux.just(consumerRecord)));
        when(objectMapper.readValue(consumerRecord.value(), FlowValue.class)).thenReturn(flowValue);
        when(flowInventoryExtractionService.extractInventoryFromFlow(eq(flowValue))).thenReturn(flowInventory);
        when(inventorySenderService.sendInventoryObjects(eq(flowInventory))).thenReturn(Mono.empty());

        flowReceiverService.consumeFlows();

        await().atMost(Duration.ofSeconds(5))
                .untilAsserted(() -> {
                    verify(kafkaFlowReceiver, times(1)).receiveAutoAck();
                    verify(objectMapper, times(1)).readValue(consumerRecord.value(), FlowValue.class);
                    verify(flowInventoryExtractionService, times(1)).extractInventoryFromFlow(flowValue);
                    verify(inventorySenderService, times(1)).sendInventoryObjects(eq(flowInventory));
                });
    }

    @Test
    public void testConsumeFlows_errorInFlowValueFromKafkaRecord() throws Exception {
        when(kafkaFlowReceiver.receiveAutoAck()).thenReturn(Flux.just(Flux.just(consumerRecord)));
        when(objectMapper.readValue(consumerRecord.value(), FlowValue.class)).thenThrow(new RuntimeException("Parsing error"));

        flowReceiverService.consumeFlows();

        await().atMost(Duration.ofSeconds(5))
                .untilAsserted(() -> {
                    verify(kafkaFlowReceiver, times(1)).receiveAutoAck();
                    verify(flowInventoryExtractionService, never()).extractInventoryFromFlow(any());
                    verify(inventorySenderService, never()).sendInventoryObjects(any());
                });
    }

    @Test
    public void testConsumeFlows_errorInExtractInventoryFromFlow() throws Exception {
        FlowValue flowValue = new FlowValue();

        when(kafkaFlowReceiver.receiveAutoAck()).thenReturn(Flux.just(Flux.just(consumerRecord)));
        when(objectMapper.readValue(consumerRecord.value(), FlowValue.class)).thenReturn(flowValue);
        when(flowInventoryExtractionService.extractInventoryFromFlow(flowValue)).thenThrow(new RuntimeException("Extraction error"));

        flowReceiverService.consumeFlows();

        await().atMost(Duration.ofSeconds(5))
                .untilAsserted(() -> {
                    verify(kafkaFlowReceiver, times(1)).receiveAutoAck();
                    verify(flowInventoryExtractionService, times(1)).extractInventoryFromFlow(flowValue);
                    verify(inventorySenderService, never()).sendInventoryObjects(any());
                });
    }

    @Test
    public void testConsumeFlows_errorInSendInventoryObjects() throws Exception {
        FlowValue flowValue = new FlowValue();
        FlowInventory flowInventory = new FlowInventory();

        when(kafkaFlowReceiver.receiveAutoAck()).thenReturn(Flux.just(Flux.just(consumerRecord)));
        when(objectMapper.readValue(consumerRecord.value(), FlowValue.class)).thenReturn(flowValue);
        when(flowInventoryExtractionService.extractInventoryFromFlow(eq(flowValue))).thenReturn(flowInventory);
        when(inventorySenderService.sendInventoryObjects(eq(flowInventory))).thenThrow(new RuntimeException("Sending error"));

        flowReceiverService.consumeFlows();

        await().atMost(Duration.ofSeconds(5))
                .untilAsserted(() -> {
                    verify(kafkaFlowReceiver, times(1)).receiveAutoAck();
                    verify(flowInventoryExtractionService, times(1)).extractInventoryFromFlow(flowValue);
                });
    }

}