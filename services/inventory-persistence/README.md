# Inventory Persistence

The **Inventory Persistence** service allows for the consumption of inventory objects and their relations as well as 
their persistence into an Azure CosmosDB Postgres database. The schema of this database is documented here:
https://confluence.illum.io/pages/viewpage.action?spaceKey=~john.colarusso&title=Network+Device+Inventory+Schema
---

## Running Locally

### 1. Modify `application.yml`
Ensure the following configurations are set:

1. **Kafka Inventory Consumer Config** - Kafka Consumer reading inventory objects and their relations
3. **Azure Cosmos DB Postgres Config** – Azure Cosmos DB Postgres instance to persist inventory to.

### 2. Run Locally Using IntelliJ

To run the application from within IntelliJ, use the following run configuration:

- **Run Configuration**: `inventory-persistence/InventoryPersistenceApplication`