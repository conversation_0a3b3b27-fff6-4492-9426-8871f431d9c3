plugins {
    id 'org.springframework.boot'
    id 'com.google.cloud.tools.jib'
    id 'com.github.johnrengelman.shadow'
}

apply from: "${project.rootDir}/gradle/scripts/jib-build.gradle"
apply from: "${project.rootDir}/gradle/scripts/opentelemetry.gradle"

dependencies {
    // implementation
    implementation project(":commons:utility-commons")
    implementation project(":commons:inventory-commons")
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.kafka:spring-kafka'
    implementation 'io.projectreactor.kafka:reactor-kafka'
    implementation 'co.elastic.logging:logback-ecs-encoder'
    implementation 'org.apache.commons:commons-lang3'
    implementation 'org.springframework.boot:spring-boot-starter-data-r2dbc'
    implementation 'io.r2dbc:r2dbc-postgresql'
    implementation 'org.postgresql:postgresql'

    // jackson
    implementation 'com.fasterxml.jackson.core:jackson-annotations'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'

    // resilience4j
    implementation 'io.github.resilience4j:resilience4j-ratelimiter'

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // spring
    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"

    // test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
}

jib {
    container {
        entrypoint = [
                'java', '-cp', '@/app/jib-classpath-file',
                'com.illumio.data.InventoryPersistenceApplication',
                '--spring.config.location=file:/var/resources/application.yml',
                '--spring.profiles.active=prod'
        ]
        environment = [
                "JAVA_TOOL_OPTIONS": "-javaagent:/otel/opentelemetry-javaagent.jar",
                "OTEL_METRICS_EXPORTER": "prometheus",
                "OTEL_TRACES_EXPORTER": "none",
                "OTEL_LOGS_EXPORTER": "none"
        ]
    }
}
