package com.illumio.data.repositories;

import lombok.extern.slf4j.Slf4j;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;

import static com.illumio.data.util.InventoryPersistenceConstants.TABLE_DEVICE;

@Slf4j
@Service
public class DeviceRepository extends AbstractInventoryItemRepository {

    public DeviceRepository(final DatabaseClient databaseClient) {
        super(databaseClient);
    }

    @Override
    protected String getTableName() {
        return TABLE_DEVICE;
    }
}