package com.illumio.data.repositories;

import com.illumio.data.model.IdentityToDeviceRelationship;
import io.r2dbc.spi.Result;
import io.r2dbc.spi.Statement;
import lombok.RequiredArgsConstructor;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

import static com.illumio.data.util.InventoryPersistenceConstants.TABLE_IDENTITY_TO_DEVICE;

@Service
@RequiredArgsConstructor
public class IdentityToDeviceRepository extends AbstractInventoryObjectRepository<IdentityToDeviceRelationship> {

    private final DatabaseClient databaseClient;

    @Override
    public Mono<Void> upsertBatch(final List<IdentityToDeviceRelationship> relationships) {
        if (relationships.isEmpty()) {
            return Mono.empty();
        }

        return databaseClient.inConnectionMany(connection -> {
            final String sql = """
            INSERT INTO %s (
                identity_id, device_id, created_at
            ) VALUES (
                $1, $2, NOW()
            )
            ON CONFLICT DO NOTHING
            """.formatted(TABLE_IDENTITY_TO_DEVICE);

            final Statement statement = connection.createStatement(sql);
            relationships.forEach(relationship ->
                    statement
                            .bind(0, relationship.getIdentityId())
                            .bind(1, relationship.getDeviceId())
                            .add()
            );

            return Flux.from(statement.execute())
                    .flatMap(Result::getRowsUpdated);
        }).then();
    }

}
