package com.illumio.data.repositories;

import com.illumio.data.model.DeviceToDeviceRelationship;
import io.r2dbc.spi.Result;
import io.r2dbc.spi.Statement;
import lombok.RequiredArgsConstructor;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

import static com.illumio.data.util.InventoryPersistenceConstants.TABLE_DEVICE_TO_DEVICE;

@Service
@RequiredArgsConstructor
public class DeviceToDeviceRepository extends AbstractInventoryObjectRepository<DeviceToDeviceRelationship> {

    private final DatabaseClient databaseClient;

    @Override
    public Mono<Void> upsertBatch(final List<DeviceToDeviceRelationship> relationships) {
        if (relationships.isEmpty()) {
           return Mono.empty();
        }

        return databaseClient.inConnectionMany(connection -> {
            final String sql = """
            INSERT INTO %s (
                src_device_id, dest_device_id, created_at
            ) VALUES (
                $1, $2, NOW()
            )
            ON CONFLICT DO NOTHING
            """.formatted(TABLE_DEVICE_TO_DEVICE);

            final Statement statement = connection.createStatement(sql);

            relationships.forEach(relationship ->
                    statement
                            .bind(0, relationship.getSrcDeviceId())
                            .bind(1, relationship.getDestDeviceId())
                            .add()
            );

            return Flux.from(statement.execute())
                    .flatMap(Result::getRowsUpdated);
        }).then();
    }

}
