package com.illumio.data.repositories;

import com.illumio.data.model.InventoryItem;
import io.r2dbc.spi.Result;
import io.r2dbc.spi.Statement;
import lombok.RequiredArgsConstructor;
import org.springframework.r2dbc.core.DatabaseClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
public abstract class AbstractInventoryItemRepository extends AbstractInventoryObjectRepository<InventoryItem> {

    private final DatabaseClient databaseClient;

    protected abstract String getTableName();

    @Override
    public Mono<Void> upsertBatch(final List<InventoryItem> inventoryItems) {
        if (inventoryItems.isEmpty()) {
            return Mono.empty();
        }
        return databaseClient.inConnectionMany(connection -> {
            final String sql = """
            INSERT INTO %s (
                id, tenant_id, type, entity_id, resource_id, data, hash, created_at, last_seen_at, updated_at
            ) VALUES (
                $1, $2, $3, $4, $5, $6::jsonb, $7, NOW(), $8, NOW()
            )
            ON CONFLICT (id) DO UPDATE SET
                tenant_id = EXCLUDED.tenant_id,
                type = EXCLUDED.type,
                entity_id = EXCLUDED.entity_id,
                resource_id = EXCLUDED.resource_id,
                data = EXCLUDED.data,
                hash = EXCLUDED.hash,
                last_seen_at = EXCLUDED.last_seen_at,
                updated_at = NOW()
            """.formatted(getTableName());

            final Statement statement = connection.createStatement(sql);
            inventoryItems.forEach(inventoryItem -> {
                statement
                        .bind(0, inventoryItem.getId())
                        .bind(1, inventoryItem.getTenantId())
                        .bind(2, inventoryItem.getType().name())
                        .bind(3, inventoryItem.getEntityId())
                        .bind(4, inventoryItem.getResourceId())
                        .bind(5, inventoryItem.getData())
                        .bind(6, inventoryItem.getHash());
                Optional.ofNullable(inventoryItem.getLastSeenAt())
                        .ifPresentOrElse(
                                lastSeenAt -> statement.bind(7, lastSeenAt),
                                () -> statement.bindNull(7, Instant.class));
                statement.add();
            });

            return Flux.from(statement.execute())
                    .flatMap(Result::getRowsUpdated);
        }).then();
    }

}
