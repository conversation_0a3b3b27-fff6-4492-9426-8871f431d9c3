package com.illumio.data.repositories;

import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.stereotype.Service;

import static com.illumio.data.util.InventoryPersistenceConstants.TABLE_IDENTITY;

@Service
public class IdentityRepository extends AbstractInventoryItemRepository {

    public IdentityRepository(final DatabaseClient databaseClient) {
        super(databaseClient);
    }

    @Override
    protected String getTableName() {
        return TABLE_IDENTITY;
    }
}