package com.illumio.data.configuration;

import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class ConsumerRateLimiterConfig {
    private final InventoryPersistenceConfig inventoryPersistenceConfig;

    @Bean
    public RateLimiter consumerRateLimiter() {
        final RateLimiterConfig rateLimiterConfig = RateLimiterConfig.custom()
                .limitForPeriod(inventoryPersistenceConfig.getSamplingConfig().getLimitForPeriod())
                .limitRefreshPeriod(inventoryPersistenceConfig.getSamplingConfig().getLimitRefreshPeriod())
                .build();

        return RateLimiter.of("inventory-consumer-rate-limiter", rateLimiterConfig);
    }
}
