logging:
  level:
    ROOT: INFO
spring:
  application:
    name: inventory-persistence
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive
  r2dbc:
    url: r2dbc:postgresql://c-inventory-test.kb7kz5lr267y22.postgres.cosmos.azure.com:5432/inventory
    username: citus
    password: DO_NOT_COMMIT
    properties:
      # refer to https://www.postgresql.org/docs/current/libpq-ssl.html#LIBPQ-SSL-PROTECTION for sslMode types
      sslMode: require # recommendation: "allow" if connecting to local postgres instance; "require" for Azure deployed postgres instances
server:
  port: 8082

retry-config:
  parameters:
    min-backoff: 30s
    max-retries: 2

inventory-persistence:
  kafka-inventory-consumer-config:
    bootstrap-servers: test-arch-eventhub.servicebus.windows.net:9093
    sasl-jaas-config: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";
    topic: inventory-sync-v1
    group-id: inventory-persistence-1
    auto-offset-reset: latest

  sampling-config:
    enabled: true
    limit-for-period: 100_000
    limit-refresh-period: 1m

  batch-processing-config:
    limit-rate-low-tide: 75
    limit-rate-high-tide: 100
    buffer-timeout-max-size: 20
    buffer-timeout-duration: 1m