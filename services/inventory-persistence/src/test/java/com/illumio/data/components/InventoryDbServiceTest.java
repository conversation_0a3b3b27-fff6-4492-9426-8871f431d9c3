package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.illumio.data.model.*;
import com.illumio.data.repositories.DeviceRepository;
import com.illumio.data.repositories.DeviceToDeviceRepository;
import com.illumio.data.repositories.IdentityRepository;
import com.illumio.data.repositories.IdentityToDeviceRepository;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InOrder;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Instant;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InventoryDbServiceTest {

    @Mock
    private DeviceRepository deviceRepository;
    @Mock
    private IdentityRepository identityRepository;
    @Mock
    private DeviceToDeviceRepository deviceToDeviceRepository;
    @Mock
    private IdentityToDeviceRepository identityToDeviceRepository;

    private final ObjectMapper objectMapper = new ObjectMapper();

    private InventoryDbService inventoryDbService;

    final String validDeviceJson1 = "{\"createdAt\":null,\"id\":\"0197a973-fb8b-76d5-a027-660311abb216\",\"tenantId\":\"6f1d0df6-3a70-4cf3-b3c1-33d9b9b0a2af\",\"type\":\"DEVICE\",\"superType\":\"DEVICE\",\"entityId\":\"*********\",\"resourceId\":\"9a9176e4-7a27-580e-a462-17ddda3f6c4b\",\"data\":\"{\\\"hostName\\\":\\\"dest-host-02\\\",\\\"ip\\\":\\\"*********\\\",\\\"macAddress\\\":\\\"00-16-17-29-45-67\\\"}\",\"hash\":\"KE4jTRLsVJjERM0H6TpiyLUpz+VETAWVAgwJKsNjgtw=\",\"lastSeenAt\":\"2025-06-25T23:37:20.011709Z\",\"updatedAt\":null}";
    final String validDeviceJson2 = "{\"createdAt\":null,\"id\":\"0197a973-fb8b-7f16-b308-d04a1a062e7b\",\"tenantId\":\"6f1d0df6-3a70-4cf3-b3c1-33d9b9b0a2af\",\"type\":\"FIREWALL\",\"superType\":\"DEVICE\",\"entityId\":\"firewall-01\",\"resourceId\":\"fc8339f7-032a-58ae-8efe-212748bb0755\",\"data\":\"{\\\"hostName\\\":\\\"firewall-01\\\",\\\"ip\\\":\\\"**********\\\",\\\"macAddress\\\":\\\"AA-BB-CC-DD-EE-FF\\\",\\\"externalId\\\":\\\"ext-device-001\\\",\\\"version\\\":\\\"9.1.0\\\",\\\"vendor\\\":null,\\\"product\\\":\\\"Palo Alto NGFW\\\"}\",\"hash\":\"TWFsUkgJtflJkqLVm7k6SCk0Re/+apRgaLsHPHhzUcQ=\",\"lastSeenAt\":\"2025-06-25T23:37:20.011893Z\",\"updatedAt\":null}";
    final String validIdentityJson1 = "{\"createdAt\":null,\"id\":\"0197a973-fb8b-76ab-b51f-5592fe2b5831\",\"tenantId\":\"6f1d0df6-3a70-4cf3-b3c1-33d9b9b0a2af\",\"type\":\"USER\",\"superType\":\"IDENTITY\",\"entityId\":\"<EMAIL>\",\"resourceId\":\"ef9d931f-e739-5a8f-807a-3a0ec102ad72\",\"data\":\"{\\\"userId\\\":\\\"user-src-abc\\\",\\\"userName\\\":\\\"<EMAIL>\\\",\\\"userPrivileges\\\":\\\"admin\\\"}\",\"hash\":\"52At8z+6C6JxzfECRFLsd0d/V2zIJBKKvwtB+uLBwnY=\",\"lastSeenAt\":\"2025-06-25T23:37:20.011763Z\",\"updatedAt\":null}";
    final String validIdentityJson2 = "{\"createdAt\":null,\"id\":\"0197a973-fb8b-7594-9a77-65d58176474f\",\"tenantId\":\"6f1d0df6-3a70-4cf3-b3c1-33d9b9b0a2af\",\"type\":\"USER\",\"superType\":\"IDENTITY\",\"entityId\":\"<EMAIL>\",\"resourceId\":\"afc576bb-c09a-5f84-9971-84464d63e7d1\",\"data\":\"{\\\"userId\\\":\\\"user-dest-def\\\",\\\"userName\\\":\\\"<EMAIL>\\\",\\\"userPrivileges\\\":null}\",\"hash\":\"TH14yB2rxqZptNN/BfAQzAIbatw8j1FA8JjtCiqhxiI=\",\"lastSeenAt\":\"2025-06-25T23:37:20.011827Z\",\"updatedAt\":null}";
    final String validDeviceToDeviceJson1 = "{\"createdAt\":null,\"srcDeviceId\":\"2caf7119-e00b-5439-bfb6-72c00a6c9d06\",\"destDeviceId\":\"fc8339f7-032a-58ae-8efe-212748bb0755\"}";
    final String validDeviceToDeviceJson2 = "{\"createdAt\":null,\"srcDeviceId\":\"2caf7119-e00b-5439-bfb6-72c00a6c9d04\",\"destDeviceId\":\"fc8339f7-032a-58ae-8efe-212748bb0754\"}";
    final String validIdentityToDeviceJson1 = "{\"createdAt\":null,\"deviceId\":\"2caf7119-e00b-5439-bfb6-72c00a6c9d06\",\"identityId\":\"ef9d931f-e739-5a8f-807a-3a0ec102ad72\"}";
    final String validIdentityToDeviceJson2 = "{\"createdAt\":null,\"deviceId\":\"2caf7119-e00b-5439-bfb6-72c00a6c9d07\",\"identityId\":\"ef9d931f-e739-5a8f-807a-3a0ec102ad73\"}";

    final InventoryItem device1 = InventoryItem.builder()
            .id(UUID.fromString("0197a973-fb8b-76d5-a027-660311abb216"))
            .tenantId(UUID.fromString("6f1d0df6-3a70-4cf3-b3c1-33d9b9b0a2af"))
            .type(InventoryItemType.DEVICE)
            .superType(InventoryObjectType.DEVICE)
            .entityId("*********")
            .resourceId(UUID.fromString("9a9176e4-7a27-580e-a462-17ddda3f6c4b"))
            .data("{\"hostName\":\"dest-host-02\",\"ip\":\"*********\",\"macAddress\":\"00-16-17-29-45-67\"}")
            .hash(Base64.getDecoder().decode("KE4jTRLsVJjERM0H6TpiyLUpz+VETAWVAgwJKsNjgtw="))
            .lastSeenAt(Instant.parse("2025-06-25T23:37:20.011709Z"))
            .updatedAt(null)
            .build();
    final InventoryItem device2 = InventoryItem.builder()
            .id(UUID.fromString("0197a973-fb8b-7f16-b308-d04a1a062e7b"))
            .tenantId(UUID.fromString("6f1d0df6-3a70-4cf3-b3c1-33d9b9b0a2af"))
            .type(InventoryItemType.FIREWALL)
            .superType(InventoryObjectType.DEVICE)
            .entityId("firewall-01")
            .resourceId(UUID.fromString("fc8339f7-032a-58ae-8efe-212748bb0755"))
            .data("{\"hostName\":\"firewall-01\",\"ip\":\"**********\",\"macAddress\":\"AA-BB-CC-DD-EE-FF\",\"externalId\":\"ext-device-001\",\"version\":\"9.1.0\",\"vendor\":null,\"product\":\"Palo Alto NGFW\"}")
            .hash(Base64.getDecoder().decode("TWFsUkgJtflJkqLVm7k6SCk0Re/+apRgaLsHPHhzUcQ="))
            .lastSeenAt(Instant.parse("2025-06-25T23:37:20.011893Z"))
            .updatedAt(null)
            .build();
    final InventoryItem identity1 = InventoryItem.builder()
            .id(UUID.fromString("0197a973-fb8b-76ab-b51f-5592fe2b5831"))
            .tenantId(UUID.fromString("6f1d0df6-3a70-4cf3-b3c1-33d9b9b0a2af"))
            .type(InventoryItemType.USER)
            .superType(InventoryObjectType.IDENTITY)
            .entityId("<EMAIL>")
            .resourceId(UUID.fromString("ef9d931f-e739-5a8f-807a-3a0ec102ad72"))
            .data("{\"userId\":\"user-src-abc\",\"userName\":\"<EMAIL>\",\"userPrivileges\":\"admin\"}")
            .hash(Base64.getDecoder().decode("52At8z+6C6JxzfECRFLsd0d/V2zIJBKKvwtB+uLBwnY="))
            .lastSeenAt(Instant.parse("2025-06-25T23:37:20.011763Z"))
            .updatedAt(null)
            .build();
    final InventoryItem identity2 = InventoryItem.builder()
            .id(UUID.fromString("0197a973-fb8b-7594-9a77-65d58176474f"))
            .tenantId(UUID.fromString("6f1d0df6-3a70-4cf3-b3c1-33d9b9b0a2af"))
            .type(InventoryItemType.USER)
            .superType(InventoryObjectType.IDENTITY)
            .entityId("<EMAIL>")
            .resourceId(UUID.fromString("afc576bb-c09a-5f84-9971-84464d63e7d1"))
            .data("{\"userId\":\"user-dest-def\",\"userName\":\"<EMAIL>\",\"userPrivileges\":null}")
            .hash(Base64.getDecoder().decode("TH14yB2rxqZptNN/BfAQzAIbatw8j1FA8JjtCiqhxiI="))
            .lastSeenAt(Instant.parse("2025-06-25T23:37:20.011827Z"))
            .updatedAt(null)
            .build();
    final DeviceToDeviceRelationship deviceToDeviceRelationship1 = DeviceToDeviceRelationship.builder()
            .srcDeviceId(UUID.fromString("2caf7119-e00b-5439-bfb6-72c00a6c9d06"))
            .destDeviceId(UUID.fromString("fc8339f7-032a-58ae-8efe-212748bb0755"))
            .build();
    final DeviceToDeviceRelationship deviceToDeviceRelationship2 = DeviceToDeviceRelationship.builder()
            .srcDeviceId(UUID.fromString("2caf7119-e00b-5439-bfb6-72c00a6c9d04"))
            .destDeviceId(UUID.fromString("fc8339f7-032a-58ae-8efe-212748bb0754"))
            .build();
    final IdentityToDeviceRelationship identityToDeviceRelationship1 = IdentityToDeviceRelationship.builder()
            .deviceId(UUID.fromString("2caf7119-e00b-5439-bfb6-72c00a6c9d06"))
            .identityId(UUID.fromString("ef9d931f-e739-5a8f-807a-3a0ec102ad72"))
            .build();
    final IdentityToDeviceRelationship identityToDeviceRelationship2 = IdentityToDeviceRelationship.builder()
            .deviceId(UUID.fromString("2caf7119-e00b-5439-bfb6-72c00a6c9d07"))
            .identityId(UUID.fromString("ef9d931f-e739-5a8f-807a-3a0ec102ad73"))
            .build();

    @BeforeEach
    void setup() {
        objectMapper.registerModule(new JavaTimeModule());
        inventoryDbService = new InventoryDbService(objectMapper, deviceRepository, identityRepository, deviceToDeviceRepository, identityToDeviceRepository);
        inventoryDbService.init();
    }

    @Test
    void persistInventoryObjects_shouldPersistAllValidObjects() {
        when(deviceRepository.upsertBatch(any())).thenReturn(Mono.empty());
        when(identityRepository.upsertBatch(any())).thenReturn(Mono.empty());
        when(deviceToDeviceRepository.upsertBatch(any())).thenReturn(Mono.empty());
        when(identityToDeviceRepository.upsertBatch(any())).thenReturn(Mono.empty());

        final List<Pair<InventoryObjectType, String>> inventoryTypesAndJsons = List.of(
                Pair.of(InventoryObjectType.IDENTITY_DEVICE_RELATIONSHIP, validIdentityToDeviceJson1),
                Pair.of(InventoryObjectType.DEVICE_DEVICE_RELATIONSHIP, validDeviceToDeviceJson1),
                Pair.of(InventoryObjectType.DEVICE, validDeviceJson1),
                Pair.of(InventoryObjectType.DEVICE_DEVICE_RELATIONSHIP, validDeviceToDeviceJson2),
                Pair.of(InventoryObjectType.IDENTITY, validIdentityJson1),
                Pair.of(InventoryObjectType.IDENTITY_DEVICE_RELATIONSHIP, validIdentityToDeviceJson2),
                Pair.of(InventoryObjectType.DEVICE, validDeviceJson2),
                Pair.of(InventoryObjectType.IDENTITY, validIdentityJson2)
        );
        StepVerifier.create(inventoryDbService.persistInventoryObjects(inventoryTypesAndJsons))
                .verifyComplete();

        InOrder inOrder = inOrder(deviceRepository, identityRepository, deviceToDeviceRepository, identityToDeviceRepository);
        inOrder.verify(deviceRepository, times(1)).upsertBatch(argThat(upsertItems ->
                upsertItems.containsAll(List.of(device1, device2)) && upsertItems.size() == 2
        ));
        inOrder.verify(identityRepository, times(1)).upsertBatch(argThat(upsertItems ->
                upsertItems.containsAll(List.of(identity1, identity2)) && upsertItems.size() == 2
        ));
        inOrder.verify(deviceToDeviceRepository, times(1)).upsertBatch(argThat(upsertItems ->
                upsertItems.containsAll(List.of(deviceToDeviceRelationship1, deviceToDeviceRelationship2)) && upsertItems.size() == 2
        ));
        inOrder.verify(identityToDeviceRepository, times(1)).upsertBatch(argThat(upsertItems ->
                upsertItems.containsAll(List.of(identityToDeviceRelationship1, identityToDeviceRelationship2)) && upsertItems.size() == 2
        ));
    }

    @Test
    void persistInventoryObjects_shouldIgnoreInvalidObjects() {
        when(deviceRepository.upsertBatch(any())).thenReturn(Mono.empty());

        final List<Pair<InventoryObjectType, String>> inventoryTypesAndJsons = List.of(
                Pair.of(InventoryObjectType.DEVICE, validDeviceJson1),
                Pair.of(InventoryObjectType.DEVICE, "INVALID_DEVICE_JSON")
        );
        StepVerifier.create(inventoryDbService.persistInventoryObjects(inventoryTypesAndJsons))
                .verifyComplete();

        verify(deviceRepository, times(1)).upsertBatch(argThat(upsertItems ->
                upsertItems.contains(device1) && upsertItems.size() == 1
        ));
    }

}
