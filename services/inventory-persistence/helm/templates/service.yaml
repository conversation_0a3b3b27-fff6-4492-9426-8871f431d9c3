apiVersion: v1
kind: Service
metadata:
  name: {{ include "InventoryPersistence.name" . }}
  labels:
    {{- include "InventoryPersistence.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "InventoryPersistence.selectorLabels" . | nindent 4 }}
