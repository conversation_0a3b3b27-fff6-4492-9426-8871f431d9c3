apiVersion: v1
kind: Secret
metadata:
  name: {{ include "InventoryPersistence.fullname" . }}-env-secrets
  labels:
    {{- include "InventoryPersistence.labels" . | nindent 4 }}
type: Opaque
stringData:
  SPRING_R2DBC_PASSWORD: {{ .Values.spring.r2dbc.password }}
  INVENTORYPERSISTENCE_KAFKAINVENTORYCONSUMERCONFIG_SASLJAASCONFIG:  org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.inventoryPersistence.kafkaInventoryConsumerConfig.connectionString }}";