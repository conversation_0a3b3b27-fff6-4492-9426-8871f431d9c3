logging:
  level:
    ROOT: INFO

spring:
  application:
    name: flow-puller
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

vault:
  uri: "http://127.0.0.1:8200"
  token: DO_NOT_COMMIT
  cache-time-mins: 1

storage-accounts:
  accounts:
    inglogpoc-sa:
      storage-account-endpoint: https://inglogpoc.blob.core.windows.net/
      container: flow-puller

kafka:
  senders:
    flow-sender:
      topic: test-flow-log-event
      bootstrap-servers: "test-arch-eventhub.servicebus.windows.net:9093"
      is-connection-string: true
      is-managed-identity: false
      sasl-jaas-config: "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"$ConnectionString\" password=\"DO_NOT_COMMIT\";"
      request-timeout-ms: 300000
      delivery-timeout-ms: 300000
    time-sender:
      topic: blob-time
      bootstrap-servers: "test-arch-eventhub.servicebus.windows.net:9093"
      is-connection-string: true
      is-managed-identity: false
      sasl-jaas-config: "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"$ConnectionString\" password=\"DO_NOT_COMMIT\";"
      request-timeout-ms: 300000
      delivery-timeout-ms: 300000
  receivers:
    time-receiver:
      topic: blob-time
      bootstrap-servers: "test-arch-eventhub.servicebus.windows.net:9093"
      is-connection-string: true
      is-managed-identity: false
      sasl-jaas-config: "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"$ConnectionString\" password=\"DO_NOT_COMMIT\";"
      group-id: flow-puller
      auto-offset-reset: earliest
      request-timeout-ms: 180000
      max-poll-records: 20000
      max-partition-fetch-bytes: 5242880

flow-puller:
  storage-account:
    name: inglogpoc-sa
    vault-path: secret/data/inglogpoc-creds
  kafka-flow-sender: flow-sender
  kafka-time-sender: time-sender
  kafka-time-receiver: time-receiver
  blob-sync-config:
    period-duration: 5m
    initial-time-sync-kafka-poll-duration: 2m
    initial-time-sync-fallback-period-duration: 12h