package com.illumio.data.components;

import static com.illumio.data.util.Constants.KAFKA_FLOW_UPDATE_HEADER_KEY_CLOUDPROVIDER;
import static com.illumio.data.util.Constants.KAFKA_FLOW_UPDATE_HEADER_KEY_COMMUNICATIONMODEL;
import static com.illumio.data.util.Constants.KAFKA_FLOW_UPDATE_HEADER_KEY_PARTITIONKEY;
import static com.illumio.data.util.Constants.KAFKA_FLOW_UPDATE_HEADER_VALUE_AZURE;
import static com.illumio.data.util.Constants.KAFKA_FLOW_UPDATE_HEADER_VALUE_PULL;

import com.azure.core.credential.TokenCredential;
import com.azure.storage.blob.BlobContainerAsyncClient;
import com.azure.storage.blob.models.BlobItem;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.FlowPullerConfig;
import com.illumio.data.pojo.AzureFlowLog;
import com.illumio.data.util.KafkaConfigUtil;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class FlowPullerService {

    private final BlobPullerService blobPullerService;
    private final BlobTimeService blobTimeService;
    private final KafkaService kafkaService;
    private final VaultService vaultService;
    private final FlowPullerConfig flowPullerConfig;

    public Mono<Void> scanAndPublishModifiedFlows() {
        log.info("Starting scan for modified flows...");
        final long scanStartTime = System.currentTimeMillis();

        return blobTimeService.getLatestBlobTimeCutoff()
                .flatMapMany(this::getModifiedFlowLogsFromBlobTimeCutoff)
                .collectList()
                .flatMap(modifiedFlows -> sendFlowLogsToKafka(modifiedFlows, scanStartTime));
    }

    public boolean isBlobModifiedAfter(final BlobItem blobItem, final long cutoffTime) {
        return blobItem.getProperties().getLastModified().toInstant().toEpochMilli() >= cutoffTime;
    }
    
    private Flux<AzureFlowLog> getModifiedFlowLogsFromBlobTimeCutoff(final long blobTimeCutoff) {
        String storageAccount = flowPullerConfig.getStorageAccount().getName();
        String vaultPath = flowPullerConfig.getStorageAccount().getVaultPath();

        if(StringUtils.hasText(vaultPath)) {
            Supplier<Mono<TokenCredential>> credentialsSupplier = createCredentialsSupplier(vaultPath);
            return blobPullerService.getBlobs(storageAccount, blob -> isBlobModifiedAfter(blob, blobTimeCutoff), credentialsSupplier).map(this::getAzureFlowLogFromBlobItem);
        }

        return blobPullerService.getBlobs(storageAccount, blob -> isBlobModifiedAfter(blob, blobTimeCutoff)).map(this::getAzureFlowLogFromBlobItem);
    }

    private Mono<Void> sendFlowLogsToKafka(final List<AzureFlowLog> flowLogs, long scanStartTime) {
        if (flowLogs.isEmpty()) {
            log.info("No modified flow logs to send");
            return blobTimeService.updateBlobTimeCutoff(scanStartTime);
        }

        String flowSenderTopic = KafkaConfigUtil.getSenderTopic(flowPullerConfig.getKafkaConfig(), flowPullerConfig.getKafkaFlowSender());

        return Flux.fromIterable(flowLogs)
                .flatMap(flowLog -> {
                    String message = convertFlowLogToMessage(flowLog);
                    String key = flowLog.getData().getUrl();
                    Map<String, String> headers = createKafkaHeaders(flowLog);

                    return kafkaService.sendWithFlowSender(flowSenderTopic, key, message, headers);
                })
                .then(blobTimeService.updateBlobTimeCutoff(scanStartTime));
    }

    private AzureFlowLog getAzureFlowLogFromBlobItem(final BlobPullerService.BlobItemWithClient blobItemWithClient) {
        BlobItem blobItem = blobItemWithClient.getBlobItem();
        BlobContainerAsyncClient blobContainerClient = blobItemWithClient.getBlobContainerClient();

        return AzureFlowLog.builder()
                .eventTime(blobItem.getProperties().getLastModified().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
                .data(AzureFlowLog.Data.builder()
                        .contentLength(blobItem.getProperties().getContentLength())
                        .url(blobContainerClient.getBlobAsyncClient(blobItem.getName()).getBlobUrl())
                        .build())
                .build();
    }

    @SneakyThrows
    private String convertFlowLogToMessage(final AzureFlowLog azureFlowLog) {
        return new ObjectMapper().writeValueAsString(azureFlowLog);
    }

    private Map<String, String> createKafkaHeaders(final AzureFlowLog azureFlowLog) {
        return Map.of(
                KAFKA_FLOW_UPDATE_HEADER_KEY_CLOUDPROVIDER, KAFKA_FLOW_UPDATE_HEADER_VALUE_AZURE,
                KAFKA_FLOW_UPDATE_HEADER_KEY_COMMUNICATIONMODEL, KAFKA_FLOW_UPDATE_HEADER_VALUE_PULL,
                KAFKA_FLOW_UPDATE_HEADER_KEY_PARTITIONKEY, azureFlowLog.getData().getUrl()
        );
    }

    private Supplier<Mono<TokenCredential>> createCredentialsSupplier(String vaultPath) {
        return () -> vaultService.getAzureTokenCredentialFromCSVault(vaultPath);
    }
}
