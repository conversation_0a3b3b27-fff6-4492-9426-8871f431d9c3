package com.illumio.data.components;

import com.illumio.data.component.KafkaClientFactory;
import com.illumio.data.config.KafkaConfig;
import com.illumio.data.configuration.FlowPullerConfig;
import com.illumio.data.util.KafkaConfigUtil;
import jakarta.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.util.retry.Retry;

@Slf4j
@Service
@RequiredArgsConstructor
public class KafkaService {

    private final FlowPullerConfig flowPullerConfig;
    private final KafkaConfig kafkaConfig;
    private final KafkaClientFactory kafkaClientFactory;

    private KafkaSender<String, String> kafkaFlowSender;
    private KafkaSender<String, String> kafkaTimeSender;
    private KafkaReceiver<String, String> kafkaTimeReceiver;

    @PostConstruct
    private void initKafkaClients() {
        this.kafkaTimeSender = kafkaClientFactory.createKafkaSender(flowPullerConfig.getKafkaTimeSender());
        this.kafkaFlowSender = kafkaClientFactory.createKafkaSender(flowPullerConfig.getKafkaFlowSender());
        this.kafkaTimeReceiver = kafkaClientFactory.createKafkaReceiver(flowPullerConfig.getKafkaTimeReceiver());
    }

    public Mono<Void> sendWithFlowSender(String topic, String key, String message, Map<String, String> headers) {
        return send(kafkaFlowSender, flowPullerConfig.getKafkaFlowSender(), topic, key, message, headers);
    }

    public Mono<Void> sendWithTimeSender(String topic, String key, String message, Map<String, String> headers) {
        return send(kafkaTimeSender, flowPullerConfig.getKafkaTimeSender(), topic, key, message, headers);
    }

    public Flux<String> receive() {
        return kafkaTimeReceiver.receive()
                .map(record -> {
                    record.receiverOffset().acknowledge();
                    return record.value();
                })
                .doOnError(error -> log.error("Failed to consume messages from Kafka. Message: {}", error.getMessage()));
    }

    private Mono<Void> send(KafkaSender<String, String> kafkaSender, String senderName, String topic, String key, String message, Map<String, String> headers) {
        KafkaConfig.SenderConfig config = KafkaConfigUtil.getSenderConfig(kafkaConfig, senderName);

        List<Header> kafkaHeaders = headers.entrySet().stream()
                .map(entry -> new RecordHeader(entry.getKey(), entry.getValue().getBytes(StandardCharsets.UTF_8)))
                .collect(Collectors.toList());

        ProducerRecord<String, String> producerRecord = new ProducerRecord<>(topic, null, key, message, kafkaHeaders);
        SenderRecord<String, String, String> senderRecord = SenderRecord.create(producerRecord, null);

        return kafkaSender.send(Mono.just(senderRecord))
                .retryWhen(Retry.backoff(config.getSenderMaxAttempts(), config.getSenderMinBackOff())
                        .doBeforeRetry(retrySignal ->
                                log.warn("Error sending message to Kafka, retrying {}...", retrySignal.toString())))
                .doOnError(error ->
                        log.error("Error sending to Kafka. Message: ", error))
                .then();
    }

    @PreDestroy
    private void closeKafkaClients() {
        Optional.ofNullable(kafkaTimeSender).ifPresent(KafkaSender::close);
        Optional.ofNullable(kafkaFlowSender).ifPresent(KafkaSender::close);
    }
}


