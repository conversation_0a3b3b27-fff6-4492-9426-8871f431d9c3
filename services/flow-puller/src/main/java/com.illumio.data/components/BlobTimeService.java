package com.illumio.data.components;

import static com.illumio.data.util.Constants.KAFKA_BLOB_TIME_KEY;

import com.illumio.data.configuration.FlowPullerConfig;
import com.illumio.data.util.KafkaConfigUtil;
import java.time.Duration;
import java.time.Instant;
import java.util.Collections;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class BlobTimeService {

    private final KafkaService kafkaService;
    private final FlowPullerConfig flowPullerConfig;

    private final AtomicReference<Long> largestBlobTimeCutoff = new AtomicReference<>(null);

    public Mono<Long> getLatestBlobTimeCutoff() {
        return Mono.just(
                Optional.ofNullable(largestBlobTimeCutoff.get())
                        .orElse(Instant.now()
                                .minus(flowPullerConfig.getBlobSyncConfig().getInitialTimeSyncFallbackPeriodDuration())
                                .toEpochMilli())
        );
    }

    public Mono<Void> updateBlobTimeCutoff(final long blobTimeCutoff) {
        String timeSenderTopic = KafkaConfigUtil.getSenderTopic(flowPullerConfig.getKafkaConfig(), flowPullerConfig.getKafkaTimeSender());

        String message = String.valueOf(blobTimeCutoff);

        return kafkaService.sendWithTimeSender(timeSenderTopic, KAFKA_BLOB_TIME_KEY, message, Collections.emptyMap())
                .doOnSuccess(__ -> {
                    largestBlobTimeCutoff.updateAndGet(current ->
                            (current == null || current < blobTimeCutoff) ? blobTimeCutoff : current
                    );
                    log.info("Updated largestBlobTimeCutoff to {}", blobTimeCutoff);
                })
                .doOnError(error -> log.error("Failed to send blob time cutoff to Kafka. Not updating local variable.", error));
    }

    public Flux<Long> scanForLatestBlobTimeCutoff() {
        return kafkaService.receive()
                .take(flowPullerConfig.getBlobSyncConfig().getInitialTimeSyncKafkaPollDuration())
                .doOnNext(newBlobTimeCutoffStr -> {
                    log.info("Received Kafka message for blob time cutoff successfully with blobTimeCutoff = {}", newBlobTimeCutoffStr);

                    largestBlobTimeCutoff.updateAndGet(currentBlobTimeCutoff -> {
                        final long newBlobTimeCutoff = Long.parseLong(newBlobTimeCutoffStr);

                        if (currentBlobTimeCutoff == null || currentBlobTimeCutoff < newBlobTimeCutoff) {
                            log.info("Updating largest blobTimeCutoff = {}", newBlobTimeCutoff);
                            return newBlobTimeCutoff;
                        }
                        return currentBlobTimeCutoff;
                    });
                })
                .map(Long::parseLong)
                .retryWhen(reactor.util.retry.Retry.backoff(10, Duration.ofSeconds(5L))
                        .doBeforeRetry(retrySignal ->
                                log.warn("Error receiving blob time cutoff message from Kafka, retrying {}...", retrySignal.toString())))
                .doOnError(error -> log.error("Error receiving blob time cutoff message from Kafka", error));
    }
}
