package com.illumio.data;

import com.illumio.data.components.FlowPullerService;
import com.illumio.data.components.BlobTimeService;
import com.illumio.data.configuration.FlowPullerConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.scheduler.Schedulers;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

@Slf4j
@Component
@RequiredArgsConstructor
@EnableConfigurationProperties(FlowPullerConfig.class)
public class AppListener {

    private final FlowPullerService flowPullerService;
    private final BlobTimeService blobTimeService;
    private final FlowPullerConfig config;

    private final ScheduledExecutorService scheduler =
            Executors.newScheduledThreadPool(1);

    @EventListener(ApplicationReadyEvent.class)
    public void handleReadyEvent() {
        log.info("Starting flow puller schedule");
        blobTimeService.scanForLatestBlobTimeCutoff()
                .thenMany(Flux.interval(config.getBlobSyncConfig().getPeriodDuration())
                        .flatMap(__ ->
                                        flowPullerService.scanAndPublishModifiedFlows()
                                                .subscribeOn(Schedulers.boundedElastic())))
                .blockLast();

    }

    @EventListener(ContextClosedEvent.class)
    public void handleClosedEvent() {
        log.info("Stopping flow puller schedule");
        scheduler.shutdown();
    }

}
