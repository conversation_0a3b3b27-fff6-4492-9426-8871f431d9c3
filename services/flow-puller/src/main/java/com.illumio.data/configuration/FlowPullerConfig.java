package com.illumio.data.configuration;

import static com.illumio.data.util.Constants.FLOW_PULLER_CONFIG_PREFIX;

import com.illumio.data.config.BlobStorageConfig;
import com.illumio.data.config.KafkaConfig;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.Duration;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.validation.annotation.Validated;

@EnableConfigurationProperties({BlobStorageConfig.class, KafkaConfig.class})
@ConfigurationProperties(prefix = FLOW_PULLER_CONFIG_PREFIX)
@Validated
@Getter
@Setter
public class FlowPullerConfig {

    @Valid
    @NotNull(message = "Mandatory property 'storageAccount' is missing in FlowPullerConfig")
    private StorageAccountConfig storageAccount;

    @NotBlank(message = "Mandatory property 'kafkaFlowSender' is missing in FlowPullerConfig")
    private String kafkaFlowSender;

    @NotBlank(message = "Mandatory property 'kafkaTimeSender' is missing in FlowPullerConfig")
    private String kafkaTimeSender;

    @NotBlank(message = "Mandatory property 'kafkaTimeReceiver' is missing in FlowPullerConfig")
    private String kafkaTimeReceiver;

    private final BlobStorageConfig blobStorageConfig;
    private final KafkaConfig kafkaConfig;

    private BlobSyncConfig blobSyncConfig = new BlobSyncConfig();

    @Autowired
    public FlowPullerConfig(BlobStorageConfig blobStorageConfig, KafkaConfig kafkaConfig) {
        this.blobStorageConfig = blobStorageConfig;
        this.kafkaConfig = kafkaConfig;
    }

    @Getter
    @Setter
    public static class StorageAccountConfig {
        @NotBlank(message = "Mandatory property 'name' is missing in 'flow-puller.storage-account'")
        private String name;

        // Optional: only present if Vault is used
        private String vaultPath;
    }

    @Getter
    @Setter
    public static class BlobSyncConfig {
        private Duration periodDuration = Duration.ofMinutes(5);
        private Duration initialTimeSyncKafkaPollDuration = Duration.ofMinutes(2);
        private Duration initialTimeSyncFallbackPeriodDuration = Duration.ofHours(12);
    }
}