# Flow Puller Connector

The **Flow Puller** is a data connector that scans configured Storage Accounts for new or updated blobs, and publishes a Kafka message for each new/updated blob.

---

## Prerequisites

- Credentials for Azure Storage Accounts. (Please contact the development team to obtain credentials --> LastPass.)

---

## Running Locally

### 1. Modify `application-local-kafka.yaml`
Ensure the following configurations are set:

1. **Storage Accounts Config** – Configuration of the Storage Accounts to be scanned.
2. **Kafka Config** – Configuration of the Kafka sender.

### 2. Running Locally Using IntelliJ

To run the application in IntelliJ, use the following configuration:

- **Run Configuration**: `flow-puller/FlowPullerApplication+LocalKafka`

### 4. Running on Docker Locally

#### 1. Building the Docker Image

To build the Docker image using Gradle, use the following command:

```bash
gradlew jibDockerBuild
```

 This will build a new Docker image locally based on your project configuration.

### 2. Running the Docker Container

To run the Docker container locally, follow these steps:

1. Mount the `application.yml` file to the container using the `-v` option to ensure proper configuration.
2. Expose the necessary port (`8080`) to access the service using the `-p` option.

Example command:

```bash
docker run -d \
  -v C:/Users/<USER>/Documents/IdeaProjects/connector/services/flow-puller/src/main/resources/application.yml:/var/resources/application.yml \
  -p 8080:8081 illum.azurecr.io/flow-puller:1.0.0-c44c58b
 ```