apiVersion: v1
kind: Service
metadata:
  name: {{ include "FlowPuller.name" . }}
  labels:
    {{- include "FlowPuller.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "FlowPuller.selectorLabels" . | nindent 4 }}
