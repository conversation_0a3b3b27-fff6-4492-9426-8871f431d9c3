apiVersion: v1
kind: Secret
metadata:
  name: {{ include "FlowPuller.fullname" . }}-env-secrets
  labels:
    {{- include "FlowPuller.labels" . | nindent 4 }}
type: Opaque
stringData:
  VAULT_TOKEN: {{ .Values.vault.token }}
  FLOWPULLER_KAFKAFLOWSENDERCONFIG_SASLJAASCONFIG: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.flowPuller.kafkaFlowSenderConfig.connectionString }}";
  FLOWPULLER_KAFKATIMESENDERCONFIG_SASLJAASCONFIG: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.flowPuller.kafkaTimeSenderConfig.connectionString }}";
  FLOWPULLER_KAFKATIMERECEIVERCONFIG_SASLJAASCONFIG: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.flowPuller.kafkaTimeReceiverConfig.connectionString }}";