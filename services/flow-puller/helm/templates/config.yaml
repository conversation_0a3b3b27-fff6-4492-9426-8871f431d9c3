apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "FlowPuller.fullname" . }}-env-configmap
  labels:
    {{- include "FlowPuller.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
    
    spring:
      application:
        name: "flow-puller"
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: none
    
    vault:
      uri: "{{.Values.vault.uri}}"
      cache-time-mins: {{.Values.vault.cacheTimeMins}}
    
    flow-puller:
      source-storage-accounts-azure-config:
        {{- range $storageAccount, $storageAccountConfig := .Values.flowPuller.sourceStorageAccountsAzureConfig }}
        {{ $storageAccount }}:
          storage-account-endpoint: {{ $storageAccountConfig.storageAccountEndpoint }}
          container: {{ $storageAccountConfig.container }}
          auth-config:
            is-vault-config: {{ $storageAccountConfig.authConfig.isVaultConfig }}
            vault-path: {{ $storageAccountConfig.authConfig.vaultPath }}
        {{- end }}
    
      blob-sync-period-duration: {{.Values.flowPuller.blobSyncPeriodDuration}}
      initial-blob-time-sync-kafka-poll-duration: {{.Values.flowPuller.initialBlobTimeSyncKafkaPollDuration}}
      initial-blob-time-sync-fallback-period-duration: {{.Values.flowPuller.initialBlobTimeSyncFallbackPeriodDuration}}
    
      kafka-flow-sender-config:
        topic: "{{.Values.flowPuller.kafkaFlowSenderConfig.topic}}"
        bootstrapServers: "{{.Values.flowPuller.kafkaFlowSenderConfig.bootstrapServers}}"
        isConnectionString: "{{.Values.flowPuller.kafkaFlowSenderConfig.isConnectionString}}"
        isManagedIdentity: "{{.Values.flowPuller.kafkaFlowSenderConfig.isManagedIdentity}}"
        requestTimeoutMs: "{{.Values.flowPuller.kafkaFlowSenderConfig.requestTimeoutMs}}"
        deliveryTimeoutMs: "{{.Values.flowPuller.kafkaFlowSenderConfig.deliveryTimeoutMs}}"
        maxBlockMs: "{{.Values.flowPuller.kafkaFlowSenderConfig.maxBlockMs}}"
      kafka-time-sender-config:
        topic: "{{.Values.flowPuller.kafkaTimeSenderConfig.topic}}"
        bootstrapServers: "{{.Values.flowPuller.kafkaTimeSenderConfig.bootstrapServers}}"
        isConnectionString: "{{.Values.flowPuller.kafkaTimeSenderConfig.isConnectionString}}"
        isManagedIdentity: "{{.Values.flowPuller.kafkaTimeSenderConfig.isManagedIdentity}}"
        requestTimeoutMs: "{{.Values.flowPuller.kafkaTimeSenderConfig.requestTimeoutMs}}"
        deliveryTimeoutMs: "{{.Values.flowPuller.kafkaTimeSenderConfig.deliveryTimeoutMs}}"
        maxBlockMs: "{{.Values.flowPuller.kafkaTimeSenderConfig.maxBlockMs}}"
      kafka-time-receiver-config:
        topic: "{{.Values.flowPuller.kafkaTimeReceiverConfig.topic}}"
        groupId: "{{.Values.flowPuller.kafkaTimeReceiverConfig.groupId}}"
        bootstrapServers: "{{.Values.flowPuller.kafkaTimeReceiverConfig.bootstrapServers}}"
        isConnectionString: "{{.Values.flowPuller.kafkaTimeReceiverConfig.isConnectionString}}"
        isManagedIdentity: "{{.Values.flowPuller.kafkaTimeReceiverConfig.isManagedIdentity}}"
        autoOffsetReset: "{{.Values.flowPuller.kafkaTimeReceiverConfig.autoOffsetReset}}"
        requestTimeoutMs: {{.Values.flowPuller.kafkaTimeReceiverConfig.requestTimeoutMs}}
        maxPollRecords: {{.Values.flowPuller.kafkaTimeReceiverConfig.maxPollRecords}}
        maxPartitionFetchBytes: {{.Values.flowPuller.kafkaTimeReceiverConfig.maxPartitionFetchBytes}}