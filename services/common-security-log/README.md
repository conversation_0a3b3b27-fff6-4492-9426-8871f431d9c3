# Common Security Log (Sentinel) Source Connector
A source data connnector which receives data from an event hub, and sends data to another event hub.

# Running locally
1. Modify application.yaml
   1. Kafka Common Config
   2. Kafka Receiver Config
   3. Kafka Sender Config
2. Create src/main/resources/secrets.yml and fill in secrets:
```yaml
common-security-log:
  kafka-common-config:
    saslJaasConfig: _DO_NOT_COMMIT_
```
3. Use IntelliJ Run Configuration:
- common-security-log/CommonSecurityLogApplication