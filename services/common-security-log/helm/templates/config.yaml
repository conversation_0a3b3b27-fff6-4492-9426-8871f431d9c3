apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "CommonSecurityLog.fullname" . }}-env-configmap
  labels:
    {{- include "CommonSecurityLog.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
        org:
          apache:
            kafka: INFO
    spring:
      application:
        name: "common-security-log"
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: none
    
    common-security-log:
      kafka-common-config:
        bootstrapServers: "{{.Values.commonSecurityLog.kafkaCommonConfig.bootstrapServers}}"
        isSasl: true
      kafka-receiver-config:
        topic: "{{.Values.commonSecurityLog.kafkaReceiverConfig.topic}}"
        groupId: "{{.Values.commonSecurityLog.kafkaReceiverConfig.groupId}}"
        autoOffsetReset: "{{.Values.commonSecurityLog.kafkaReceiverConfig.autoOffsetReset}}"
        requestTimeoutMs: "{{.Values.commonSecurityLog.kafkaReceiverConfig.requestTimeoutMs}}"
        maxPollRecords: "{{.Values.commonSecurityLog.kafkaReceiverConfig.maxPollRecords}}"
        maxPartitionFetchBytes: "{{.Values.commonSecurityLog.kafkaReceiverConfig.maxPartitionFetchBytes}}"
      kafka-sender-config:
        flows-sink-topic: "{{.Values.commonSecurityLog.kafkaSenderConfig.flowsSinkTopic}}"
        requestTimeoutMs: "{{.Values.commonSecurityLog.kafkaSenderConfig.requestTimeoutMs}}"
        deliveryTimeoutMs: "{{.Values.commonSecurityLog.kafkaSenderConfig.deliveryTimeoutMs}}"
        maxBlockMs: "{{.Values.commonSecurityLog.kafkaSenderConfig.maxBlockMs}}"
      samplerConfig:
        isEnabled: "{{.Values.commonSecurityLog.samplerConfig.isEnabled}}"
        sampleFrequency: "{{.Values.commonSecurityLog.samplerConfig.sampleFrequency}}"
    
