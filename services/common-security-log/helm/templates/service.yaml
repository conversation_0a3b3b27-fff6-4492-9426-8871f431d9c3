apiVersion: v1
kind: Service
metadata:
  name: {{ include "CommonSecurityLog.name" . }}
  labels:
    {{- include "CommonSecurityLog.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "CommonSecurityLog.selectorLabels" . | nindent 4 }}
