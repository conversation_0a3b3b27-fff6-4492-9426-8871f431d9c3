spring:
  application:
    name: common-security-log
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

common-security-log:
  kafka-common-config:
    bootstrapServers: arch-eventhub.servicebus.windows.net:9093
    isSasl: false
    saslJaasConfig: _DO_NOT_COMMIT_

  kafka-receiver-config:
    topic: illumio-common-security-log
    groupId: connector-group
    autoOffsetReset: latest
    requestTimeoutMs: 180000

  kafka-sender-config:
    flows-sink-topic: illumio-commonsecurity-flow
