{"records": [{"Category": "Azure Monitor Agent", "Computer": "VMauto0022", "ComputerEnvironment": "Azure", "ComputerIP": "**********", "ComputerPrivateIPs": ["********"], "MG": "00000000-0000-0000-0000-000000000002", "OSMajorVersion": "11", "OSMinorVersion": "11", "OSName": "Debian GNU/Linux", "OSType": "Linux", "RemoteIPCountry": "United States", "RemoteIPLatitude": 47.23, "RemoteIPLongitude": -119.85, "Resource": "VM_auto0022", "ResourceGroup": "rg-auto0022-peer", "ResourceId": "/subscriptions/87332a70-7c1b-4437-aa3b-ec7c00d72de0/resourceGroups/rg-auto0022-peer/providers/Microsoft.Compute/virtualMachines/VM_auto0022", "ResourceProvider": "Microsoft.Compute", "ResourceType": "virtualMachines", "SCAgentChannel": "Direct", "SourceComputerId": "3d0040d1-b025-4347-a5ee-f38c7ac3088e", "SourceSystem": "OpsManager", "SubscriptionId": "87332a70-7c1b-4437-aa3b-ec7c00d72de0", "TimeGenerated": "2024-05-10T19:24:28.9754943Z", "TenantId": "6e2b8f8f-cbb1-468c-a06e-c9dff7839965", "Type": "Heartbeat", "Version": "1.30.3", "VMUUID": "3d0040d1-b025-4347-a5ee-f38c7ac3088c", "_ItemId": "eb22ad71-0f02-11ef-90ec-0022482b0d42", "_Internal_WorkspaceResourceId": "/subscriptions/87332a70-7c1b-4437-aa3b-ec7c00d72de0/resourcegroups/mc_container-dev_george-classic_westus2/providers/microsoft.operationalinsights/workspaces/loganalyticstest1", "_ResourceId": "/subscriptions/87332a70-7c1b-4437-aa3b-ec7c00d72de0/resourceGroups/rg-auto0022-peer/providers/Microsoft.Compute/virtualMachines/VM_auto0022"}]}