{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"workspaceName": {"defaultValue": "workspace-name", "type": "String"}, "workspaceLocation": {"defaultValue": "workspace-region", "type": "string"}, "storageAccountRuleName": {"defaultValue": "storage-account-rule-name", "type": "string"}, "storageAccountResourceId": {"defaultValue": "/subscriptions/********-0000-0000-0000-************/resourceGroups/resource-group-name/providers/Microsoft.Storage/storageAccounts/storage-account-name", "type": "String"}}, "variables": {}, "resources": [{"type": "microsoft.operationalinsights/workspaces", "apiVersion": "2020-08-01", "name": "[parameters('workspaceName')]", "location": "[parameters('workspaceLocation')]", "resources": [{"type": "microsoft.operationalinsights/workspaces/dataexports", "apiVersion": "2020-08-01", "name": "[concat(parameters('workspaceName'), '/' , parameters('storageAccountRuleName'))]", "dependsOn": ["[resourceId('microsoft.operationalinsights/workspaces', parameters('workspaceName'))]"], "properties": {"destination": {"resourceId": "[parameters('storageAccountResourceId')]"}, "tableNames": ["Heartbeat", "InsightsMetrics", "VMConnection", "Usage"], "enable": true}}]}]}