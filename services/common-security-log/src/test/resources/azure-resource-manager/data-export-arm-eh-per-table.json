{"$schema": "https://schema.management.azure.com/schemas/2015-01-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"workspaceName": {"defaultValue": "workspace-name", "type": "String"}, "workspaceLocation": {"defaultValue": "workspace-region", "type": "string"}, "eventhubRuleName": {"defaultValue": "event-hub-rule-name", "type": "string"}, "namespacesResourceId": {"defaultValue": "/subscriptions/00000000-0000-0000-0000-000000000000/resourceGroups/resource-group-name/providers/microsoft.eventhub/namespaces/namespaces-name", "type": "String"}}, "variables": {}, "resources": [{"type": "microsoft.operationalinsights/workspaces", "apiVersion": "2020-08-01", "name": "[parameters('workspaceName')]", "location": "[parameters('workspaceLocation')]", "resources": [{"type": "microsoft.operationalinsights/workspaces/dataexports", "apiVersion": "2020-08-01", "name": "[concat(parameters('workspaceName'), '/', parameters('eventhubRuleName'))]", "dependsOn": ["[resourceId('microsoft.operationalinsights/workspaces', parameters('workspaceName'))]"], "properties": {"destination": {"resourceId": "[parameters('namespacesResourceId')]"}, "tableNames": ["Usage", "Heartbeat"], "enable": true}}]}]}