package com.illumio.data;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.*;
import com.illumio.data.configuration.CommonSecurityLogConfig;
import lombok.SneakyThrows;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Answers;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOffset;
import reactor.kafka.receiver.ReceiverRecord;
import reactor.kafka.sender.KafkaSender;
import reactor.test.StepVerifier;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.time.Duration;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CommonSecurityLogPipelineTest {

    ObjectMapper objectMapper;

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    CommonSecurityLogConfig commonSecurityLogConfig;

    @Mock KafkaReceiver<String, String> kafkaReceiver;
    @Mock KafkaSender<String, String> kafkaSender;

    @Mock ZoneFilter zoneFilter;

    @Mock TrafficFilter trafficFilter;

    @Mock ProtocolFilter protocolFilter;

    @Mock IPFilter ipFilter;
    CommonSecurityLogReader commonSecurityLogReader;
    FlowTransformer flowTransformer;

    private CommonSecurityLogPipeline commonSecurityLogPipeline;

    @SneakyThrows
    public static String readFromStream(InputStream inputStream) {
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        for (int length; (length = inputStream.read(buffer)) != -1; ) {
            result.write(buffer, 0, length);
        }
        // StandardCharsets.UTF_8.name() > JDK 7
        return result.toString("UTF-8");
    }

    @BeforeEach
    void setup() {
        when(commonSecurityLogConfig.getKafkaSenderConfig().getFlowsSinkTopic())
                .thenReturn("flows");
        objectMapper = new ObjectMapper();
        commonSecurityLogReader = new CommonSecurityLogReader(objectMapper);
        flowTransformer = new FlowTransformer(objectMapper);
        commonSecurityLogPipeline =
                new CommonSecurityLogPipeline(
                        commonSecurityLogConfig,
                        kafkaReceiver,
                        kafkaSender,
                        commonSecurityLogReader,
                        flowTransformer,
                        trafficFilter,
                        zoneFilter,
                        protocolFilter,
                        ipFilter);
    }

    @Test
    void testBadRecord() {
        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", "value");
        ReceiverOffset receiverOffset = mock(ReceiverOffset.class);
        ReceiverRecord<String, String> receiverRecord =
                new ReceiverRecord<>(consumerRecord, receiverOffset);

        StepVerifier.create(
                        Flux.just(consumerRecord)
                                .flatMap(
                                        receiverRecord1 ->
                                                commonSecurityLogPipeline.processConsumerRecord(
                                                        receiverRecord1)))
                .expectSubscription()
                .thenAwait(Duration.ofSeconds(1))
                .expectNextCount(0)
                .verifyComplete();
    }

    @Test
    void testEmptyJson() {
        final String value =
                readFromStream(
                        Objects.requireNonNull(
                                this.getClass()
                                        .getClassLoader()
                                        .getResourceAsStream(
                                                "unit-test-examples/CommonSecurityLog-empty-json.json")));

        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);
        ReceiverOffset receiverOffset = mock(ReceiverOffset.class);
        ReceiverRecord<String, String> receiverRecord =
                new ReceiverRecord<>(consumerRecord, receiverOffset);

        StepVerifier.create(
                        Flux.just(consumerRecord)
                                .flatMap(
                                        receiverRecord1 ->
                                                commonSecurityLogPipeline.processConsumerRecord(
                                                        receiverRecord1)))
                .expectSubscription()
                .expectNextCount(0)
                .verifyComplete();
    }

    @Test
    void testNoRecords() {
        final String value =
                readFromStream(
                        Objects.requireNonNull(
                                this.getClass()
                                        .getClassLoader()
                                        .getResourceAsStream(
                                                "unit-test-examples/CommonSecurityLog-no-records.json")));

        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);
        ReceiverOffset receiverOffset = mock(ReceiverOffset.class);
        ReceiverRecord<String, String> receiverRecord =
                new ReceiverRecord<>(consumerRecord, receiverOffset);

        StepVerifier.create(
                        Flux.just(consumerRecord)
                                .flatMap(
                                        receiverRecord1 ->
                                                commonSecurityLogPipeline.processConsumerRecord(
                                                        receiverRecord1)))
                .expectSubscription()
                .expectNextCount(0)
                .verifyComplete();
    }

    @Test
    void testOneRecord() {
        when(zoneFilter.test(any())).thenReturn(true);
        when(trafficFilter.test(any())).thenReturn(true);
        when(protocolFilter.test(any())).thenReturn(true);
        when(ipFilter.test(any())).thenReturn(true);
        final String value =
                readFromStream(
                        Objects.requireNonNull(
                                this.getClass()
                                        .getClassLoader()
                                        .getResourceAsStream(
                                                "unit-test-examples/CommonSecurityLog-1-record.json")));

        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);
        ReceiverOffset receiverOffset = mock(ReceiverOffset.class);
        ReceiverRecord<String, String> receiverRecord =
                new ReceiverRecord<>(consumerRecord, receiverOffset);

        StepVerifier.create(
                        Flux.just(consumerRecord)
                                .flatMap(
                                        receiverRecord1 ->
                                                commonSecurityLogPipeline.processConsumerRecord(
                                                        receiverRecord1)))
                .expectSubscription()
                .expectNextCount(1)
                .verifyComplete();
    }

    @Test
    void testTwoRecords() {
        when(zoneFilter.test(any())).thenReturn(true);
        when(trafficFilter.test(any())).thenReturn(true);
        when(protocolFilter.test(any())).thenReturn(true);
        when(ipFilter.test(any())).thenReturn(true);
        final String value =
                readFromStream(
                        Objects.requireNonNull(
                                this.getClass()
                                        .getClassLoader()
                                        .getResourceAsStream(
                                                "unit-test-examples/CommonSecurityLog-2-records.json")));

        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);
        ReceiverOffset receiverOffset = mock(ReceiverOffset.class);
        ReceiverRecord<String, String> receiverRecord =
                new ReceiverRecord<>(consumerRecord, receiverOffset);

        StepVerifier.create(
                        Flux.just(consumerRecord)
                                .flatMap(
                                        receiverRecord1 ->
                                                commonSecurityLogPipeline.processConsumerRecord(
                                                        receiverRecord1)))
                .expectSubscription()
                .expectNextCount(2)
                .verifyComplete();
    }

    @Test
    void testTrafficFilter() {
        when(trafficFilter.test(any())).thenReturn(false);
        //        when(zoneFilter.test(any())).thenReturn(false);
        final String value =
                readFromStream(
                        Objects.requireNonNull(
                                this.getClass()
                                        .getClassLoader()
                                        .getResourceAsStream(
                                                "unit-test-examples/CommonSecurityLog-2-records.json")));

        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);
        ReceiverOffset receiverOffset = mock(ReceiverOffset.class);
        ReceiverRecord<String, String> receiverRecord =
                new ReceiverRecord<>(consumerRecord, receiverOffset);

        StepVerifier.create(
                        Flux.just(consumerRecord)
                                .flatMap(
                                        receiverRecord1 ->
                                                commonSecurityLogPipeline.processConsumerRecord(
                                                        receiverRecord1)))
                .expectSubscription()
                .expectNextCount(0)
                .verifyComplete();
    }

    @Test
    void testZoneFilter() {
        when(trafficFilter.test(any())).thenReturn(true);
        when(zoneFilter.test(any())).thenReturn(false);
        final String value =
                readFromStream(
                        Objects.requireNonNull(
                                this.getClass()
                                        .getClassLoader()
                                        .getResourceAsStream(
                                                "unit-test-examples/CommonSecurityLog-2-records.json")));

        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);
        ReceiverOffset receiverOffset = mock(ReceiverOffset.class);
        ReceiverRecord<String, String> receiverRecord =
                new ReceiverRecord<>(consumerRecord, receiverOffset);

        StepVerifier.create(
                        Flux.just(consumerRecord)
                                .flatMap(
                                        receiverRecord1 ->
                                                commonSecurityLogPipeline.processConsumerRecord(
                                                        receiverRecord1)))
                .expectSubscription()
                .expectNextCount(0)
                .verifyComplete();
    }

    @Test
    void testProtocolFilter() {
        when(trafficFilter.test(any())).thenReturn(true);
        when(zoneFilter.test(any())).thenReturn(true);
        when(protocolFilter.test(any())).thenReturn(false);
        final String value =
                readFromStream(
                        Objects.requireNonNull(
                                this.getClass()
                                        .getClassLoader()
                                        .getResourceAsStream(
                                                "unit-test-examples/CommonSecurityLog-2-records.json")));

        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);
        ReceiverOffset receiverOffset = mock(ReceiverOffset.class);
        ReceiverRecord<String, String> receiverRecord =
                new ReceiverRecord<>(consumerRecord, receiverOffset);

        StepVerifier.create(
                        Flux.just(consumerRecord)
                                .flatMap(
                                        receiverRecord1 ->
                                                commonSecurityLogPipeline.processConsumerRecord(
                                                        receiverRecord1)))
                .expectSubscription()
                .expectNextCount(0)
                .verifyComplete();
    }

    @Test
    void testIPFilter() {
        when(trafficFilter.test(any())).thenReturn(true);
        when(zoneFilter.test(any())).thenReturn(true);
        when(protocolFilter.test(any())).thenReturn(true);
        when(ipFilter.test(any())).thenReturn(false);
        final String value =
                readFromStream(
                        Objects.requireNonNull(
                                this.getClass()
                                        .getClassLoader()
                                        .getResourceAsStream(
                                                "unit-test-examples/CommonSecurityLog-2-records.json")));

        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);
        ReceiverOffset receiverOffset = mock(ReceiverOffset.class);
        ReceiverRecord<String, String> receiverRecord =
                new ReceiverRecord<>(consumerRecord, receiverOffset);

        StepVerifier.create(
                        Flux.just(consumerRecord)
                                .flatMap(
                                        receiverRecord1 ->
                                                commonSecurityLogPipeline.processConsumerRecord(
                                                        receiverRecord1)))
                .expectSubscription()
                .expectNextCount(0)
                .verifyComplete();
    }
}
