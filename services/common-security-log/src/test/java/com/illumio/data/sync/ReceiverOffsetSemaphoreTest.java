package com.illumio.data.sync;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import reactor.kafka.receiver.ReceiverOffset;

import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@Slf4j
class ReceiverOffsetSemaphoreTest {
    @Test
    void testWithZero() {
        ReceiverOffset offset = mock(ReceiverOffset.class);
        ReceiverOffsetSemaphore receiverOffsetSemaphore = ReceiverOffsetSemaphore.builder()
                .semaphore(new AtomicInteger(0))
                .receiverOffset(offset)
                .build();

        assertTrue(receiverOffsetSemaphore.maybeAck());
        verify(offset, times(1)).acknowledge();
    }

    @ParameterizedTest
    @ValueSource(ints = {1, 2, 3, 4, 5, 10, 15, 20})
    void testWithPositiveNumbers(int i) {
        ReceiverOffset offset = mock(ReceiverOffset.class);
        ReceiverOffsetSemaphore receiverOffsetSemaphore = ReceiverOffsetSemaphore.builder()
                .semaphore(new AtomicInteger(i))
                .receiverOffset(offset)
                .build();

        for (int j = i; j > 1; j--) {
            assertFalse(receiverOffsetSemaphore.maybeAck());
        }
        assertTrue(receiverOffsetSemaphore.maybeAck());

        verify(offset, times(1)).acknowledge();
    }

    @ParameterizedTest
    @ValueSource(ints = {-1, -2, -3, -4, -5, -10, -15, -20})
    void testWithNegativeNumbers(int i) {
        ReceiverOffset offset = mock(ReceiverOffset.class);
        ReceiverOffsetSemaphore receiverOffsetSemaphore = ReceiverOffsetSemaphore.builder()
                .semaphore(new AtomicInteger(i))
                .receiverOffset(offset)
                .build();

        assertTrue(receiverOffsetSemaphore.maybeAck());
        verify(offset, times(1)).acknowledge();
    }
}