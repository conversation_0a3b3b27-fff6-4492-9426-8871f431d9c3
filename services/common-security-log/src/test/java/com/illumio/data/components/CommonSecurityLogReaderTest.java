package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.CommonSecurityLogRecords;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;

import static org.junit.jupiter.api.Assertions.*;

class CommonSecurityLogReaderTest {
    ObjectMapper objectMapper = new ObjectMapper();
    @Test
    void parseEmptyJson() {
        String json = "{}";
        CommonSecurityLogReader commonSecurityLogReader = new CommonSecurityLogReader(objectMapper);
        Mono<CommonSecurityLogRecords> result = commonSecurityLogReader.readValue(json);
        CommonSecurityLogRecords commonSecurityLogRecords = result.block();
        assertNotNull(commonSecurityLogRecords);
        assertNotNull(commonSecurityLogRecords.getRecords());
        assertEquals(0, commonSecurityLogRecords.getRecords().size());
    }
}
