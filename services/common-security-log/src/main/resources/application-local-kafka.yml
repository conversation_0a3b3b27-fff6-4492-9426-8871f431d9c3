logging:
  level:
    ROOT: INFO
    org:
      apache:
        kafka: INFO
spring:
  application:
    name: common-security-log
  output:
    ansi:
      enabled: ALWAYS
#  main:
#    web-application-type: none
server:
  port : 8089

common-security-log:
  kafka-common-config:
    bootstrapServers: localhost:9092
    isSasl: false

  kafka-receiver-config:
    topic: illumio-common-security-log
    groupId: connector-group
    autoOffsetReset: earliest
    requestTimeoutMs: 300000
    maxPollRecords: 100
    maxPartitionFetchBytes: "1048576" # 1 MB

  kafka-sender-config:
    flows-sink-topic: illumio-commonsecurity-flow
    requestTimeoutMs: 300000
    deliveryTimeoutMs: 300000
#    Non-Defaults below
#    lingerMs: 1500
#    batchSize: 300000
#    bufferMemory: "33554432" # 32 MB

  samplerConfig:
    isEnabled: false
    sampleFrequency: 1s