package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.CommonSecurityLog;
import com.illumio.data.pojo.Flow;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@Slf4j
@RequiredArgsConstructor
public class FlowTransformer {
    private final ObjectMapper objectMapper;
    public Mono<Flow> transform(CommonSecurityLog commonSecurityLog) {
        return Mono.fromCallable(
                () -> {
                    Flow flow =
                            Flow.builder()
                                    .SourceHostName(commonSecurityLog.getSourceHostName())
                                    .SourceIP(commonSecurityLog.getSourceIP())
                                    .SourceMACAddress(commonSecurityLog.getSourceMACAddress())
                                    .SourceNTDomain(commonSecurityLog.getSourceNTDomain())
                                    .SourceProcessId(commonSecurityLog.getSourceProcessId())
                                    .SourceProcessName(commonSecurityLog.getSourceProcessName())
                                    .SourceUserID(commonSecurityLog.getSourceUserID())
                                    .SourceUserName(commonSecurityLog.getSourceUserName())
                                    .SourceUserPrivileges(
                                            commonSecurityLog.getSourceUserPrivileges())
                                    .SourcePort(commonSecurityLog.getSourcePort())
                                    .DeviceAction(commonSecurityLog.getDeviceAction())
                                    .DeviceAddress(commonSecurityLog.getDeviceAddress())
                                    .DestinationDnsDomain(
                                            commonSecurityLog.getDestinationDnsDomain())
                                    .DestinationHostName(commonSecurityLog.getDestinationHostName())
                                    .DestinationIP(commonSecurityLog.getDestinationIP())
                                    .DestinationMACAddress(
                                            commonSecurityLog.getDestinationMACAddress())
                                    .DestinationNTDomain(commonSecurityLog.getDestinationNTDomain())
                                    .DestinationPort(commonSecurityLog.getDestinationPort())
                                    .DestinationProcessId(
                                            commonSecurityLog.getDestinationProcessId())
                                    .DestinationProcessName(
                                            commonSecurityLog.getDestinationProcessName())
                                    .DestinationServiceName(
                                            commonSecurityLog.getDestinationServiceName())
                                    .DestinationTranslatedAddress(
                                            commonSecurityLog.getDestinationTranslatedAddress())
                                    .DestinationUserID(commonSecurityLog.getDestinationUserID())
                                    .DestinationUserName(commonSecurityLog.getDestinationUserName())
                                    .Protocol(commonSecurityLog.getProtocol())
                                    .LogSeverity(commonSecurityLog.getLogSeverity())
                                    .MaliciousIP(commonSecurityLog.getMaliciousIP())
                                    .MaliciousIPCountry(commonSecurityLog.getMaliciousIPCountry())
                                    .MaliciousIPLatitude(commonSecurityLog.getMaliciousIPLatitude())
                                    .MaliciousIPLongitude(
                                            commonSecurityLog.getMaliciousIPLongitude())
                                    .SentBytes(commonSecurityLog.getSentBytes())
                                    .ReceivedBytes(commonSecurityLog.getReceivedBytes())
                                    .TenantId(commonSecurityLog.getTenantId())
                                    .ThreatConfidence(commonSecurityLog.getThreatConfidence())
                                    .ThreatDescription(commonSecurityLog.getThreatDescription())
                                    .ThreatSeverity(commonSecurityLog.getThreatSeverity())
                                    .StartTime(commonSecurityLog.getStartTime())
                                    .EndTime(commonSecurityLog.getEndTime())
                                    .SourceDnsDomain(commonSecurityLog.getSourceDnsDomain())
                                    .SourceServiceName(commonSecurityLog.getSourceServiceName())
                                    .SourceSystem(commonSecurityLog.getSourceSystem())
                                    .DeviceMacAddress(commonSecurityLog.getDeviceMacAddress())
                                    .DeviceName(commonSecurityLog.getDeviceName())
                                    .DeviceOutboundInterface(
                                            commonSecurityLog.getDeviceOutboundInterface())
                                    .DeviceProduct(commonSecurityLog.getDeviceProduct())
                                    .DeviceTranslatedAddress(
                                            commonSecurityLog.getDeviceTranslatedAddress())
                                    .DeviceVersion(commonSecurityLog.getDeviceVersion())
                                    .DeviceTimeZone(commonSecurityLog.getDeviceTimeZone())
                                    .DeviceExternalID(commonSecurityLog.getDeviceExternalId())
                                    .DeviceCustomNumber3(commonSecurityLog.getDeviceCustomNumber3())
                                    .ReceiptTime(commonSecurityLog.getReceiptTime())
                                    .TimeGenerated(commonSecurityLog.getTimeGenerated())
                                    .Activity(commonSecurityLog.getActivity())
                                    .AdditionalExtensions(
                                            commonSecurityLog.getAdditionalExtensions())
                                    .SourceZone(commonSecurityLog.getDeviceCustomString4())
                                    .DestinationZone(commonSecurityLog.getDeviceCustomString5())
                                    .RequestURL(commonSecurityLog.getRequestURL())
                                    .Computer(commonSecurityLog.getComputer())
                                    .build();
                    return flow;
                });
    }

    @SneakyThrows
    public String asValue(Flow flow) {
        return objectMapper.writeValueAsString(flow);
    }

    public String asKey(Flow flow) {
        String combination = flow.getSourceIP() +
                "_" +
                flow.getDestinationIP();
        int hashCodeInt = combination.hashCode();
        String hashCode = String.valueOf(hashCodeInt);
        return hashCode;
    }
}
