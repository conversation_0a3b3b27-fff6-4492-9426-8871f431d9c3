package com.illumio.data;

import com.illumio.data.components.*;
import com.illumio.data.configuration.CommonSecurityLogConfig;
import com.illumio.data.pojo.Flow;

import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.stereotype.Component;

import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.kafka.sender.SenderResult;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Collections;
import java.util.NoSuchElementException;
import java.util.Optional;

@Slf4j
@Component
@Setter
public class CommonSecurityLogPipeline {

    private final CommonSecurityLogConfig commonSecurityLogConfig;
    private final KafkaReceiver<String, String> kafkaReceiver;
    private final KafkaSender<String, String> kafkaSender;
    private final CommonSecurityLogReader commonSecurityLogReader;
    private final FlowTransformer flowTransformer;
    private final TrafficFilter trafficFilter;
    private final ZoneFilter zoneFilter;
    private final String flowsSinkTopic;
    private final ProtocolFilter protocolFilter;
    private final IPFilter ipFilter;

    private Disposable disposable;

    public CommonSecurityLogPipeline(
            CommonSecurityLogConfig commonSecurityLogConfig,
            KafkaReceiver<String, String> kafkaReceiver,
            KafkaSender<String, String> kafkaSender,
            CommonSecurityLogReader commonSecurityLogReader,
            FlowTransformer flowTransformer,
            TrafficFilter trafficFilter,
            ZoneFilter zoneFilter,
            ProtocolFilter protocolFilter,
            IPFilter ipFilter) {
        this.commonSecurityLogConfig = commonSecurityLogConfig;
        this.kafkaReceiver = kafkaReceiver;
        this.kafkaSender = kafkaSender;
        this.commonSecurityLogReader = commonSecurityLogReader;
        this.flowTransformer = flowTransformer;
        this.trafficFilter = trafficFilter;
        this.zoneFilter = zoneFilter;
        this.flowsSinkTopic = getFlowsSinkTopic(commonSecurityLogConfig);
        this.protocolFilter = protocolFilter;
        this.ipFilter = ipFilter;
    }

    /**
     * Fail early if sink topic is not defined.
     *
     * @param commonSecurityLogConfig
     * @return
     */
    private String getFlowsSinkTopic(CommonSecurityLogConfig commonSecurityLogConfig) {
        return Optional.of(commonSecurityLogConfig)
                .map(CommonSecurityLogConfig::getKafkaSenderConfig)
                .map(CommonSecurityLogConfig.KafkaSenderConfig::getFlowsSinkTopic)
                .orElseThrow(
                        () ->
                                new NoSuchElementException(
                                        "No flows topic configured. Please make sure common-security-log.kafka-sender-config.flows-sink-topic is set."));
    }

    public void start() {
        if (Boolean.TRUE.equals(commonSecurityLogConfig.getSamplerConfig().getIsEnabled())) {
            log.info(
                    "Starting with sampling and duration is {}",
                    commonSecurityLogConfig.getSamplerConfig().getSampleFrequency());
            this.disposable = startSampling().subscribe();
        } else {
            log.info("Starting with no sampling");
            this.disposable = startInternal().subscribe();
        }
    }

    public Flux<SenderResult<String>> startSampling() {
        return kafkaSender
                .send(
                        kafkaReceiver
                                .receiveAutoAck()
                                .publishOn(Schedulers.boundedElastic())
                                .retryWhen(
                                        Retry.backoff(10, Duration.ofSeconds(5L))
                                                .doBeforeRetry(
                                                        retrySignal ->
                                                                log.warn(
                                                                        "Error receiving from kafka {}",
                                                                        retrySignal.toString())))
                                .concatMap(r -> r)
                                .sample(
                                        commonSecurityLogConfig
                                                .getSamplerConfig()
                                                .getSampleFrequency())
                                .flatMap(this::processConsumerRecord))
                .doOnNext(
                        stringSenderResult ->
                                log.info(
                                        "Produced message for consumed data {} to {}",
                                        stringSenderResult.correlationMetadata(),
                                        stringSenderResult.recordMetadata()))
                .retryWhen(
                        Retry.backoff(10, Duration.ofSeconds(5L))
                                .doBeforeRetry(
                                        retrySignal ->
                                                log.warn(
                                                        "Error sending to kafka {}",
                                                        retrySignal.toString())));
    }

    public Flux<SenderResult<String>> startInternal() {
        return kafkaSender
                .send(
                        kafkaReceiver
                                .receiveAutoAck()
                                .publishOn(Schedulers.boundedElastic())
                                .retryWhen(Retry.backoff(5, Duration.ofSeconds(3L)))
                                .concatMap(r -> r)
                                .flatMap(this::processConsumerRecord))
                .retryWhen(Retry.backoff(5, Duration.ofSeconds(3L)));
    }

    public void stop() {
        this.disposable.dispose();
    }

    public Flux<SenderRecord<String, String, String>> processConsumerRecord(
            ConsumerRecord<String, String> consumerRecord) {
        return Mono.just(consumerRecord)
                .doOnNext(__ -> log.info("Processing record {}", consumerRecord))
                .publishOn(Schedulers.parallel())
                .flatMap(
                        __ ->
                                commonSecurityLogReader
                                        .readValue(consumerRecord.value())
                                        .publishOn(Schedulers.parallel())
                                        .doOnError(
                                                throwable ->
                                                        log.error(
                                                                "Dropping and acknowledging record: {}",
                                                                consumerRecordString(
                                                                        consumerRecord),
                                                                throwable))
                                        .onErrorReturn(
                                                CommonSecurityLogRecords.builder()
                                                        .records(Collections.EMPTY_LIST)
                                                        .build()))
                .doOnNext(
                        commonSecurityLogRecords ->
                                log.info(
                                        "Processing batch of {} records from {}",
                                        commonSecurityLogRecords.getRecords().size(),
                                        consumerRecordString(consumerRecord)))
                .filter(commonSecurityLogRecords -> null != commonSecurityLogRecords.getRecords())
                .flatMapMany(
                        commonSecurityLogRecords ->
                                Flux.fromIterable(commonSecurityLogRecords.getRecords()))
                .flatMap(
                        commonSecurityLog ->
                                Mono.just(commonSecurityLog)
                                        .filter(
                                                currentCSL -> {
                                                    boolean result = trafficFilter.test(currentCSL);
                                                    if (!result) {
                                                        log.debug(
                                                                "Traffic filter filtered out a record from the batch. {}",
                                                                consumerRecordString(
                                                                        consumerRecord));
                                                    }
                                                    return result;
                                                })
                                        .filter(
                                                currentCSL -> {
                                                    boolean result = zoneFilter.test(currentCSL);
                                                    if (!result) {
                                                        log.debug(
                                                                "Zone filter filtered out a record from the batch. {}",
                                                                consumerRecordString(
                                                                        consumerRecord));
                                                    }
                                                    return result;
                                                })
                                        .filter(
                                                currentCSL -> {
                                                    boolean result =
                                                            protocolFilter.test(currentCSL);
                                                    if (!result) {
                                                        log.debug(
                                                                "Protocol filter filtered out a record from the batch: {}",
                                                                consumerRecordString(
                                                                        consumerRecord));
                                                    }
                                                    return result;
                                                })
                                        .filter(
                                                currentCSL -> {
                                                    boolean result =
                                                            ipFilter.test(currentCSL);
                                                    if (!result) {
                                                        log.debug(
                                                                "IP filter filtered out a record from the batch: {}",
                                                                consumerRecordString(
                                                                        consumerRecord));
                                                    }
                                                    return result;
                                                })
                                        .flatMap(flowTransformer::transform)
                                        .flatMap(flow -> createSenderRecord(flow, consumerRecord))
                                        .doOnNext(
                                                senderRecord ->
                                                        log.debug(
                                                                "Sending record: {}",
                                                                senderRecord)));
    }

    private Mono<SenderRecord<String, String, String>> createSenderRecord(
            Flow flow, ConsumerRecord<String, String> consumerRecord) {
        return Mono.just(
                SenderRecord.create(
                        new ProducerRecord<>(
                                flowsSinkTopic,
                                flowTransformer.asKey(flow),
                                flowTransformer.asValue(flow)),
                        consumerRecordString(consumerRecord)));
    }

    private String consumerRecordString(ConsumerRecord<String, String> consumerRecord) {
        return consumerRecord.topic()
                + "-"
                + consumerRecord.partition()
                + "@"
                + consumerRecord.offset();
    }
}
