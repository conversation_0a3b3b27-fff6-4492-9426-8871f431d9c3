package com.illumio.data.components;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.CommonSecurityLogRecords;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.concurrent.Callable;

@Component
@RequiredArgsConstructor
@Slf4j
public class CommonSecurityLogReader {
    private final ObjectMapper objectMapper;

    /**
     * Returns a parsed @{@link CommonSecurityLogRecords} object.
     *
     * @param value String value
     * @return parsed object
     */
    public Mono<CommonSecurityLogRecords> readValue(String value) {
        return Mono.fromCallable(
                new Callable<CommonSecurityLogRecords>() {
                    @Override
                    @SneakyThrows
                    public CommonSecurityLogRecords call() throws Exception {
                        CommonSecurityLogRecords commonSecurityLogRecords =
                                objectMapper.readValue(value, CommonSecurityLogRecords.class);
                        if (null == commonSecurityLogRecords.getRecords()) {
                            commonSecurityLogRecords.setRecords(Collections.emptyList());
                        }
                        return commonSecurityLogRecords;
                    }
                });
    }
}
