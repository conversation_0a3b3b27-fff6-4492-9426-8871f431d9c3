package com.illumio.data.components;

import com.illumio.data.CommonSecurityLog;
import org.springframework.stereotype.Component;

import java.util.function.Predicate;

/**
 * Filters out CommonSecurityLogs with the equivalent KQL: | where Protocol has 'tcp' or Protocol
 * has 'udp' or Protocol has 'icmp' or Protocol has 'ipv6_icmp'
 */
@Component
public class ProtocolFilter implements Predicate<CommonSecurityLog> {

    private static final String TCP = "tcp";
    private static final String UDP = "udp";
    private static final String ICMP = "icmp";
    private static final String IPV6_ICMP = "ipv6_icmp";

    @Override
    public boolean test(CommonSecurityLog commonSecurityLog) {
        final String protocol = commonSecurityLog.getProtocol().toLowerCase();
        return protocol.contains(TCP)
                || protocol.contains(UDP)
                || protocol.contains(ICMP)
                || protocol.contains(IPV6_ICMP);
    }
}
