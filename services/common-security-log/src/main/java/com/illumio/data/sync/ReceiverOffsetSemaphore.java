package com.illumio.data.sync;

import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import reactor.kafka.receiver.ReceiverOffset;

import java.util.concurrent.atomic.AtomicInteger;

@Builder
@Slf4j
public record ReceiverOffsetSemaphore(ReceiverOffset receiverOffset, AtomicInteger semaphore) {
    public boolean maybeAck() {
        if (semaphore.decrementAndGet() <= 0) {
            receiverOffset.acknowledge();
            return true;
        }
        return false;
    }
}
