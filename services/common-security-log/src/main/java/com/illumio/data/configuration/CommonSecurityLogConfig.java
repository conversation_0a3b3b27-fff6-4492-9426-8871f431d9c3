package com.illumio.data.configuration;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
@ConfigurationProperties(prefix = "common-security-log")
@Getter
@Setter
public class CommonSecurityLogConfig {
    private final KafkaCommonConfig kafkaCommonConfig = new KafkaCommonConfig();
    private final KafkaReceiverConfig kafkaReceiverConfig = new KafkaReceiverConfig();
    private final KafkaSenderConfig kafkaSenderConfig = new KafkaSenderConfig();
    private final SamplerConfig samplerConfig = new SamplerConfig();

    @Getter
    @Setter
    public static class KafkaCommonConfig {
        private String bootstrapServers;
        private Boolean isSasl = false;
        private String saslJaasConfig;
    }

    @Getter
    @Setter
    public static class KafkaReceiverConfig {
        private String topic;
        private String groupId;
        private String autoOffsetReset;
        private Integer requestTimeoutMs;
        private Integer maxPollRecords;
        private Integer maxPartitionFetchBytes;
    }

    @Getter
    @Setter
    public static class KafkaSenderConfig {
        private String flowsSinkTopic;
        private Integer requestTimeoutMs;
        private Integer deliveryTimeoutMs;
        private Integer lingerMs;
        private Integer batchSize;
        private Integer bufferMemory;
        private Integer maxBlockMs;
    }

    @Configuration
    @Getter
    @Setter
    public static class SamplerConfig {
        private Boolean isEnabled = false;
        private Duration sampleFrequency = Duration.ofSeconds(30);
    }
}
