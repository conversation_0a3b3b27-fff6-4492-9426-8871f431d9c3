package com.illumio.data.configuration;

import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;

import java.util.HashMap;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class KafkaSenderConfiguration {
    private final CommonSecurityLogConfig config;

    @Bean
    public KafkaSender<String, String> kafkaSender() {
        Map<String, Object> producerProps = new HashMap<>();
        producerProps.put(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
                config.getKafkaCommonConfig().getBootstrapServers());
        producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        if (null != config.getKafkaCommonConfig().getIsSasl()
                && config.getKafkaCommonConfig().getIsSasl()) {
            producerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            producerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            producerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    config.getKafkaCommonConfig().getSaslJaasConfig());
        }
        producerProps.put(
                CommonClientConfigs.REQUEST_TIMEOUT_MS_CONFIG,
                config.getKafkaSenderConfig().getRequestTimeoutMs());
        producerProps.put(
                ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG,
                config.getKafkaSenderConfig().getDeliveryTimeoutMs());
        if (null != config.getKafkaSenderConfig().getLingerMs()) {
            producerProps.put(
                    ProducerConfig.LINGER_MS_CONFIG,
                    config.getKafkaSenderConfig().getLingerMs());
        }
        if (null != config.getKafkaSenderConfig().getBatchSize()) {
            producerProps.put(
                    ProducerConfig.BATCH_SIZE_CONFIG,
                    config.getKafkaSenderConfig().getBatchSize());
        }
        if (null != config.getKafkaSenderConfig().getBufferMemory()) {
            producerProps.put(
                    ProducerConfig.BUFFER_MEMORY_CONFIG,
                    config.getKafkaSenderConfig().getBufferMemory());
        }
        if (null != config.getKafkaSenderConfig().getMaxBlockMs()) {
            producerProps.put(
                    ProducerConfig.MAX_BLOCK_MS_CONFIG,
                    config.getKafkaSenderConfig().getMaxBlockMs());
        }

        SenderOptions<String, String> senderOptions =
                SenderOptions.<String, String>create(producerProps)
                        // Non-blocking back-pressure
                        .maxInFlight(1024);

        return KafkaSender.create(senderOptions);
    }
}
