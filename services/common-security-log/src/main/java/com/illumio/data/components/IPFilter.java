package com.illumio.data.components;

import com.illumio.data.CommonSecurityLog;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.function.Predicate;

/**
 * Filters out specific IPs in {@link CommonSecurityLog}'s with a specific source IP and destination
 * IP. See IP_BLACKLIST for IPs filtered out.
 */
@Component
public class IPFilter implements Predicate<CommonSecurityLog> {

    private static final Set<String> IP_BLACKLIST =
            Set.of(
                    "**************" // Scanner IP that is causing an issue
                    );

    /**
     * Tests if the CommonSecurityLog has an IP to filter out. If the test passes, the
     * CommonSecurityLog is not filtered out, and replay continues. If the test fails, the
     * CommonSecurityLog is dropped, and the Mono in the caller is completed.
     *
     * @param commonSecurityLog the input argument
     * @return true if the SourceIP or DestinationIP are not on the blacklist. false if the SourceIP
     *     or DestinationIP are on the blacklist
     */
    @Override
    public boolean test(CommonSecurityLog commonSecurityLog) {
        return !IP_BLACKLIST.contains(commonSecurityLog.getSourceIP())
                && !IP_BLACKLIST.contains(commonSecurityLog.getDestinationIP());
    }
}
