package com.illumio.data.components;

import com.illumio.data.CommonSecurityLog;
import org.springframework.stereotype.Component;

import java.util.function.Predicate;

/**
 * Filters out CommonSecurityLogs that are not Activity TRAFFIC.
 * Equivalent KQL filter: | where Activity == 'TRAFFIC'
 * For example, Activity == THREAT would be filtered out.
 */
@Component
public class TrafficFilter implements Predicate<CommonSecurityLog> {
    public static final String TRAFFIC = "TRAFFIC";

    @Override
    public boolean test(CommonSecurityLog commonSecurityLog) {
        return commonSecurityLog.getActivity().equalsIgnoreCase(TRAFFIC);
    }
}
