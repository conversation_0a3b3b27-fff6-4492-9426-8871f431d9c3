package com.illumio.data.pojo;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class Flow {
    @JsonProperty("SourceHostName")
    private String SourceHostName;
    @JsonProperty("SourceIP")
    private String SourceIP;
    @JsonProperty("SourceMACAddress")
    private String SourceMACAddress;
    @JsonProperty("SourceNTDomain")
    private String SourceNTDomain;
    @JsonProperty("SourceProcessId")
    private Integer SourceProcessId;
    @JsonProperty("SourceProcessName")
    private String SourceProcessName;
    @JsonProperty("SourceUserID")
    private String SourceUserID;
    @JsonProperty("SourceUserName")
    private String SourceUserName;
    @JsonProperty("SourceUserPrivileges")
    private String SourceUserPrivileges;
    @JsonProperty("SourcePort")
    private Integer SourcePort;
    @JsonProperty("DeviceAction")
    private String DeviceAction;
    @JsonProperty("DeviceAddress")
    private String DeviceAddress;
    @JsonProperty("DestinationDnsDomain")
    private String DestinationDnsDomain;
    @JsonProperty("DestinationHostName")
    private String DestinationHostName;
    @JsonProperty("DestinationIP")
    private String DestinationIP;
    @JsonProperty("DestinationMACAddress")
    private String DestinationMACAddress;
    @JsonProperty("DestinationNTDomain")
    private String DestinationNTDomain;
    @JsonProperty("DestinationPort")
    private Integer DestinationPort;
    @JsonProperty("DestinationProcessId")
    private Integer DestinationProcessId;
    @JsonProperty("DestinationProcessName")
    private String DestinationProcessName;
    @JsonProperty("DestinationServiceName")
    private String DestinationServiceName;
    @JsonProperty("DestinationTranslatedAddress")
    private String DestinationTranslatedAddress;
    @JsonProperty("DestinationUserID")
    private String DestinationUserID;
    @JsonProperty("DestinationUserName")
    private String DestinationUserName;
    @JsonProperty("Protocol")
    private String Protocol;
    @JsonProperty("LogSeverity")
    private String LogSeverity;
    @JsonProperty("MaliciousIP")
    private String MaliciousIP;
    @JsonProperty("MaliciousIPCountry")
    private String MaliciousIPCountry;
    @JsonProperty("MaliciousIPLatitude")
    private Double MaliciousIPLatitude;
    @JsonProperty("MaliciousIPLongitude")
    private Double MaliciousIPLongitude;
    @JsonProperty("SentBytes")
    private Long SentBytes;
    @JsonProperty("ReceivedBytes")
    private Long ReceivedBytes;
    // Read as JsonAlias value. Written as JsonProperty value.
    @JsonAlias("TenantId")
    @JsonProperty("LAWTenantId")
    private String TenantId;
    @JsonProperty("ThreatConfidence")
    private String ThreatConfidence;
    @JsonProperty("ThreatDescription")
    private String ThreatDescription;
    @JsonProperty("ThreatSeverity")
    private Integer ThreatSeverity;
    @JsonProperty("StartTime")
    private String StartTime;
    @JsonProperty("EndTime")
    private String EndTime;
    @JsonProperty("SourceDnsDomain")
    private String SourceDnsDomain;
    @JsonProperty("SourceServiceName")
    private String SourceServiceName;
    @JsonProperty("SourceSystem")
    private String SourceSystem;
    @JsonProperty("DeviceMacAddress")
    private String DeviceMacAddress;
    @JsonProperty("DeviceName")
    private String DeviceName;
    @JsonProperty("DeviceOutboundInterface")
    private String DeviceOutboundInterface;
    @JsonProperty("DeviceProduct")
    private String DeviceProduct;
    @JsonProperty("DeviceTranslatedAddress")
    private String DeviceTranslatedAddress;
    @JsonProperty("DeviceVersion")
    private String DeviceVersion;
    @JsonProperty("DeviceTimeZone")
    private String DeviceTimeZone;
    @JsonProperty("DeviceExternalID")
    private String DeviceExternalID;
    @JsonProperty("DeviceCustomNumber3")
    private Integer DeviceCustomNumber3;
    @JsonProperty("ReceiptTime")
    private String ReceiptTime;
    @JsonProperty("TimeGenerated")
    private String TimeGenerated;
    @JsonProperty("Activity")
    private String Activity;
    @JsonProperty("AdditionalExtensions")
    private String AdditionalExtensions;
    @JsonProperty("SourceZone")
    private String SourceZone;
    @JsonProperty("DestinationZone")
    private String DestinationZone;
    @JsonProperty("RequestURL")
    private String RequestURL;
    @JsonProperty("Computer")
    private String Computer;
}
