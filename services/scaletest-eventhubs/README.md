# Scale Test Event Hubs
A kafka producer that creates load on Azure's Event Hubs (Managed Kafka).

## Running locally
1. Fill out application.yml with the required parameters
   1. topic. The topic name to produces messages to.
   2. connectionString. The connection string of your Event Hub. Should start with "Endpoint=" and end with '='
   3. folderWithTestFiles. A folder containing test files. The application picks these up, generates batches of 2 MB using the test files, and sends it to Kafka.
2. Create src/main/resources/secrets.yml and fill in secrets
```yaml
loadtest:
   connectionString: Endpoint=...
```
3. Use one of the IntelliJ Run Configurations:
   - .run/ScaletestEventHubsApplication - CommonSecurityLog.run.xml
   - .run/ScaletestEventHubsApplication - Decorated Flows.run.xml