apiVersion: v1
kind: Service
metadata:
  name: {{ include "ScaletestEventhubs.name" . }}
  labels:
    {{- include "ScaletestEventhubs.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "ScaletestEventhubs.selectorLabels" . | nindent 4 }}
