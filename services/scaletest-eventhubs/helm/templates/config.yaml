apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "ScaletestEventhubs.fullname" . }}-env-configmap
  labels:
    {{- include "ScaletestEventhubs.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}

    loadtest:
      topic: "{{.Values.scaletestEventhubs.topic}}"
      log-interval: {{.Values.scaletestEventhubs.log_interval}}
      csv-input:
        test-file-class-path: {{.Values.scaletestEventhubs.csv_input.test_file_class_path}}
