apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "ScaletestEventhubs.fullname" . }}
  labels:
    {{- include "ScaletestEventhubs.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "ScaletestEventhubs.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
      {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.podMetricsAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "ScaletestEventhubs.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "ScaletestEventhubs.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repositoryBase }}{{ .Values.image.repositoryName }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          envFrom:
            - secretRef:
                name: {{ include "ScaletestEventhubs.fullname" . }}-env-secrets
          ports:
          {{- range .Values.ports }}
            - name: {{ .name }}
              containerPort: {{ .port }}
              protocol: TCP
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: {{ include "ScaletestEventhubs.fullname" . }}-env-configmap
              mountPath: /var/resources/
      volumes:
        - name: {{ include "ScaletestEventhubs.fullname" . }}-env-configmap
          configMap:
            name: {{ include "ScaletestEventhubs.fullname" . }}-env-configmap

      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}

