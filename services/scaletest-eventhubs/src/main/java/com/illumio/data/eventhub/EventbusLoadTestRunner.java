package com.illumio.data.eventhub;

import com.azure.messaging.eventhubs.EventData;
import com.azure.messaging.eventhubs.EventDataBatch;
import com.azure.messaging.eventhubs.EventHubProducerClient;
import com.azure.messaging.eventhubs.models.CreateBatchOptions;
import com.google.common.collect.Iterables;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

@AllArgsConstructor
@Slf4j
public class EventbusLoadTestRunner implements Runnable{

    private EventHubProducerClient eventHubProducerClient;
    private List<String> payloads;
    private int intervalTime;

    @Override
    public void run() {
//        cycle();
        runOnce();
    }

    private void cycle() {
        Iterable<String> infinitePayloads = Iterables.cycle(payloads);
        Iterator<String> infinitePayloadsIterator = infinitePayloads.iterator();
        CreateBatchOptions batchOptions = new CreateBatchOptions();
        batchOptions.setMaximumSizeInBytes(1024 * 1024);

        EventDataBatch eventDataBatch = eventHubProducerClient.createBatch(batchOptions);

        long startTimeMillis = System.currentTimeMillis();
        long lastLogTimeMillis = startTimeMillis;

        int intervalBatchCount = 0;
        int intervalEventCount = 0;
        long intervalSizeInBytes = 0;

        while (true) {
            // Send current batch
            eventHubProducerClient.send(eventDataBatch);

            intervalBatchCount++;
            intervalEventCount += eventDataBatch.getCount();
            intervalSizeInBytes += eventDataBatch.getSizeInBytes();

            // Create a new batch
            eventDataBatch = eventHubProducerClient.createBatch(batchOptions);

            // Add events to the new batch until it reaches the maximum size
            while (true) {
                EventData eventData = new EventData(infinitePayloadsIterator.next());
                if (!eventDataBatch.tryAdd(eventData)) {
                    break;
                }
            }

            // Check if interval have passed
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastLogTimeMillis >= intervalTime) {
                log.info("Thread Name: {}\n Number of events in last {} seconds: {} \n Number of batches in last {} seconds: {} \n Total size(Bytes) in last {} seconds: {} \n",
                        Thread.currentThread().getName(), (intervalTime/1000), intervalEventCount, (intervalTime/1000), intervalBatchCount, (intervalTime/1000), intervalSizeInBytes);

                // Reset interval counters
                intervalBatchCount = 0;
                intervalEventCount = 0;
                intervalSizeInBytes = 0;

                // Update last log time
                lastLogTimeMillis = currentTime;
            }
        }
    }

    private void runOnce() {
        CreateBatchOptions batchOptions = new CreateBatchOptions();
//        batchOptions.setMaximumSizeInBytes(1024 * 1024);
        List<String> payloadsToSend = new ArrayList<>();
        for (int i = 0; i < 200; i++) {
            payloadsToSend.addAll(payloads);
        }


        EventDataBatch eventDataBatch = eventHubProducerClient.createBatch(batchOptions);
        payloadsToSend.forEach(payload -> {
            EventData eventData = new EventData(payload);
            eventDataBatch.tryAdd(eventData);
        });

        eventHubProducerClient.send(eventDataBatch);
    }
}
