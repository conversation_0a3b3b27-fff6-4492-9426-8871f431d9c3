package com.illumio.data.eventhub;

import lombok.Data;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "loadtest")
@Data
public class LoadTestConfigurationProperties {

    private String topic;
    private String connectionString;
    private String testRunningTime;
    private JsonInput jsonInput = new JsonInput();
    private CsvInput csvInput = new CsvInput();
    private PlainTextInput plainTextInput = new PlainTextInput();
    private int logInterval;

    @Getter
    @Setter
    public static class JsonInput {
        private String testFolderClassPath;
        private String testFolderAbsolutePath;
    }

    @Getter
    @Setter
    public static class CsvInput {
        private String testFileClassPath;
        private String testFileAbsolutePath;
    }

    @Getter
    @Setter
    public static class PlainTextInput {
        private String testFolderClassPath;
        private String testFolderAbsolutePath;
    }
}
