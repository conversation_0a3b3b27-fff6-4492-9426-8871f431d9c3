package com.illumio.data.eventhub;

import com.azure.messaging.eventhubs.EventHubProducerClient;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.MappingIterator;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvParser;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.illumio.data.CommonSecurityLog;
import com.illumio.data.CommonSecurityLogRecords;
import com.illumio.data.util.Util;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Stream;

@Service
@Slf4j
public class LoadTestProducerService implements CommandLineRunner {

    public static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    public static final CsvMapper CSV_MAPPER = new CsvMapper()
            .enable(CsvParser.Feature.IGNORE_TRAILING_UNMAPPABLE);

    private final EventHubProducerClient eventHubProducerClient;
    private final ExecutorService executorService;
    private final int numberOfProcessors;
    private LoadTestConfigurationProperties loadTestConfigurationProperties;
    @Autowired
    public LoadTestProducerService(EventHubProducerClient eventHubProducerClient,
                                   LoadTestConfigurationProperties loadtestConfigurationProperties){
        this.eventHubProducerClient = eventHubProducerClient;
        numberOfProcessors = Math.max(Runtime.getRuntime().availableProcessors() -1, 1);// Leaving one processor for os related tasks
        log.info("Number of available threads/processors: {}, starting process on {} threads", Runtime.getRuntime().availableProcessors(), numberOfProcessors);
        executorService = Executors.newFixedThreadPool(numberOfProcessors);
        this.loadTestConfigurationProperties = loadtestConfigurationProperties;
    }

    @Override
    public void run(String... args) throws Exception{
        produceMessages();
    }

    private void produceMessages() {
        List<String> payloads = new LinkedList<>();
        payloads.addAll(readJsonPayloads());
        payloads.addAll(readCsvPayloads());
        payloads.addAll(readPlainTextPayloads());
        log.info("Read csv and added to payload");

        for (int i = 0; i < numberOfProcessors; i++) {
            log.info("Calling batch sender...");
            Runnable task = new EventbusLoadTestRunner(eventHubProducerClient,
                    payloads, loadTestConfigurationProperties.getLogInterval());
            executorService.submit(task);
        }
    }

    @SneakyThrows
    private List<String> readPlainTextPayloads() {
        List<String> payloads = new ArrayList<>();

        final File testFolder = Util.getFileFromClassPathOrAbsolutePath(
                loadTestConfigurationProperties.getPlainTextInput().getTestFolderClassPath(),
                loadTestConfigurationProperties.getPlainTextInput().getTestFolderAbsolutePath()
        );
        log.info("Reading plain text files from {}", testFolder);
        if (null != testFolder) {
            try (Stream<Path> paths = Files.walk(testFolder.toPath())) {
                paths.filter(Files::isRegularFile)
                        .map(path -> {
                            try {
                                log.info("Reading file: {}", path);
                                return Files.readString(path);
                            } catch (IOException e) {
                                log.error("Couldn't read file from {}", path, e);
                                throw new RuntimeException(e);
                            }
                        })
                        .forEach(payloads::add);
            }
        }
        return payloads;
    }

    @SneakyThrows
    private List<String> readJsonPayloads() {
        List<String> payloads = new ArrayList<>();

        final File jsonTestFolder = Util.getFileFromClassPathOrAbsolutePath(
                loadTestConfigurationProperties.getJsonInput().getTestFolderClassPath(),
                loadTestConfigurationProperties.getJsonInput().getTestFolderAbsolutePath()
        );
        if (null != jsonTestFolder) {
            try (Stream<Path> paths = Files.walk(jsonTestFolder.toPath())) {
                paths.filter(Files::isRegularFile)
                        .map(path -> {
                            try {
                                return Files.readString(path);
                            } catch (IOException e) {
                                log.error("Couldn't read file from {}", path, e);
                                throw new RuntimeException(e);
                            }
                        })
                        .map(s -> new Gson().fromJson(s, JsonObject.class))
                        .map(JsonElement::toString)
                        .forEach(payloads::add);
            }
        }
        return payloads;
    }

    @SneakyThrows
    private List<String> readCsvPayloads() {
        List<String> payloads = new ArrayList<>();
        final File testCsvFile = Util.getFileFromClassPathOrAbsolutePath(
                loadTestConfigurationProperties.getCsvInput().getTestFileClassPath(),
                loadTestConfigurationProperties.getCsvInput().getTestFileAbsolutePath()
        );
        if (null == testCsvFile) {
            log.error("Couldn't find CSV file for resource logs");
            return payloads;
        }

        List<CommonSecurityLog> commonSecurityLogs;
        CsvSchema csvSchema = CSV_MAPPER.schemaFor(CommonSecurityLog.class)
                .withHeader()
                .withColumnReordering(true);
        try (MappingIterator<CommonSecurityLog> iterator = CSV_MAPPER
                .disable(CsvParser.Feature.FAIL_ON_MISSING_HEADER_COLUMNS)
                .readerFor(CommonSecurityLog.class)
                .with(csvSchema)
                .readValues(testCsvFile)) {

            // read all
            commonSecurityLogs = iterator.readAll();
        } catch (IOException e) {
            log.error("Couldn't read CSV file for resource logs", e);
            throw e;
        }

        payloads = commonSecurityLogs.stream()
                .map(commonSecurityLog -> CommonSecurityLogRecords.builder()
                        .records(Collections.singletonList(commonSecurityLog))
                        .build())
                .map(commonSecurityLogRecords -> {
                    try {
                        return OBJECT_MAPPER.writeValueAsString(commonSecurityLogRecords);
                    } catch (JsonProcessingException e) {
                        log.error("Couldn't write object as JSON", e);
                        throw new RuntimeException(e);
                    }
                })
                .toList();
        return payloads;
    }
}
