package com.illumio.data.eventhub;

import com.azure.messaging.eventhubs.EventHubClientBuilder;
import com.azure.messaging.eventhubs.EventHubProducerClient;

import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class EventBusConfiguration {

    public LoadTestConfigurationProperties loadTestConfigurationProperties;

    public EventBusConfiguration(LoadTestConfigurationProperties loadTestConfigurationProperties){
        this.loadTestConfigurationProperties = loadTestConfigurationProperties;
    }

    @Bean
    public EventHubProducerClient eventHubProducerClient(){

        return new EventHubClientBuilder()
                .connectionString(loadTestConfigurationProperties.getConnectionString())
                .eventHubName(loadTestConfigurationProperties.getTopic())
                .buildProducerClient();

    }
}
