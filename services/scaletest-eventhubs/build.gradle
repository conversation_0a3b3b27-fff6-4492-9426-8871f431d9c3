plugins {
    id 'org.springframework.boot'
    id 'com.google.cloud.tools.jib'
}

apply from: "${project.rootDir}/gradle/scripts/jib-build.gradle"

dependencies {
    // azure
    implementation 'com.azure:azure-messaging-eventhubs'

    // implementation
    implementation project(":commons:azure-commons")
    implementation project(":commons:utility-commons")
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.kafka:spring-kafka'
    implementation "io.projectreactor.kafka:reactor-kafka"

    //google gson
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    // collections
    implementation 'com.google.collections:google-collections:1.0'
    implementation('com.fasterxml.jackson.dataformat:jackson-dataformat-csv')

    // test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
    testImplementation 'org.springframework.kafka:spring-kafka-test'
}

jib {
    container {
        entrypoint = [
                'java', '-cp', '@/app/jib-classpath-file',
                'com.illumio.data.ScaletestEventHubsApplication',
                '--spring.config.location=file:/var/resources/application.yml',
                '--spring.profiles.active=prod'
        ]
    }
}
