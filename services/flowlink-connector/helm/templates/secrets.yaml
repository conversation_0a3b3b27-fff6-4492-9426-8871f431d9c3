apiVersion: v1
kind: Secret
metadata:
  name: {{ include "FlowlinkConnector.fullname" . }}-env-secrets
  labels:
    {{- include "FlowlinkConnector.labels" . | nindent 4 }}
type: Opaque
stringData:
  FLOWLINKCONNECTOR_KAFKAFLOWPROTOCONSUMERCONFIG_SASLJAASCONFIG:  org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.connectionString }}";
  FLOWLINKCONNECTOR_KAFKAFLOWCEFPRODUCERCONFIG_SASLJAASCONFIG:  org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.flowlinkConnector.kafkaFlowCefProducerConfig.connectionString }}";