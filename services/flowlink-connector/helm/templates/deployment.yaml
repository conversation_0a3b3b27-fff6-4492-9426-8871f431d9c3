apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "FlowlinkConnector.fullname" . }}
  labels:
    {{- include "FlowlinkConnector.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "FlowlinkConnector.selectorLabels" . | nindent 6 }}
      {{- if .Values.extraLabels }}
        {{ toYaml .Values.extraLabels | nindent 6 }}
      {{- end }}
  template:
    metadata:
      annotations:
      {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.podMetricsAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "FlowlinkConnector.selectorLabels" . | nindent 8 }}
        {{- if .Values.extraLabels }}
          {{ toYaml .Values.extraLabels | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "FlowlinkConnector.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repositoryBase }}/{{ .Values.image.repositoryName }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          envFrom:
            - secretRef:
                name: {{ include "FlowlinkConnector.fullname" . }}-env-secrets
          ports:
          {{- range .Values.ports }}
            - name: {{ .name }}
              containerPort: {{ .port }}
              protocol: TCP
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: {{ include "FlowlinkConnector.fullname" . }}-env-configmap
              mountPath: /var/resources/
      volumes:
        - name: {{ include "FlowlinkConnector.fullname" . }}-env-configmap
          configMap:
            name: {{ include "FlowlinkConnector.fullname" . }}-env-configmap

      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}

