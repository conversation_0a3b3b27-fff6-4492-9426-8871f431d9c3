apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "FlowlinkConnector.fullname" . }}-env-configmap
  labels:
    {{- include "FlowlinkConnector.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
        org:
          apache:
            kafka: {{.Values.logging.level.kafka}}
    spring:
      application:
        name: "flowlink-connector"
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: reactive
    server:
      port: {{.Values.server.port}}
    retry-config:
      parameters:
        min-backoff: "{{.Values.retryConfig.parameters.minBackoff}}"
        max-retries: {{.Values.retryConfig.parameters.maxRetries}}

    flowlink-connector:
      kafka-flow-proto-consumer-config:
        bootstrapServers: "{{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.bootstrapServers}}"
        topic: "{{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.topic}}"
        isConnectionString: {{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.isConnectionString}}
        groupId: "{{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.groupId}}"
        autoOffsetReset: "{{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.autoOffsetReset}}"
        heartbeatIntervalMs: {{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.heartbeatIntervalMs}}
        maxPollIntervalMs:  {{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.maxPollIntervalMs}}
        sessionTimeoutMs: {{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.sessionTimeoutMs}}
        metadataMaxAgeMs: {{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.metadataMaxAgeMs}}
        connectionsMaxIdleMs: {{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.connectionsMaxIdleMs}}
        partitionAssignmentStrategy: "{{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.partitionAssignmentStrategy}}"
        isGroupInstanceIdEnabled: "{{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.isGroupInstanceIdEnabled}}"
        maxPollRecords: {{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.maxPollRecords}}
        fetchMaxWaitMs: {{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.fetchMaxWaitMs}}
        enableAutoCommit: {{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.enableAutoCommit}}
        concurrency: {{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.concurrency}}
        backPressureEvents: {{.Values.flowlinkConnector.kafkaFlowProtoConsumerConfig.backPressureEvents}}
      kafka-flow-cef-producer-config:
        bootstrapServers: "{{.Values.flowlinkConnector.kafkaFlowCefProducerConfig.bootstrapServers}}"
        topic: "{{.Values.flowlinkConnector.kafkaFlowCefProducerConfig.topic}}"
        isConnectionString: {{.Values.flowlinkConnector.kafkaFlowCefProducerConfig.isConnectionString}}
        maxInFlight: "{{.Values.flowlinkConnector.kafkaFlowCefProducerConfig.maxInFlight}}"
        requestTimeoutMs: "{{.Values.flowlinkConnector.kafkaFlowCefProducerConfig.requestTimeoutMs}}"
        deliveryTimeoutMs: "{{.Values.flowlinkConnector.kafkaFlowCefProducerConfig.deliveryTimeoutMs}}"
        maxBlockMs: "{{.Values.flowlinkConnector.kafkaFlowCefProducerConfig.maxBlockMs}}"
        lingerMs: "{{.Values.flowlinkConnector.kafkaFlowCefProducerConfig.lingerMs}}"
        batchSize: "{{.Values.flowlinkConnector.kafkaFlowCefProducerConfig.batchSize}}"
        bufferMemory: "{{.Values.flowlinkConnector.kafkaFlowCefProducerConfig.bufferMemory}}"