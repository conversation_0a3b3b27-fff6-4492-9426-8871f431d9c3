apiVersion: v1
kind: Service
metadata:
  name: {{ include "FlowlinkConnector.name" . }}
  labels:
    {{- include "FlowlinkConnector.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "FlowlinkConnector.selectorLabels" . | nindent 4 }}
