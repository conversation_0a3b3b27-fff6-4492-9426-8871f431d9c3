# Flowlink Connector

The **Flowlink Connector** service allows for the consumption of aggregated traffic data from Flowlink devices published
to a Kafka topic where traffic data is proto-encoded and GZIPed. Flows are extracted from this traffic data and then
converted to CEF format and published to a Kafka topic for downstream processing.
---

## Running Locally

### 1. Modify `application.yml`
Ensure the following configurations are set:

1. **Kafka Flow Proto Consumer Config** - Kafka Consumer reading proto-encoded GZIPed flow data
3. **Kafka Flow Cef Producer Config** – Kafka Producer writing CEF flows

### 2. Run Locally Using IntelliJ

To run the application from within IntelliJ, use the following run configuration:

- **Run Configuration**: `flowlink-connector/FlowlinkConnectorApplication`