package com.illumio.data.configuration;

import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "flowlink-connector")
@Getter
@Setter
public class FlowlinkConnectorConfig {

    private final KafkaConsumerConfig kafkaFlowProtoConsumerConfig = new KafkaConsumerConfig();
    private final KafkaProducerConfig kafkaFlowCefProducerConfig = new KafkaProducerConfig();

    @Getter
    @Setter
    public static class KafkaConsumerConfig {
        private String bootstrapServers;
        private Boolean isConnectionString = false;
        private String saslJaasConfig;
        private String topic;
        private String groupId;
        private String autoOffsetReset;
        private Integer connectionsMaxIdleMs;
        private Integer metadataMaxAgeMs;
        private Integer heartbeatIntervalMs;
        private Integer sessionTimeoutMs;
        private Integer maxPollIntervalMs;
        private String partitionAssignmentStrategy;
        private String groupInstanceId;
        private Integer maxPollRecords = 2000;
        private Integer fetchMaxWaitMs = 100;
        private Boolean enableAutoCommit = false;
        private Integer concurrency = 50;
        private Integer backPressureEvents = 5000;
    }

    @Getter
    @Setter
    public static class KafkaProducerConfig {
        private String bootstrapServers;
        private Boolean isConnectionString = false;
        private String saslJaasConfig;
        private String topic;
        private Integer requestTimeoutMs;
        private Integer deliveryTimeoutMs;
        private Integer lingerMs;
        private Integer batchSize;
        private Integer bufferMemory;
        private Integer maxBlockMs;
        private Integer maxInFlight = 1024;
    }

}
