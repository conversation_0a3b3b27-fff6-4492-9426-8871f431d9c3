package com.illumio.data.configuration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class KafkaProducerConfig {
    private final FlowlinkConnectorConfig flowlinkConnectorConfig;

    @Bean
    public KafkaSender<String, String> kafkaJsonFlowSender() {
        final Map<String, Object> producerProps = producerOptions();
        final SenderOptions<String, String> senderOptions = SenderOptions.<String, String>create(producerProps)
                                                                   .maxInFlight(flowlinkConnectorConfig
                                                                           .getKafkaFlowCefProducerConfig()
                                                                           .getMaxInFlight());
        return KafkaSender.create(senderOptions);
    }

    private Map<String, Object> producerOptions() {
        final Map<String, Object> producerProps = new HashMap<>();
        producerProps.put(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
                flowlinkConnectorConfig.getKafkaFlowCefProducerConfig().getBootstrapServers());
        producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        if (Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowCefProducerConfig)
                .map(FlowlinkConnectorConfig.KafkaProducerConfig::getIsConnectionString)
                .orElse(Boolean.FALSE)) {
            producerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            producerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            producerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    flowlinkConnectorConfig.getKafkaFlowCefProducerConfig().getSaslJaasConfig());
        }

        Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowCefProducerConfig)
                .map(FlowlinkConnectorConfig.KafkaProducerConfig::getRequestTimeoutMs)
                .ifPresent(requestTimeoutMs ->
                        producerProps.put(
                                CommonClientConfigs.REQUEST_TIMEOUT_MS_CONFIG,
                                requestTimeoutMs));
        Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowCefProducerConfig)
                .map(FlowlinkConnectorConfig.KafkaProducerConfig::getDeliveryTimeoutMs)
                .ifPresent(deliveryTimeoutMs ->
                        producerProps.put(
                                ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG,
                                deliveryTimeoutMs));
        Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowCefProducerConfig)
                .map(FlowlinkConnectorConfig.KafkaProducerConfig::getLingerMs)
                .ifPresent(lingerMs ->
                        producerProps.put(
                                ProducerConfig.LINGER_MS_CONFIG,
                                lingerMs));
        Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowCefProducerConfig)
                .map(FlowlinkConnectorConfig.KafkaProducerConfig::getBatchSize)
                .ifPresent(batchSize ->
                        producerProps.put(
                                ProducerConfig.BATCH_SIZE_CONFIG,
                                batchSize));
        Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowCefProducerConfig)
                .map(FlowlinkConnectorConfig.KafkaProducerConfig::getBufferMemory)
                .ifPresent(bufferMemory ->
                        producerProps.put(
                                ProducerConfig.BUFFER_MEMORY_CONFIG,
                                bufferMemory));
        Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowCefProducerConfig)
                .map(FlowlinkConnectorConfig.KafkaProducerConfig::getMaxBlockMs)
                .ifPresent(maxBlockMs ->
                        producerProps.put(
                                ProducerConfig.MAX_BLOCK_MS_CONFIG,
                                maxBlockMs));

        return producerProps;
    }
}
