package com.illumio.data.configuration;

import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;

import java.time.Duration;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Configuration
@RequiredArgsConstructor
public class KafkaConsumerConfig {
    private final FlowlinkConnectorConfig flowlinkConnectorConfig;

    @Bean
    public KafkaReceiver<String, byte[]> kafkaReceiver() {
        Map<String, Object> consumerProps = new HashMap<>();
        consumerProps.put(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig().getBootstrapServers());
        consumerProps.put(
                ConsumerConfig.GROUP_ID_CONFIG,
                flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig().getGroupId());
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, ByteArrayDeserializer.class);

        if (Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowProtoConsumerConfig)
                .map(FlowlinkConnectorConfig.KafkaConsumerConfig::getIsConnectionString)
                .orElse(Boolean.FALSE)) {
            consumerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            consumerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            consumerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig().getSaslJaasConfig());
        }

        if (Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowProtoConsumerConfig)
                .map(FlowlinkConnectorConfig.KafkaConsumerConfig::getAutoOffsetReset)
                .isPresent()) {
            consumerProps.put(
                    ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,
                    flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig().getAutoOffsetReset());
        }

        if (Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowProtoConsumerConfig)
                .map(FlowlinkConnectorConfig.KafkaConsumerConfig::getConnectionsMaxIdleMs)
                .isPresent()) {
            consumerProps.put(
                    ConsumerConfig.CONNECTIONS_MAX_IDLE_MS_CONFIG,
                    flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig().getConnectionsMaxIdleMs());
        }

        if (Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowProtoConsumerConfig)
                .map(FlowlinkConnectorConfig.KafkaConsumerConfig::getMetadataMaxAgeMs)
                .isPresent()) {
            consumerProps.put(
                    ConsumerConfig.METADATA_MAX_AGE_CONFIG,
                    flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig().getMetadataMaxAgeMs());
        }

        if (Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowProtoConsumerConfig)
                .map(FlowlinkConnectorConfig.KafkaConsumerConfig::getHeartbeatIntervalMs)
                .isPresent()) {
            consumerProps.put(
                    ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG,
                    flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig().getHeartbeatIntervalMs());
        }

        if (Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowProtoConsumerConfig)
                .map(FlowlinkConnectorConfig.KafkaConsumerConfig::getSessionTimeoutMs)
                .isPresent()) {
            consumerProps.put(
                    ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG,
                    flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig().getSessionTimeoutMs());
        }

        if (Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowProtoConsumerConfig)
                .map(FlowlinkConnectorConfig.KafkaConsumerConfig::getMaxPollIntervalMs)
                .isPresent()) {
            consumerProps.put(
                    ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG,
                    flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig().getMaxPollIntervalMs());
        }

        if (Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowProtoConsumerConfig)
                .map(FlowlinkConnectorConfig.KafkaConsumerConfig::getPartitionAssignmentStrategy)
                .isPresent()) {
            consumerProps.put(
                    ConsumerConfig.PARTITION_ASSIGNMENT_STRATEGY_CONFIG,
                    flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig().getPartitionAssignmentStrategy());
        }

        if (Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowProtoConsumerConfig)
                .map(FlowlinkConnectorConfig.KafkaConsumerConfig::getGroupInstanceId)
                .isPresent()) {
            consumerProps.put(
                    ConsumerConfig.GROUP_INSTANCE_ID_CONFIG,
                    flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig().getGroupInstanceId());
        }

        if (Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowProtoConsumerConfig)
                .map(FlowlinkConnectorConfig.KafkaConsumerConfig::getMaxPollRecords)
                .isPresent()) {
            consumerProps.put(
                    ConsumerConfig.MAX_POLL_RECORDS_CONFIG,
                    flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig().getMaxPollRecords());
        }

        if (Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowProtoConsumerConfig)
                .map(FlowlinkConnectorConfig.KafkaConsumerConfig::getFetchMaxWaitMs)
                .isPresent()) {
            consumerProps.put(
                    ConsumerConfig.FETCH_MAX_WAIT_MS_CONFIG,
                    flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig().getFetchMaxWaitMs());
        }

        if (Optional.of(flowlinkConnectorConfig)
                .map(FlowlinkConnectorConfig::getKafkaFlowProtoConsumerConfig)
                .map(FlowlinkConnectorConfig.KafkaConsumerConfig::getEnableAutoCommit)
                .isPresent()) {
            consumerProps.put(
                    ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG,
                    flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig().getEnableAutoCommit());
        }

        ReceiverOptions<String, byte[]> receiverOptions =
                ReceiverOptions.<String, byte[]>create(consumerProps)
                        .subscription(
                                Collections.singleton(
                                        flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig().getTopic()));

        receiverOptions = applyReactorKafkaOptions(receiverOptions,
                flowlinkConnectorConfig.getKafkaFlowProtoConsumerConfig());

        return KafkaReceiver.create(receiverOptions);
    }

    private ReceiverOptions<String, byte[]> applyReactorKafkaOptions(
            ReceiverOptions<String, byte[]> options,
            FlowlinkConnectorConfig.KafkaConsumerConfig config) {

        if (Optional.ofNullable(config.getBackPressureEvents()).isPresent()) {
            options = options.maxDelayRebalance(Duration.ofMillis(config.getBackPressureEvents()));
        }

        if (Optional.ofNullable(config.getConcurrency()).isPresent()) {
            options = options.schedulerSupplier(() -> reactor.core.scheduler.Schedulers.newParallel("kafka-consumer", config.getConcurrency()));
        }

        return options;
    }
}