package com.illumio.data.components;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.StringJoiner;

import com.illumio.data.model.TrafficData;
import com.illumio.data.model.TrafficDataRecord;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

@Slf4j
@UtilityClass
public class TrafficDataCefMapper {

    public static Flux<String> cefFromTrafficData(TrafficData trafficData) {
        return Flux.fromStream(trafficData.getFlowsList().stream()
                .map(trafficDataRecord -> cefFromFlow(trafficDataRecord, trafficData)));
    }

    public static String cefFromFlow(TrafficDataRecord flow, TrafficData trafficData) {
        Map<String, String> extensions = new LinkedHashMap<>();
        // Standard fields
        extensions.put("src", flow.getSrcIp().toStringUtf8());
        extensions.put("dst", flow.getDstIp().toStringUtf8());
        extensions.put("dpt", String.valueOf(flow.getDstPort()));
        extensions.put("proto", String.valueOf(flow.getProto()));
        extensions.put("cnt", String.valueOf(flow.getCount()));
        extensions.put("rt", String.valueOf(flow.getTimestamp()));

        // Custom fields
        extensions.put("cs1Label", "Tenant ID");
        extensions.put("cs1", trafficData.getOrgId().toStringUtf8());

        extensions.put("cs2Label", "Aggregation Seconds");
        extensions.put("cs2", String.valueOf(trafficData.getAggregationSeconds()));

        extensions.put("cs3Label", "Installation ID");
        extensions.put("cs3", trafficData.getInstallationId().toStringUtf8());

        return formatFlowlinkCefString(extensions);
    }

    private static String formatFlowlinkCefString(Map<String, String> extensions) {
        final String cefVersion = "0";
        final String deviceVendor = "Illumio";
        final String deviceProduct = "Flowlink";
        final String deviceVersion = "1.0";
        final String deviceEventClassId = "100";
        final String name = "Traffic Flow";
        final String severity = "Unknown";

        StringBuilder sb = new StringBuilder();
        sb.append("CEF:")
                .append(cefVersion).append("|")
                .append(deviceVendor).append("|")
                .append(deviceProduct).append("|")
                .append(deviceVersion).append("|")
                .append(deviceEventClassId).append("|")
                .append(name).append("|")
                .append(severity).append("|");

        if (!extensions.isEmpty()) {
            StringJoiner joiner = new StringJoiner(" ");
            for (Map.Entry<String, String> entry : extensions.entrySet()) {
                joiner.add(entry.getKey() + "=" + escape(entry.getValue()));
            }
            sb.append(joiner);
        }

        return sb.toString();
    }

    /**
     * Escape characters according to CEF spec
     */
    private static String escape(String value) {
        if (value == null) return "";
        return value.replace("\\", "\\\\")
                .replace("|", "\\|")
                .replace("=", "\\=");
    }

}
