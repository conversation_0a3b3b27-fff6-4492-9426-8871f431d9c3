package com.illumio.data.components;

import com.illumio.data.annotations.retry.RetryReactiveOnError;
import com.illumio.data.configuration.FlowlinkConnectorConfig;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;

@Slf4j
@Service
@RequiredArgsConstructor
public class CefFlowSenderService {

    private final KafkaSender<String, String> kafkaJsonFlowSender;
    private final FlowlinkConnectorConfig flowlinkConnectorConfig;

    @RetryReactiveOnError
    public Mono<Void> sendFlows(final Flux<String> flowCefs) {
        return kafkaJsonFlowSender.send(flowCefs.map(this::createRecord))
                .doOnComplete(() ->
                        log.debug("Successfully pushed flows to Kafka"))
                .then();
    }

    @SneakyThrows
    private SenderRecord<String, String, String> createRecord(final String flowCef) {
        final String kafkaTopic = flowlinkConnectorConfig.getKafkaFlowCefProducerConfig().getTopic();
        // TODO: populate partition key according to spec
        final ProducerRecord<String, String> producerRecord = new ProducerRecord<>(kafkaTopic, "TODO", flowCef);
        return SenderRecord.create(producerRecord, null);
    }
}
