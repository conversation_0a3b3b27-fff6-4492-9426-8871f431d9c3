package com.illumio.data.components;

import com.illumio.data.annotations.retry.RetryReactiveOnError;
import com.illumio.data.model.TrafficData;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.receiver.KafkaReceiver;


import java.io.ByteArrayInputStream;
import java.util.zip.GZIPInputStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProtoFlowReceiverService {
    private final KafkaReceiver<String, byte[]> kafkaFlowReceiver;
    private final CefFlowSenderService cefFlowSenderService;

    @PostConstruct
    @RetryReactiveOnError
    public void consumeFlows() {
        kafkaFlowReceiver.receiveAutoAck()
                .publishOn(Schedulers.boundedElastic())
                .flatMap(trafficDataRecords -> trafficDataRecords)
                .doOnNext(trafficDataRecord -> log.debug("Received traffic data record: key={}, value={}",
                        trafficDataRecord.key(), trafficDataRecord.value()))
                .flatMap(trafficDataRecord -> trafficDataFromEncodedGzip(trafficDataRecord.value()))
                .flatMap(TrafficDataCefMapper::cefFromTrafficData)
                .transform(cefFlowSenderService::sendFlows)
                .onErrorContinue((throwable, o) ->
                        log.error("Error occurred while consuming traffic data. Continuing flow consumption.", throwable))
                .subscribe();
    }

    @SneakyThrows
    private static Mono<TrafficData> trafficDataFromEncodedGzip(byte[] compressed) {
        try (ByteArrayInputStream bais = new ByteArrayInputStream(compressed);
             GZIPInputStream gzipIn = new GZIPInputStream(bais)) {
            return Mono.just(TrafficData.parseFrom(gzipIn.readAllBytes()));
        }
    }

}
