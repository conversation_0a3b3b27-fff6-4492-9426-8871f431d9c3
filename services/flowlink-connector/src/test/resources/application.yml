logging:
  level:
    ROOT: INFO
spring:
  application:
    name: flowlink-connector
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive
server:
  port: 8081

retry-config:
  parameters:
    min-backoff: 30s
    max-retries: 2

flowlink-connector:
  kafka-flow-proto-consumer-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    isConnectionString: true
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";
    topic: flowlink-flows-proto-v1
    groupId: flowlink-connector-1

  kafka-flow-cef-producer-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    topic: syslog-raw-cef-v1
    isConnectionString: true
    saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="DO_NOT_COMMIT";