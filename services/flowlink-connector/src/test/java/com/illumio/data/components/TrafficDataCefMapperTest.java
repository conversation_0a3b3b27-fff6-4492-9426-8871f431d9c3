package com.illumio.data.components;

import com.illumio.data.model.TrafficData;
import com.illumio.data.model.TrafficDataRecord;
import com.google.protobuf.ByteString;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class TrafficDataCefMapperTest {

    @Test
    void testCefFromFlow_basicMapping() {
        TrafficDataRecord flow = TrafficDataRecord.newBuilder()
                .setSrcIp(ByteString.copyFromUtf8("***********"))
                .setDstIp(ByteString.copyFromUtf8("********"))
                .setDstPort(443)
                .setProto(6)
                .setCount(11)
                .setTimestamp(1756464198L)
                .build();

        TrafficData trafficData = TrafficData.newBuilder()
                .setOrgId(ByteString.copyFromUtf8("org-123"))
                .setInstallationId(ByteString.copyFromUtf8("inst-456"))
                .setAggregationSeconds(60)
                .addAllFlows(Collections.singletonList(flow))
                .build();

        String cefString = TrafficDataCefMapper.cefFromFlow(flow, trafficData);

        assertTrue(cefString.startsWith("CEF:0|Illumio|Flowlink|1.0|100|Traffic Flow|Unknown|"));
        assertTrue(cefString.contains("src=***********"));
        assertTrue(cefString.contains("dst=********"));
        assertTrue(cefString.contains("dpt=443"));
        assertTrue(cefString.contains("proto=6"));
        assertTrue(cefString.contains("cnt=11"));
        assertTrue(cefString.contains("rt=1756464198"));
        assertTrue(cefString.contains("cs1Label=Tenant ID"));
        assertTrue(cefString.contains("cs1=org-123"));
        assertTrue(cefString.contains("cs2=60"));
        assertTrue(cefString.contains("cs3=inst-456"));
    }

    @Test
    void testCefFromTrafficData_multipleFlows() {
        TrafficDataRecord flow1 = TrafficDataRecord.newBuilder()
                .setSrcIp(ByteString.copyFromUtf8("***********"))
                .setDstIp(ByteString.copyFromUtf8("********"))
                .setDstPort(443)
                .setProto(6)
                .setCount(11)
                .setTimestamp(1111L)
                .build();

        TrafficDataRecord flow2 = TrafficDataRecord.newBuilder()
                .setSrcIp(ByteString.copyFromUtf8("***********"))
                .setDstIp(ByteString.copyFromUtf8("********"))
                .setDstPort(80)
                .setProto(6)
                .setCount(22)
                .setTimestamp(2222L)
                .build();

        TrafficData trafficData = TrafficData.newBuilder()
                .setOrgId(ByteString.copyFromUtf8("org-123"))
                .setInstallationId(ByteString.copyFromUtf8("inst-456"))
                .setAggregationSeconds(60)
                .addAllFlows(Arrays.asList(flow1, flow2))
                .build();

        StepVerifier.create(TrafficDataCefMapper.cefFromTrafficData(trafficData))
                .expectNextMatches(msg -> msg.contains("src=***********") && msg.contains("dst=********"))
                .expectNextMatches(msg -> msg.contains("src=***********") && msg.contains("dst=********"))
                .verifyComplete();
    }

    @Test
    void testCefFromFlow_escapeSpecialCharacters() {
        TrafficDataRecord flow = TrafficDataRecord.newBuilder()
                .setSrcIp(ByteString.copyFromUtf8("192|168=1\\1"))
                .setDstIp(ByteString.copyFromUtf8("********"))
                .setDstPort(443)
                .setProto(6)
                .setCount(1)
                .setTimestamp(12345L)
                .build();

        TrafficData trafficData = TrafficData.newBuilder()
                .setOrgId(ByteString.copyFromUtf8("org|123"))
                .setInstallationId(ByteString.copyFromUtf8("inst=456"))
                .setAggregationSeconds(60)
                .addAllFlows(Collections.singletonList(flow))
                .build();

        String cefString = TrafficDataCefMapper.cefFromFlow(flow, trafficData);

        assertTrue(cefString.contains("src=192\\|168\\=1\\\\1"));
        assertTrue(cefString.contains("cs1=org\\|123"));
        assertTrue(cefString.contains("cs3=inst\\=456"));
    }

    @Test
    void testCefFromTrafficData_emptyFlows() {
        TrafficData trafficData = TrafficData.newBuilder()
                .setOrgId(ByteString.copyFromUtf8("org-123"))
                .setInstallationId(ByteString.copyFromUtf8("inst-456"))
                .setAggregationSeconds(60)
                .addAllFlows(Collections.emptyList())
                .build();

        StepVerifier.create(TrafficDataCefMapper.cefFromTrafficData(trafficData))
                .verifyComplete(); // No emissions expected
    }
}
