package com.illumio.data.components;

import com.illumio.data.model.TrafficData;
import com.illumio.data.model.TrafficDataRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.kafka.receiver.KafkaReceiver;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.Duration;
import java.util.List;
import java.util.zip.GZIPOutputStream;
import com.google.protobuf.ByteString;
import reactor.kafka.receiver.ReceiverRecord;

import static org.assertj.core.api.Assertions.assertThat;
import static org.awaitility.Awaitility.await;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ProtoFlowReceiverServiceTest {

    @Mock
    private KafkaReceiver<String, byte[]> kafkaReceiver;
    @Mock
    private CefFlowSenderService cefFlowSenderService;
    private ProtoFlowReceiverService protoFlowReceiverService;

    @BeforeEach
    void setUp() {
        protoFlowReceiverService = new ProtoFlowReceiverService(kafkaReceiver, cefFlowSenderService);
    }

    @Test
    void testConsumeFlows_ShouldDecompressAndSend() throws IOException {
        byte[] gzippedData = createGzippedTrafficData();

        final ReceiverRecord<String, byte[]> record = mock(ReceiverRecord.class);
        when(record.key()).thenReturn("tenant-123");
        when(record.value()).thenReturn(gzippedData);

        when(kafkaReceiver.receiveAutoAck()).thenReturn(Flux.just(Flux.just(record)));

        when(cefFlowSenderService.sendFlows(any()))
                .thenAnswer(invocation -> {
                    Flux<?> inputFlux = invocation.getArgument(0);
                    return inputFlux.then();
                });

        protoFlowReceiverService.consumeFlows();

        ArgumentCaptor<Flux<String>> fluxCaptor = ArgumentCaptor.forClass((Class) Flux.class);
        await().atMost(Duration.ofSeconds(5))
                .untilAsserted(() -> {
                    verify(cefFlowSenderService, times(1)).sendFlows(fluxCaptor.capture());

                    Flux<String> capturedFlux = fluxCaptor.getValue();
                    List<String> flows = capturedFlux.collectList().block();

                    String expectedFlow1 = "CEF:0|Illumio|Flowlink|1.0|100|Traffic Flow|Unknown|src=************ dst=******** dpt=443 proto=6 cnt=11 cs1Label=Tenant ID cs1=tenant-123 cs2Label=Aggregation Seconds cs2=60 cs3Label=Installation ID cs3=datacenter-1";
                    String expectedFlow2 = "CEF:0|Illumio|Flowlink|1.0|100|Traffic Flow|Unknown|src=************ dst=******** dpt=443 proto=6 cnt=11 cs1Label=Tenant ID cs1=tenant-123 cs2Label=Aggregation Seconds cs2=60 cs3Label=Installation ID cs3=datacenter-1";

                    // Normalize flows by removing dynamic rt field
                    List<String> normalizedFlows = flows.stream()
                            .map(flow -> flow.replaceAll(" rt=\\d+", ""))
                            .toList();

                    assertThat(normalizedFlows).containsExactly(expectedFlow1, expectedFlow2);
                });
    }


    public static byte[] createGzippedTrafficData() throws IOException {
        TrafficDataRecord record = TrafficDataRecord.newBuilder()
                .setTimestamp(System.currentTimeMillis())
                .setSrcIp(ByteString.copyFromUtf8("************"))
                .setDstIp(ByteString.copyFromUtf8("********"))
                .setDstPort(443)
                .setProto(6) // TCP
                .setCount(11)
                .setSrcMac(ByteString.copyFromUtf8("aa:bb:cc:dd:ee:ff"))
                .setDstMac(ByteString.copyFromUtf8("ff:ee:dd:cc:bb:aa"))
                .build();

        TrafficData trafficData = TrafficData.newBuilder()
                .setAggregationSeconds(60)
                .setOrgId(ByteString.copyFromUtf8("tenant-123"))
                .setInstallationId(ByteString.copyFromUtf8("datacenter-1"))
                .addFlows(record)
                .addFlows(record.toBuilder().setDstIp(ByteString.copyFromUtf8("********")).build()) // second flow
                .build();

        byte[] protoBytes = trafficData.toByteArray();

        return gzip(protoBytes);
    }

    private static byte[] gzip(byte[] data) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipOut = new GZIPOutputStream(baos)) {
            gzipOut.write(data);
        }
        return baos.toByteArray();
    }
}
