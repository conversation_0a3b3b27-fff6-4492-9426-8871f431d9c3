apiVersion: v1
kind: Secret
metadata:
  name: {{ include "FlowPush.fullname" . }}-env-secrets
  labels:
    {{- include "FlowPush.labels" . | nindent 4 }}
type: Opaque
stringData:
  VAULT_TOKEN: {{ .Values.vault.token }}
  FLOWPUSHCONFIG_KSTREAMSCONFIG_SASLJAASCONFIG: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.eventhub.password }}";