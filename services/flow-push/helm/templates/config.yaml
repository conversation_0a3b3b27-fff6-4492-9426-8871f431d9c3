apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "FlowPush.fullname" . }}-env-configmap
  labels:
    {{- include "FlowPush.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
        org:
          apache:
            kafka: {{.Values.logging.level.kafka}}
    spring:
      application:
        name: "flow-push"
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: none
    
    flow-push-config:
      kstreams-config:
        application-id: {{.Values.flowPushConfig.kstreamsConfig.applicationId}}"
        bootstrap-servers: {{.Values.flowPushConfig.kstreamsConfig.bootstrapServers}}
        state-store-cache-max-bytes: {{.Values.flowPushConfig.kstreamsConfig.stateStoreCacheMaxBytes}}
        commit-interval-ms: {{.Values.flowPushConfig.kstreamsConfig.commitIntervalMs}}
        state-store-dir: {{.Values.flowPushConfig.kstreamsConfig.stateStoreDir}}
    
      streams-builder-config:
        input-topic: {{.Values.flowPushConfig.streamsBuilderConfig.inputTopic}}
        state-store-name: {{.Values.flowPushConfig.streamsBuilderConfig.stateStoreName}}
        punctuate-interval: {{.Values.flowPushConfig.streamsBuilderConfig.punctuateInterval}}
        output-topic: {{.Values.flowPushConfig.streamsBuilderConfig.outputTopic}}
        devBuildIgnoreBlobOffsetsStore: {{.Values.flowPushConfig.streamsBuilderConfig.devBuildIgnoreBlobOffsetsStore}}
    
      storage-accounts-config:
        {{- range $storageAccount, $storageAccountConfig := .Values.flowPushConfig.storageAccountsConfig }}
        {{ $storageAccount }}:
          storage-account-endpoint: {{ $storageAccountConfig.endpoint }}
          auth-config:
            is-vault-config: {{ $storageAccountConfig.authConfig.isVaultConfig }}
            vault-path: {{ $storageAccountConfig.authConfig.vaultPath }}
        {{- end }}
    
      cloud-secure-config:
        tenant-id: {{.Values.flowPushConfig.cloudSecureConfig.tenantId}}
    
    vault:
      uri: {{.Values.vault.uri}}
      token: DO_NOT_COMMIT
      cache-time-mins: {{.Values.vault.cacheTimeMins}}
