# Default values for connector.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
logging:
  level:
    root: DEBUG
    kafka: DEBUG
    
ports:
- name: admin
  port: 8084
- name: rest
  port: 8081

servicePorts:
- name: rest
  podPort: rest
  servicePort: 8081

service:
  type: ClusterIP

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

replicaCount: 1

image:
  repositoryBase: illum.azurecr.io/
  repositoryName: flow-push
  tag:      # value given at helm deployment 
  pullPolicy: Always

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

podAnnotations: {}

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"



podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000



resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

ansi:
  enabled: ALWAYS
webApplicationType: none
flowPushConfig:
  kstreamsConfig:
    applicationId: flow-push
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    stateStoreCacheMaxBytes: "********"
    commitIntervalMs: "1000"
  streamsBuilderConfig:
    inputTopic: flow-events-topic
    stateStoreName: blob-offsets
    punctuateInterval: 30s
    outputTopic: raw-flow-topic
    devBuildIgnoreBlobOffsetsStore: false
  storageAccountsConfig:
    inglocpoc:
      endpoint: https://inglogpoc.blob.core.windows.net/
      authConfig:
        isVaultConfig: true
        vaultPath:  # should give at deployment time
  cloudSecureConfig:
    tenantId:  # should give at deployment time

eventhub:
  password: # should give at deployment time
vault:
  token: # should give at deployment time

extraLabels: {}