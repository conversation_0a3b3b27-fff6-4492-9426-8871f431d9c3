logging:
  level:
    ROOT: INFO
    org:
      apache:
        kafka: INFO
spring:
  application:
    name: flow-push
  output:
    ansi:
      enabled: ALWAYS
#  main:
#    web-application-type: none
server:
  port : 8090

flow-push-config:
  kstreams-config:
    application-id: flow-push
    bootstrap-servers: adx-eg-dataexplorer1.servicebus.windows.net:9093
    state-store-cache-max-bytes: ******** # 1 MB across all stream threads
    commit-interval-ms: 1000 # 1s
    state-store-dir: rocksdb-flow-push
#    event hub config
    is-sasl: true

  streams-builder-config:
    input-topic: flow-log-events
    state-store-name: blob-offsets
    punctuate-interval: 10s
    output-topic: flow-stream

  storage-accounts-config:
    inglogpoc:
      endpoint: https://inglogpoc.blob.core.windows.net/
    karthikvnettest:
      endpoint: https://karthikvnettest.blob.core.windows.net/
    rahulvnettest:
      endpoint: https://rahulvnettest.blob.core.windows.net/

  cloud-secure-config:
    tenant-id: *********

vault:
  uri: http://127.0.0.1:8200
  token: DO_NOT_COMMIT
  cache-time-mins: 60