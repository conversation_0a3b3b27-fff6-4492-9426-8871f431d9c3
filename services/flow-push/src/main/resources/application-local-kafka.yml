logging:
  level:
    ROOT: DEBUG
    org:
      apache:
        kafka: INFO
spring:
  application:
    name: flow-push
  output:
    ansi:
      enabled: ALWAYS
#  main:
#    web-application-type: none
server:
  port : 8090

flow-push-config:
  kstreams-config:
    application-id: flow-push
    bootstrap-servers: localhost:9092
    state-store-cache-max-bytes: ******** # 1 MB across all stream threads
    commit-interval-ms: 1000 # 1s
    state-store-dir: rocksdb-flow-push

  streams-builder-config:
    input-topic: flow-log-events
    state-store-name: blob-offsets
    punctuate-interval: 10s
    output-topic: flow-stream
    # Warning: for development only. DO NOT enable in production.
    devBuildIgnoreBlobOffsetsStore: true

  storage-accounts-config:
    inglogpoc:
      endpoint: https://inglogpoc.blob.core.windows.net/
      auth-config:
        azure-client-id: DO_NOT_COMMIT
        azure-client-secret: DO_NOT_COMMIT
        azure-tenant-id: DO_NOT_COMMIT

  cloud-secure-config:
    tenant-id: *********

vault:
  uri: http://127.0.0.1:8200
  token: DO_NOT_COMMIT
  cache-time-mins: 60