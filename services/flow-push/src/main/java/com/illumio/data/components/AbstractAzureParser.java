package com.illumio.data.components;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.illumio.data.IllumioCommonSecurityFlow;
import com.illumio.data.pojo.ParserResult;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public abstract class AbstractAzureParser<T> implements Parser<T> {
    private static final String RECORDS = "records";

    @SneakyThrows
    @Override
    public ParserResult parseFromInputStream(InputStream inputStream) {
        log.debug("Parsing flow log");
        List<IllumioCommonSecurityFlow> result = new ArrayList<>();
        long offset = 0L;
        try (JsonParser jsonParser = getJsonFactory().createParser(inputStream)) {
            while (true) {
                JsonNode jsonNode;
                try {
                    jsonNode = getObjectMapper().readTree(jsonParser);
                } catch (JsonParseException e) {
                    if (e.getMessage().startsWith("Unexpected character (',' (code 44))")) {
                        log.debug("Found ',', attempting to get next token");
                        jsonParser.nextToken();
                        continue;
                    } else if (e.getMessage().startsWith("Unexpected close marker ']'")) {
                        log.debug("Found end of file, found character ']'");
                        break;
                    } else {
                        log.warn("Couldn't parse json from stream. Completing at offset {}", offset, e);
                        break;
                    }
                }
                // TODO test?
                if (null == jsonNode) {
                    log.info("Completed processing flow logs from InputStream at offset {}", offset);
                    break;
                }
                // TODO test?
                if (!jsonNode.isObject()) {
                    log.debug("Expected object at json node {}", jsonNode);
                    continue;
                }
                ObjectNode objectNode = (ObjectNode) jsonNode;
                if (objectNode.has(RECORDS)) {
                    // start: {"records":[{<flow-log-object}...
                    JsonNode records = objectNode.get(RECORDS);
                    if (!records.isArray()) {
                        log.debug("Expected array of records at json node {}", records);
                        continue;
                    }
                    ArrayNode list = (ArrayNode) records;
                    for (int i = 0; i < list.size(); i++) {
                        JsonNode current = list.get(i);
                        JavaType javaType = getJavaType();
                        T flowRecord =
                                getObjectMapper().convertValue(current, javaType);
                        log.debug("Processing record new: {}", flowRecord);
                        result.addAll(processFlows(flowRecord));
                        // ending characters: ]}
                        offset = jsonParser.getCurrentLocation().getByteOffset() - 2;
                    }
                } else {
                    // start: {flow-log-object}...
                    JavaType javaType = getJavaType();
                    T flowRecord =
                            getObjectMapper().convertValue(objectNode, javaType);
                    log.debug("Processing record append: {}", flowRecord);
                    result.addAll(processFlows(flowRecord));
                    offset = jsonParser.getCurrentLocation().getByteOffset();
                }
            }
        }

        return ParserResult.builder().result(result).offset(offset).build();
    }

    public abstract JavaType getJavaType();

    public abstract JsonFactory getJsonFactory();

    public abstract ObjectMapper getObjectMapper();
}
