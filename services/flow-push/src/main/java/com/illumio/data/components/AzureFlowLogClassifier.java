package com.illumio.data.components;

import com.azure.storage.blob.BlobUrlParts;

public class AzureFlowLogClassifier {
    private static final String VNET_CONTAINER = "insights-logs-flowlogflowevent";
    private static final String NSG_CONTAINER = "insights-logs-networksecuritygroupflowevent";

    public enum FlowLogType {
        Vnet<PERSON>lowLog,
        NsgFlowLog,
        Unknown
    }

    public static FlowLogType classify(BlobUrlParts blobUrlParts) {
        String container = blobUrlParts.getBlobContainerName();
        return switch (container) {
            case VNET_CONTAINER -> FlowLogType.VnetFlowLog;
            case NSG_CONTAINER ->  FlowLogType.NsgFlowLog;
            default -> FlowLogType.Unknown;
        };
    }
}
