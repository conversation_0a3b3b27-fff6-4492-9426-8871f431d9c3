package com.illumio.data.components;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.illumio.data.IllumioCommonSecurityFlow;
import com.illumio.data.pojo.FlowTuple;
import com.illumio.data.pojo.VnetFlowRecord;

import lombok.*;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
@Getter
public class VnetFlowParser extends AbstractAzureParser<VnetFlowRecord> {
    private final JsonFactory jsonFactory;
    private final ObjectMapper objectMapper;
    private final FlowTupleParser flowTupleParser;
    private final VnetFlowTupleParser vnetFlowTupleParser;

    @Override
    public List<IllumioCommonSecurityFlow> processFlows(VnetFlowRecord vnetFlowRecord) {
        List<IllumioCommonSecurityFlow> result = new ArrayList<>();
        VnetFlowRecord.FlowRecords flowRecords = vnetFlowRecord.getFlowRecords();
        if (null == flowRecords) {
            log.debug("No flow records, returning empty list: {}", vnetFlowRecord);
            return result;
        }

        for (VnetFlowRecord.Flow flow : flowRecords.getFlows()) {
            for (VnetFlowRecord.FlowGroup flowGroup : flow.getFlowGroups()) {
                for (String flowTupleString : flowGroup.getFlowTuples()) {
                    FlowTuple flowTuple = flowTupleParser.fromString(flowTupleString);
                    IllumioCommonSecurityFlow illumioCommonSecurityFlow =
                            vnetFlowTupleParser.fromFlowTuple(flowTuple);
                    result.add(illumioCommonSecurityFlow);
                }
            }
        }
        return result;
    }

    @Override
    public JavaType getJavaType() {
        TypeFactory typeFactory = TypeFactory.defaultInstance();
        return typeFactory.constructType(VnetFlowRecord.class);
    }
}
