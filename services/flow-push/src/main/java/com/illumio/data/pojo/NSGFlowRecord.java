package com.illumio.data.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@Jacksonized
@JsonIgnoreProperties(ignoreUnknown = true)
public class NSGFlowRecord {
    private FlowProperties properties;

    @Data
    @Builder
    @Jacksonized
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FlowProperties {
        @Builder.Default private List<FlowRules> flows = new ArrayList<>();
        @JsonProperty("Version")
        private Integer version;
    }

    @Data
    @Builder
    @Jacksonized
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FlowRules {
        @Builder.Default private List<Flow> flows = new ArrayList<>();
    }

    @Data
    @Builder
    @Jacksonized
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Flow {
        @Builder.Default private List<String> flowTuples = new ArrayList<>();
    }
}
