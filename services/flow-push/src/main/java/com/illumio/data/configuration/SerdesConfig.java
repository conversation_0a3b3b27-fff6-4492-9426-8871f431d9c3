package com.illumio.data.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.illumio.data.IllumioCommonSecurityFlow;
import com.illumio.data.pojo.BlobCreatedEvent;

import com.illumio.data.pojo.BlobOffset;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.common.errors.SerializationException;
import org.apache.kafka.common.serialization.Deserializer;
import org.apache.kafka.common.serialization.Serde;
import org.apache.kafka.common.serialization.Serdes;
import org.apache.kafka.common.serialization.Serializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class SerdesConfig {
    private final ObjectMapper objectMapper;

    @Bean
    public Serde<List<BlobCreatedEvent>> listBlobCreatedEventSerde() {
        return new ListBlobCreatedSerde(objectMapper);
    }

    @Bean
    public Serde<BlobCreatedEvent> blobCreatedEventSerde() {
        Map<String, Object> serdeProps = new HashMap<>();
        final Serializer<BlobCreatedEvent> serializer = new JsonPOJOSerializer<>(objectMapper);
        serdeProps.put("JsonPOJOClass", BlobCreatedEvent.class);
        serializer.configure(serdeProps, false);
        final Deserializer<BlobCreatedEvent> deserializer = new JsonPOJODeserializer<>(objectMapper);
        serdeProps.put("JsonPOJOClass", BlobCreatedEvent.class);
        deserializer.configure(serdeProps, false);
        return Serdes.serdeFrom(serializer, deserializer);
    }

    @Bean
    public Serde<IllumioCommonSecurityFlow> illumioCommonSecurityFlowSerde() {
        Map<String, Object> serdeProps = new HashMap<>();
        final Serializer<IllumioCommonSecurityFlow> serializer = new JsonPOJOSerializer<>(objectMapper);
        serdeProps.put("JsonPOJOClass", IllumioCommonSecurityFlow.class);
        serializer.configure(serdeProps, false);
        final Deserializer<IllumioCommonSecurityFlow> deserializer = new JsonPOJODeserializer<>(objectMapper);
        serdeProps.put("JsonPOJOClass", IllumioCommonSecurityFlow.class);
        deserializer.configure(serdeProps, false);
        return Serdes.serdeFrom(serializer, deserializer);
    }

    @Bean
    public Serde<BlobOffset> blobOffsetSerde() {
        Map<String, Object> serdeProps = new HashMap<>();
        final Serializer<BlobOffset> serializer = new JsonPOJOSerializer<>(objectMapper);
        serdeProps.put("JsonPOJOClass", BlobOffset.class);
        serializer.configure(serdeProps, false);
        final Deserializer<BlobOffset> deserializer = new JsonPOJODeserializer<>(objectMapper);
        serdeProps.put("JsonPOJOClass", BlobOffset.class);
        deserializer.configure(serdeProps, false);
        return Serdes.serdeFrom(serializer, deserializer);
    }

    public static class JsonPOJOSerializer<T> implements Serializer<T> {
        private final ObjectMapper objectMapper;

        /**
         * Default constructor needed by Kafka
         */
        public JsonPOJOSerializer(ObjectMapper objectMapper) {
            this.objectMapper = objectMapper;
        }

        @Override
        public void configure(Map<String, ?> props, boolean isKey) {
        }

        @Override
        public byte[] serialize(String topic, T data) {
            if (data == null)
                return null;

            try {
                return objectMapper.writeValueAsBytes(data);
            } catch (Exception e) {
                throw new SerializationException("Error serializing JSON message", e);
            }
        }

        @Override
        public void close() {
        }

    }

    public static class JsonPOJODeserializer<T> implements Deserializer<T> {
        private ObjectMapper objectMapper;

        private Class<T> tClass;

        /**
         * Default constructor needed by Kafka
         */
        public JsonPOJODeserializer(ObjectMapper objectMapper) {
            this.objectMapper = objectMapper;
        }

        @SuppressWarnings("unchecked")
        @Override
        public void configure(Map<String, ?> props, boolean isKey) {
            tClass = (Class<T>) props.get("JsonPOJOClass");
        }

        @Override
        public T deserialize(String topic, byte[] bytes) {
            if (bytes == null)
                return null;

            T data;
            try {
                data = objectMapper.readValue(bytes, tClass);
            } catch (Exception e) {
                throw new SerializationException(e);
            }

            return data;
        }

        @Override
        public void close() {

        }
    }
}
