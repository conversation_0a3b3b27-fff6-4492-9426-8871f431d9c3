package com.illumio.data.configuration;

import static org.apache.kafka.clients.CommonClientConfigs.BOOTSTRAP_SERVERS_CONFIG;
import static org.apache.kafka.streams.StreamsConfig.*;

import lombok.RequiredArgsConstructor;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.streams.StreamsConfig;
import org.apache.kafka.streams.errors.LogAndContinueExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.EnableKafkaStreams;
import org.springframework.kafka.annotation.KafkaStreamsDefaultConfiguration;
import org.springframework.kafka.config.KafkaStreamsConfiguration;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Configuration
@EnableKafka
@EnableKafkaStreams
@RequiredArgsConstructor
public class KafkaStreamsConfig {
    private final FlowPushConfig flowPushConfig;

    @Bean(name = KafkaStreamsDefaultConfiguration.DEFAULT_STREAMS_CONFIG_BEAN_NAME)
    KafkaStreamsConfiguration kStreamsConfig() {
        Map<String, Object> props = new HashMap<>();
        props.put(APPLICATION_ID_CONFIG, flowPushConfig.getKStreamsConfig().getApplicationId());
        props.put(
                BOOTSTRAP_SERVERS_CONFIG,
                flowPushConfig.getKStreamsConfig().getBootstrapServers());

        if (null != flowPushConfig.getKStreamsConfig().getIsSasl()
                && Boolean.TRUE.equals(flowPushConfig.getKStreamsConfig().getIsSasl())) {
            props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            props.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            props.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    flowPushConfig.getKStreamsConfig().getSaslJaasConfig());
        }

        Optional.of(flowPushConfig)
                .map(FlowPushConfig::getKStreamsConfig)
                .map(FlowPushConfig.KStreamsConfig::getStateStoreCacheMaxBytes)
                .ifPresent(
                        stateStoreMaxCacheBytes ->
                                props.put(
                                        StreamsConfig.STATESTORE_CACHE_MAX_BYTES_CONFIG,
                                        stateStoreMaxCacheBytes));

        Optional.of(flowPushConfig)
                .map(FlowPushConfig::getKStreamsConfig)
                .map(FlowPushConfig.KStreamsConfig::getCommitIntervalMs)
                .ifPresent(
                        commitIntervalMs ->
                                props.put(
                                        StreamsConfig.COMMIT_INTERVAL_MS_CONFIG, commitIntervalMs));

        props.put(
                DEFAULT_DESERIALIZATION_EXCEPTION_HANDLER_CLASS_CONFIG,
                LogAndContinueExceptionHandler.class);

        return new KafkaStreamsConfiguration(props);
    }
}
