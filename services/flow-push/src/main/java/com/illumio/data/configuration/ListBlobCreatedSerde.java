package com.illumio.data.configuration;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.illumio.data.pojo.BlobCreatedEvent;
import org.apache.kafka.common.errors.SerializationException;
import org.apache.kafka.common.serialization.Deserializer;
import org.apache.kafka.common.serialization.Serde;
import org.apache.kafka.common.serialization.Serializer;

import java.io.IOException;
import java.util.List;

public class ListBlobCreatedSerde implements Serde<List<BlobCreatedEvent>> {

    private final ObjectMapper objectMapper;

    public ListBlobCreatedSerde(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public Serializer<List<BlobCreatedEvent>> serializer() {
        return new ListPojoSerializer(objectMapper);
    }

    @Override
    public Deserializer<List<BlobCreatedEvent>> deserializer() {
        return new ListPojoDeserializer(objectMapper);
    }

    private static class ListPojoSerializer implements Serializer<List<BlobCreatedEvent>> {

        private final ObjectMapper objectMapper;

        public ListPojoSerializer(ObjectMapper objectMapper) {
            this.objectMapper = objectMapper;
        }

        @Override
        public byte[] serialize(String topic, List<BlobCreatedEvent> data) {
            if (data == null) {
                return null;
            }
            try {
                return objectMapper.writeValueAsBytes(data);
            } catch (JsonProcessingException e) {
                throw new SerializationException("Error serializing List of POJOs", e);
            }
        }
    }

    private static class ListPojoDeserializer implements Deserializer<List<BlobCreatedEvent>> {

        private final ObjectMapper objectMapper;

        public ListPojoDeserializer(ObjectMapper objectMapper) {
            this.objectMapper = objectMapper;
        }

        @Override
        public List<BlobCreatedEvent> deserialize(String topic, byte[] data) {
            if (data == null) {
                return null;
            }
            try {
                JavaType type = objectMapper.getTypeFactory().constructParametricType(List.class, BlobCreatedEvent.class);
                List<BlobCreatedEvent> result = objectMapper.readValue(data, type);
                return result;
            } catch (IOException e) {
                throw new SerializationException("Error deserializing List of POJOs", e);
            }
        }
    }
}
