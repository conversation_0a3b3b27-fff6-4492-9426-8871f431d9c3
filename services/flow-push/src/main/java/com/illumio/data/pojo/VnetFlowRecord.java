package com.illumio.data.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@Jacksonized
@JsonIgnoreProperties(ignoreUnknown = true)
public class VnetFlowRecord {
    private String macAddress;
    private Integer flowLogVersion;
    private FlowRecords flowRecords;

    @Data
    @Builder
    @Jacksonized
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FlowRecords {
        private @Builder.Default List<Flow> flows = new ArrayList<>();
    }

    @Data
    @Builder
    @Jacksonized
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Flow {
        private @Builder.Default List<FlowGroup> flowGroups = new ArrayList<>();
    }

    @Data
    @Builder
    @Jacksonized
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FlowGroup {
        private @Builder.Default List<String> flowTuples = new ArrayList<>();
    }
}
