package com.illumio.data.kstreams;

import com.illumio.data.IllumioCommonSecurityFlow;
import com.illumio.data.components.*;
import com.illumio.data.configuration.FlowPushConfig;
import com.illumio.data.pojo.BlobCreatedEvent;
import com.illumio.data.pojo.BlobOffset;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.streams.processor.PunctuationType;
import org.apache.kafka.streams.processor.api.Processor;
import org.apache.kafka.streams.processor.api.ProcessorContext;
import org.apache.kafka.streams.processor.api.Record;
import org.apache.kafka.streams.state.KeyValueStore;

import java.util.List;

@Slf4j
@AllArgsConstructor
@Builder
public class LeftJoinAndUpdateProcessor
        implements Processor<String, List<BlobCreatedEvent>, String, IllumioCommonSecurityFlow> {
    private final BlobClientCache blobClientCache;
    private final NSGFlowParser nsgFlowParser;
    private final VnetFlowParser vnetFlowParser;
    private final FlowPushConfig flowPushConfig;

    private KeyValueStore<String, BlobOffset> blobOffsetsStore;
    private ProcessorContext<String, IllumioCommonSecurityFlow> context;

    @Override
    public void init(ProcessorContext<String, IllumioCommonSecurityFlow> context) {
        this.blobOffsetsStore =
                context.getStateStore(
                        flowPushConfig.getStreamsBuilderConfig().getStateStoreName());
        this.context = context;
        context.schedule(
                flowPushConfig.getStreamsBuilderConfig().getPunctuateInterval(),
                PunctuationType.WALL_CLOCK_TIME,
                new TTLPunctuator(blobOffsetsStore));
    }

    @Override
    public void process(Record<String, List<BlobCreatedEvent>> record) {
        BlobProcessor blobProcessor = BlobProcessor.builder()
                .flowPushConfig(flowPushConfig)
                .blobClientCache(blobClientCache)
                .blobOffsetsStore(blobOffsetsStore)
                .nsgFlowParser(nsgFlowParser)
                .vnetFlowParser(vnetFlowParser)
                .context(context)
                .record(record)
                .build();
        blobProcessor.process();
    }

    @Override
    public void close() {
        // NOOP
    }
}
