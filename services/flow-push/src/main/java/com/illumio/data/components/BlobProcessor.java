package com.illumio.data.components;

import com.azure.storage.blob.BlobUrlParts;
import com.azure.storage.blob.models.BlobRange;
import com.azure.storage.blob.models.BlobStorageException;
import com.azure.storage.blob.specialized.BlobInputStream;
import com.illumio.data.IllumioCommonSecurityFlow;
import com.illumio.data.configuration.FlowPushConfig;
import com.illumio.data.pojo.BlobCreatedEvent;
import com.illumio.data.pojo.BlobOffset;
import com.illumio.data.pojo.ParserResult;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.streams.processor.api.ProcessorContext;
import org.apache.kafka.streams.processor.api.Record;
import org.apache.kafka.streams.state.KeyValueStore;

import java.io.BufferedInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Optional;

/**
 * Blob Processor which parses a blob stream, and emits flow log(s) to the kafka streams context.
 * Handles blob appends by maintaining an offset store, which stores the latest read offset per blob.
 */
@Data
@Builder
@Slf4j
public class BlobProcessor {
    private final FlowPushConfig flowPushConfig;
    private final BlobClientCache blobClientCache;
    private final KeyValueStore<String, BlobOffset> blobOffsetsStore;
    private final NSGFlowParser nsgFlowParser;
    private final VnetFlowParser vnetFlowParser;
    private final ProcessorContext<String, IllumioCommonSecurityFlow> context;
    private final Record<String, List<BlobCreatedEvent>> record;

    public void process() {
        log.info("Processing record {}", record);
        if (null == record.key()) {
            log.warn("Skipping record due to null key {}", record);
            return;
        }
        try {
            Long currentOffset = getCurrentOffset();
            BlobUrlParts blobUrlParts = blobUrlParts();
            InputStream blobInputStream = getInputStream(currentOffset, blobUrlParts);
            ParserResult parserResult = parseBlob(blobInputStream, blobUrlParts);
            decoratedWithCloudSecureTenant(parserResult);
            forwardFlows(parserResult);
            updateBlobOffsetsStore(currentOffset, parserResult);
        } catch (Exception e) {
            log.warn("Skipping record {}", record, e);
        }
    }

    private Long getCurrentOffset() {
        long currentOffset;
        if (Boolean.TRUE.equals(
                flowPushConfig.getStreamsBuilderConfig().getDevBuildIgnoreBlobOffsetsStore())) {
            log.warn("Dev build: Ignoring blob offset store for {}", record.key());
            currentOffset = 0L;
        } else {
            BlobOffset blobOffset = blobOffsetsStore.get(record.key());
            currentOffset = null == blobOffset ? 0L : blobOffset.getOffset();
        }
        return currentOffset;
    }

    private BlobUrlParts blobUrlParts() {
        BlobUrlParts blobUrlParts;
        try {
            blobUrlParts = BlobUrlParts.parse(record.key());
        } catch (Exception e) {
            log.error(
                    "Couldn't parse record key for blob storage URL {}, skipping record {}",
                    record.key(),
                    record,
                    e);
            throw e;
        }
        return blobUrlParts;
    }

    private InputStream getInputStream(
            Long currentOffset,
            BlobUrlParts blobUrlParts) {
        log.info("Downloading blob {} from offset {}", record.key(), currentOffset);
        BlobInputStream blobInputStream;
        // TODO retry once if credentials are expired? by refetching credentials from vault?
        try {
            BlobRange blobRange = new BlobRange(currentOffset);
            blobInputStream =
                    blobClientCache
                            .get(blobUrlParts.getAccountName())
                            .getBlobContainerClient(blobUrlParts.getBlobContainerName())
                            .getBlobClient(blobUrlParts.getBlobName())
                            .openInputStream(blobRange, null);
        } catch (BlobStorageException e) {
            log.error("Couldn't read from blob storage for record {}", record, e);
            throw e;
        }

        return new BufferedInputStream(blobInputStream);
    }

    private ParserResult parseBlob(
            InputStream inputStream,
            BlobUrlParts blobUrlParts) {
        AzureFlowLogClassifier.FlowLogType flowLogType =
                AzureFlowLogClassifier.classify(blobUrlParts);
        log.info("Classified flow log as {}, record {}", flowLogType.name(), record);
        if (flowLogType.equals(AzureFlowLogClassifier.FlowLogType.NsgFlowLog)) {
            return nsgFlowParser.parseFromInputStream(inputStream);
        } else if (flowLogType.equals(AzureFlowLogClassifier.FlowLogType.VnetFlowLog)) {
            return vnetFlowParser.parseFromInputStream(inputStream);
        } else {
            log.warn("Unknown flow log type. Skipping record {}", record);
            return ParserResult.builder().build();
        }
    }

    private void decoratedWithCloudSecureTenant(ParserResult parserResult) {
        if (Optional.ofNullable(flowPushConfig.getCloudSecureConfig())
                .map(FlowPushConfig.CloudSecureConfig::getTenantId)
                .isEmpty()) {
            return;
        }
        List<IllumioCommonSecurityFlow> flows = parserResult.getResult();
        for (IllumioCommonSecurityFlow flow : flows) {
            flow.setTenantId(flowPushConfig.getCloudSecureConfig().getTenantId());
        }
    }

    // TODO add destination port, protocol, and action to key in ingestion and aggregation
    private void forwardFlows(ParserResult parserResult) {
        List<IllumioCommonSecurityFlow> flows = parserResult.getResult();
        flows.forEach(flow ->
                this.context.forward(
                        new Record<>(
                                flow.getSourceIP() + "_" + flow.getDestinationIP(),
                                flow,
                                record.timestamp()))
        );
    }

    private void updateBlobOffsetsStore(
            Long currentOffset,
            ParserResult parserResult) {
        long newOffset = currentOffset + parserResult.getOffset();
        if (newOffset > currentOffset) {
            Long currentTime = System.currentTimeMillis();
            BlobOffset newBlobOffset =
                    BlobOffset.builder().offset(newOffset).updatedTimestamp(currentTime).build();
            log.info("Updating blob offset to {} for blob {}", newOffset, record.key());
            blobOffsetsStore.put(record.key(), newBlobOffset);
        } else {
            log.debug("Not updating blob offset for blob {}", record.key());
        }
    }
}
