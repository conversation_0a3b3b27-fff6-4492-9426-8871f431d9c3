package com.illumio.data.pojo;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@JsonPropertyOrder({
    "timestamp",
    "sourceIp",
    "destinationIp",
    "sourcePort",
    "destinationPort",
    "protocol",
    "direction",
    "decision",
    "flowStatus",
    "packetsSent",
    "bytesSent",
    "packetsReceived",
    "bytesReceived"
})
public class FlowTuple {
    private Long timestamp;
    private String sourceIp;
    private String destinationIp;
    private Integer sourcePort;
    private Integer destinationPort;
    private String protocol;
    private String direction;
    private String decision;
    private String flowStatus;
    private Long packetsSent;
    private Long bytesSent;
    private Long packetsReceived;
    private Long bytesReceived;
}
