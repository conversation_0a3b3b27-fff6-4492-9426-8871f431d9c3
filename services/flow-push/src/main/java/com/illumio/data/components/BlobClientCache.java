package com.illumio.data.components;

import com.azure.core.credential.TokenCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.azure.identity.DefaultAzureCredentialBuilder;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.FlowPushConfig;
import com.illumio.data.pojo.VaultAzSecrets;
import lombok.RequiredArgsConstructor;

import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class BlobClientCache {

    private final Map<String, BlobServiceClient> sourceBlobServiceClients = new HashMap<>();
    private final FlowPushConfig flowPushConfig;
    private final VaultService vaultService;
    private final ObjectMapper objectMapper;

    public BlobServiceClient get(final String storageAccount) {
        if (!flowPushConfig.getStorageAccountsConfig().containsKey(storageAccount)) {
            throw new IllegalArgumentException(
                    "Storage account credentials are not configured for storageAccount="
                            + storageAccount);
        }

        BlobServiceClient client =
                sourceBlobServiceClients.computeIfAbsent(
                        storageAccount,
                        sa -> {
                            final FlowPushConfig.StorageAccountConfig storageAccountConfig =
                                    flowPushConfig.getStorageAccountsConfig().get(sa);

                            BlobServiceClientBuilder blobServiceClientBuilder =
                                    new BlobServiceClientBuilder()
                                            .endpoint(storageAccountConfig.getEndpoint());

                            if (Optional.ofNullable(storageAccountConfig.getAuthConfig())
                                    .map(
                                            FlowPushConfig.AzureAuthConfig
                                                    ::getIsVaultStoredClientCredentials)
                                    .orElse(false)) {
                                TokenCredential clientSecretCredentials =
                                        fetchCredentialsFromVault(storageAccountConfig);
                                blobServiceClientBuilder.credential(clientSecretCredentials);
                            } else if (Optional.ofNullable(storageAccountConfig.getAuthConfig())
                                    .map(FlowPushConfig.AzureAuthConfig::getIsConnectionString)
                                    .orElse(false)) {
                                blobServiceClientBuilder.connectionString(
                                        storageAccountConfig.getAuthConfig().getConnectionString());
                            } else {
                                final TokenCredential storageAccountCredential =
                                        new DefaultAzureCredentialBuilder().build();
                                blobServiceClientBuilder.credential(storageAccountCredential);
                            }
                            return blobServiceClientBuilder.buildClient();
                        });
        return client;
    }

    @SneakyThrows
    private TokenCredential fetchCredentialsFromVault(
            FlowPushConfig.StorageAccountConfig storageAccountConfig) {
        Map<String, Object> config =
                vaultService
                        .getSecretWithCaching(storageAccountConfig.getAuthConfig().getVaultPath())
                        .block();
        String serializedCredentials = (String) config.getOrDefault("value", "");
        VaultAzSecrets vaultAzSecrets =
                objectMapper.readValue(serializedCredentials, VaultAzSecrets.class);
        TokenCredential clientSecretCredentials =
                new ClientSecretCredentialBuilder()
                        .clientId(vaultAzSecrets.getClientId())
                        .clientSecret(vaultAzSecrets.getSecretKey())
                        .tenantId(vaultAzSecrets.getTenantId())
                        .build();

        return clientSecretCredentials;
    }
}
