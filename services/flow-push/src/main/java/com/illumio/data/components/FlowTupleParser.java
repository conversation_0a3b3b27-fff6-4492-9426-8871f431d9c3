package com.illumio.data.components;

import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import com.illumio.data.pojo.FlowTuple;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class FlowTupleParser {
    private final CsvMapper csvMapper;

    @SneakyThrows
    public FlowTuple fromString(String flowTupleString) {
        CsvSchema csvSchema = csvMapper.schemaFor(FlowTuple.class).withoutHeader();

        FlowTuple tuple =
                csvMapper.readerFor(FlowTuple.class).with(csvSchema).readValue(flowTupleString);
        return tuple;
    }
}
