package com.illumio.data.kstreams;

import com.illumio.data.IllumioCommonSecurityFlow;
import com.illumio.data.components.BlobClientCache;
import com.illumio.data.components.NSGFlowParser;
import com.illumio.data.components.VnetFlowParser;
import com.illumio.data.configuration.FlowPushConfig;
import com.illumio.data.pojo.BlobCreatedEvent;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.streams.processor.api.Processor;
import org.apache.kafka.streams.processor.api.ProcessorSupplier;
import org.apache.kafka.streams.state.StoreBuilder;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Set;

@Configuration
@RequiredArgsConstructor
@Slf4j
public class LeftJoinAndUpdateProcessorSupplier
        implements ProcessorSupplier<
                String, List<BlobCreatedEvent>, String, IllumioCommonSecurityFlow> {
    private final FlowPushConfig flowPushConfig;
    private final BlobClientCache blobClientCache;
    private final NSGFlowParser NSGFlowParser;
    private final VnetFlowParser vnetFlowParser;

    @Override
    public Processor<String, List<BlobCreatedEvent>, String, IllumioCommonSecurityFlow> get() {
        return LeftJoinAndUpdateProcessor.builder()
                .blobClientCache(blobClientCache)
                .nsgFlowParser(NSGFlowParser)
                .vnetFlowParser(vnetFlowParser)
                .flowPushConfig(flowPushConfig)
                .build();
    }

    @Override
    public Set<StoreBuilder<?>> stores() {
        return ProcessorSupplier.super.stores();
    }
}
