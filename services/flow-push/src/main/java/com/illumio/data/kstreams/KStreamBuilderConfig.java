package com.illumio.data.kstreams;

import com.illumio.data.IllumioCommonSecurityFlow;
import com.illumio.data.configuration.FlowPushConfig;
import com.illumio.data.pojo.BlobCreatedEvent;

import com.illumio.data.pojo.BlobOffset;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.common.serialization.Serde;
import org.apache.kafka.common.serialization.Serdes;
import org.apache.kafka.streams.StreamsBuilder;
import org.apache.kafka.streams.kstream.*;
import org.apache.kafka.streams.state.Stores;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@RequiredArgsConstructor
@Slf4j
public class KStreamBuilderConfig {
    private final FlowPushConfig flowPushConfig;
    private final Serde<List<BlobCreatedEvent>> listBlobCreatedEventSerde;
    private final Serde<IllumioCommonSecurityFlow> illumioCommonSecurityFlowSerde;
    private final LeftJoinAndUpdateProcessorSupplier leftJoinAndUpdateProcessorSupplier;
    private final Serde<BlobOffset> blobOffsetSerde;

    @Bean
    public KStream<String, List<BlobCreatedEvent>> stream(StreamsBuilder kStreamBuilder) {
        KStream<String, List<BlobCreatedEvent>> kStream =
                kStreamBuilder.stream(
                        flowPushConfig.getStreamsBuilderConfig().getInputTopic(),
                        Consumed.with(Serdes.String(), listBlobCreatedEventSerde));

        kStreamBuilder.addStateStore(
                Stores.keyValueStoreBuilder(
                        Stores.inMemoryKeyValueStore(
                                flowPushConfig.getStreamsBuilderConfig().getStateStoreName()),
                        Serdes.String(),
                        blobOffsetSerde));

        KStream<String, IllumioCommonSecurityFlow> flowKStream =
                kStream.process(
                        leftJoinAndUpdateProcessorSupplier,
                        flowPushConfig.getStreamsBuilderConfig().getStateStoreName());

        flowKStream.to(
                flowPushConfig.getStreamsBuilderConfig().getOutputTopic(),
                Produced.with(Serdes.String(), illumioCommonSecurityFlowSerde));
        return kStream;
    }
}
