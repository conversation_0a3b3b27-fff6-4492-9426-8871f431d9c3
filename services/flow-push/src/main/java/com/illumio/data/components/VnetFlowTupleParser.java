package com.illumio.data.components;

import com.illumio.data.IllumioCommonSecurityFlow;
import com.illumio.data.pojo.FlowTuple;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Component
public class VnetFlowTupleParser {
    public IllumioCommonSecurityFlow fromFlowTuple(FlowTuple flowTuple) {
        IllumioCommonSecurityFlow.IllumioCommonSecurityFlowBuilder builder = IllumioCommonSecurityFlow.builder()
                .StartTime(
                        Optional.ofNullable(flowTuple.getTimestamp())
                                .map(this::convertToIso8601)
                                .orElse(null))
                .EndTime(Optional.ofNullable(flowTuple.getTimestamp())
                        .map(this::convertToIso8601)
                        .orElse(null))
                .SourceIP(flowTuple.getSourceIp())
                .SourcePort(flowTuple.getSourcePort())
                .DestinationIP(flowTuple.getDestinationIp())
                .DestinationPort(flowTuple.getDestinationPort())
                .Protocol(vnetProtocol(flowTuple))
                .SentBytes(flowTuple.getBytesSent())
                .ReceivedBytes(flowTuple.getBytesReceived())
                .DeviceAction(vnetDecision(flowTuple.getDecision()));
        // TODO switch direction if "f.Src.Port() < f.Dst.Port()"
        return builder.build();
    }

    private String vnetDecision(String decision) {
        return switch (decision) {
            case "D" -> "deny";
            default -> "allow";
        };
    }

    private String vnetProtocol(FlowTuple flowTuple) {
        return switch (flowTuple.getProtocol()) {
            case "6" -> "tcp";
            case "17" -> "udp";
            default -> null;
        };
    }

    private String nsgDirection(String direction) {
        return switch (direction) {
            case "I" -> "inbound";
            case "O" -> "outbound";
            default -> null;
        };
    }

    private String nsgStatus(String status) {
        return switch (status) {
            case "B" -> "begin";
            case "C" -> "continue";
            case "E" -> "end";
            case "D" -> "unknown";
            default -> null;
        };
    }

    private String convertToIso8601(Long epochMillis) {
        String iso8601 = Instant.ofEpochMilli(epochMillis)
                .atZone(ZoneId.of("UTC"))
                .format(DateTimeFormatter.ISO_INSTANT);
        return iso8601;
    }
}
