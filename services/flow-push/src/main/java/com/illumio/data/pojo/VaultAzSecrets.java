package com.illumio.data.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
@JsonIgnoreProperties(ignoreUnknown = true)
public class VaultAzSecrets {
    @JsonProperty("tenant_id")
    private final String tenantId;
    @JsonProperty("client_id")
    private final String clientId;
    @JsonProperty("secret_key")
    private final String secretKey;
}
