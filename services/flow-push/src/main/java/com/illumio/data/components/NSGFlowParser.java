package com.illumio.data.components;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.illumio.data.IllumioCommonSecurityFlow;
import com.illumio.data.pojo.FlowTuple;
import com.illumio.data.pojo.NSGFlowRecord;

import lombok.*;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
@Getter
public class NSGFlowParser extends AbstractAzureParser<NSGFlowRecord> {
    private final JsonFactory jsonFactory;
    private final ObjectMapper objectMapper;
    private final FlowTupleParser flowTupleParser;
    private final NSGFlowTupleParser NSGFlowTupleParser;

    @Override
    public List<IllumioCommonSecurityFlow> processFlows(NSGFlowRecord nsgFlowRecord) {
        List<IllumioCommonSecurityFlow> result = new ArrayList<>();
        NSGFlowRecord.FlowProperties flowProperties = nsgFlowRecord.getProperties();
        if (null == flowProperties) {
            log.debug("No flow properties, returning empty list: {}", nsgFlowRecord);
            return result;
        }

        if (flowProperties.getVersion() != 2) {
            log.warn(
                    "Found a flow low of version {}, can only support version 2. Ignoring record {}",
                    flowProperties.getVersion(),
                    nsgFlowRecord);
            return result;
        }

        for (NSGFlowRecord.FlowRules flowRules : flowProperties.getFlows()) {
            for (NSGFlowRecord.Flow flow : flowRules.getFlows()) {
                for (String flowTupleString : flow.getFlowTuples()) {
                    FlowTuple flowTuple = flowTupleParser.fromString(flowTupleString);
                    IllumioCommonSecurityFlow illumioCommonSecurityFlow =
                            NSGFlowTupleParser.fromFlowTuple(flowTuple);
                    result.add(illumioCommonSecurityFlow);
                }
            }
        }
        return result;
    }

    @Override
    public JavaType getJavaType() {
        TypeFactory typeFactory = TypeFactory.defaultInstance();
        return typeFactory.constructType(NSGFlowRecord.class);
    }
}
