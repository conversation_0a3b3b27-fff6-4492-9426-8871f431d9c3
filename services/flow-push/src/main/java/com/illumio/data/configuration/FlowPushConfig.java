package com.illumio.data.configuration;

import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

@Configuration
@ConfigurationProperties(prefix = "flow-push-config")
@Getter
@Setter
public class FlowPushConfig {
    private KStreamsConfig kStreamsConfig;
    private StreamsBuilderConfig streamsBuilderConfig;
    private final Map<String, StorageAccountConfig> storageAccountsConfig = new HashMap<>();
    private CloudSecureConfig cloudSecureConfig;

    @Configuration
    @Getter
    @Setter
    public static class KStreamsConfig {
        private String applicationId;
        private String bootstrapServers;
        private Long stateStoreCacheMaxBytes;
        private Integer commitIntervalMs;
        private Boolean isSasl = false;
        private String saslJaasConfig;
    }

    @Configuration
    @Getter
    @Setter
    public static class StreamsBuilderConfig {
        private String inputTopic;
        private String stateStoreName;
        private Duration punctuateInterval = Duration.ofMinutes(5);
        private String outputTopic;
        private Boolean devBuildIgnoreBlobOffsetsStore = false;
    }

    @Configuration
    @Getter
    @Setter
    public static class AzureAuthConfig {
        private Boolean isProvidedClientCredentials;
        private String azureClientId;
        private String azureClientSecret;
        private String azureTenantId;
        private Boolean isManagedIdentity;
        private String managedIdentityClientId;
        private Boolean isWorkloadIdentity;
        private WorkloadIdentity workloadIdentity;
        private Boolean isConnectionString;
        private String connectionString;
        private Boolean isVaultStoredClientCredentials;
        private String vaultPath;
    }

    @Configuration
    @Getter
    @Setter
    public static class WorkloadIdentity {
        private String clientId;
        private String tenantId;
        private String tokenFilePath;
    }

    @Configuration
    @Getter
    @Setter
    public static class StorageAccountConfig {
        private String endpoint;
        private AzureAuthConfig authConfig;
    }

    @Configuration
    @Getter
    @Setter
    public static class CloudSecureConfig {
        private String tenantId;
    }
}
