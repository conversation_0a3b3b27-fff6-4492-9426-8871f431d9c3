package com.illumio.data.kstreams;

import com.illumio.data.pojo.BlobOffset;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.streams.KeyValue;
import org.apache.kafka.streams.processor.Punctuator;
import org.apache.kafka.streams.processor.api.RecordMetadata;
import org.apache.kafka.streams.state.KeyValueIterator;
import org.apache.kafka.streams.state.KeyValueStore;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
public class TTLPunctuator implements Punctuator {
    private final KeyValueStore<String, BlobOffset> blobOffsetsStore;

    @Override
    public void punctuate(long timestamp) {
        log.debug("Punctuating at time {}", prettyPrintTime(timestamp));
        //  TODO make configurable
        Duration maxAge = Duration.ofMinutes(1);
        final long cutoff = timestamp - maxAge.toMillis();

        try (final KeyValueIterator<String, BlobOffset> all = blobOffsetsStore.all()) {
            while (all.hasNext()) {
                final KeyValue<String, BlobOffset> record = all.next();
                if (record.value != null && record.value.getUpdatedTimestamp() < cutoff) {
                    log.info(
                            "Deleting key {} since timestamp {} is before cutoff {}",
                            record.key,
                            prettyPrintTime(record.value.getUpdatedTimestamp()),
                            prettyPrintTime(cutoff));
                    blobOffsetsStore.delete(record.key);
                }
            }
        }
    }

    private String prettyPrintTime(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = timestamp + "/" + localDateTime.format(formatter);
        return formattedDateTime;
    }
}
