package com.illumio.data.components;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.illumio.data.pojo.ParserResult;

import lombok.SneakyThrows;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

class VnetFlowParserTest {
    JsonFactory jsonFactory;
    ObjectMapper objectMapper;
    CsvMapper csvMapper;
    VnetFlowParser vnetFlowParser;
    FlowTupleParser flowTupleParser;
    VnetFlowTupleParser vnetFlowTupleParser;

    @BeforeEach
    void init() {
        jsonFactory = new JsonFactory();
        objectMapper = new ObjectMapper();
        csvMapper = new CsvMapper();
        flowTupleParser = new FlowTupleParser(csvMapper);
        vnetFlowTupleParser = new VnetFlowTupleParser();
        vnetFlowParser = new VnetFlowParser(jsonFactory, objectMapper, flowTupleParser, vnetFlowTupleParser);
    }

    // test properly formatted json
    @Test
    @SneakyThrows
    void properJson() {
        try (InputStream inputStream =
                this.getClass()
                        .getClassLoader()
                        .getResourceAsStream("test-files/vnet/1-vnet-create.json")) {
            ParserResult actual = vnetFlowParser.parseFromInputStream(inputStream);
            assertNotNull(actual);
            assertEquals(2, actual.getResult().size());
            assertEquals(857, actual.getOffset());
        }
    }

    // test partial json no comma
    // without - {"records":[
    // starting with { <flow log object here> }
    @Test
    @SneakyThrows
    void startsWithJsonObject() {
        try (InputStream inputStream =
                     this.getClass()
                             .getClassLoader()
                             .getResourceAsStream("test-files/vnet/2-vnet-start-object.json")) {
            ParserResult actual = vnetFlowParser.parseFromInputStream(inputStream);
            assertNotNull(actual);
            assertEquals(2, actual.getResult().size());
            assertEquals(845, actual.getOffset());
        }
    }

    // test partial with comma
    // ex. ,{ <flow log object here }
    @Test
    @SneakyThrows
    void startsWithComma() {
        try (InputStream inputStream =
                this.getClass()
                        .getClassLoader()
                        .getResourceAsStream("test-files/vnet/3-vnet-start-comma.json")) {
            ParserResult actual = vnetFlowParser.parseFromInputStream(inputStream);
            assertNotNull(actual);
            assertEquals(2, actual.getResult().size());
            assertEquals(846, actual.getOffset());
        }
    }

    // test last two bytes
    // ex ]}
    @Test
    @SneakyThrows
    void startsWithEndArray() {
        String json = "]}";
        InputStream inputStream = new ByteArrayInputStream(json.getBytes());
        ParserResult actual = vnetFlowParser.parseFromInputStream(inputStream);
        assertNotNull(actual);
        assertEquals(0, actual.getResult().size());
        assertEquals(0, actual.getOffset());
    }

    @Test
    @SneakyThrows
    void multipleFlows() {
        try (InputStream inputStream =
                this.getClass()
                        .getClassLoader()
                        .getResourceAsStream("test-files/vnet/4-vnet-multiple-flows.json")) {
            ParserResult actual = vnetFlowParser.parseFromInputStream(inputStream);
            assertNotNull(actual);
            assertEquals(16, actual.getResult().size());
            assertEquals(5357, actual.getOffset());
        }
    }

    @Test
    @SneakyThrows
    void multipleFlowsStartsWithObject() {
        try (InputStream inputStream =
                     this.getClass()
                             .getClassLoader()
                             .getResourceAsStream("test-files/vnet/5-vnet-multiple-flows-start-object.json")) {
            ParserResult actual = vnetFlowParser.parseFromInputStream(inputStream);
            assertNotNull(actual);
            assertEquals(16, actual.getResult().size());
            assertEquals(5345, actual.getOffset());
        }
    }

    @Test
    @SneakyThrows
    void edgeCaseEmptyJson() {
        String json = "{\"records\":[{}]}";

        InputStream inputStream = new ByteArrayInputStream(json.getBytes());
        ParserResult actual = vnetFlowParser.parseFromInputStream(inputStream);
        assertNotNull(actual);
        assertEquals(0, actual.getResult().size());
        assertEquals(14, actual.getOffset());
    }
}
