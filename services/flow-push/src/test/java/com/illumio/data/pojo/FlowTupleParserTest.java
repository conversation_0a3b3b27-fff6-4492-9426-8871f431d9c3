package com.illumio.data.pojo;

import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.illumio.data.components.FlowTupleParser;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class FlowTupleParserTest {
    CsvMapper csvMapper;
    FlowTupleParser flowTupleParser;

    @BeforeEach
    void init() {
        csvMapper = new CsvMapper();
        flowTupleParser = new FlowTupleParser(csvMapper);
    }

    @Test
    void testVnetTuple() {
        String tuple = "1712844006000,168.196.49.235,10.0.0.4,45358,23,6,I,D,NX,0,0,0,0";
        FlowTuple flowTuple = flowTupleParser.fromString(tuple);
        assertNotNull(flowTuple);
    }

    @Test
    void testNSGTuple() {
        String tuple = "1726006962,10.0.1.8,20.60.168.70,46822,443,T,O,A,B,,,,";
        FlowTuple flowTuple = flowTupleParser.fromString(tuple);
        assertNotNull(flowTuple);

        tuple = "1726006968,10.0.1.8,20.60.168.70,46822,443,T,O,A,E,10,2040,0,0";
        flowTuple = flowTupleParser.fromString(tuple);
        assertNotNull(flowTuple);

        tuple = "1691695412,10.0.1.6,10.0.2.4,54836,8080,T,I,A,E,1,78,0,0";
        flowTuple = flowTupleParser.fromString(tuple);
        assertNotNull(flowTuple);
    }
}
