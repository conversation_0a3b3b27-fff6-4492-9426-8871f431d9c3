package com.illumio.data.components;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;

import com.illumio.data.pojo.ParserResult;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

class NSGFlowParserTest {
    JsonFactory jsonFactory;
    ObjectMapper objectMapper;
    CsvMapper csvMapper;
    NSGFlowParser NSGFlowParser;
    FlowTupleParser flowTupleParser;
    NSGFlowTupleParser NSGFlowTupleParser;

    @BeforeEach
    void init() {
        jsonFactory = new JsonFactory();
        objectMapper = new ObjectMapper();
        csvMapper = new CsvMapper();
        flowTupleParser = new FlowTupleParser(csvMapper);
        NSGFlowTupleParser = new NSGFlowTupleParser();
        NSGFlowParser =
                new NSGFlowParser(
                        jsonFactory, objectMapper, flowTupleParser, NSGFlowTupleParser);
    }

    // test properly formatted json
    @Test
    @SneakyThrows
    void properJson() {
        try (InputStream inputStream =
                this.getClass().getClassLoader().getResourceAsStream("test-files/nsg/1-nsg.json")) {
            ParserResult actual = NSGFlowParser.parseFromInputStream(inputStream);
            assertNotNull(actual);
            assertEquals(2, actual.getResult().size());
            assertEquals(634, actual.getOffset());
        }
    }

    // test partial json no comma
    // without - {"records":[
    // starting with { <flow log object here> }
    @Test
    @SneakyThrows
    void startsWithJsonObject() {
        try (InputStream inputStream =
                this.getClass()
                        .getClassLoader()
                        .getResourceAsStream("test-files/nsg/2-nsg-start-object.json")) {
            ParserResult actual = NSGFlowParser.parseFromInputStream(inputStream);
            assertNotNull(actual);
            assertEquals(2, actual.getResult().size());
            assertEquals(622, actual.getOffset());
        }
    }

    // test partial with comma
    // ex. ,{ <flow log object here }
    @Test
    @SneakyThrows
    void startsWithComma() {
        try (InputStream inputStream =
                this.getClass()
                        .getClassLoader()
                        .getResourceAsStream("test-files/nsg/3-nsg-start-comma.json")) {
            ParserResult actual = NSGFlowParser.parseFromInputStream(inputStream);
            assertNotNull(actual);
            assertEquals(2, actual.getResult().size());
            assertEquals(623, actual.getOffset());
        }
    }

    // test last two bytes
    // ex ]}
    @Test
    void startsWithEndArray() {
        String json = "]}";
        InputStream inputStream = new ByteArrayInputStream(json.getBytes());
        ParserResult actual = NSGFlowParser.parseFromInputStream(inputStream);
        assertNotNull(actual);
        assertEquals(0, actual.getResult().size());
        assertEquals(0, actual.getOffset());
    }

    @Test
    @SneakyThrows
    void multipleFlows() {
        try (InputStream inputStream =
                this.getClass()
                        .getClassLoader()
                        .getResourceAsStream("test-files/nsg/4-nsg-multiple-flows.json")) {
            ParserResult actual = NSGFlowParser.parseFromInputStream(inputStream);
            assertNotNull(actual);
            assertEquals(4, actual.getResult().size());
            assertEquals(1756, actual.getOffset());
        }
    }

    @Test
    @SneakyThrows
    void multipleFlowsStartsWithObjectWithArray() {
        try (InputStream inputStream =
                this.getClass()
                        .getClassLoader()
                        .getResourceAsStream("test-files/nsg/5-nsg-multiple-flows-start-object.json")) {
            ParserResult actual = NSGFlowParser.parseFromInputStream(inputStream);
            assertNotNull(actual);
            assertEquals(4, actual.getResult().size());
            assertEquals(1744, actual.getOffset());
        }
    }

    @Test
    void edgeCaseEmptyJson() {
        String json = "{\"records\":[{}]}";

        InputStream inputStream = new ByteArrayInputStream(json.getBytes());
        ParserResult actual = NSGFlowParser.parseFromInputStream(inputStream);
        assertNotNull(actual);
        assertEquals(0, actual.getResult().size());
        assertEquals(14, actual.getOffset());
    }
}
