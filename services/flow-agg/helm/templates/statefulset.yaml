apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "flow-agg.fullname" . }}
spec:
  selector:
    matchLabels:
      {{- include "flow-agg.selectorLabels" . | nindent 8 }}
  serviceName: "flow-agg"
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}

  template:
    metadata:
      annotations:
      {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.podMetricsAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "flow-agg.selectorLabels" . | nindent 8 }}
        {{- if .Values.extraLabels }}
          {{ toYaml .Values.extraLabels | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 100
              podAffinityTerm:
                labelSelector:
                  matchExpressions:
                    - key: appkind
                      operator: In
                      values: [ kstream ]
                topologyKey: kubernetes.io/hostname
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repositoryBase }}{{ .Values.image.repositoryName }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          - name: POD_INDEX
            valueFrom:
              fieldRef:
                fieldPath: metadata.labels['app.kubernetes.io/pod-index']
          envFrom:
            - secretRef:
                name: {{ include "flow-agg.fullname" . }}-env-secrets
          ports:
          {{- range .Values.ports }}
            - name: {{ .name }}
              containerPort: {{ .port }}
              protocol: TCP
          {{- end }}
          {{- if .Values.probes.liveness.enabled }}
          livenessProbe:
            httpGet:
              path: {{.Values.probes.liveness.path}}
              port: admin
            failureThreshold: {{.Values.probes.liveness.threshold}}
            periodSeconds: {{.Values.probes.liveness.periodSec}}
          {{- end }}
          {{- if .Values.probes.startup.enabled }}
          startupProbe:
            httpGet:
              path: {{.Values.probes.startup.path}}
              port: admin
            failureThreshold: {{.Values.probes.startup.threshold}}
            periodSeconds: {{.Values.probes.startup.periodSec}}
          {{end}}

          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: data
              mountPath: /rocksdb-dir
            - name: {{ include "flow-agg.fullname" . }}-env-configmap
              mountPath: /var/resources/
      volumes:
        - name: {{ include "flow-agg.fullname" . }}-env-configmap
          configMap:
            name: {{ include "flow-agg.fullname" . }}-env-configmap
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}

  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes: [ "ReadWriteOnce" ]
        storageClassName: {{ .Values.storageClass }}
        resources:
          requests:
            storage: {{ .Values.diskSize }}
