apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "flow-agg.fullname" . }}-env-configmap
  labels:
    {{- include "flow-agg.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
    level:
      ROOT: INFO
      org:
        apache:
          kafka: INFO
    spring:
      application:
        name: flow-agg
      output:
        ansi:
          enabled: ALWAYS
    server:
      port : {{.Values.server.port}}

    flow-agg-config:
      kstreams-config:
        application-id: {{.Values.flowAggConfig.kstreamsConfig.applicationId}}
        bootstrap-servers: {{.Values.flowAggConfig.kstreamsConfig.bootstrapServers}}
        state-store-cache-max-bytes: {{.Values.flowAggConfig.kstreamsConfig.stateStoreCacheMaxBytes}}
        commit-interval-ms: {{.Values.flowAggConfig.kstreamsConfig.commitIntervalMs}}
        state-store-dir: {{.Values.flowAggConfig.kstreamsConfig.stateStoreDir}}
        group-id: {{.Values.flowAggConfig.kstreamsConfig.groupId}}
        is-sasl: {{.Values.flowAggConfig.kstreamsConfig.isSasl}}

      streams-builder-config:
        input-topic: {{.Values.flowAggConfig.streamsBuilderConfig.inputTopic}}
        window-size: {{.Values.flowAggConfig.streamsBuilderConfig.windowSize}}
        state-store-name: {{.Values.flowAggConfig.streamsBuilderConfig.stateStoreName}}
        state-store-retention: {{.Values.flowAggConfig.streamsBuilderConfig.stateStoreRetention}}
        punctuate-interval: {{.Values.flowAggConfig.streamsBuilderConfig.punctuateInterval}}
        output-topic: {{.Values.flowAggConfig.streamsBuilderConfig.outputTopic}}

    eventhub:
      password: {{.Values.eventhub.password}}


