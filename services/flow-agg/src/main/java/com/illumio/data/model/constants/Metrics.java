package com.illumio.data.model.constants;

import lombok.Getter;

@Getter
public enum Metrics {
    FLOW_AGGREGATION_COUNT_METRIC("flow_aggregation_count_total"),
    FLOW_AGGREGATION_DURATION_METRIC("flow_aggregation_duration_seconds"),
    FLOW_AGGREGATION_SIZE_METRIC("flow_aggregation_size_bytes"),
    FLOW_PROCESSING_LATENCY_METRIC("flow_processing_latency_seconds");

    private final String value;
    Metrics(String value) {
        this.value = value;
    }
}
