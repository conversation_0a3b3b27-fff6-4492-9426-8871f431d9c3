package com.illumio.data.component;

import com.illumio.data.model.FlowValue;
import com.illumio.data.service.MetricRecordService;
import com.illumio.data.utils.OverwriteNullFields;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FlowAggregation {

    private static final OverwriteNullFields overwriteNullFields = new OverwriteNullFields();

    public static <T> FlowValue aggregate(T key, FlowValue value, FlowValue aggregate, MeterRegistry meterRegistry, MetricRecordService metricRecordService) {
        Timer.Sample timer = Timer.start(meterRegistry);
        String tenantId = value.getIllumioTenantId();

        try {
            log.debug("Aggregating flow: {}", value);

            if (aggregate.getFlowCount() == null || aggregate.getFlowCount() == 0) {
                aggregate.setFlowCount(1L);
            } else if (value.getFlowCount() != null) {
                aggregate.setFlowCount(aggregate.getFlowCount() + value.getFlowCount());
            } else {
                aggregate.setFlowCount(aggregate.getFlowCount() + 1L);
            }

            if (value.getSentBytes() != null) {
                aggregate.setSentBytes(aggregate.getSentBytes() + value.getSentBytes());
            }
            if (value.getReceivedBytes() != null) {
                aggregate.setReceivedBytes(aggregate.getReceivedBytes() + value.getReceivedBytes());
            }
            if (value.getPacketsSent() != null) {
                aggregate.setPacketsSent(aggregate.getPacketsSent() + value.getPacketsSent());
            }
            if (value.getPacketsReceived() != null) {
                aggregate.setPacketsReceived(
                        aggregate.getPacketsReceived() + value.getPacketsReceived());
            }
            if (aggregate.getStartTime() == null && value.getStartTime() != null) {
                aggregate.setStartTime(value.getStartTime());
                aggregate.setPort(value.getPort());
            }
            if (value.getStartTime() != null
                    && value.getStartTime().isBefore(aggregate.getStartTime())) {
                aggregate.setStartTime(value.getStartTime());
            }
            if (aggregate.getEndTime() == null && value.getEndTime() != null) {
                aggregate.setEndTime(value.getEndTime());
            }
            if (value.getEndTime() != null && value.getEndTime().isAfter(aggregate.getEndTime())) {
                aggregate.setEndTime(value.getEndTime());
            }
            if(aggregate.getIllumioTenantId() == null && value.getIllumioTenantId() != null) {
                aggregate.setIllumioTenantId(value.getIllumioTenantId());
            }

            overwriteNullFields.replaceNullValuesInAggregate(value, aggregate);
            
            // Record successful aggregation metrics
            if (metricRecordService != null && meterRegistry != null) {
                metricRecordService.recordTimeAndCountMetrics(
                    meterRegistry, timer, "success", 
                    "Time taken for flow aggregation", tenantId);
            }
            
            log.debug("Aggregated flow is: {}", aggregate);
            
        } catch (Exception e) {
            log.error("Error during flow aggregation for tenant {}: {}", tenantId, e.getMessage(), e);
            
            // Record error metrics
            if (metricRecordService != null && meterRegistry != null) {
                metricRecordService.recordTimeAndCountMetrics(
                    meterRegistry, timer, "error", 
                    "Time taken for failed flow aggregation", tenantId);
            }
            
            throw e;
        }

        return aggregate;
    }
    
    // Backward compatibility method
    public static <T> FlowValue aggregate(T key, FlowValue value, FlowValue aggregate) {
        return aggregate(key, value, aggregate, null, null);
    }
}
