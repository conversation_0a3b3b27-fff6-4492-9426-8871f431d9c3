package com.illumio.data.kstreams;

import static org.apache.kafka.clients.CommonClientConfigs.BOOTSTRAP_SERVERS_CONFIG;
import static org.apache.kafka.streams.StreamsConfig.*;

import com.illumio.data.configuration.FlowAggConfig;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.streams.StreamsConfig;
import org.apache.kafka.streams.errors.LogAndContinueExceptionHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.EnableKafkaStreams;
import org.springframework.kafka.annotation.KafkaStreamsDefaultConfiguration;
import org.springframework.kafka.config.KafkaStreamsConfiguration;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Configuration
@EnableKafka
@EnableKafkaStreams
@RequiredArgsConstructor
public class KafkaStreamsConfig {
    private final FlowAggConfig flowAggConfig;

    @Bean(name = KafkaStreamsDefaultConfiguration.DEFAULT_STREAMS_CONFIG_BEAN_NAME)
    public KafkaStreamsConfiguration kStreamsConfig() {
        Map<String, Object> props = new HashMap<>();
        props.put(APPLICATION_ID_CONFIG, flowAggConfig.getKStreamsConfig().getApplicationId());
        props.put(BOOTSTRAP_SERVERS_CONFIG, flowAggConfig.getKStreamsConfig().getBootstrapServers());

        if (null != flowAggConfig.getKStreamsConfig().getIsSasl()
                && Boolean.TRUE.equals(flowAggConfig.getKStreamsConfig().getIsSasl())) {
            props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            props.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            props.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    flowAggConfig.getKStreamsConfig().getSaslJaasConfig());
        }

        Optional.of(flowAggConfig)
                .map(FlowAggConfig::getKStreamsConfig)
                .map(FlowAggConfig.KStreamsConfig::getStateStoreCacheMaxBytes)
                .ifPresent(
                        stateStoreMaxCacheBytes ->
                                props.put(
                                        StreamsConfig.STATESTORE_CACHE_MAX_BYTES_CONFIG,
                                        stateStoreMaxCacheBytes));

        Optional.of(flowAggConfig)
                .map(FlowAggConfig::getKStreamsConfig)
                .map(FlowAggConfig.KStreamsConfig::getCommitIntervalMs)
                .ifPresent(
                        commitIntervalMs ->
                                props.put(
                                        StreamsConfig.COMMIT_INTERVAL_MS_CONFIG, commitIntervalMs));

        Optional.of(flowAggConfig)
                .map(FlowAggConfig::getKStreamsConfig)
                .map(FlowAggConfig.KStreamsConfig::getStateStoreDir)
                        .ifPresent(stateStoreDir -> props.put(StreamsConfig.STATE_DIR_CONFIG, stateStoreDir));

        props.put(
                DEFAULT_DESERIALIZATION_EXCEPTION_HANDLER_CLASS_CONFIG,
                LogAndContinueExceptionHandler.class);

        props.put(TOPOLOGY_OPTIMIZATION_CONFIG, OPTIMIZE);

        return new KafkaStreamsConfiguration(props);
    }
}
