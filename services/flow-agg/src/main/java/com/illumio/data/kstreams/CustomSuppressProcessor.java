package com.illumio.data.kstreams;

import com.illumio.data.model.FlowValue;


import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.streams.KeyValue;
import org.apache.kafka.streams.kstream.Windowed;
import org.apache.kafka.streams.processor.PunctuationType;
import org.apache.kafka.streams.processor.api.Processor;
import org.apache.kafka.streams.processor.api.ProcessorContext;
import org.apache.kafka.streams.processor.api.Record;
import org.apache.kafka.streams.state.KeyValueIterator;
import org.apache.kafka.streams.state.TimestampedWindowStore;
import org.apache.kafka.streams.state.ValueAndTimestamp;

import java.time.Duration;
import java.time.Instant;
import java.util.StringJoiner;

@Slf4j
public class CustomSuppressProcessor implements Processor<String, FlowValue, String, FlowValue> {

    private final Duration scanFrequency;
    private final String tableStateStoreName;
    private TimestampedWindowStore<String, FlowValue> stateStore;

    public CustomSuppressProcessor(final Duration scanFrequency, final String stateStoreName) {
        this.scanFrequency = scanFrequency;
        this.tableStateStoreName = stateStoreName;
    }

    @Override
    public void init(ProcessorContext<String, FlowValue> context) {
        this.stateStore = context.getStateStore(tableStateStoreName);
        context.schedule(
                scanFrequency,
                PunctuationType.WALL_CLOCK_TIME,
                timestamp -> {
                    log.info(
                            "Punctuate @ {} for task {}",
                            Instant.ofEpochMilli(timestamp),
                            context.taskId());
                    try (final KeyValueIterator<Windowed<String>, ValueAndTimestamp<FlowValue>>
                            all = stateStore.all()) {
                        while (all.hasNext()) {
                            final KeyValue<Windowed<String>, ValueAndTimestamp<FlowValue>> record =
                                    all.next();
                            if (timestamp >= record.key.window().end()) {
                                context.forward(
                                        new Record<>(
                                                record.key.key().toString(),
                                                record.value.value(),
                                                timestamp));
                                log.debug("Adding record to state store with timestamp {}, window end {}", timestamp, record.key.window().end());
                                stateStore.put(record.key.key(), null, record.key.window().start());
                            }
                        }
                    }
                });
    }

    @Override
    public void process(Record<String, FlowValue> record) {
        // NOOP
    }

    @Override
    public void close() {
        // close any resources managed by this processor
        // Note: Do not close any StateStores as these are managed by the library
    }

}
