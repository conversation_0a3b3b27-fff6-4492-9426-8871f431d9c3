package com.illumio.data.kstreams;

import com.illumio.data.component.FlowAggregation;
import com.illumio.data.configuration.FlowAggConfig;
import com.illumio.data.model.FlowValue;
import com.illumio.data.service.MetricRecordService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.common.serialization.Serde;
import org.apache.kafka.common.serialization.Serdes;
import org.apache.kafka.common.utils.Bytes;
import org.apache.kafka.streams.KeyValue;
import org.apache.kafka.streams.StreamsBuilder;
import org.apache.kafka.streams.kstream.*;
import org.apache.kafka.streams.state.WindowStore;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import io.micrometer.core.instrument.MeterRegistry;

@Configuration
@RequiredArgsConstructor
@Slf4j
public class FlowAggKStreamsBuilderConfig {
    private final Serde<FlowValue> flowValueSerde;
    private final FlowAggConfig flowAggConfig;
    private final MeterRegistry meterRegistry;
    private final MetricRecordService metricRecordService;

    @Bean
    public KStream<String, FlowValue> kStream(StreamsBuilder kStreamBuilder) {
        KStream<String, FlowValue> stream =
                kStreamBuilder.stream(
                        flowAggConfig.getStreamsBuilderConfig().getInputTopic(),
                        Consumed.with(Serdes.String(), flowValueSerde).withName("raw-flow-topic"));

        var windowedAggregateFlowKTable =
               stream
                        .groupByKey(Grouped.with(Serdes.String(), flowValueSerde))
                        .windowedBy(
                                TimeWindows.ofSizeWithNoGrace(
                                        flowAggConfig.getStreamsBuilderConfig().getWindowSize()))
                        .aggregate(
                                FlowValue::new,
                                (key, value, aggregate) -> FlowAggregation.aggregate(key, value, aggregate, meterRegistry, metricRecordService),
                                Named.as("flow-aggregation"),
                                Materialized.<String, FlowValue, WindowStore<Bytes, byte[]>>as(
                                                flowAggConfig
                                                        .getStreamsBuilderConfig()
                                                        .getStateStoreName())
                                        .withKeySerde(Serdes.String())
                                        .withValueSerde(flowValueSerde)
                                        .withRetention(
                                                flowAggConfig
                                                        .getStreamsBuilderConfig()
                                                        .getStateStoreRetention())
                                        .withCachingEnabled()
                                        .withLoggingDisabled());

        var suppressedKStream =
                windowedAggregateFlowKTable
                        .toStream(Named.as("windowed-aggregate-to-stream"))
                        .map(
                                (windowedId, value) -> new KeyValue<>(windowedId.key(), value),
                                Named.as("windowed-key"))
                        .process(() -> new CustomSuppressProcessor(
                                flowAggConfig
                                        .getStreamsBuilderConfig()
                                        .getPunctuateInterval(),
                                flowAggConfig
                                        .getStreamsBuilderConfig()
                                        .getStateStoreName())
                                ,
                                Named.as("custom-suppress-window"),
                                flowAggConfig.getStreamsBuilderConfig().getStateStoreName());

        suppressedKStream.to(
                flowAggConfig.getStreamsBuilderConfig().getOutputTopic(),
                Produced.with(Serdes.String(), flowValueSerde).withName("decorated-flow-topic"));

        return suppressedKStream;
    }
}
