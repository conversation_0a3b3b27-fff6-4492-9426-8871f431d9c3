package com.illumio.data.configuration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.common.header.Headers;
import org.springframework.kafka.support.serializer.JsonDeserializer;
import org.springframework.kafka.support.serializer.JsonSerde;
import org.springframework.kafka.support.serializer.JsonSerializer;

@Slf4j
public class CustomJsonSerde<T> extends JsonSerde<T> {
    public CustomJsonSerde(ObjectMapper objectMapper, Class<T> targetType) {
        super(
                new CustomJsonSerializer<>(objectMapper),
                new CustomJsonDeserializer<>(objectMapper, targetType));
    }

    private static class CustomJsonSerializer<T> extends JsonSerializer<T> {
        public CustomJsonSerializer(ObjectMapper objectMapper) {
            super(objectMapper);
            this.setAddTypeInfo(false);
        }

        @Override
        public byte[] serialize(String topic, Headers headers, T data) {
            try {
                return super.serialize(topic, headers, data);
            } catch (Exception e) {
                log.error("Error in record serialization, {}, {}, {}", topic, headers, data, e);
                throw e;
            }
        }
    }

    private static class CustomJsonDeserializer<T> extends JsonDeserializer<T> {
        public CustomJsonDeserializer(ObjectMapper objectMapper, Class<T> targetType) {
            super(targetType, objectMapper);
            this.objectMapper.registerModule(new JavaTimeModule());
        }

        @Override
        public T deserialize(String topic, Headers headers, byte[] data) {
            try {
                return super.deserialize(topic, headers, data);
            } catch (Exception e) {
                log.error("Error in record deserialization, {}, {}, {}", topic, headers, data, e);
                throw e;
            }
        }
    }
}
