package com.illumio.data.configuration;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
@ConfigurationProperties(prefix = "flow-agg-config")
@Getter
@Setter
public class FlowAggConfig {
    private KStreamsConfig kStreamsConfig;
    private StreamsBuilderConfig streamsBuilderConfig;

    @Configuration
    @Getter
    @Setter
    public static class KStreamsConfig {
        private String applicationId;
        private String bootstrapServers;
        private String groupId;
        private Long stateStoreCacheMaxBytes;
        private Integer commitIntervalMs;
        private String stateStoreDir;
        private Boolean isSasl = false;
        private String saslJaasConfig;
    }

    @Configuration
    @Getter
    @Setter
    public static class StreamsBuilderConfig {
        private String inputTopic;
        private Duration windowSize = Duration.ofHours(1);
        private String stateStoreName;
        private Duration stateStoreRetention = Duration.ofHours(2);
        private Duration punctuateInterval = Duration.ofMinutes(5);
        private String outputTopic;
    }
}
