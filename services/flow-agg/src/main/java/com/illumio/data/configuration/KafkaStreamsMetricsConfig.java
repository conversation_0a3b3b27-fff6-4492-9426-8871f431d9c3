package com.illumio.data.configuration;

import io.micrometer.core.instrument.MeterRegistry;

import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.instrumentation.micrometer.v1_5.OpenTelemetryMeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.streams.Topology;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.StreamsBuilderFactoryBean;
import org.springframework.kafka.config.StreamsBuilderFactoryBeanConfigurer;
import org.springframework.kafka.streams.KafkaStreamsMicrometerListener;

@Configuration
@Slf4j
public class KafkaStreamsMetricsConfig {
    @Bean
    public StreamsBuilderFactoryBeanConfigurer configurer(MeterRegistry meterRegistry) {
        return new StreamsBuilderFactoryBeanConfigurer() {
            @Override
            public void configure(StreamsBuilderFactoryBean fb) {
                log.info("Adding Micrometer listener to kafka streams");
                fb.addListener(new KafkaStreamsMicrometerListener(meterRegistry));
            }
        };
    }
}
