logging:
  level:
    ROOT: INFO
    org:
      apache:
        kafka: INFO
spring:
  application:
    name: flow-agg
  output:
    ansi:
      enabled: ALWAYS
#  main:
#    web-application-type: none
management:
  endpoints:
    web:
      exposure:
        include: "*"

server:
  port : 8089

flow-agg-config:
  kstreams-config:
    application-id: flow-agg
    bootstrap-servers: localhost:29092
    state-store-cache-max-bytes: 10485760 # 1 MB across all stream threads
    commit-interval-ms: 1000 # 1s
    group-id: flow-agg-group
    state-store-dir: rocksdb-flow-agg

  streams-builder-config:
    input-topic: raw-flow-v1
    window-size: 60s
    state-store-name: flow-state-store
    state-store-retention: 2h
    punctuate-interval: 10s
    output-topic: aggregated-flow-v1