logging:
  level:
    ROOT: INFO
    org:
      apache:
        kafka: INFO
spring:
  application:
    name: flow-kstreams
  output:
    ansi:
      enabled: ALWAYS
#  main:
#    web-application-type: none
server:
  port : 8089

flow-agg-config:
  kstreams-config:
    application-id: flow-agg
    bootstrap-servers: test-arch-eventhub.servicebus.windows.net:9093
    state-store-cache-max-bytes: 10485760 # 1 MB across all stream threads
    commit-interval-ms: 1000 #1s
    group-id: flow-agg-group
    state-store-dir: rocksdb-flow-agg
    #    event hub config
    is-sasl: true

  streams-builder-config:
    input-topic: flow-stream
    window-size: 10s
    state-store-name: flow-state-store
    state-store-retention: 2h
    punctuate-interval: 10s
    output-topic: flow-1hour-stream