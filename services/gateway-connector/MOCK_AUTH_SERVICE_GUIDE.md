# MockAuthService - Testing AuthService Timeout and Hang Scenarios

## Overview

This guide provides a comprehensive solution for testing AuthService timeout and hang scenarios in the gateway-connector service. The mock service simulates various failure conditions to help you test how your application handles different AuthService behaviors.

## Quick Start

### 1. Run the Mock Server (Interactive Mode)

```bash
# Using Gradle task (recommended)
./gradlew :services:gateway-connector:runMockAuthService

# Or using the shell script
./services/gateway-connector/run-mock-auth-service.sh

# Or specify a custom port
./gradlew :services:gateway-connector:runMockAuthService -Pport=8082
```

### 2. Configure Gateway-Connector

Update your `application-local.yml`:

```yaml
grpc:
  authservice:
    host: localhost
    port: 8081  # Match the port from step 1
    use-tls: false  # Disable TLS for local testing with mock service

grpctimeout: "5s"  # Adjust timeout for testing
```

### 3. Test Different Scenarios

Once the mock server is running, you can switch behaviors interactively:

```
MockAuthService> normal      # Normal responses
MockAuthService> delayed     # Delayed responses (specify seconds)
MockAuthService> hung        # Never responds (simulates hung service)
MockAuthService> timeout     # 15s delay (longer than typical timeout)
MockAuthService> error       # Return gRPC errors
MockAuthService> intermittent # Random 30% failure rate
MockAuthService> quit        # Shutdown server
```

## Available Test Scenarios

### 1. Normal Operation
- **Command**: `normal`
- **Behavior**: Returns successful responses immediately
- **Use Case**: Baseline functionality testing

### 2. Slow Service
- **Command**: `delayed`
- **Behavior**: Returns responses after configurable delay (1-10 seconds)
- **Use Case**: Testing performance under load, verify timeout handling

### 3. Hung Service
- **Command**: `hung`
- **Behavior**: Never responds (simulates completely hung service)
- **Use Case**: Testing client timeout behavior and circuit breaker activation

### 4. Service Timeout
- **Command**: `timeout`
- **Behavior**: Delays 15+ seconds (longer than typical client timeout)
- **Use Case**: Testing long-running request handling

### 5. Service Errors
- **Command**: `error`
- **Behavior**: Returns gRPC INTERNAL errors
- **Use Case**: Testing error handling and fallback mechanisms

### 6. Intermittent Failures
- **Command**: `intermittent`
- **Behavior**: Randomly fails 30% of requests
- **Use Case**: Testing retry logic and resilience patterns

## Running Automated Tests

```bash
# Run all timeout-related tests
./gradlew :services:gateway-connector:test --tests "*Timeout*"

# Run specific test classes
./gradlew :services:gateway-connector:test --tests "AuthServiceTimeoutTest"
./gradlew :services:gateway-connector:test --tests "PermissionFilterTimeoutTest"

# Run with debug logging
./gradlew :services:gateway-connector:test --tests "AuthServiceTimeoutTest" --debug
```

## Integration with Existing Code

The mock service integrates seamlessly with your existing `PermissionFilter`:

<augment_code_snippet path="services/gateway-connector/src/main/java/com/illumio/data/filter/PermissionFilter.java" mode="EXCERPT">
````java
return authServiceStub.getUserSessionPermissions(request)
        .timeout(grpcTimeout)  // This timeout will be triggered by hung/timeout behaviors
        .flatMap(grpcResponse -> {
            // Normal processing
            return chain.filter(exchange);
        })
        .onErrorResume(e -> {
            // This will catch timeout and error scenarios
            log.error("gRPC error in PermissionFilter", e);
            exchange.getResponse().setStatusCode(HttpStatus.INTERNAL_SERVER_ERROR);
            return exchange.getResponse().setComplete();
        });
````
</augment_code_snippet>

## Testing Workflow

1. **Start Mock Service**: Run the interactive mock server
2. **Configure Gateway**: Point gateway-connector to mock service
3. **Test Normal Flow**: Verify everything works with `normal` behavior
4. **Test Timeout Handling**: Switch to `hung` behavior and verify timeout handling
5. **Test Error Handling**: Switch to `error` behavior and verify error responses
6. **Test Resilience**: Use `intermittent` behavior to test retry logic

## Monitoring and Debugging

### Enable Debug Logging

Add to your `application-local.yml`:

```yaml
logging:
  level:
    com.illumio.data.mock: INFO
    com.illumio.data.filter.PermissionFilter: DEBUG
    io.grpc: DEBUG
```

### Key Metrics to Monitor

- Request timeout frequency
- Circuit breaker state changes
- Cache hit/miss ratios during failures
- Response time distribution

## Files Created

- `MockAuthService.java` - Main mock implementation
- `MockAuthServiceServer.java` - Standalone server for manual testing
- `AuthServiceBehavior.java` - Behavior configuration enum
- `MockAuthServiceTestHelper.java` - Test utility class
- `AuthServiceTimeoutTest.java` - Integration tests
- `PermissionFilterTimeoutTest.java` - Filter-specific tests
- `application-test.yml` - Test configuration
- `run-mock-auth-service.sh` - Convenience script

## Troubleshooting

### Common Issues

1. **Port conflicts**: Change port with `-Pport=8082`
2. **Timeout too short**: Increase `grpctimeout` in configuration
3. **Tests hanging**: Ensure proper cleanup in test teardown
4. **gRPC connection issues**: Verify host/port configuration
5. **TLS/SSL errors**: If you see "HTTP/2 client preface string missing" errors, ensure `use-tls: false` is set in your configuration

### Debug Commands

```bash
# Check if mock server is running
lsof -i :8081

# Test gRPC connectivity (requires grpcurl)
grpcurl -plaintext localhost:8081 list

# Monitor gateway-connector logs
tail -f logs/gateway-connector.log | grep -i "grpc\|auth\|timeout"
```

### Fixing TLS/SSL Issues

If you encounter this error:
```
HTTP/2 client preface string missing or corrupt. Hex dump for received bytes: 16030301...
```

This means the client is trying to use TLS but the mock server runs without TLS. Fix by:

1. **Update your configuration** to disable TLS:
```yaml
grpc:
  authservice:
    use-tls: false
```

2. **For production environments**, you may want to enable TLS on the mock server instead:
```yaml
grpc:
  authservice:
    use-tls: true  # Keep TLS enabled for production-like testing
```

This mock service provides a comprehensive testing framework for AuthService timeout and hang scenarios, enabling thorough testing of your application's resilience and error handling capabilities.
