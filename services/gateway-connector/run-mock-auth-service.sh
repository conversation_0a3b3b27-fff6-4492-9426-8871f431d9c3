#!/bin/bash

# Script to run the MockAuthService standalone server for testing
# This allows manual testing of timeout and hang scenarios

set -e

# Default port
PORT=${1:-8088}

echo "Starting MockAuthService on port $PORT..."
echo "Make sure to configure gateway-connector to use this port in application-local.yml:"
echo ""
echo "grpc:"
echo "  authservice:"
echo "    host: localhost"
echo "    port: $PORT"
echo ""

# Navigate to root directory to run gradlew
cd "$(dirname "$0")/../.."

# Build the test classes first
echo "Building test classes..."
./gradlew :services:gateway-connector:compileTestJava

# Run the mock server
echo "Starting MockAuthService server..."
echo "Available commands once started:"
echo "  normal      - Normal responses"
echo "  delayed     - Delayed responses (specify seconds)"
echo "  hung        - Never responds (simulates hung service)"
echo "  timeout     - 15s delay (longer than typical timeout)"
echo "  error       - Return gRPC errors"
echo "  intermittent - Random 30% failure rate"
echo "  quit        - Shutdown server"
echo ""

# Run the server using the gradle task (simpler and more reliable)
./gradlew :services:gateway-connector:runMockAuthService -Pport=$PORT
