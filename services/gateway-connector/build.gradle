plugins {
    id 'org.springframework.boot'
    id 'com.google.cloud.tools.jib'
    id 'com.google.protobuf'
    id 'jacoco'
}

apply from: "${project.rootDir}/gradle/scripts/jib-build.gradle"
apply from: "${project.rootDir}/gradle/scripts/opentelemetry.gradle"

dependencies {
    // implementation
    implementation project(":commons:azure-commons")
    implementation("com.github.ben-manes.caffeine:caffeine")
    implementation("com.github.ben-manes.caffeine:guava")
    implementation("com.github.ben-manes.caffeine:jcache")
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.springframework.cloud:spring-cloud-starter-circuitbreaker-reactor-resilience4j'
    implementation 'org.springframework.cloud:spring-cloud-starter-gateway'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'co.elastic.logging:logback-ecs-encoder'
    implementation("com.azure:azure-monitor-query")
    implementation("com.azure:azure-identity")
    // Adding jsonwebtoken API
    implementation 'io.jsonwebtoken:jjwt-api'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson' // for JSON serialization

    //implementation 'io.grpc:grpc-netty-shaded'
    implementation 'io.grpc:grpc-protobuf'
    implementation 'io.grpc:grpc-stub'
    implementation 'io.grpc:grpc-api'
    implementation 'com.google.protobuf:protobuf-java'
    implementation 'javax.annotation:javax.annotation-api'
    implementation 'com.salesforce.servicelibs:reactor-grpc-stub'
    implementation 'com.salesforce.servicelibs:reactor-grpc'

    implementation 'io.grpc:grpc-netty' // or latest compatible version
    implementation 'io.grpc:grpc-protobuf'
    implementation 'io.grpc:grpc-stub'
    implementation 'io.grpc:grpc-core'
    implementation 'io.grpc:grpc-api'
    implementation 'com.google.protobuf:protobuf-java-util'

    implementation 'com.azure:azure-core-metrics-opentelemetry:1.0.0-beta.25'
    implementation 'io.micrometer:micrometer-core'
    implementation 'io.projectreactor:reactor-core-micrometer:1.1.8'
    implementation("io.opentelemetry.instrumentation:opentelemetry-micrometer-1.5:2.7.0-alpha")

    // Resilience4j for circuit breaker
    implementation 'io.github.resilience4j:resilience4j-reactor:2.1.0'
    implementation 'io.github.resilience4j:resilience4j-circuitbreaker:2.1.0'
    implementation 'io.github.resilience4j:resilience4j-micrometer:2.1.0'

    implementation("io.opentelemetry:opentelemetry-api")
    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // spring
    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"

    // test
    testImplementation platform('org.junit:junit-bom')
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
    testImplementation 'io.grpc:grpc-testing'
    testImplementation 'io.grpc:grpc-inprocess'
}

sourceSets {
    main {
        java {
            srcDirs = ['src/main/java', 'build/generated/source/proto/main/java', 'build/generated/source/proto/main/grpc', 'build/generated/source/proto/main/reactor']
        }
    }
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:3.25.5"
    }
    plugins {
        grpc {
            artifact = 'io.grpc:protoc-gen-grpc-java:1.68.1'
        }
        reactor {
            artifact = 'com.salesforce.servicelibs:reactor-grpc:1.2.4'
        }
    }
    generateProtoTasks {
        all().forEach { task ->
            task.plugins {
                grpc {}
                reactor {
                    option 'reactorGrpcStubOnly=true'
                }
            }
        }
    }
}

compileJava.dependsOn(generateProto)

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:2023.0.1"
    }
}

jib {
    container {
        entrypoint = [
                'java', '-cp', '@/app/jib-classpath-file',
                'com.illumio.data.GatewayConnectorApplication',
                '--spring.config.location=file:/var/resources/application.yml',
                '--spring.profiles.active=prod'
        ]
        environment = [
                "JAVA_TOOL_OPTIONS": "-javaagent:/otel/opentelemetry-javaagent.jar",
                "OTEL_METRICS_EXPORTER": "prometheus",
                "OTEL_TRACES_EXPORTER": "none",
                "OTEL_LOGS_EXPORTER": "none"
        ]
    }
}

jacoco {
    toolVersion = "0.8.10" // Or latest compatible version
}

jacocoTestReport {
    dependsOn test

    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: [
                    'authservice/**',             // exclude generated protobuf/gRPC
                    '**/config/**',
                    '**/constants/**'
            ])
        }))
    }

    reports {
        html.required = true
        xml.required = true
        csv.required = false
        html.outputLocation = layout.buildDirectory.dir('reports/jacoco/html')
    }
}

jacocoTestCoverageVerification {
    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: [
                    'authservice/**',             // exclude generated protobuf/gRPC
                    '**/proto/**',
                    '**/config/**',
                    '**/constants/**',
                    '**/cache/**',
                    '**/controller/**',
                    '**/service/**',
                    '**/properties/**',
                    '**/constants/**',
                    '**/exception/**',
                    'com/illumio/data/filter/*$Config.class',
                    '**/*Application.class',
                    'com/illumio/data/filter/CoreHeader**'
            ])
        }))
    }
    violationRules {
        rule {
            enabled = true
            element = 'CLASS'
            limit {
                counter = 'LINE'
                value = 'COVEREDRATIO'
                minimum = 0.5 // Set to your required threshold
            }
        }
    }
}

check.dependsOn jacocoTestReport
check.dependsOn jacocoTestCoverageVerification

// Task to run the MockAuthService standalone server
task runMockAuthService(type: JavaExec) {
    description = 'Run MockAuthService standalone server for testing timeout scenarios'
    group = 'testing'

    dependsOn compileTestJava
    classpath = sourceSets.test.runtimeClasspath
    mainClass = 'com.illumio.data.mock.MockAuthServiceServer'

    // Default port, can be overridden with -Pport=8082
    args = [project.hasProperty('port') ? project.port : '8081']

    // Make it interactive
    standardInput = System.in
}

