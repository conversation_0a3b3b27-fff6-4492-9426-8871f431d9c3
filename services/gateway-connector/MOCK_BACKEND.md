# Mock backend (WireMock) for gateway load testing

This mock backend runs as its own Deployment and Service so you can load test the gateway without real downstreams.

## Deploy

```bash
kubectl apply -n dev-gatewayconnector -f services/gateway-connector/k8s/mock-backend.yaml
kubectl get svc,pods -n dev-gatewayconnector | grep mock-backend
```

## Test the mock directly

- Port-forward locally and curl:
```bash
kubectl -n dev-gatewayconnector port-forward svc/mock-backend 8081:8080 &
curl -s http://localhost:8081/Qos/health
curl -s http://localhost:8081/Qos/any/path
```

- In-cluster curl (no port-forward):
```bash
kubectl -n dev-gatewayconnector run curl --rm -i --tty \
  --restart=Never --image=curlimages/curl -- \
  curl -sS http://mock-backend:8080/Qos/health
```

- DNS with FQDN:
```bash
curl -sS http://mock-backend.dev-gatewayconnector.svc.cluster.local:8080/Qos/health
```

Responses are JSON and include the request path/method, e.g.:
```json
{ "service": "mock-backend", "path": "/Qos/any/path", "method": "GET" }
```

## Point the gateway to the mock

Set downstream `uri:` to the mock Service and keep `StripPrefix` so `/api/<svc>/**` maps to `/Qos/**`:

```
uri: http://mock-backend:8080/Qos
```

Or FQDN:
```
uri: http://mock-backend.dev-gatewayconnector.svc.cluster.local:8080/Qos
```

Then test through the gateway (example uses port-forward):
```bash
kubectl -n dev-gatewayconnector port-forward svc/gateway-connector 8080:8080 &

# Example proxied route → /Qos/ping downstream
curl -s http://localhost:8080/api/integrations/ping
# { "service": "mock-backend", "path": "/Qos/ping", "method": "GET" }
```

## Clean up
```bash
kubectl delete -n dev-gatewayconnector -f services/gateway-connector/k8s/mock-backend.yaml
```

## Remove / Redeploy

Remove everything:
```bash
kubectl delete -n dev-gatewayconnector -f services/gateway-connector/k8s/mock-backend.yaml --ignore-not-found
```

Re-deploy (fresh):
```bash
kubectl apply -n dev-gatewayconnector -f services/gateway-connector/k8s/mock-backend.yaml
kubectl rollout status -n dev-gatewayconnector deploy/mock-backend
kubectl get pods -n dev-gatewayconnector -l app=mock-backend -w
```

If you just need a rolling restart without deleting objects:
```bash
kubectl rollout restart -n dev-gatewayconnector deploy/mock-backend
kubectl rollout status -n dev-gatewayconnector deploy/mock-backend
```

## Advanced mocking
- Increase `replicas:` in `mock-backend.yaml` to simulate multiple downstream pods.
- Add latency/errors in the ConfigMap by adding WireMock mappings (e.g., `fixedDelayMilliseconds`, custom `status`).
