apiVersion: v1
kind: ConfigMap
metadata:
  name: mock-backend-mappings
data:
  health.json: |
    {
      "priority": 1,
      "request": { "method": "GET", "urlPath": "/Qos/health" },
      "response": {
        "status": 200,
        "headers": { "Content-Type": "application/json" },
        "body": "{ \"status\": \"ok\" }"
      }
    }
  default-mock.json: |
    {
      "priority": 10,
      "request": { "urlPathPattern": "/Qos(/.*)?" },
      "response": {
        "status": 200,
        "headers": { "Content-Type": "application/json" },
        "body": "{ \"service\": \"mock-backend\", \"path\": \"{{request.path}}\", \"method\": \"{{request.method}}\" }"
      }
    }
  root.json: |
    {
      "priority": 5,
      "request": { "urlPath": "/Qos" },
      "response": {
        "status": 200,
        "headers": { "Content-Type": "application/json" },
        "body": "{ \"service\": \"mock-backend\", \"path\": \"{{request.path}}\", \"method\": \"{{request.method}}\" }"
      }
    }
  echo.json: |
    {
      "priority": 6,
      "request": { "urlPathPattern": "/Qos/echo(/.*)?" },
      "response": {
        "status": 200,
        "headers": { "Content-Type": "application/json" },
        "body": "{ \"path\": \"{{request.path}}\", \"method\": \"{{request.method}}\", \"query\": \"{{request.query}}\", \"headers\": { \"X-tenant-subdomain\": \"{{request.headers.X-tenant-subdomain.0}}\", \"X-csi\": \"{{request.headers.X-csi.0}}\", \"user_uuid\": \"{{request.headers.user_uuid.0}}\", \"uc_session\": \"{{request.headers.uc_session.0}}\", \"ucid\": \"{{request.headers.ucid.0}}\", \"X-PCE-FQDN\": \"{{request.headers.X-PCE-FQDN.0}}\" } }"
      }
    }
  headers.json: |
    {
      "priority": 6,
      "request": { "urlPathPattern": "/Qos/headers" },
      "response": {
        "status": 200,
        "headers": { "Content-Type": "application/json" },
        "body": "{ \"X-tenant-subdomain\": \"{{request.headers.X-tenant-subdomain.0}}\", \"X-csi\": \"{{request.headers.X-csi.0}}\", \"user_uuid\": \"{{request.headers.user_uuid.0}}\", \"uc_session\": \"{{request.headers.uc_session.0}}\", \"ucid\": \"{{request.headers.ucid.0}}\", \"X-PCE-FQDN\": \"{{request.headers.X-PCE-FQDN.0}}\" }"
      }
    }
  secure-ok.json: |
    {
      "priority": 2,
      "request": {
        "urlPathPattern": "/Qos/ri/secure(/.*)?",
        "headers": { "x-auth-key": { "equalTo": "secret" } }
      },
      "response": {
        "status": 200,
        "headers": { "Content-Type": "application/json" },
        "body": "{ \"status\": \"authorized\" }"
      }
    }
  secure-unauth-absent.json: |
    {
      "priority": 3,
      "request": {
        "urlPathPattern": "/Qos/ri/secure(/.*)?",
        "headers": { "x-auth-key": { "absent": true } }
      },
      "response": {
        "status": 401,
        "headers": { "Content-Type": "application/json" },
        "body": "{ \"error\": \"missing x-auth-key\" }"
      }
    }
  secure-unauth-wrong.json: |
    {
      "priority": 4,
      "request": {
        "urlPathPattern": "/Qos/ri/secure(/.*)?",
        "headers": { "x-auth-key": { "matches": "^(?!secret$).*" } }
      },
      "response": {
        "status": 401,
        "headers": { "Content-Type": "application/json" },
        "body": "{ \"error\": \"invalid x-auth-key\" }"
      }
    }
  slow.json: |
    {
      "priority": 7,
      "request": { "urlPathPattern": "/Qos/slow(/.*)?" },
      "response": {
        "status": 200,
        "fixedDelayMilliseconds": 800,
        "headers": { "Content-Type": "application/json" },
        "body": "{ \"status\": \"slow\", \"delayMs\": 800 }"
      }
    }
  post-resource.json: |
    {
      "priority": 6,
      "request": { "method": "POST", "urlPath": "/Qos/resource" },
      "response": {
        "status": 201,
        "headers": { "Content-Type": "application/json" },
        "body": "{ \"result\": \"created\", \"method\": \"{{request.method}}\", \"path\": \"{{request.path}}\" }"
      }
    }
  put-resource.json: |
    {
      "priority": 6,
      "request": { "method": "PUT", "urlPath": "/Qos/resource" },
      "response": {
        "status": 200,
        "headers": { "Content-Type": "application/json" },
        "body": "{ \"result\": \"updated\", \"method\": \"{{request.method}}\", \"path\": \"{{request.path}}\" }"
      }
    }
  delete-resource.json: |
    {
      "priority": 6,
      "request": { "method": "DELETE", "urlPath": "/Qos/resource" },
      "response": {
        "status": 204,
        "headers": { "Content-Length": "0" }
      }
    }
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: mock-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: mock-backend
  template:
    metadata:
      labels:
        app: mock-backend
    spec:
      containers:
        - name: wiremock
          image: wiremock/wiremock:2.35.0
          args:
            - "--verbose"
            - "--global-response-templating"
          ports:
            - containerPort: 8080
          resources:
            requests:
              cpu: "3000m"
              memory: "3000Mi"
            limits:
              cpu: "3000m"
              memory: "3000Mi"
          volumeMounts:
            - name: mappings
              mountPath: /home/<USER>/mappings
      volumes:
        - name: mappings
          configMap:
            name: mock-backend-mappings
---
apiVersion: v1
kind: Service
metadata:
  name: mock-backend
spec:
  selector:
    app: mock-backend
  ports:
    - name: http
      port: 8080
      targetPort: 8080


