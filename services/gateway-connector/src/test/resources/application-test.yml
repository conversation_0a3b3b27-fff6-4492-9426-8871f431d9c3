server:
  port: 0  # Random port for tests

JWT_SECRET: ""  # Base64 encoded test secret
JWT_ALGORITHM: "HS256"

maximumIdleTime: "30s"
maximumLifeTime: "5m"
acquiringTimeOut: "10s"
evictInBackground: "30s"

grpctimeout: "5s"  # Shorter timeout for testing

maximumConnections: 500

cache:
  maximum-size: 100
  expire-after-write: 1  # Shorter cache time for testing

demo:
  tenant:
    id: "test-tenant-id"

spring:
  application:
    name: gateway-connector-test
  main:
    web-application-type: reactive

grpc:
  authservice:
    host: localhost
    port: 0  # Will be set dynamically in tests
    use-tls: false  # Disable TLS for testing

management:
  endpoints:
    web:
      exposure:
        include: health, info
  endpoint:
    health:
      show-details: always

logging:
  level:
    root: WARN
    com.illumio.data: DEBUG
    com.illumio.data.mock: INFO
