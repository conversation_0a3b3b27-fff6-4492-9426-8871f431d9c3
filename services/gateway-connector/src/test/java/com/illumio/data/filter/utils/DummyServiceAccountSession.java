package com.illumio.data.filter.utils;

import authservice.CloudsecureServiceAccountPermissions;
import authservice.CorePermission;
import authservice.CoreRole;
import authservice.CoreServiceAccountPermissions;
import authservice.GetServiceAccountPermissionsResponse;
import authservice.ScopeObject;
import authservice.ServiceAccountActor;
import authservice.UserSessionAccessRestriction;

public class DummyServiceAccountSession {
    public static GetServiceAccountPermissionsResponse createGetServiceAccountPermissionsResponse() {
        return GetServiceAccountPermissionsResponse.newBuilder()
                .setIss("dummy-issuer")
                .addAud("aud1")
                .addAud("aud2")
                .setSub("service-account-id")
                .setSubType("internal")
                .setSvcName("svc_name")
                .setSvcManaging("svc_owner")
                .setAct(ServiceAccountActor.newBuilder()
                        .setSub("svc_sub")
                        .setType("svc_type")
                        .setApiKeyId("api-key-123")
                        .setName("svc_actor")
                        .setExp(999999)
                        .build())
                .setCore(CoreServiceAccountPermissions.newBuilder()
                        .setOrgId(123)
                        .setOrgUuid("org-uuid-xyz")
                        .setPceFqdn("example.com:443")
                        .setSessionTimeoutMinutes(30)
                        .addPermissions(CorePermission.newBuilder()
                                .setRole(CoreRole.newBuilder()
                                        .setHref("role:admin")
                                        .build())
                                .addScope(ScopeObject.newBuilder()
                                        .setType("app")
                                        .setValue("infra")
                                        .build())
                                .addScope(ScopeObject.newBuilder()
                                        .setType("env")
                                        .setValue("prod")
                                        .build())
                                .build())
                        .setAccessRestriction(UserSessionAccessRestriction.newBuilder()
                                .setName("default")
                                .addEnforcementExclusions("API")
                                .addIps("********")
                                .build())
                        .build())
                .setCloudsecure(CloudsecureServiceAccountPermissions.newBuilder()
                        .setId("cloud-id-1")
                        .setKeyId("cloud-key-xyz")
                        .setTenantId("tenant-123")
                        .addPermissions("traffic:w")
                        .addPermissions("billing:w")
                        .addPermissions("insights:r")
                        .build())
                .setPermLastUpdated("2025-05-01T10:00:00Z")
                .build();
    }
    public static GetServiceAccountPermissionsResponse createLargePayloadServiceAccountPermissionsResponse() {
        CoreServiceAccountPermissions.Builder coreBuilder = CoreServiceAccountPermissions.newBuilder()
                .setOrgId(123)
                .setOrgUuid("org-uuid-xyz")
                .setPceFqdn("example.com:443")
                .setSessionTimeoutMinutes(30);

        // Add many permissions to increase JSON size
        for (int i = 0; i < 1000; i++) {
            CorePermission permission = CorePermission.newBuilder()
                    .setRole(CoreRole.newBuilder().setHref("role:admin_" + i).build())
                    .addScope(ScopeObject.newBuilder()
                            .setType("app")
                            .setValue("infra_" + i)
                            .build())
                    .build();
            coreBuilder.addPermissions(permission);
        }

        return GetServiceAccountPermissionsResponse.newBuilder()
                .setIss("dummy-issuer")
                .addAud("aud1")
                .setSub("service-account-id")
                .setSubType("internal")
                .setSvcName("svc_name")
                .setSvcManaging("svc_owner")
                .setAct(ServiceAccountActor.newBuilder()
                        .setSub("svc_sub")
                        .setType("svc_type")
                        .setApiKeyId("api-key-123")
                        .setName("svc_actor")
                        .setExp(999999)
                        .build())
                .setCore(coreBuilder.build())
                .setPermLastUpdated("2025-05-01T10:00:00Z")
                .build();
    }

}
