package com.illumio.data.filter;

import com.illumio.data.service.ErrorHandlingService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.same;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BackendErrorHandlingFilterTest {

    @Mock
    ErrorHandlingService errorHandlingService;

    @Mock
    GatewayFilterChain chain;

    BackendErrorHandlingFilter filter;

    @BeforeEach
    void setUp() {
        filter = new BackendErrorHandlingFilter(errorHandlingService);
    }

    @Test
    void noError_passthrough_noServiceCall() {
        BackendErrorHandlingFilter.Config cfg = new BackendErrorHandlingFilter.Config();
        ServerWebExchange exchange = MockServerWebExchange.from(
                MockServerHttpRequest.get("http://localhost/api").build()
        );

        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        StepVerifier.create(filter.apply(cfg).filter(exchange, chain))
                .verifyComplete();

        verify(chain).filter(any(ServerWebExchange.class));
        verifyNoInteractions(errorHandlingService);
    }

    @Test
    void errorDownstream_delegatesToService_withDefaultConfig() {
        BackendErrorHandlingFilter.Config cfg = new BackendErrorHandlingFilter.Config();
        ServerWebExchange exchange = MockServerWebExchange.from(
                MockServerHttpRequest.get("http://localhost/api").build()
        );
        RuntimeException boom = new RuntimeException("boom");

        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.error(boom));
        when(errorHandlingService.handleError(any(ServerWebExchange.class), any(Throwable.class), any(ErrorHandlingService.ErrorContext.class)))
                .thenReturn(Mono.empty());

        ArgumentCaptor<ErrorHandlingService.ErrorContext> ctxCap =
                ArgumentCaptor.forClass(ErrorHandlingService.ErrorContext.class);
        ArgumentCaptor<Throwable> thrCap = ArgumentCaptor.forClass(Throwable.class);

        StepVerifier.create(filter.apply(cfg).filter(exchange, chain))
                .verifyComplete();

        verify(errorHandlingService).handleError(same(exchange), thrCap.capture(), ctxCap.capture());
        assertSame(boom, thrCap.getValue());

        ErrorHandlingService.ErrorContext ctx = ctxCap.getValue();
        assertEquals("backend-service", ctx.getServiceName());
        assertFalse(ctx.isIncludeServiceInfo());
        assertFalse(ctx.isIncludeErrorDetails());
        assertEquals("", ctx.getHealthCheckUrl());
    }

    @Test
    void errorDownstream_delegatesToService_withCustomConfig() {
        BackendErrorHandlingFilter.Config cfg = new BackendErrorHandlingFilter.Config();
        cfg.setServiceName("auth-svc");
        cfg.setIncludeServiceInfo(true);
        cfg.setIncludeErrorDetails(true);
        cfg.setHealthCheckUrl("http://auth/health");

        ServerWebExchange exchange = MockServerWebExchange.from(
                MockServerHttpRequest.get("http://localhost/login").build()
        );
        IllegalStateException boom = new IllegalStateException("backend broke");

        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.error(boom));
        when(errorHandlingService.handleError(any(ServerWebExchange.class), any(Throwable.class), any(ErrorHandlingService.ErrorContext.class)))
                .thenReturn(Mono.empty());

        ArgumentCaptor<ErrorHandlingService.ErrorContext> ctxCap =
                ArgumentCaptor.forClass(ErrorHandlingService.ErrorContext.class);

        StepVerifier.create(filter.apply(cfg).filter(exchange, chain))
                .verifyComplete();

        verify(errorHandlingService).handleError(same(exchange), same(boom), ctxCap.capture());

        ErrorHandlingService.ErrorContext ctx = ctxCap.getValue();
        assertEquals("auth-svc", ctx.getServiceName());
        assertTrue(ctx.isIncludeServiceInfo());
        assertTrue(ctx.isIncludeErrorDetails());
        assertEquals("http://auth/health", ctx.getHealthCheckUrl());
    }

    @Test
    void errorHandlerResult_isPropagated() {
        BackendErrorHandlingFilter.Config cfg = new BackendErrorHandlingFilter.Config();
        ServerWebExchange exchange = MockServerWebExchange.from(
                MockServerHttpRequest.get("http://localhost/api").build()
        );
        RuntimeException boom = new RuntimeException("boom");

        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.error(boom));

        // Case A: handler completes -> pipeline completes
        when(errorHandlingService.handleError(any(ServerWebExchange.class), any(Throwable.class), any(ErrorHandlingService.ErrorContext.class)))
                .thenReturn(Mono.empty());
        StepVerifier.create(filter.apply(cfg).filter(exchange, chain))
                .verifyComplete();

        // Case B: handler emits error -> pipeline errors with that error
        IllegalArgumentException handledErr = new IllegalArgumentException("handled-as-error");
        when(errorHandlingService.handleError(any(ServerWebExchange.class), any(Throwable.class), any(ErrorHandlingService.ErrorContext.class)))
                .thenReturn(Mono.error(handledErr));
        StepVerifier.create(filter.apply(cfg).filter(exchange, chain))
                .expectErrorMatches(t -> t == handledErr)
                .verify();
    }
}
