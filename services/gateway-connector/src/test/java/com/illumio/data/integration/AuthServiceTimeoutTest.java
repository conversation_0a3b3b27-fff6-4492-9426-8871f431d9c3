package com.illumio.data.integration;

import authservice.GetUserSessionPermissionsRequest;
import authservice.GetUserSessionPermissionsResponse;
import authservice.ReactorAuthServiceGrpc;
import com.illumio.data.mock.AuthServiceBehavior;
import com.illumio.data.mock.MockAuthService;
import io.grpc.ManagedChannel;
import io.grpc.Server;
import io.grpc.inprocess.InProcessChannelBuilder;
import io.grpc.inprocess.InProcessServerBuilder;
import io.grpc.testing.GrpcCleanupRule;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.TimeoutException;

/**
 * Integration tests for AuthService timeout and hang scenarios
 */
@Slf4j
public class AuthServiceTimeoutTest {
    
    public final GrpcCleanupRule grpcCleanup = new GrpcCleanupRule();
    
    private MockAuthService mockAuthService;
    private ReactorAuthServiceGrpc.ReactorAuthServiceStub authServiceStub;
    private Server server;
    private ManagedChannel channel;
    
    @BeforeEach
    void setUp() throws IOException {
        mockAuthService = new MockAuthService();
        
        // Generate a unique in-process server name
        String serverName = InProcessServerBuilder.generateName();
        
        // Create the server
        server = InProcessServerBuilder
                .forName(serverName)
                .directExecutor()
                .addService(mockAuthService)
                .build()
                .start();
        
        // Create the channel
        channel = InProcessChannelBuilder
                .forName(serverName)
                .directExecutor()
                .build();
        
        // Create the stub
        authServiceStub = ReactorAuthServiceGrpc.newReactorStub(channel);
        
        // Register for cleanup
        grpcCleanup.register(server);
        grpcCleanup.register(channel);
    }
    
    @AfterEach
    void tearDown() {
        if (server != null) {
            server.shutdown();
        }
        if (channel != null) {
            channel.shutdown();
        }
    }
    
    @Test
    void testNormalBehavior() {
        // Given
        mockAuthService.setBehavior(AuthServiceBehavior.NORMAL);
        GetUserSessionPermissionsRequest request = createTestRequest();
        
        // When & Then
        Mono<GetUserSessionPermissionsResponse> response = authServiceStub.getUserSessionPermissions(request);
        
        StepVerifier.create(response)
                .expectNextMatches(resp -> 
                    "mock-auth-service".equals(resp.getIss()) &&
                    "test-user-123".equals(resp.getSub()))
                .verifyComplete();
    }
    
    @Test
    void testDelayedBehavior() {
        // Given
        mockAuthService.setBehavior(AuthServiceBehavior.DELAYED);
        mockAuthService.setDelay(Duration.ofMillis(500));
        GetUserSessionPermissionsRequest request = createTestRequest();
        
        // When & Then
        Mono<GetUserSessionPermissionsResponse> response = authServiceStub.getUserSessionPermissions(request);
        
        StepVerifier.create(response)
                .expectNextMatches(resp -> "mock-auth-service".equals(resp.getIss()))
                .expectComplete()
                .verify(Duration.ofSeconds(2)); // Allow up to 2 seconds for the delayed response
    }
    
    @Test
    void testHungBehavior() {
        // Given
        mockAuthService.setBehavior(AuthServiceBehavior.HUNG);
        GetUserSessionPermissionsRequest request = createTestRequest();
        
        // When & Then
        Mono<GetUserSessionPermissionsResponse> response = authServiceStub.getUserSessionPermissions(request)
                .timeout(Duration.ofSeconds(1)); // Add timeout to prevent test from hanging
        
        StepVerifier.create(response)
                .expectError(TimeoutException.class)
                .verify();
    }
    
    @Test
    void testTimeoutBehavior() {
        // Given
        mockAuthService.setBehavior(AuthServiceBehavior.TIMEOUT);
        GetUserSessionPermissionsRequest request = createTestRequest();
        
        // When & Then - Apply a shorter timeout than the service delay
        Mono<GetUserSessionPermissionsResponse> response = authServiceStub.getUserSessionPermissions(request)
                .timeout(Duration.ofSeconds(2));
        
        StepVerifier.create(response)
                .expectError(TimeoutException.class)
                .verify();
    }
    
    @Test
    void testErrorBehavior() {
        // Given
        mockAuthService.setBehavior(AuthServiceBehavior.ERROR);
        GetUserSessionPermissionsRequest request = createTestRequest();
        
        // When & Then
        Mono<GetUserSessionPermissionsResponse> response = authServiceStub.getUserSessionPermissions(request);
        
        StepVerifier.create(response)
                .expectErrorMatches(throwable -> 
                    throwable.getMessage().contains("Mock service error"))
                .verify();
    }
    
    @Test
    void testIntermittentFailureBehavior() {
        // Given - Set high failure rate to ensure we see both success and failure
        mockAuthService.setBehavior(AuthServiceBehavior.INTERMITTENT_FAILURE);
        mockAuthService.setFailureRate(0.5); // 50% failure rate for more predictable testing
        GetUserSessionPermissionsRequest request = createTestRequest();

        // When & Then - Make multiple requests to verify intermittent behavior
        int totalRequests = 20;
        int successCount = 0;
        int failureCount = 0;

        for (int i = 0; i < totalRequests; i++) {
            Mono<GetUserSessionPermissionsResponse> response = authServiceStub.getUserSessionPermissions(request);

            // Use block() with timeout to get the result synchronously
            try {
                GetUserSessionPermissionsResponse result = response
                        .timeout(Duration.ofSeconds(5))
                        .block();

                if (result != null && "mock-auth-service".equals(result.getIss())) {
                    successCount++;
                    log.debug("Request {} succeeded", i + 1);
                } else {
                    failureCount++;
                    log.debug("Request {} returned unexpected result", i + 1);
                }
            } catch (Exception e) {
                failureCount++;
                log.debug("Request {} failed: {}", i + 1, e.getMessage());
                // Verify it's the expected intermittent failure
                assert e.getMessage().contains("Intermittent failure") ||
                       e.getMessage().contains("UNAVAILABLE") :
                       "Unexpected error type: " + e.getMessage();
            }
        }

        log.info("Intermittent test results: {} successes, {} failures out of {} requests",
                successCount, failureCount, totalRequests);

        // Verify we got both successes and failures (with some tolerance for randomness)
        assert successCount > 0 : "Expected at least some successful requests, but got 0";
        assert failureCount > 0 : "Expected at least some failed requests, but got 0";
        assert (successCount + failureCount) == totalRequests : "Total count mismatch";

        // With 50% failure rate and 20 requests, we expect roughly 8-12 failures (40-60% range)
        // This gives us reasonable confidence that intermittent behavior is working
        assert failureCount >= 4 && failureCount <= 16 :
            String.format("Expected 4-16 failures with 50%% rate, but got %d", failureCount);
    }

    @Test
    void testHighFailureRateScenario() {
        // Given - Set very high failure rate to simulate severely degraded service
        mockAuthService.setBehavior(AuthServiceBehavior.INTERMITTENT_FAILURE);
        mockAuthService.setFailureRate(0.99); // 99% failure rate
        GetUserSessionPermissionsRequest request = createTestRequest();

        // When & Then - Make multiple requests and expect mostly failures
        int totalRequests = 10;
        int successCount = 0;
        int failureCount = 0;

        for (int i = 0; i < totalRequests; i++) {
            Mono<GetUserSessionPermissionsResponse> response = authServiceStub.getUserSessionPermissions(request);

            try {
                GetUserSessionPermissionsResponse result = response
                        .timeout(Duration.ofSeconds(5))
                        .block();

                if (result != null && "mock-auth-service".equals(result.getIss())) {
                    successCount++;
                } else {
                    failureCount++;
                }
            } catch (Exception e) {
                failureCount++;
                // Verify it's the expected intermittent failure
                assert e.getMessage().contains("Intermittent failure") ||
                       e.getMessage().contains("UNAVAILABLE") :
                       "Unexpected error type: " + e.getMessage();
            }
        }

        log.info("High failure rate test results: {} successes, {} failures out of {} requests",
                successCount, failureCount, totalRequests);

        // With 90% failure rate, we expect at least 7 failures out of 10 requests
        assert failureCount >= 7 :
            String.format("Expected at least 7 failures with 90%% rate, but got %d", failureCount);
        assert (successCount + failureCount) == totalRequests : "Total count mismatch";
    }
    
    private GetUserSessionPermissionsRequest createTestRequest() {
        return GetUserSessionPermissionsRequest.newBuilder()
                .setCsi("test-csi-token")
                .setXff("127.0.0.1")
                .setReferrer("AuthServiceTimeoutTest")
                .setLastPolledAt("2024-01-01T00:00:00Z")
                .build();
    }
}
