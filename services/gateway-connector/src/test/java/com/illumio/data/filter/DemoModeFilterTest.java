package com.illumio.data.filter;

import authservice.GetUserSessionPermissionsResponse;
import com.illumio.data.filter.utils.DummyUserSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class DemoModeFilterTest {

    private DemoModeFilter filter;
    private static final String DEMO_TENANT_ID = "39e868b6-fdfc-4118-b664-a7d4b04728e8";

    @BeforeEach
    void setUp() {
        filter = new DemoModeFilter();
        // Set the demo tenant ID using reflection since it's a @Value injection
        ReflectionTestUtils.setField(filter, "demoTenantId", DEMO_TENANT_ID);
    }

    @Test
    void testDemoModeEnabledModifiesPath() {
        // Arrange
        DemoModeFilter.Config config = new DemoModeFilter.Config();
        config.setMessage("Test demo filter");

        GatewayFilter gatewayFilter = filter.apply(config);
        GetUserSessionPermissionsResponse response = DummyUserSession.createDummySessionResponse();

        MockServerHttpRequest request = MockServerHttpRequest.get("/api/v1/search/tenant/original-tenant-id/data")
                .header("X-ILLUMIO-DEMO", "true")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        exchange.getAttributes().put("grpcSession", response);

        GatewayFilterChain chain = mock(GatewayFilterChain.class);
        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        // Act & Assert
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        verify(chain).filter(any(ServerWebExchange.class));
    }

    @Test
    void testDemoModeDisabledPassesThrough() {
        // Arrange
        DemoModeFilter.Config config = new DemoModeFilter.Config();
        GatewayFilter gatewayFilter = filter.apply(config);

        MockServerHttpRequest request = MockServerHttpRequest.get("/api/v1/search/tenant/original-tenant-id/data")
                .header("X-ILLUMIO-DEMO", "false")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        GatewayFilterChain chain = mock(GatewayFilterChain.class);
        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        // Act & Assert
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        verify(chain).filter(exchange);
    }

    @Test
    void testNoDemoHeaderPassesThrough() {
        // Arrange
        DemoModeFilter.Config config = new DemoModeFilter.Config();
        GatewayFilter gatewayFilter = filter.apply(config);

        MockServerHttpRequest request = MockServerHttpRequest.get("/api/v1/search/tenant/original-tenant-id/data")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        GatewayFilterChain chain = mock(GatewayFilterChain.class);
        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        // Act & Assert
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        verify(chain).filter(exchange);
    }

    @Test
    void testOptionsRequestSkipped() {
        // Arrange
        DemoModeFilter.Config config = new DemoModeFilter.Config();
        GatewayFilter gatewayFilter = filter.apply(config);

        MockServerHttpRequest request = MockServerHttpRequest.options("/api/v1/search/tenant/test/data")
                .header("X-ILLUMIO-DEMO", "true")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        GatewayFilterChain chain = mock(GatewayFilterChain.class);
        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        // Act & Assert
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        verify(chain).filter(exchange);
    }

    @Test
    void testConfigGetterAndSetter() {
        // Test Config class coverage
        DemoModeFilter.Config config = new DemoModeFilter.Config();

        // Test setter and getter
        config.setMessage("test message");
        assertEquals("test message", config.getMessage());

        // Test with null
        config.setMessage(null);
        assertEquals(null, config.getMessage());

        // Test with empty string
        config.setMessage("");
        assertEquals("", config.getMessage());
    }
}
