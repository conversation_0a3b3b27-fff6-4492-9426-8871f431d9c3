package com.illumio.data.mock;

import authservice.*;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Random;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Mock implementation of AuthService for testing timeout and hang scenarios
 */
@Slf4j
public class MockAuthService extends ReactorAuthServiceGrpc.AuthServiceImplBase {
    
    private final AtomicReference<AuthServiceBehavior> behavior = new AtomicReference<>(AuthServiceBehavior.NORMAL);
    private final AtomicReference<Duration> delay = new AtomicReference<>(Duration.ofMillis(100));
    private final AtomicReference<Double> failureRate = new AtomicReference<>(0.3); // 30% failure rate for intermittent failures
    private final Random random = new Random();
    
    /**
     * Set the behavior of the mock service
     */
    public void setBehavior(AuthServiceBehavior behavior) {
        this.behavior.set(behavior);
        log.info("MockAuthService behavior set to: {}", behavior);
    }
    
    /**
     * Set the delay for DELAYED behavior
     */
    public void setDelay(Duration delay) {
        this.delay.set(delay);
        log.info("MockAuthService delay set to: {}", delay);
    }

    /**
     * Set the failure rate for INTERMITTENT_FAILURE behavior
     */
    public void setFailureRate(double failureRate) {
        if (failureRate < 0.0 || failureRate > 1.0) {
            throw new IllegalArgumentException("Failure rate must be between 0.0 and 1.0");
        }
        this.failureRate.set(failureRate);
        log.info("MockAuthService failure rate set to: {}", failureRate);
    }
    
    @Override
    public Mono<GetUserSessionPermissionsResponse> getUserSessionPermissions(GetUserSessionPermissionsRequest request) {
        log.info("MockAuthService received getUserSessionPermissions request with behavior: {}", behavior.get());
        
        return handleRequest(() -> createUserSessionResponse(request));
    }
    
    @Override
    public Mono<GetServiceAccountPermissionsResponse> getServiceAccountPermissions(GetServiceAccountPermissionsRequest request) {
        log.info("MockAuthService received getServiceAccountPermissions request with behavior: {}", behavior.get());
        
        return handleRequest(() -> createServiceAccountResponse(request));
    }
    
    private <T> Mono<T> handleRequest(java.util.function.Supplier<T> responseSupplier) {
        AuthServiceBehavior currentBehavior = behavior.get();
        
        switch (currentBehavior) {
            case NORMAL:
                return Mono.just(responseSupplier.get());
                
            case DELAYED:
                return Mono.just(responseSupplier.get())
                        .delayElement(delay.get());
                        
            case HUNG:
                // Never complete - simulates a hung service
                return Mono.never();
                
            case TIMEOUT:
                // Delay longer than typical client timeout (15+ seconds)
                return Mono.just(responseSupplier.get())
                        .delayElement(Duration.ofSeconds(15));
                        
            case ERROR:
                return Mono.error(new StatusRuntimeException(
                    Status.INTERNAL.withDescription("Mock service error")));
                    
            case INTERMITTENT_FAILURE:
                if (random.nextDouble() < failureRate.get()) {
                    return Mono.error(new StatusRuntimeException(
                        Status.UNAVAILABLE.withDescription("Intermittent failure")));
                } else {
                    return Mono.just(responseSupplier.get());
                }
                
            default:
                return Mono.just(responseSupplier.get());
        }
    }
    
    private GetUserSessionPermissionsResponse createUserSessionResponse(GetUserSessionPermissionsRequest request) {
        return GetUserSessionPermissionsResponse.newBuilder()
                .setIss("mock-auth-service")
                .addAud("test-audience")
                .setSub("test-user-123")
                .setSubType("user")
                .setEmail("<EMAIL>")
                .setCore(CoreSessionPermissions.newBuilder()
                        .setUserId("test-user-123")
                        .setUserUuid("user-uuid-123")
                        .setUsername("testuser")
                        .setOrgId(1)
                        .setUserOrgId(1)
                        .setPceFqdn("test.illumio.com")
                        .setOrgUuid("org-uuid-123")
                        .setLocked(false)
                        .setSessionTimeoutMinutes(60)
                        .setUserType("standard")
                        .build())
                .setCloudsecure(CloudsecurePermissions.newBuilder()
                        .setTenantId("tenant-123")
                        .setUserId("user-123")
                        .addPermissions("read")
                        .addPermissions("write")
                        .build())
                .setPermLastUpdated("2024-01-01T00:00:00Z")
                .build();
    }
    
    private GetServiceAccountPermissionsResponse createServiceAccountResponse(GetServiceAccountPermissionsRequest request) {
        return GetServiceAccountPermissionsResponse.newBuilder()
                .setIss("mock-auth-service")
                .addAud("test-audience")
                .setSub("test-service-account")
                .setSubType("service_account")
                .setSvcName("test-service")
                .setSvcManaging("test-managing")
                .setAct(ServiceAccountActor.newBuilder()
                        .setSub("service-account-123")
                        .setType("service_account")
                        .setApiKeyId("api-key-123")
                        .setName("Test Service Account")
                        .setExp(**********) // Future timestamp
                        .build())
                .setCore(CoreServiceAccountPermissions.newBuilder()
                        .setOrgId(1)
                        .setPceFqdn("test.illumio.com")
                        .setOrgUuid("org-uuid-123")
                        .setSessionTimeoutMinutes(60)
                        .build())
                .setCloudsecure(CloudsecureServiceAccountPermissions.newBuilder()
                        .setId("service-account-123")
                        .setKeyId("key-123")
                        .setTenantId("tenant-123")
                        .addPermissions("read")
                        .addPermissions("write")
                        .build())
                .setPermLastUpdated("2024-01-01T00:00:00Z")
                .build();
    }
}
