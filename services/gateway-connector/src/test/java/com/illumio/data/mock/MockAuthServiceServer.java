package com.illumio.data.mock;

import io.grpc.Server;
import io.grpc.ServerBuilder;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.Duration;
import java.util.Scanner;
import java.util.concurrent.TimeUnit;

/**
 * Standalone server for running MockAuthService for manual testing
 * This allows you to test timeout and hang scenarios with the actual gateway-connector
 */
@Slf4j
public class MockAuthServiceServer {
    
    private final int port;
    private final MockAuthService mockService;
    private Server server;
    
    public MockAuthServiceServer(int port) {
        this.port = port;
        this.mockService = new MockAuthService();
    }
    
    public void start() throws IOException {
        server = ServerBuilder.forPort(port)
                .addService(mockService)
                .build()
                .start();
                
        log.info("MockAuthService server started on port {}", port);
        log.info("Available commands:");
        log.info("  normal    - Set normal behavior");
        log.info("  delayed   - Set delayed behavior (with custom delay)");
        log.info("  hung      - Set hung behavior (never responds)");
        log.info("  timeout   - Set timeout behavior (15s delay)");
        log.info("  error     - Set error behavior");
        log.info("  intermittent - Set intermittent failure behavior");
        log.info("  quit      - Shutdown server");
        
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            log.info("Shutting down MockAuthService server");
            try {
                MockAuthServiceServer.this.stop();
            } catch (InterruptedException e) {
                log.error("Error during shutdown", e);
            }
        }));
    }
    
    public void stop() throws InterruptedException {
        if (server != null) {
            server.shutdown().awaitTermination(30, TimeUnit.SECONDS);
        }
    }
    
    public void blockUntilShutdown() throws InterruptedException {
        if (server != null) {
            server.awaitTermination();
        }
    }
    
    public void runInteractiveMode() {
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            System.out.print("MockAuthService> ");
            String command = scanner.nextLine().trim().toLowerCase();
            
            switch (command) {
                case "normal":
                    mockService.setBehavior(AuthServiceBehavior.NORMAL);
                    System.out.println("Set to NORMAL behavior");
                    break;
                    
                case "delayed":
                    System.out.print("Enter delay in seconds (default 2): ");
                    String delayInput = scanner.nextLine().trim();
                    int delaySeconds = delayInput.isEmpty() ? 2 : Integer.parseInt(delayInput);
                    mockService.setBehavior(AuthServiceBehavior.DELAYED);
                    mockService.setDelay(Duration.ofSeconds(delaySeconds));
                    System.out.println("Set to DELAYED behavior with " + delaySeconds + "s delay");
                    break;
                    
                case "hung":
                    mockService.setBehavior(AuthServiceBehavior.HUNG);
                    System.out.println("Set to HUNG behavior (will never respond)");
                    break;
                    
                case "timeout":
                    mockService.setBehavior(AuthServiceBehavior.TIMEOUT);
                    System.out.println("Set to TIMEOUT behavior (15s delay)");
                    break;
                    
                case "error":
                    mockService.setBehavior(AuthServiceBehavior.ERROR);
                    System.out.println("Set to ERROR behavior");
                    break;
                    
                case "intermittent":
                    mockService.setBehavior(AuthServiceBehavior.INTERMITTENT_FAILURE);
                    System.out.println("Set to INTERMITTENT_FAILURE behavior (30% failure rate)");
                    break;
                    
                case "quit":
                case "exit":
                    System.out.println("Shutting down server...");
                    return;
                    
                default:
                    System.out.println("Unknown command: " + command);
                    System.out.println("Available: normal, delayed, hung, timeout, error, intermittent, quit");
            }
        }
    }
    
    public static void main(String[] args) throws IOException, InterruptedException {
        int port = args.length > 0 ? Integer.parseInt(args[0]) : 8081;
        
        MockAuthServiceServer server = new MockAuthServiceServer(port);
        server.start();
        
        // Run in interactive mode
        server.runInteractiveMode();
        
        server.stop();
    }
}
