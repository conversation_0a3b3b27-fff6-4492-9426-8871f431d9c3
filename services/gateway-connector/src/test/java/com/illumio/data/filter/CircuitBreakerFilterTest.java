package com.illumio.data.filter;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.net.ConnectException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CircuitBreakerFilterTest {

    @Mock
    GatewayFilterChain chain;

    MeterRegistry registry;
    CircuitBreakerFilter filter;

    @BeforeEach
    void setUp() {
        registry = new SimpleMeterRegistry();
        filter = new CircuitBreakerFilter(registry);
    }

    @Test
    void noError_passthrough_noCountersIncremented() {
        CircuitBreakerFilter.Config cfg = new CircuitBreakerFilter.Config();
        GatewayFilter gf = filter.apply(cfg);

        ServerWebExchange exchange = MockServerWebExchange.from(
                MockServerHttpRequest.get("http://localhost/api").build());

        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        StepVerifier.create(gf.filter(exchange, chain)).verifyComplete();

        verify(chain).filter(any(ServerWebExchange.class));
        assertEquals(0.0, registry.counter("gateway.circuit.breaker.open").count(), 0.0001);
        assertEquals(0.0, registry.counter("gateway.circuit.breaker.half.open").count(), 0.0001);
        assertFalse(exchange.getResponse().isCommitted());
    }

    @Test
    void recordableError_connectException_opensBreaker_andReturns503() throws Exception {
        CircuitBreakerFilter.Config cfg = new CircuitBreakerFilter.Config();
        cfg.setServiceName("orders");
        cfg.setSlidingWindowSize(1);
        cfg.setMinimumNumberOfCalls(1);
        cfg.setFailureRateThreshold(50.0f); // 100% failure > 50% => OPEN
        cfg.setWaitDurationInOpenState(1);  // seconds

        GatewayFilter gf = filter.apply(cfg);

        MockServerWebExchange exchange = MockServerWebExchange.from(
                MockServerHttpRequest.get("http://localhost/orders").build());

        when(chain.filter(any(ServerWebExchange.class)))
                .thenReturn(Mono.error(new ConnectException("Connection refused")));

        StepVerifier.create(gf.filter(exchange, chain)).verifyComplete();

        assertEquals(HttpStatus.SERVICE_UNAVAILABLE, exchange.getResponse().getStatusCode());
        assertEquals(MediaType.APPLICATION_JSON_VALUE, exchange.getResponse().getHeaders().getFirst("Content-Type"));
        assertEquals("60", exchange.getResponse().getHeaders().getFirst("Retry-After"));

        String body = exchange.getResponse().getBodyAsString().block(); // Mono<String> in your Spring version
        assertNotNull(body);
        assertTrue(body.contains("\"error_code\":\"CIRCUIT_BREAKER_OPEN\""));
        assertTrue(body.contains("\"service\":\"orders\""));

        assertEquals(1.0, registry.counter("gateway.circuit.breaker.open").count(), 0.0001);
        assertEquals(0.0, registry.counter("gateway.circuit.breaker.half.open").count(), 0.0001);
    }

    @Test
    void afterOpen_wait_allowsHalfOpen_andCounterIncrements() throws Exception {
        CircuitBreakerFilter.Config cfg = new CircuitBreakerFilter.Config();
        cfg.setServiceName("payments");
        cfg.setSlidingWindowSize(1);
        cfg.setMinimumNumberOfCalls(1);
        cfg.setFailureRateThreshold(50.0f);
        cfg.setWaitDurationInOpenState(1); // seconds

        GatewayFilter gf = filter.apply(cfg);

        // 1) First call fails -> breaker opens (CLOSED -> OPEN)
        ServerWebExchange ex1 = MockServerWebExchange.from(
                MockServerHttpRequest.get("http://localhost/pay").build());
        when(chain.filter(any(ServerWebExchange.class)))
                .thenReturn(Mono.error(new ConnectException("Connection refused")),
                        Mono.empty()); // second call will be permitted after wait

        StepVerifier.create(gf.filter(ex1, chain)).verifyComplete();
        assertEquals(1.0, registry.counter("gateway.circuit.breaker.open").count(), 0.0001);

        // 2) Wait for open state to elapse, then next call should move to HALF_OPEN
        Thread.sleep(1100L); // waitDurationInOpenState = 1s

        ServerWebExchange ex2 = MockServerWebExchange.from(
                MockServerHttpRequest.get("http://localhost/pay").build());

        StepVerifier.create(gf.filter(ex2, chain)).verifyComplete();

        assertEquals(1.0, registry.counter("gateway.circuit.breaker.half.open").count(), 0.0001);
    }

    @Test
    void nonRecordableError_doesNotOpenBreaker_butStillReturns503FromHandler() {
        CircuitBreakerFilter.Config cfg = new CircuitBreakerFilter.Config();
        cfg.setServiceName("inventory");
        cfg.setSlidingWindowSize(1);
        cfg.setMinimumNumberOfCalls(1);
        cfg.setFailureRateThreshold(50.0f);
        cfg.setWaitDurationInOpenState(1);

        GatewayFilter gf = filter.apply(cfg);

        MockServerWebExchange exchange = MockServerWebExchange.from(
                MockServerHttpRequest.get("http://localhost/inv").build());

        // This RuntimeException doesn't match the recordException predicate
        when(chain.filter(any(ServerWebExchange.class)))
                .thenReturn(Mono.error(new RuntimeException("something else")));

        StepVerifier.create(gf.filter(exchange, chain)).verifyComplete();

        assertEquals(HttpStatus.SERVICE_UNAVAILABLE, exchange.getResponse().getStatusCode());
        String body = exchange.getResponse().getBodyAsString().block(); // Mono<String> -> block()
        assertNotNull(body);
        assertTrue(body.contains("\"error_code\":\"CIRCUIT_BREAKER_OPEN\""));
        assertTrue(body.contains("\"service\":\"inventory\""));

        assertEquals(0.0, registry.counter("gateway.circuit.breaker.open").count(), 0.0001);
        assertEquals(0.0, registry.counter("gateway.circuit.breaker.half.open").count(), 0.0001);
    }
}
