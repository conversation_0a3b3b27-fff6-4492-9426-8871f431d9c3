package com.illumio.data.filter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class AsgardFilterTest {
    private AsgardFilter filter;

    private static String SUBDOMIAN_HEADER = "X-tenant-subdomain";
    @BeforeEach
    void setUp() {
        filter = new AsgardFilter();
    }

    @Test
    void testSubdomainExtractionFromRefererInHeaders() {
        // Setup config with a test message
        AsgardFilter.Config config = new AsgardFilter.Config();
        config.setMessage("Test Subdomain Extraction");

        // Create the GatewayFilter
        GatewayFilter gatewayFilter = filter.apply(config);

        // Mock the request
        MockServerHttpRequest request = MockServerHttpRequest.get("http://wells.console.illumio.io/asgard/test")
                .build();
        ServerWebExchange exchange = MockServerWebExchange.from(request);

        // Capture mutated exchange
        ArgumentCaptor<ServerWebExchange> exchangeCaptor = ArgumentCaptor.forClass(ServerWebExchange.class);

        // Mock chain
        GatewayFilterChain chain = mock(GatewayFilterChain.class);
        when(chain.filter(any())).thenReturn(Mono.empty());

        // Apply the filter
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        // Verify the chain was invoked
        verify(chain, times(1)).filter(exchangeCaptor.capture());

        ServerWebExchange mutatedExchange = exchangeCaptor.getValue();
        assertEquals("wells", mutatedExchange.getRequest().getHeaders().getFirst(SUBDOMIAN_HEADER));
    }
}