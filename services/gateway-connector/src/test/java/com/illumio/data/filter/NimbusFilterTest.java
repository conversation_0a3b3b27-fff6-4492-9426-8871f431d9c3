package com.illumio.data.filter;

import authservice.GetServiceAccountPermissionsResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.filter.utils.DummyServiceAccountSession;
import com.illumio.data.service.JwtService;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.lang.reflect.Field;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class NimbusFilterTest {

    @Mock
    JwtService jwtService;

    @Mock
    private GatewayFilterChain chain;

    NimbusFilter filter;

    @BeforeEach
    void setUp() {
        filter = new NimbusFilter(jwtService, new SimpleMeterRegistry());
    }

    @Test
    void testJwtInjection_withDummySessionResponse() throws Exception {
        GetServiceAccountPermissionsResponse serviceAccountPermissionsResponse = DummyServiceAccountSession.createGetServiceAccountPermissionsResponse();

        MockServerHttpRequest request = MockServerHttpRequest.get("http://localhost/integrations/")
                .build();

        ServerWebExchange exchange = MockServerWebExchange.from(request);
        exchange.getAttributes().put("serviceAccountSession", serviceAccountPermissionsResponse);

        when(jwtService.generatePermissionsJwt(any(), eq(10))).thenReturn("dummy.jwt.token");

        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        StepVerifier.create(filter.apply(new NimbusFilter.Config()).filter(exchange, chain))
                .verifyComplete();


        verify(jwtService).generatePermissionsJwt(any(), eq(10));
    }

    @Test
    void testJwtPayloadSizeTooLarge_skipsPermissions() throws Exception {
        // Inflate payload
        GetServiceAccountPermissionsResponse serviceAccountPermissionsResponse = DummyServiceAccountSession.createLargePayloadServiceAccountPermissionsResponse();

        MockServerHttpRequest request = MockServerHttpRequest.get("http://localhost/integrations/")
                .build();

        ServerWebExchange exchange = MockServerWebExchange.from(request);
        exchange.getAttributes().put("serviceAccountSession", serviceAccountPermissionsResponse);

        when(chain.filter(any())).thenReturn(Mono.empty());
        when(jwtService.generatePermissionsJwt(any(), eq(10))).thenReturn("large.jwt.token");

        filter.apply(new NimbusFilter.Config()).filter(exchange, chain).block();

        verify(jwtService).generatePermissionsJwt(argThat(map -> {
            Object permExcluded = ((java.util.Map<?, ?>) map).get("perm_excluded");
            return Boolean.TRUE.equals(permExcluded);
        }), eq(10));
    }
//
    @Test
    void testJwtGenerationFailure() throws Exception {
        GetServiceAccountPermissionsResponse serviceAccountPermissionsResponse = DummyServiceAccountSession.createLargePayloadServiceAccountPermissionsResponse();

        MockServerHttpRequest request = MockServerHttpRequest.get("http://localhost/integrations/")
                .build();

        ServerWebExchange exchange = MockServerWebExchange.from(request);
        exchange.getAttributes().put("serviceAccountSession", serviceAccountPermissionsResponse);


        when(jwtService.generatePermissionsJwt(any(), eq(10)))
                .thenThrow(new NoSuchAlgorithmException("Algorithm not found"));

        try {
            filter.apply(new NimbusFilter.Config()).filter(exchange, chain).block();
        } catch (RuntimeException e) {
            assertEquals("Algorithm not found", e.getCause().getMessage());
        }
    }

    @Test
    void testJwtGenerationInvalidKeySpec_isWrapped() throws Exception {
        GetServiceAccountPermissionsResponse resp = DummyServiceAccountSession.createLargePayloadServiceAccountPermissionsResponse();
        ServerWebExchange exch = MockServerWebExchange.from(
                MockServerHttpRequest.get("http://localhost/integrations/").build());
        exch.getAttributes().put("serviceAccountSession", resp);

        when(jwtService.generatePermissionsJwt(any(), eq(10)))
                .thenThrow(new InvalidKeySpecException("Bad key"));

        RuntimeException thrown = org.junit.jupiter.api.Assertions.assertThrows(
                RuntimeException.class,
                () -> filter.apply(new NimbusFilter.Config()).filter(exch, chain).block()
        );
        assertEquals("Bad key", thrown.getCause().getMessage());
    }

    @Test
    void testJsonProcessingException_isWrapped() throws Exception {
        NimbusFilter f = new NimbusFilter(jwtService, new SimpleMeterRegistry()); // original ctor

        // Spy and make readValue throw
        ObjectMapper mapper = Mockito.spy(new ObjectMapper());
        doThrow(new JsonProcessingException("bad json") {}).when(mapper)
                .readValue(any(String.class), any(TypeReference.class));

        // Swap the private field via reflection
        Field omField = NimbusFilter.class.getDeclaredField("objectMapper");
        omField.setAccessible(true);
        omField.set(f, mapper);

        var resp = DummyServiceAccountSession.createGetServiceAccountPermissionsResponse();
        ServerWebExchange exch = MockServerWebExchange.from(
                MockServerHttpRequest.get("http://localhost/integrations/").build());
        exch.getAttributes().put("serviceAccountSession", resp);

        RuntimeException thrown = org.junit.jupiter.api.Assertions.assertThrows(
                RuntimeException.class,
                () -> f.apply(new NimbusFilter.Config()).filter(exch, chain).block()
        );
        assertEquals("bad json", thrown.getCause().getMessage());
    }
}
