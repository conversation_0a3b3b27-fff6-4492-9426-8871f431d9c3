# MockAuthService - Testing AuthService Timeout and Hang Scenarios

This package provides a comprehensive mock implementation of the AuthService for testing various failure scenarios including timeouts, hangs, and errors.

## Components

### 1. AuthServiceBehavior (Enum)
Defines different behaviors the mock service can exhibit:
- `NORMAL` - Returns successful responses immediately
- `DELAYED` - Returns responses after a configurable delay
- `HUNG` - Never responds (simulates a hung service)
- `TIMEOUT` - Delays longer than typical client timeout (15+ seconds)
- `ERROR` - Returns gRPC errors
- `INTERMITTENT_FAILURE` - Randomly fails 30% of requests

### 2. MockAuthService
The main mock implementation that:
- Implements both `getUserSessionPermissions` and `getServiceAccountPermissions`
- Supports all behavior types defined in `AuthServiceBehavior`
- Returns realistic test data
- Provides thread-safe behavior switching

### 3. MockAuthServiceServer
A standalone server for manual testing:
- Runs on configurable port (default 8081)
- Interactive command-line interface
- Real-time behavior switching
- Perfect for testing with actual gateway-connector

### 4. MockAuthServiceTestHelper
Utility class for integration tests:
- Easy setup/teardown of in-process gRPC server
- Simplified behavior configuration
- Channel management

## Usage Examples

### Manual Testing with Standalone Server

1. **Start the mock server:**
```bash
cd services/gateway-connector
./gradlew test --tests MockAuthServiceServer
# Or run directly:
java -cp build/classes/test:build/classes/main com.illumio.data.mock.MockAuthServiceServer 8081
```

2. **Configure gateway-connector to use mock:**
Update `application-local.yml`:
```yaml
grpc:
  authservice:
    host: localhost
    port: 8081
```

3. **Test different scenarios:**
```
MockAuthService> normal      # Normal responses
MockAuthService> delayed     # Delayed responses (specify seconds)
MockAuthService> hung        # Never responds
MockAuthService> timeout     # 15s delay
MockAuthService> error       # Return errors
MockAuthService> intermittent # Random failures
MockAuthService> quit        # Shutdown
```

### Integration Testing

```java
@Test
void testAuthServiceTimeout() throws Exception {
    MockAuthServiceTestHelper helper = new MockAuthServiceTestHelper();
    helper.start(AuthServiceBehavior.HUNG);
    
    try {
        // Your test code here
        // The AuthService will never respond, causing timeouts
    } finally {
        helper.stop();
    }
}
```

### Unit Testing with PermissionFilter

```java
@Test
void testPermissionFilterWithTimeout() {
    // Setup mock service
    mockAuthHelper.setBehavior(AuthServiceBehavior.TIMEOUT);
    
    // Create filter and test
    GatewayFilter filter = permissionFilter.apply(new PermissionFilter.Config());
    Mono<Void> result = filter.filter(exchange, chain);
    
    // Verify timeout handling
    StepVerifier.create(result)
        .expectComplete()
        .verify(Duration.ofSeconds(5));
}
```

## Testing Scenarios

### 1. Normal Operation
- **Behavior**: `NORMAL`
- **Expected**: Fast, successful responses
- **Use Case**: Baseline functionality testing

### 2. Slow Service
- **Behavior**: `DELAYED` (1-3 seconds)
- **Expected**: Responses within timeout window
- **Use Case**: Testing performance under load

### 3. Service Hang
- **Behavior**: `HUNG`
- **Expected**: Client timeout after configured duration
- **Use Case**: Testing timeout handling and circuit breaker

### 4. Service Timeout
- **Behavior**: `TIMEOUT` (15+ seconds)
- **Expected**: Client timeout before service responds
- **Use Case**: Testing long-running request handling

### 5. Service Errors
- **Behavior**: `ERROR`
- **Expected**: gRPC error responses
- **Use Case**: Testing error handling and fallback mechanisms

### 6. Intermittent Failures
- **Behavior**: `INTERMITTENT_FAILURE`
- **Expected**: Mix of success and failure responses
- **Use Case**: Testing retry logic and resilience

## Configuration

### Timeout Settings
The gateway-connector timeout is configured in `application.yml`:
```yaml
grpctimeout: "10s"  # Adjust for testing
```

### Cache Settings
Caching behavior can be tested by adjusting:
```yaml
cache:
  maximum-size: 1000
  expire-after-write: 10  # minutes
```

## Running Tests

```bash
# Run all timeout-related tests
./gradlew test --tests "*Timeout*"

# Run specific test class
./gradlew test --tests AuthServiceTimeoutTest

# Run with debug logging
./gradlew test --tests AuthServiceTimeoutTest --debug
```

## Troubleshooting

### Common Issues

1. **Port conflicts**: Change the port in MockAuthServiceServer
2. **Timeout too short**: Increase `grpctimeout` in test configuration
3. **Cache interference**: Clear caches between tests
4. **gRPC connection issues**: Ensure proper cleanup in tests

### Debug Tips

1. Enable debug logging for gRPC:
```yaml
logging:
  level:
    io.grpc: DEBUG
    com.illumio.data.mock: DEBUG
```

2. Monitor server behavior:
```bash
# Check if server is responding
grpcurl -plaintext localhost:8081 list
```

3. Verify timeout configuration:
```java
// In tests, verify the timeout is applied
Duration actualTimeout = permissionFilter.grpcTimeout;
assertEquals(Duration.ofSeconds(5), actualTimeout);
```
