package com.illumio.data.filter;

import authservice.GetServiceAccountPermissionsResponse;
import authservice.GetUserSessionPermissionsResponse;
import authservice.ReactorAuthServiceGrpc;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.illumio.data.mock.AuthServiceBehavior;
import com.illumio.data.mock.MockAuthServiceTestHelper;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.HttpStatus;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * Test PermissionFilter behavior with different AuthService scenarios
 */
@Slf4j
public class PermissionFilterTimeoutTest {
    private MeterRegistry meterRegistry;

    private MockAuthServiceTestHelper mockAuthHelper;
    private PermissionFilter permissionFilter;
    private Cache<String, GetUserSessionPermissionsResponse> userCache;
    private Cache<String, GetServiceAccountPermissionsResponse> serviceAccountCache;
    
    @BeforeEach
    void setUp() throws Exception {
        mockAuthHelper = new MockAuthServiceTestHelper();
        mockAuthHelper.start();
        meterRegistry = new SimpleMeterRegistry();
        
        // Create caches
        userCache = Caffeine.newBuilder()
                .maximumSize(100)
                .expireAfterWrite(1, TimeUnit.MINUTES)
                .build();
                
        serviceAccountCache = Caffeine.newBuilder()
                .maximumSize(100)
                .expireAfterWrite(1, TimeUnit.MINUTES)
                .build();
        
        // Create PermissionFilter with mock service
        ReactorAuthServiceGrpc.ReactorAuthServiceStub authServiceStub = mockAuthHelper.getAuthServiceStub();
        permissionFilter = new PermissionFilter(userCache, authServiceStub, serviceAccountCache, meterRegistry);
        
        // Set a short timeout for testing
        permissionFilter.grpcTimeout = Duration.ofSeconds(2);
    }
    
    @AfterEach
    void tearDown() throws Exception {
        if (mockAuthHelper != null) {
            mockAuthHelper.stop();
        }
    }
    
    @Test
    void testNormalAuthServiceResponse() {
        // Given
        mockAuthHelper.setBehavior(AuthServiceBehavior.NORMAL);
        ServerWebExchange exchange = createExchangeWithCsiToken("valid-csi-token");
        GatewayFilterChain chain = createMockChain();

        // When
        GatewayFilter filter = permissionFilter.apply(new PermissionFilter.Config());
        Mono<Void> result = filter.filter(exchange, chain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        // Verify that the session was stored in exchange attributes
        assert exchange.getAttributes().containsKey("grpcSession") :
            "Expected grpcSession to be set in exchange attributes, but it was not found. Available keys: " +
            exchange.getAttributes().keySet();
    }
    
    @Test
    void testDelayedAuthServiceResponse() {
        // Given
        mockAuthHelper.setBehavior(AuthServiceBehavior.DELAYED);
        mockAuthHelper.setDelay(Duration.ofMillis(500)); // Less than timeout
        ServerWebExchange exchange = createExchangeWithCsiToken("valid-csi-token");
        GatewayFilterChain chain = createMockChain();
        
        // When
        GatewayFilter filter = permissionFilter.apply(new PermissionFilter.Config());
        Mono<Void> result = filter.filter(exchange, chain);
        
        // Then
        StepVerifier.create(result)
                .verifyComplete();
    }
    
    @Test
    void testAuthServiceTimeout() {
        // Given
        mockAuthHelper.setBehavior(AuthServiceBehavior.HUNG); // Never responds
        ServerWebExchange exchange = createExchangeWithCsiToken("valid-csi-token");
        GatewayFilterChain chain = createMockChain();

        // When
        GatewayFilter filter = permissionFilter.apply(new PermissionFilter.Config());
        Mono<Void> result = filter.filter(exchange, chain);

        // Then - Should complete due to timeout handling in PermissionFilter
        StepVerifier.create(result)
                .expectComplete()
                .verify(Duration.ofSeconds(5));

        // Verify that response status was set to SERVICE_UNAVAILABLE
        // Note: The status is set during the reactive chain execution
        assert exchange.getResponse().getStatusCode() == HttpStatus.SERVICE_UNAVAILABLE :
            "Expected SERVICE_UNAVAILABLE but got: " + exchange.getResponse().getStatusCode();
    }
    
    @Test
    void testAuthServiceError() {
        // Given
        mockAuthHelper.setBehavior(AuthServiceBehavior.ERROR);
        ServerWebExchange exchange = createExchangeWithCsiToken("valid-csi-token");
        GatewayFilterChain chain = createMockChain();

        // When
        GatewayFilter filter = permissionFilter.apply(new PermissionFilter.Config());
        Mono<Void> result = filter.filter(exchange, chain);

        // Then
        StepVerifier.create(result)
                .verifyComplete();

        // Verify that response status was set to SERVICE_UNAVAILABLE
        assert exchange.getResponse().getStatusCode() == HttpStatus.SERVICE_UNAVAILABLE :
            "Expected SERVICE_UNAVAILABLE but got: " + exchange.getResponse().getStatusCode();
    }
    
    @Test
    void testServiceAccountTimeout() {
        // Given
        mockAuthHelper.setBehavior(AuthServiceBehavior.HUNG);
        ServerWebExchange exchange = createExchangeWithApiKey("test-api-key", "test-secret");
        GatewayFilterChain chain = createMockChain();

        // When
        GatewayFilter filter = permissionFilter.apply(new PermissionFilter.Config());
        Mono<Void> result = filter.filter(exchange, chain);

        // Then - Service account path has timeout configured, should complete with error status
        StepVerifier.create(result)
                .expectComplete()
                .verify(Duration.ofSeconds(5));

        // Verify that response status was set to INTERNAL_SERVER_ERROR
        // Note: Service account error handler sets INTERNAL_SERVER_ERROR
        assert exchange.getResponse().getStatusCode() == HttpStatus.INTERNAL_SERVER_ERROR :
            "Expected INTERNAL_SERVER_ERROR but got: " + exchange.getResponse().getStatusCode();
    }
    
    private ServerWebExchange createExchangeWithCsiToken(String csiToken) {
        MockServerHttpRequest request = MockServerHttpRequest.get("/api/test")
                .header("X-csi", csiToken)  // PermissionFilter looks for X-csi header, not cookie
                .header("X-Forwarded-For", "127.0.0.1")
                .build();
        return MockServerWebExchange.from(request);
    }
    
    private ServerWebExchange createExchangeWithApiKey(String apiKey, String apiSecret) {
        MockServerHttpRequest request = MockServerHttpRequest.get("/api/test")
                .header("X-api-key", apiKey)  // PermissionFilter looks for X-api-key (lowercase)
                .header("X-api-secret", apiSecret)  // PermissionFilter looks for X-api-secret (lowercase)
                .build();
        return MockServerWebExchange.from(request);
    }
    
    private GatewayFilterChain createMockChain() {
        return exchange -> {
            log.info("Mock chain filter called - request passed through");
            return Mono.empty();
        };
    }
}
