package com.illumio.data.filter;

import authservice.GetUserRequest;
import authservice.GetUserResponse;
import authservice.GetUserSessionPermissionsRequest;
import authservice.GetUserSessionPermissionsResponse;
import authservice.ReactorAuthServiceGrpc;
import com.illumio.data.filter.utils.DummyUserSession;
import com.illumio.data.service.DateTimeUtils;
import com.illumio.data.service.MagicLink;
import io.micrometer.core.instrument.Counter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import com.github.benmanes.caffeine.cache.Cache;
import io.micrometer.core.instrument.MeterRegistry;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MagicLinkAuthFilterTest {

    @Mock
    private ReactorAuthServiceGrpc.ReactorAuthServiceStub authServiceStub;

    @Mock
    private MagicLink magicLink;

    @Mock
    private GatewayFilterChain chain;

    @Mock
    private Cache<String, GetUserSessionPermissionsResponse> csiPermissionCache;

    @Mock
    private MeterRegistry meterRegistry;

    @Mock
    private Counter mockCounter;

    private MagicLinkAuthFilter filter;

    private static final String TEST_USER_ID = "user-123";
    private static final String TEST_SESSION_ID = "session-456";
    private static final String TEST_UCID = "ucid-789";
    private static final String TEST_TENANT_ID = "tenant-abc";
    private static final String TEST_JWT_TOKEN = "mock.jwt.token";
    private static final String TEST_EXPIRY_TIME = "2025-08-01T12:00:00Z";
    private static final String TEST_CSI_TOKEN = "test-csi-token";
    private static final int TEST_EXPIRY_DAYS = 7;

    @BeforeEach
    void setUp() {
        when(meterRegistry.counter(anyString())).thenReturn(mockCounter);
        filter = new MagicLinkAuthFilter(authServiceStub, magicLink, csiPermissionCache, meterRegistry);
        ReflectionTestUtils.setField(filter, "expiryDays", TEST_EXPIRY_DAYS);
        ReflectionTestUtils.setField(filter, "grpcTimeout", Duration.ofSeconds(5));
    }

    @Test
    void testSuccessfulAuthenticationReturnsJwtToken() {
        // Arrange
        MagicLinkAuthFilter.Config config = new MagicLinkAuthFilter.Config();
        GatewayFilter gatewayFilter = filter.apply(config);

        GetUserSessionPermissionsResponse grpcSession = DummyUserSession.createDummySessionResponse();
        GetUserResponse userResponse = GetUserResponse.newBuilder()
                .setId(TEST_USER_ID)
                .build();

        // Mock the cache to return the grpcSession for the CSI token
        when(csiPermissionCache.getIfPresent(TEST_CSI_TOKEN)).thenReturn(grpcSession);

        ReactorAuthServiceGrpc.ReactorAuthServiceStub mockStubWithMetadata = mock(ReactorAuthServiceGrpc.ReactorAuthServiceStub.class);
        when(authServiceStub.withInterceptors(any())).thenReturn(mockStubWithMetadata);
        when(mockStubWithMetadata.getUser(any(GetUserRequest.class))).thenReturn(Mono.just(userResponse));
        when(magicLink.generateJwtToken(anyString(), eq(TEST_USER_ID)))
                .thenReturn(TEST_JWT_TOKEN);

        MockServerHttpRequest request = MockServerHttpRequest.get("/api/test")
                .header("X-csi", TEST_CSI_TOKEN)
                .header("user_uuid", TEST_USER_ID)
                .header("uc_session", TEST_SESSION_ID)
                .header("ucid", TEST_UCID)
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        try (MockedStatic<DateTimeUtils> mockedDateTimeUtils = mockStatic(DateTimeUtils.class)) {
            mockedDateTimeUtils.when(() -> DateTimeUtils.getExpiry("America/Los_Angeles", TEST_EXPIRY_DAYS))
                    .thenReturn(TEST_EXPIRY_TIME);

            // Act & Assert
            StepVerifier.create(gatewayFilter.filter(exchange, chain))
                    .verifyComplete();

            // Verify response
            assertEquals(HttpStatus.OK, exchange.getResponse().getStatusCode());
            verify(magicLink).generateJwtToken(anyString(), eq(TEST_USER_ID));
        }
    }

    @Test
    void testMissingCsiTokenReturnsUnauthorized() {
        // Arrange
        MagicLinkAuthFilter.Config config = new MagicLinkAuthFilter.Config();
        GatewayFilter gatewayFilter = filter.apply(config);

        MockServerHttpRequest request = MockServerHttpRequest.get("/api/test")
                .header("user_uuid", TEST_USER_ID)
                .header("uc_session", TEST_SESSION_ID)
                .build(); // Missing X-csi header
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // Act & Assert
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        assertEquals(HttpStatus.UNAUTHORIZED, exchange.getResponse().getStatusCode());
        verify(csiPermissionCache, never()).getIfPresent(anyString());
        verify(magicLink, never()).generateJwtToken(anyString(), anyString());
    }



    @Test
    void testMissingUserIdReturnsUnauthorized() {
        // Arrange
        MagicLinkAuthFilter.Config config = new MagicLinkAuthFilter.Config();
        GatewayFilter gatewayFilter = filter.apply(config);

        GetUserSessionPermissionsResponse grpcSession = DummyUserSession.createDummySessionResponse();
        when(csiPermissionCache.getIfPresent(TEST_CSI_TOKEN)).thenReturn(grpcSession);

        MockServerHttpRequest request = MockServerHttpRequest.get("/api/test")
                .header("X-csi", TEST_CSI_TOKEN)
                .header("uc_session", TEST_SESSION_ID)
                .build(); // Missing user_uuid header
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // Act & Assert
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        assertEquals(HttpStatus.UNAUTHORIZED, exchange.getResponse().getStatusCode());
        verify(authServiceStub, never()).withInterceptors(any());
        verify(magicLink, never()).generateJwtToken(anyString(), anyString());
    }

    @Test
    void testMissingSessionIdReturnsUnauthorized() {
        // Arrange
        MagicLinkAuthFilter.Config config = new MagicLinkAuthFilter.Config();
        GatewayFilter gatewayFilter = filter.apply(config);

        GetUserSessionPermissionsResponse grpcSession = DummyUserSession.createDummySessionResponse();
        when(csiPermissionCache.getIfPresent(TEST_CSI_TOKEN)).thenReturn(grpcSession);

        MockServerHttpRequest request = MockServerHttpRequest.get("/api/test")
                .header("X-csi", TEST_CSI_TOKEN)
                .header("user_uuid", TEST_USER_ID)
                .build(); // Missing uc_session header
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // Act & Assert
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        assertEquals(HttpStatus.UNAUTHORIZED, exchange.getResponse().getStatusCode());
        verify(authServiceStub, never()).withInterceptors(any());
        verify(magicLink, never()).generateJwtToken(anyString(), anyString());
    }

    @Test
    void testUserIdMismatchReturnsUnauthorized() {
        // Arrange
        MagicLinkAuthFilter.Config config = new MagicLinkAuthFilter.Config();
        GatewayFilter gatewayFilter = filter.apply(config);

        GetUserSessionPermissionsResponse grpcSession = DummyUserSession.createDummySessionResponse();
        GetUserResponse userResponse = GetUserResponse.newBuilder()
                .setId("different-user-id") // Different from requested user ID
                .build();

        when(csiPermissionCache.getIfPresent(TEST_CSI_TOKEN)).thenReturn(grpcSession);
        ReactorAuthServiceGrpc.ReactorAuthServiceStub mockStubWithMetadata = mock(ReactorAuthServiceGrpc.ReactorAuthServiceStub.class);
        when(authServiceStub.withInterceptors(any())).thenReturn(mockStubWithMetadata);
        when(mockStubWithMetadata.getUser(any(GetUserRequest.class))).thenReturn(Mono.just(userResponse));

        MockServerHttpRequest request = MockServerHttpRequest.get("/api/test")
                .header("X-csi", TEST_CSI_TOKEN)
                .header("user_uuid", TEST_USER_ID)
                .header("uc_session", TEST_SESSION_ID)
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // Act & Assert
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        assertEquals(HttpStatus.UNAUTHORIZED, exchange.getResponse().getStatusCode());
        verify(magicLink, never()).generateJwtToken(anyString(), anyString());
    }

    @Test
    void testAuthServiceErrorReturnsInternalServerError() {
        // Arrange
        MagicLinkAuthFilter.Config config = new MagicLinkAuthFilter.Config();
        GatewayFilter gatewayFilter = filter.apply(config);

        GetUserSessionPermissionsResponse grpcSession = DummyUserSession.createDummySessionResponse();
        when(csiPermissionCache.getIfPresent(TEST_CSI_TOKEN)).thenReturn(grpcSession);

        ReactorAuthServiceGrpc.ReactorAuthServiceStub mockStubWithMetadata = mock(ReactorAuthServiceGrpc.ReactorAuthServiceStub.class);
        when(authServiceStub.withInterceptors(any())).thenReturn(mockStubWithMetadata);
        when(mockStubWithMetadata.getUser(any(GetUserRequest.class)))
                .thenReturn(Mono.error(new RuntimeException("Auth service error")));

        MockServerHttpRequest request = MockServerHttpRequest.get("/api/test")
                .header("X-csi", TEST_CSI_TOKEN)
                .header("user_uuid", TEST_USER_ID)
                .header("uc_session", TEST_SESSION_ID)
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // Act & Assert
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exchange.getResponse().getStatusCode());
        verify(magicLink, never()).generateJwtToken(anyString(), anyString());
    }

    @Test
    void testSuccessfulAuthenticationWithoutUcidUsesDefault() {
        // Arrange
        MagicLinkAuthFilter.Config config = new MagicLinkAuthFilter.Config();
        GatewayFilter gatewayFilter = filter.apply(config);

        GetUserSessionPermissionsResponse grpcSession = DummyUserSession.createDummySessionResponse();
        GetUserResponse userResponse = GetUserResponse.newBuilder()
                .setId(TEST_USER_ID)
                .build();

        when(csiPermissionCache.getIfPresent(TEST_CSI_TOKEN)).thenReturn(grpcSession);
        ReactorAuthServiceGrpc.ReactorAuthServiceStub mockStubWithMetadata = mock(ReactorAuthServiceGrpc.ReactorAuthServiceStub.class);
        when(authServiceStub.withInterceptors(any())).thenReturn(mockStubWithMetadata);
        when(mockStubWithMetadata.getUser(any(GetUserRequest.class))).thenReturn(Mono.just(userResponse));
        when(magicLink.generateJwtToken(anyString(), eq(TEST_USER_ID)))
                .thenReturn(TEST_JWT_TOKEN);

        MockServerHttpRequest request = MockServerHttpRequest.get("/api/test")
                .header("X-csi", TEST_CSI_TOKEN)
                .header("user_uuid", TEST_USER_ID)
                .header("uc_session", TEST_SESSION_ID)
                // No ucid header
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        try (MockedStatic<DateTimeUtils> mockedDateTimeUtils = mockStatic(DateTimeUtils.class)) {
            mockedDateTimeUtils.when(() -> DateTimeUtils.getExpiry("America/Los_Angeles", TEST_EXPIRY_DAYS))
                    .thenReturn(TEST_EXPIRY_TIME);

            // Act & Assert
            StepVerifier.create(gatewayFilter.filter(exchange, chain))
                    .verifyComplete();

            assertEquals(HttpStatus.OK, exchange.getResponse().getStatusCode());
            verify(magicLink).generateJwtToken(anyString(), eq(TEST_USER_ID));
        }
    }

    @Test
    void testCsiValidationWithGrpcCall() {
        // Arrange
        MagicLinkAuthFilter.Config config = new MagicLinkAuthFilter.Config();
        GatewayFilter gatewayFilter = filter.apply(config);

        GetUserSessionPermissionsResponse grpcSession = DummyUserSession.createDummySessionResponse();
        GetUserResponse userResponse = GetUserResponse.newBuilder()
                .setId(TEST_USER_ID)
                .build();

        // Cache miss, so gRPC call will be made
        when(csiPermissionCache.getIfPresent(TEST_CSI_TOKEN)).thenReturn(null);
        when(authServiceStub.getUserSessionPermissions(any(GetUserSessionPermissionsRequest.class)))
                .thenReturn(Mono.just(grpcSession));

        ReactorAuthServiceGrpc.ReactorAuthServiceStub mockStubWithMetadata = mock(ReactorAuthServiceGrpc.ReactorAuthServiceStub.class);
        when(authServiceStub.withInterceptors(any())).thenReturn(mockStubWithMetadata);
        when(mockStubWithMetadata.getUser(any(GetUserRequest.class))).thenReturn(Mono.just(userResponse));
        when(magicLink.generateJwtToken(anyString(), eq(TEST_USER_ID)))
                .thenReturn(TEST_JWT_TOKEN);

        MockServerHttpRequest request = MockServerHttpRequest.get("/api/test")
                .header("X-csi", TEST_CSI_TOKEN)
                .header("user_uuid", TEST_USER_ID)
                .header("uc_session", TEST_SESSION_ID)
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        try (MockedStatic<DateTimeUtils> mockedDateTimeUtils = mockStatic(DateTimeUtils.class)) {
            mockedDateTimeUtils.when(() -> DateTimeUtils.getExpiry("America/Los_Angeles", TEST_EXPIRY_DAYS))
                    .thenReturn(TEST_EXPIRY_TIME);

            // Act & Assert
            StepVerifier.create(gatewayFilter.filter(exchange, chain))
                    .verifyComplete();

            assertEquals(HttpStatus.OK, exchange.getResponse().getStatusCode());
            verify(authServiceStub).getUserSessionPermissions(any(GetUserSessionPermissionsRequest.class));
            verify(csiPermissionCache).put(TEST_CSI_TOKEN, grpcSession);
            verify(magicLink).generateJwtToken(anyString(), eq(TEST_USER_ID));
        }
    }

    @Test
    void testCsiValidationGrpcError() {
        // Arrange
        MagicLinkAuthFilter.Config config = new MagicLinkAuthFilter.Config();
        GatewayFilter gatewayFilter = filter.apply(config);

        // Cache miss, gRPC call returns error
        when(csiPermissionCache.getIfPresent(TEST_CSI_TOKEN)).thenReturn(null);
        when(authServiceStub.getUserSessionPermissions(any(GetUserSessionPermissionsRequest.class)))
                .thenReturn(Mono.error(new RuntimeException("gRPC error")));

        MockServerHttpRequest request = MockServerHttpRequest.get("/api/test")
                .header("X-csi", TEST_CSI_TOKEN)
                .header("user_uuid", TEST_USER_ID)
                .header("uc_session", TEST_SESSION_ID)
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // Act & Assert
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        assertEquals(HttpStatus.SERVICE_UNAVAILABLE, exchange.getResponse().getStatusCode());
        verify(authServiceStub).getUserSessionPermissions(any(GetUserSessionPermissionsRequest.class));
        verify(magicLink, never()).generateJwtToken(anyString(), anyString());
    }
} 