package com.illumio.data.filter;


import authservice.GetUserSessionPermissionsResponse;
import com.illumio.data.filter.utils.DummyUserSession;
import com.illumio.data.service.JwtService;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.net.ConnectException;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.HashMap;

import static com.illumio.data.constants.Constants.GATEWAY_TOKEN;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InsightsSearchFilterTest {

    @Mock
    private JwtService jwtService;

    private InsightsSearchFilter filter;

    @BeforeEach
    void setUp() {
        filter = new InsightsSearchFilter(jwtService);
    }

    @Test
    void testValidCsiTokenProcessesRequest() throws NoSuchAlgorithmException, InvalidKeySpecException {
        // Arrange
        InsightsSearchFilter.Config config = new InsightsSearchFilter.Config();
        config.setMessage("Test filter");

        GatewayFilter gatewayFilter = filter.apply(config);
        GetUserSessionPermissionsResponse response = DummyUserSession.createDummySessionResponse();
        // Mock request with CSI token
        MockServerHttpRequest request = MockServerHttpRequest.get("http://localhost/api/v1/search")
                .header("X-csi", "test-csi-token")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        exchange.getAttributes().put("grpcSession", response);


        when(jwtService.generatePermissionsJwt(any(HashMap.class), anyInt())).thenReturn("dummy-jwt");

        GatewayFilterChain chain = mock(GatewayFilterChain.class);
        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        // Act & Assert
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        ArgumentCaptor<ServerWebExchange> captor = ArgumentCaptor.forClass(ServerWebExchange.class);
        verify(chain).filter(captor.capture());

        ServerWebExchange mutatedExchange = captor.getValue();
        ServerHttpRequest mutatedRequest = mutatedExchange.getRequest();
        assertEquals("dummy-jwt", mutatedRequest.getHeaders().getFirst(GATEWAY_TOKEN));
    }

    @Test
    void testOptionsRequestSkipped() {
        // Arrange
        InsightsSearchFilter.Config config = new InsightsSearchFilter.Config();
        GatewayFilter gatewayFilter = filter.apply(config);

        MockServerHttpRequest request = MockServerHttpRequest.options("http://localhost/api/v1/search")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        GatewayFilterChain chain = mock(GatewayFilterChain.class);
        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        // Act & Assert
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        // Verify the original exchange is passed through unchanged
        verify(chain).filter(exchange);
        verifyNoInteractions(jwtService);
    }

    @Test
    void testMissingGrpcSessionPassesThrough() {
        // Arrange
        InsightsSearchFilter.Config config = new InsightsSearchFilter.Config();
        GatewayFilter gatewayFilter = filter.apply(config);

        MockServerHttpRequest request = MockServerHttpRequest.get("http://localhost/api/v1/search")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        // Don't set grpcSession attribute

        GatewayFilterChain chain = mock(GatewayFilterChain.class);
        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        // Act & Assert
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        // Verify the original exchange is passed through unchanged
        verify(chain).filter(exchange);
        verifyNoInteractions(jwtService);
    }



    @Test
    void testConfigGetterAndSetter() {
        // Test Config class coverage
        InsightsSearchFilter.Config config = new InsightsSearchFilter.Config();

        // Test setter and getter
        config.setMessage("test message");
        assertEquals("test message", config.getMessage());

        // Test with null
        config.setMessage(null);
        assertEquals(null, config.getMessage());
    }

}
