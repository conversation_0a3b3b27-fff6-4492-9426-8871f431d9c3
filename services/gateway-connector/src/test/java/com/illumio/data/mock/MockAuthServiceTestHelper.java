package com.illumio.data.mock;

import authservice.ReactorAuthServiceGrpc;
import io.grpc.ManagedChannel;
import io.grpc.Server;
import io.grpc.inprocess.InProcessChannelBuilder;
import io.grpc.inprocess.InProcessServerBuilder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * Helper class for setting up MockAuthService in tests
 * Provides easy setup and teardown of in-process gRPC server
 */
@Slf4j
public class MockAuthServiceTestHelper {
    
    @Getter
    private MockAuthService mockService;
    
    @Getter
    private ReactorAuthServiceGrpc.ReactorAuthServiceStub authServiceStub;
    
    private Server server;
    private ManagedChannel channel;
    private String serverName;
    
    /**
     * Start the mock service with default behavior
     */
    public void start() throws IOException {
        start(AuthServiceBehavior.NORMAL);
    }
    
    /**
     * Start the mock service with specified behavior
     */
    public void start(AuthServiceBehavior behavior) throws IOException {
        mockService = new MockAuthService();
        mockService.setBehavior(behavior);
        
        // Generate a unique in-process server name
        serverName = InProcessServerBuilder.generateName();
        
        // Create the server
        server = InProcessServerBuilder
                .forName(serverName)
                .directExecutor()
                .addService(mockService)
                .build()
                .start();
        
        // Create the channel
        channel = InProcessChannelBuilder
                .forName(serverName)
                .directExecutor()
                .build();
        
        // Create the stub
        authServiceStub = ReactorAuthServiceGrpc.newReactorStub(channel);
        
        log.info("MockAuthService started with behavior: {}", behavior);
    }
    
    /**
     * Stop the mock service
     */
    public void stop() throws InterruptedException {
        if (server != null) {
            server.shutdown();
            server.awaitTermination(5, TimeUnit.SECONDS);
        }
        if (channel != null) {
            channel.shutdown();
            channel.awaitTermination(5, TimeUnit.SECONDS);
        }
        log.info("MockAuthService stopped");
    }
    
    /**
     * Set the behavior of the mock service
     */
    public void setBehavior(AuthServiceBehavior behavior) {
        if (mockService != null) {
            mockService.setBehavior(behavior);
        }
    }
    
    /**
     * Set the delay for DELAYED behavior
     */
    public void setDelay(Duration delay) {
        if (mockService != null) {
            mockService.setDelay(delay);
        }
    }

    /**
     * Set the failure rate for INTERMITTENT_FAILURE behavior
     */
    public void setFailureRate(double failureRate) {
        if (mockService != null) {
            mockService.setFailureRate(failureRate);
        }
    }
    
    /**
     * Get the server name for in-process communication
     */
    public String getServerName() {
        return serverName;
    }
    
    /**
     * Create a channel that can be used by other components
     */
    public ManagedChannel createChannel() {
        return InProcessChannelBuilder
                .forName(serverName)
                .directExecutor()
                .build();
    }
}
