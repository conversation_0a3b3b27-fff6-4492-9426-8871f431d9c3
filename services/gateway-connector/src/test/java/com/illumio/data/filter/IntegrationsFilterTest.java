package com.illumio.data.filter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

class IntegrationsFilterTest {
    private IntegrationsFilter filter;

    @BeforeEach
    void setUp() {
        filter = new IntegrationsFilter();
    }

    @Test
    void testApplyFilterLogsMessageAndContinuesChain() {
        // Setup config with a test message
        IntegrationsFilter.Config config = new IntegrationsFilter.Config();
        config.setMessage("Test Integrations Filter");

        // Create the GatewayFilter
        GatewayFilter gatewayFilter = filter.apply(config);

        // Mock the exchange and chain
        MockServerHttpRequest request = MockServerHttpRequest.get("http://localhost/integrations/")
                .build();
        ServerWebExchange exchange = MockServerWebExchange.from(request);

        GatewayFilterChain chain = mock(GatewayFilterChain.class);
        when(chain.filter(exchange)).thenReturn(Mono.empty());

        // Apply filter and verify it completes
        StepVerifier.create(gatewayFilter.filter(exchange, chain))
                .verifyComplete();

        // Verify that the chain was called
        verify(chain, times(1)).filter(exchange);
    }
}