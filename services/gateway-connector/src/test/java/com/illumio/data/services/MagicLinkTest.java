package com.illumio.data.services;

import com.illumio.data.service.JwtService;
import com.illumio.data.service.MagicLink;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MagicLinkTest {

    @Mock
    private JwtService jwtService;

    private MagicLink magicLink;
    
    private static final int TEST_EXPIRY_DAYS = 7;

    @BeforeEach
    void setUp() {
        magicLink = new MagicLink(jwtService);
        // Set the expiryDays field that's injected via @Value
        ReflectionTestUtils.setField(magicLink, "expiryDays", TEST_EXPIRY_DAYS);
    }

    @Test
    void testGenerateJwtToken_ShouldReturnValidToken() throws Exception {
        // Arrange
        String tenantId = "tenant-123";
        String userId = "user-456";
        String expectedToken = "mock.jwt.token";

        when(jwtService.generatePermissionsJwt(any(Map.class), eq(TEST_EXPIRY_DAYS * 24 * 60)))
                .thenReturn(expectedToken);

        // Act
        String result = magicLink.generateJwtToken(tenantId, userId);

        // Assert
        assertEquals(expectedToken, result);
        
        // Verify the JWT service was called with correct parameters
        verify(jwtService).generatePermissionsJwt(argThat(claims -> {
            Map<String, Object> claimsMap = (Map<String, Object>) claims;
            return tenantId.equals(claimsMap.get("tenant")) && 
                   userId.equals(claimsMap.get("sub"));
        }), eq(TEST_EXPIRY_DAYS * 24 * 60));
    }

    @Test
    void testGenerateJwtToken_ShouldConvertDaysToMinutes() throws Exception {
        // Arrange
        String tenantId = "tenant-123";
        String userId = "user-456";
        int customExpiryDays = 30;
        int expectedMinutes = customExpiryDays * 24 * 60; // 43200 minutes
        
        // Set custom expiry days for this test
        ReflectionTestUtils.setField(magicLink, "expiryDays", customExpiryDays);

        when(jwtService.generatePermissionsJwt(any(Map.class), anyInt()))
                .thenReturn("mock.token");

        // Act
        magicLink.generateJwtToken(tenantId, userId);

        // Assert
        verify(jwtService).generatePermissionsJwt(any(Map.class), eq(expectedMinutes));
    }

    @Test
    void testGenerateJwtToken_ShouldThrowRuntimeExceptionOnJwtServiceFailure() throws Exception {
        // Arrange
        String tenantId = "tenant-123";
        String userId = "user-456";

        when(jwtService.generatePermissionsJwt(any(Map.class), anyInt()))
                .thenThrow(new NoSuchAlgorithmException("Algorithm not found"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            magicLink.generateJwtToken(tenantId, userId);
        });

        // Verify the original exception is wrapped
        assertInstanceOf(NoSuchAlgorithmException.class, exception.getCause());
        assertEquals("Algorithm not found", exception.getCause().getMessage());
    }
} 