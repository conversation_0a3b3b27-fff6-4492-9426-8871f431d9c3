package com.illumio.data.mock;

/**
 * Enum defining different behaviors for the mock AuthService
 * to simulate various scenarios including timeouts and hangs
 */
public enum AuthServiceBehavior {
    /**
     * Normal behavior - returns successful response immediately
     */
    NORMAL,
    
    /**
     * Delayed response - returns successful response after a configurable delay
     */
    DELAYED,
    
    /**
     * Hung response - never responds (simulates a hung service)
     */
    HUNG,
    
    /**
     * Timeout response - delays longer than the client timeout
     */
    TIMEOUT,
    
    /**
     * Error response - returns gRPC error
     */
    ERROR,
    
    /**
     * Intermittent failure - randomly fails some requests
     */
    INTERMITTENT_FAILURE
}
