package com.illumio.data.filter;

import authservice.GetUserSessionPermissionsResponse;
import com.illumio.data.filter.utils.DummyUserSession;
import com.illumio.data.service.JwtService;
import io.jsonwebtoken.Jwt;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.net.URI;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.Map;

import static com.illumio.data.constants.Constants.GATEWAY_TOKEN;
import static com.illumio.data.constants.Constants.SCHEME;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_REQUEST_URL_ATTR;


@ExtendWith(MockitoExtension.class)
class CoreFilterTest {
    @Mock
    private JwtService jwtService;

    @Mock
    private GatewayFilterChain chain;

    CoreFilter coreFilter;

    @BeforeEach
    void setUp() throws Exception {
        coreFilter = new CoreFilter(jwtService, new SimpleMeterRegistry());
    }

    @Test
    void testNoGrpcSession_passthroughAndNoJwt() {
        MockServerHttpRequest request = MockServerHttpRequest.get("http://localhost/integrations/")
                .header("X-csi", "token-abc")
                .build();
        ServerWebExchange exchange = MockServerWebExchange.from(request);

        ArgumentCaptor<ServerWebExchange> exchCaptor = ArgumentCaptor.forClass(ServerWebExchange.class);
        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        StepVerifier.create(coreFilter.apply(new CoreFilter.Config()).filter(exchange, chain))
                .verifyComplete();

        verify(chain).filter(exchCaptor.capture());
        ServerWebExchange passed = exchCaptor.getValue();
        assertNull(passed.getRequest().getHeaders().getFirst(GATEWAY_TOKEN));
        verifyNoInteractions(jwtService);
        assertNull(passed.getAttribute(GATEWAY_REQUEST_URL_ATTR));
    }

    @Test
    void testSmallPayload_grpcResponse() throws NoSuchAlgorithmException, InvalidKeySpecException {
        GetUserSessionPermissionsResponse response = DummyUserSession.createDummySessionResponse();
        MockServerHttpRequest request = MockServerHttpRequest.get("http://localhost/integrations/")
                .build();

        ServerWebExchange exchange = MockServerWebExchange.from(request);
        exchange.getAttributes().put("grpcSession", response);
        when(jwtService.generatePermissionsJwt(any(), eq(30))).thenReturn("jwt.30");
        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());
        ArgumentCaptor<ServerWebExchange> exchCap = ArgumentCaptor.forClass(ServerWebExchange.class);
        ArgumentCaptor<Map<String, Object>> claimsCap = ArgumentCaptor.forClass(Map.class);

        StepVerifier.create(coreFilter.apply(new CoreFilter.Config()).filter(exchange, chain))
                .verifyComplete();

        // Verify JWT call & claims
        verify(jwtService).generatePermissionsJwt(claimsCap.capture(), eq(30));
        Map<String, Object> claims = claimsCap.getValue();
        assertEquals(false, claims.get("permissions_excluded"));
        assertTrue(claims.containsKey("last_updated_at"));
        assertTrue(claims.containsKey("core")); // small payload path serializes "core" sub-map

        // Verify mutated request: scheme/host/path and headers
        verify(chain).filter(exchCap.capture());
        ServerHttpRequest mutated = exchCap.getValue().getRequest();

        URI uri = mutated.getURI();
        assertEquals(SCHEME, uri.getScheme());           // e.g., "https"
        assertEquals("example.com", uri.getHost());      // port stripped

        // Headers preserved + injected
        assertEquals("jwt.30", mutated.getHeaders().getFirst(GATEWAY_TOKEN));

        // GATEWAY_REQUEST_URL_ATTR updated
        assertEquals(uri, exchCap.getValue().getAttribute(GATEWAY_REQUEST_URL_ATTR));
    }

}