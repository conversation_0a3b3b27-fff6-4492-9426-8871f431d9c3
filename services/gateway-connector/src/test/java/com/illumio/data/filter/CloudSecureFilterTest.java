package com.illumio.data.filter;

import authservice.GetUserSessionPermissionsResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.filter.utils.DummyUserSession;
import com.illumio.data.service.JwtService;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.lang.reflect.Field;

import static com.illumio.data.constants.Constants.GATEWAY_TOKEN;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;


@ExtendWith(MockitoExtension.class)
class CloudSecureFilterTest {

    @Mock
    JwtService jwtService;

    @Mock
    private GatewayFilterChain chain;

    CloudSecureFilter cloudSecureFilter;

    @BeforeEach
    void setUp() {
        cloudSecureFilter = new CloudSecureFilter(jwtService, new SimpleMeterRegistry());
    }
    @Test
    void testJwtInjection_withDummyUserSessionResponse() throws Exception {

        GetUserSessionPermissionsResponse response  = DummyUserSession.createDummySessionResponse();
        MockServerHttpRequest request = MockServerHttpRequest.get("http://localhost/integrations/")
                .build();

        ServerWebExchange exchange = MockServerWebExchange.from(request);
        exchange.getAttributes().put("grpcSession", response);

        when(jwtService.generatePermissionsJwt(any(), eq(30))).thenReturn("dummy.jwt.token");

        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        StepVerifier.create(cloudSecureFilter.apply(new CloudSecureFilter.Config()).filter(exchange, chain))
                .verifyComplete();

        verify(jwtService).generatePermissionsJwt(any(), eq(30));
    }

    @Test
    void testJsonParsingFailure_wrapsAndPropagates() throws Exception {
        GetUserSessionPermissionsResponse response = DummyUserSession.createDummySessionResponse();
        ServerWebExchange exchange = MockServerWebExchange.from(
                MockServerHttpRequest.get("http://localhost/integrations/").build());
        exchange.getAttributes().put("grpcSession", response);

        // Inject a spy ObjectMapper that throws
        ObjectMapper mapperSpy = org.mockito.Mockito.spy(new ObjectMapper());
        doThrow(new JsonProcessingException("bad json"){}).when(mapperSpy)
                .readValue(anyString(), any(TypeReference.class));

        Field f = CloudSecureFilter.class.getDeclaredField("objectMapper");
        f.setAccessible(true);
        f.set(cloudSecureFilter, mapperSpy);

        StepVerifier.create(cloudSecureFilter.apply(new CloudSecureFilter.Config()).filter(exchange, chain))
                .expectErrorMatches(ex -> ex instanceof RuntimeException &&
                        ex.getCause() instanceof JsonProcessingException &&
                        "bad json".equals(ex.getCause().getMessage()))
                .verify();

        verifyNoInteractions(jwtService);
        verifyNoInteractions(chain);
    }

    @Test
    void testNoGrpcSession_passthroughAndNoJwt() {
        MockServerHttpRequest request = MockServerHttpRequest.get("http://localhost/integrations/")
                .header("X-csi", "token-abc")
                .build();

        ServerWebExchange exchange = MockServerWebExchange.from(request);

        ArgumentCaptor<ServerWebExchange> exchCaptor = ArgumentCaptor.forClass(ServerWebExchange.class);
        when(chain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        StepVerifier.create(cloudSecureFilter.apply(new CloudSecureFilter.Config()).filter(exchange, chain))
                .verifyComplete();

        verify(chain).filter(exchCaptor.capture());
        ServerWebExchange passed = exchCaptor.getValue();
        assertNull(passed.getRequest().getHeaders().getFirst(GATEWAY_TOKEN), "GATEWAY_TOKEN should not be added");
        verifyNoInteractions(jwtService);
    }
}