package com.illumio.data.filter;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.ArgumentCaptor;

@ExtendWith(MockitoExtension.class)
class ResourceInsightsFilterTest {

    @Mock
    private GatewayFilterChain filterChain;

    private ResourceInsightsFilter filter;
    private static final String SOURCE_TENANT_ID = "dev-tenant-abc123";
    private static final String TARGET_TENANT_ID = "prod-tenant-xyz789";
    private static final String AUTH_KEY_VALUE = "test-auth-key-123";

    @BeforeEach
    void setUp() {
        filter = new ResourceInsightsFilter();
        // Set up the @Value injected fields using reflection
        ReflectionTestUtils.setField(filter, "sourceTenantId", SOURCE_TENANT_ID);
        ReflectionTestUtils.setField(filter, "targetTenantId", TARGET_TENANT_ID);
        ReflectionTestUtils.setField(filter, "authKeyEnabled", false);
        ReflectionTestUtils.setField(filter, "authKeyValue", AUTH_KEY_VALUE);
    }

    @Test
    void testApplyFilterLogsMessageAndContinuesChain() {
        ResourceInsightsFilter.Config config = new ResourceInsightsFilter.Config();
        config.setMessage("Test message");

        MockServerHttpRequest request = MockServerHttpRequest
                .get("/api/v1/resource-insights")
                .build();

        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        
        when(filterChain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        GatewayFilter gatewayFilter = filter.apply(config);
        Mono<Void> result = gatewayFilter.filter(exchange, filterChain);

        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    void testTenantIdMappingWhenHeaderMatches() {
        ResourceInsightsFilter.Config config = new ResourceInsightsFilter.Config();
        config.setMessage("Test message");

        MockServerHttpRequest request = MockServerHttpRequest
                .get("/api/v1/resource-insights")
                .header("x-illumio-tenant-id", SOURCE_TENANT_ID)
                .build();

        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        
        when(filterChain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        GatewayFilter gatewayFilter = filter.apply(config);
        Mono<Void> result = gatewayFilter.filter(exchange, filterChain);

        StepVerifier.create(result)
                .verifyComplete();

        // Capture the mutated exchange passed to the filter chain
        ArgumentCaptor<ServerWebExchange> exchangeCaptor = ArgumentCaptor.forClass(ServerWebExchange.class);
        verify(filterChain).filter(exchangeCaptor.capture());
        
        // Verify that the header was properly mapped to the target tenant ID
        ServerWebExchange capturedExchange = exchangeCaptor.getValue();
        String actualTenantId = capturedExchange.getRequest().getHeaders().getFirst("x-illumio-tenant-id");
        assertEquals(TARGET_TENANT_ID, actualTenantId, "Tenant ID should be mapped from source to target");
    }

    @Test
    void testTenantIdPassesThroughWhenHeaderDoesNotMatch() {
        ResourceInsightsFilter.Config config = new ResourceInsightsFilter.Config();
        config.setMessage("Test message");

        String originalTenantId = "different-tenant-id";
        MockServerHttpRequest request = MockServerHttpRequest
                .get("/api/v1/resource-insights")
                .header("x-illumio-tenant-id", originalTenantId)
                .build();

        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        
        when(filterChain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        GatewayFilter gatewayFilter = filter.apply(config);
        Mono<Void> result = gatewayFilter.filter(exchange, filterChain);

        StepVerifier.create(result)
                .verifyComplete();

        // Capture the exchange passed to the filter chain
        ArgumentCaptor<ServerWebExchange> exchangeCaptor = ArgumentCaptor.forClass(ServerWebExchange.class);
        verify(filterChain).filter(exchangeCaptor.capture());
        
        // Verify that the original header value is preserved (no mapping occurred)
        ServerWebExchange capturedExchange = exchangeCaptor.getValue();
        String actualTenantId = capturedExchange.getRequest().getHeaders().getFirst("x-illumio-tenant-id");
        assertEquals(originalTenantId, actualTenantId, "Original tenant ID should be preserved when no mapping occurs");
    }

    @Test
    void testNoMappingWhenConfigNotSet() {
        // Create filter without setting mapping config
        ResourceInsightsFilter filterWithoutConfig = new ResourceInsightsFilter();
        ReflectionTestUtils.setField(filterWithoutConfig, "sourceTenantId", "");
        ReflectionTestUtils.setField(filterWithoutConfig, "targetTenantId", "");

        ResourceInsightsFilter.Config config = new ResourceInsightsFilter.Config();
        config.setMessage("Test message");

        String originalTenantId = "any-tenant-id";
        MockServerHttpRequest request = MockServerHttpRequest
                .get("/api/v1/resource-insights")
                .header("x-illumio-tenant-id", originalTenantId)
                .build();

        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        
        when(filterChain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        GatewayFilter gatewayFilter = filterWithoutConfig.apply(config);
        Mono<Void> result = gatewayFilter.filter(exchange, filterChain);

        StepVerifier.create(result)
                .verifyComplete();

        // Capture the exchange passed to the filter chain
        ArgumentCaptor<ServerWebExchange> exchangeCaptor = ArgumentCaptor.forClass(ServerWebExchange.class);
        verify(filterChain).filter(exchangeCaptor.capture());
        
        // Verify that the original header value is preserved when config is not set
        ServerWebExchange capturedExchange = exchangeCaptor.getValue();
        String actualTenantId = capturedExchange.getRequest().getHeaders().getFirst("x-illumio-tenant-id");
        assertEquals(originalTenantId, actualTenantId, "Original tenant ID should be preserved when mapping config is not set");
    }

    @Test
    void testNoMappingWhenHeaderMissing() {
        ResourceInsightsFilter.Config config = new ResourceInsightsFilter.Config();
        config.setMessage("Test message");

        MockServerHttpRequest request = MockServerHttpRequest
                .get("/api/v1/resource-insights")
                // No x-illumio-tenant-id header
                .build();

        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        
        when(filterChain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        GatewayFilter gatewayFilter = filter.apply(config);
        Mono<Void> result = gatewayFilter.filter(exchange, filterChain);

        StepVerifier.create(result)
                .verifyComplete();

        // Capture the exchange passed to the filter chain
        ArgumentCaptor<ServerWebExchange> exchangeCaptor = ArgumentCaptor.forClass(ServerWebExchange.class);
        verify(filterChain).filter(exchangeCaptor.capture());
        
        // Verify that no header is present (no mapping added when header is missing)
        ServerWebExchange capturedExchange = exchangeCaptor.getValue();
        String actualTenantId = capturedExchange.getRequest().getHeaders().getFirst("x-illumio-tenant-id");
        assertEquals(null, actualTenantId, "No tenant ID header should be present when original request had none");
    }

    @Test
    void testConfigGetterAndSetter() {
        ResourceInsightsFilter.Config config = new ResourceInsightsFilter.Config();
        config.setMessage("Test message");
        assertEquals("Test message", config.getMessage());
    }

    @Test
    void testAuthKeyNotCheckedWhenDisabled() {
        // Auth key is disabled by default in setUp
        ResourceInsightsFilter.Config config = new ResourceInsightsFilter.Config();
        config.setMessage("Test message");

        MockServerHttpRequest request = MockServerHttpRequest
                .get("/api/v1/resource-insights")
                // No x-auth-key header, but shouldn't matter when disabled
                .build();

        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        
        when(filterChain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        GatewayFilter gatewayFilter = filter.apply(config);
        Mono<Void> result = gatewayFilter.filter(exchange, filterChain);

        StepVerifier.create(result)
                .verifyComplete();

        verify(filterChain).filter(any(ServerWebExchange.class));
    }

    @Test
    void testAuthKeyReturns401WhenMissing() {
        // Enable auth key validation
        ReflectionTestUtils.setField(filter, "authKeyEnabled", true);
        
        ResourceInsightsFilter.Config config = new ResourceInsightsFilter.Config();
        config.setMessage("Test message");

        MockServerHttpRequest request = MockServerHttpRequest
                .get("/api/v1/resource-insights")
                // No x-auth-key header
                .build();

        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        GatewayFilter gatewayFilter = filter.apply(config);
        Mono<Void> result = gatewayFilter.filter(exchange, filterChain);

        StepVerifier.create(result)
                .verifyComplete();

        // Verify 401 status was set
        assertEquals(401, exchange.getResponse().getStatusCode().value());
    }

    @Test
    void testAuthKeyReturns401WhenIncorrect() {
        // Enable auth key validation
        ReflectionTestUtils.setField(filter, "authKeyEnabled", true);
        
        ResourceInsightsFilter.Config config = new ResourceInsightsFilter.Config();
        config.setMessage("Test message");

        MockServerHttpRequest request = MockServerHttpRequest
                .get("/api/v1/resource-insights")
                .header("x-auth-key", "wrong-auth-key")
                .build();

        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        GatewayFilter gatewayFilter = filter.apply(config);
        Mono<Void> result = gatewayFilter.filter(exchange, filterChain);

        StepVerifier.create(result)
                .verifyComplete();

        // Verify 401 status was set
        assertEquals(401, exchange.getResponse().getStatusCode().value());
    }

    @Test
    void testAuthKeyAllowsRequestWhenCorrect() {
        // Enable auth key validation
        ReflectionTestUtils.setField(filter, "authKeyEnabled", true);
        
        ResourceInsightsFilter.Config config = new ResourceInsightsFilter.Config();
        config.setMessage("Test message");

        MockServerHttpRequest request = MockServerHttpRequest
                .get("/api/v1/resource-insights")
                .header("x-auth-key", AUTH_KEY_VALUE)
                .build();

        MockServerWebExchange exchange = MockServerWebExchange.from(request);
        
        when(filterChain.filter(any(ServerWebExchange.class))).thenReturn(Mono.empty());

        GatewayFilter gatewayFilter = filter.apply(config);
        Mono<Void> result = gatewayFilter.filter(exchange, filterChain);

        StepVerifier.create(result)
                .verifyComplete();

        // Verify that the filter chain was called (request was allowed through)
        verify(filterChain).filter(any(ServerWebExchange.class));
        // Verify no error status was set
        assertEquals(null, exchange.getResponse().getStatusCode());
    }
}