package com.illumio.data.filter;

import com.illumio.data.service.MagicLink;
import com.illumio.data.service.DateTimeUtils;
import authservice.GetUserRequest;
import authservice.GetUserSessionPermissionsRequest;
import authservice.GetUserSessionPermissionsResponse;
import authservice.ReactorAuthServiceGrpc;
import com.github.benmanes.caffeine.cache.Cache;
import io.grpc.ClientInterceptor;
import io.grpc.Metadata;
import io.grpc.stub.MetadataUtils;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import java.nio.charset.StandardCharsets;


@Slf4j
@Component
public class MagicLinkAuthFilter extends AbstractGatewayFilterFactory<MagicLinkAuthFilter.Config> {

    private final ReactorAuthServiceGrpc.ReactorAuthServiceStub authServiceStub;
    private final MagicLink magicLink;
    private final Cache<String, GetUserSessionPermissionsResponse> csiPermissionCache;
    private final Counter permissionTotalCounter;
    private final Counter permissionFailedCounter;

    @Value("${jwtExpiryDays}")
    private int expiryDays;

    @Value("${grpctimeout}")
    Duration grpcTimeout;

    @Autowired
    public MagicLinkAuthFilter(
            ReactorAuthServiceGrpc.ReactorAuthServiceStub authServiceStub,
            MagicLink magicLink,
            Cache<String, GetUserSessionPermissionsResponse> csiPermissionCache,
            MeterRegistry meterRegistry
    ) {
        super(Config.class);
        this.authServiceStub = authServiceStub;
        this.magicLink = magicLink;
        this.csiPermissionCache = csiPermissionCache;
        this.permissionTotalCounter = meterRegistry.counter("gateway.magiclink.permission.total.counter");
        this.permissionFailedCounter = meterRegistry.counter("gateway.magiclink.permission.failed.counter");
    }

    public static class Config {}

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            log.info("MagicLinkAuthFilter invoked Path = {}", exchange.getRequest().getPath());

            // CSI Token validation logic from PermissionFilter
            String csiToken = exchange.getRequest().getHeaders().getFirst("X-csi");

            if (csiToken == null || csiToken.isEmpty()) {
                exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
                return exchange.getResponse().setComplete();
            }

            log.info(">> csiToken: {}", csiToken);

            // Check if permissions already present in the attribute
            GetUserSessionPermissionsResponse existingSession = exchange.getAttribute("grpcSession");
            if (existingSession != null) {
                log.info("grpcSession already present in exchange. Proceeding with MagicLink logic.");
                return processMagicLinkRequest(exchange, existingSession);
            }

            String xff = exchange.getRequest().getHeaders().getFirst("X-Forwarded-For");

            GetUserSessionPermissionsRequest.Builder requestBuilder = GetUserSessionPermissionsRequest.newBuilder()
                    .setCsi(csiToken)
                    .setReferrer("MagicLinkAuthFilter");
            
            if (xff != null) {
                requestBuilder.setXff(xff);
            }
            
            GetUserSessionPermissionsRequest request = requestBuilder.build();

            GetUserSessionPermissionsResponse cachedUserSession = csiPermissionCache.getIfPresent(csiToken);
            if (cachedUserSession != null) {
                log.info("Cache hit for CSI token: {}", csiToken);
                exchange.getAttributes().put("grpcSession", cachedUserSession);
                return processMagicLinkRequest(exchange, cachedUserSession);
            }

            permissionTotalCounter.increment();
            return Mono.defer(() -> authServiceStub.getUserSessionPermissions(request)
                    .timeout(grpcTimeout)
                    .flatMap(grpcResponse -> {
                        if (grpcResponse.getCore().hasAccessRestriction()) {
                            //terminate the request here
                            exchange.getResponse().setStatusCode(HttpStatus.FORBIDDEN);
                            return exchange.getResponse().setComplete();
                        }

                        log.info("Received gRPC response for CSI token: {}", csiToken);

                        // Cache the full gRPC response
                        csiPermissionCache.put(csiToken, grpcResponse);

                        // Store in request context for downstream filters/handlers
                        exchange.getAttributes().put("grpcSession", grpcResponse);

                        return processMagicLinkRequest(exchange, grpcResponse);
                    })
                    .onErrorResume(e -> {
                        log.error("gRPC error in MagicLinkAuthFilter", e);
                        permissionFailedCounter.increment();
                        exchange.getResponse().setStatusCode(HttpStatus.SERVICE_UNAVAILABLE);
                        byte[] bytes = "Census is currently unavailable.".getBytes(StandardCharsets.UTF_8);
                        exchange.getResponse().getHeaders().add(HttpHeaders.CONTENT_TYPE, MediaType.TEXT_PLAIN_VALUE);
                        exchange.getResponse().getHeaders().setContentLength(bytes.length);

                        DataBuffer buffer = exchange.getResponse().bufferFactory().wrap(bytes);
                        return exchange.getResponse().writeWith(Mono.just(buffer));
                    }));
        };
    }

    private Mono<Void> processMagicLinkRequest(org.springframework.web.server.ServerWebExchange exchange, GetUserSessionPermissionsResponse grpcResponse) {
        String userId = exchange.getRequest().getHeaders().getFirst("user_uuid");
        String sessionId = exchange.getRequest().getHeaders().getFirst("uc_session");
        String ucid = exchange.getRequest().getHeaders().getFirst("ucid"); // optional

        if (userId == null || sessionId == null) {
            log.warn("Missing required headers: user_uuid or uc_session");
            exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
            return exchange.getResponse().setComplete();
        }

        String tenantId = grpcResponse.getCloudsecure().getTenantId();
        log.info("Tenant ID from gRPC session: {}", tenantId);

        Map<String, Object> jwtClaims = new HashMap<>();
        Metadata metadata = new Metadata();
        metadata.put(Metadata.Key.of("session_id", Metadata.ASCII_STRING_MARSHALLER), sessionId);
        metadata.put(Metadata.Key.of("ucid", Metadata.ASCII_STRING_MARSHALLER), ucid != null ? ucid : "default-ucid");

        ClientInterceptor interceptor = MetadataUtils.newAttachHeadersInterceptor(metadata);
        ReactorAuthServiceGrpc.ReactorAuthServiceStub stubWithMetadata = authServiceStub.withInterceptors(interceptor);

        GetUserRequest userRequest = GetUserRequest.newBuilder()
                .setId(userId)
                .build();

        return stubWithMetadata.getUser(userRequest)
                .flatMap(userResponse -> {
                    if (!userResponse.getId().equals(userId)) {
                        exchange.getResponse().setStatusCode(HttpStatus.UNAUTHORIZED);
                        return exchange.getResponse().setComplete();
                    }

                    log.info("User exists successfully for userId={}", userId);

                    String token = magicLink.generateJwtToken(tenantId, userId);
                    String expiryTime = DateTimeUtils.getExpiry("America/Los_Angeles", expiryDays);

                    exchange.getResponse().setStatusCode(HttpStatus.OK);
                    exchange.getResponse().getHeaders().add("Content-Type", "application/json");

                    String json = String.format("{\"expiry\":\"%s\",\"jwt\":\"%s\"}", expiryTime, token);

                    byte[] bytes = json.getBytes(StandardCharsets.UTF_8);
                    DataBuffer buffer = exchange.getResponse().bufferFactory().wrap(bytes);
                    return exchange.getResponse().writeWith(Mono.just(buffer));
                })
                .onErrorResume(e -> {
                    log.error("Error in MagicLinkAuthFilter during getUser", e);
                    exchange.getResponse().setStatusCode(HttpStatus.INTERNAL_SERVER_ERROR);
                    return exchange.getResponse().setComplete();
                });
    }
}
