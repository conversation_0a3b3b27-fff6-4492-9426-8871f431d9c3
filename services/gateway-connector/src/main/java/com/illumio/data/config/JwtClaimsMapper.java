package com.illumio.data.config;


import authservice.GetServiceAccountPermissionsResponse;
import authservice.GetUserSessionPermissionsResponse;
import java.util.Map;

public class JwtClaimsMapper {
    public static Map<String, Object> mapCoreToJwtClaim(GetUserSessionPermissionsResponse response) {
        Map<String, Object> coreMap = Map.of(
                "effective_groups_excluded", true,
                "user_id", response.getCore().getUserId(),
                "user_uuid", response.getCore().getUserUuid(),
                "org_id", response.getCore().getOrgId(),
                "org_uuid", response.getCore().getOrgUuid(),
                "pce_fqdn", response.getCore().getPceFqdn(),
                "user_type", response.getCore().getUserType(),
                "locked", response.getCore().getLocked(),
                "session_timeout_minutes", response.getCore().getSessionTimeoutMinutes()
        );

        return Map.of(
                "core", coreMap
               // "permissions_changed", response.getCore().getPermissionsChanged()
        );
    }
    public static Map<String, Object> mapServiceAccountDetails(GetServiceAccountPermissionsResponse response) {
        Map<String, Object> coreMap = Map.of(
                "org_id", response.getCore().getOrgId(),
                "org_uuid", response.getCore().getOrgUuid(),
                "pce_fqdn", response.getCore().getPceFqdn(),
                "session_timeout_minutes", response.getCore().getSessionTimeoutMinutes(),
                "access_restriction", response.getCore().getAccessRestriction()
        );
        return coreMap;
    }
}
