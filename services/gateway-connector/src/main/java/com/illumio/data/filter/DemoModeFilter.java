package com.illumio.data.filter;

import authservice.GetUserSessionPermissionsResponse;
import authservice.CloudsecurePermissions;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class DemoModeFilter extends AbstractGatewayFilterFactory<DemoModeFilter.Config> {

    @Value("${demo.tenant.id}")
    private String demoTenantId;

    private static final String DEMO_HEADER = "X-ILLUMIO-DEMO";
    private static final Pattern TENANT_PATH_PATTERN = Pattern.compile("/tenant/([^/]+)/");

    public DemoModeFilter() {
        super(Config.class);
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            if (exchange.getRequest().getMethod() == HttpMethod.OPTIONS) {
                log.info("Skipping DemoModeFilter for OPTIONS");
                return chain.filter(exchange);
            }

            String demoModeHeader = exchange.getRequest().getHeaders().getFirst(DEMO_HEADER);
            log.info("DemoModeFilter: Demo mode header value: {}", demoModeHeader);

            if (!"true".equalsIgnoreCase(demoModeHeader)) {
                log.info("DemoModeFilter: Demo mode not enabled, continuing with normal processing");
                return chain.filter(exchange);
            }

            log.info("DemoModeFilter: Demo mode enabled, modifying request for demo tenant: {}", demoTenantId);

            // Modify the path to replace tenant ID with demo tenant ID
            ServerHttpRequest originalRequest = exchange.getRequest();
            String originalPath = originalRequest.getURI().getPath();
            String modifiedPath = replaceTenantInPath(originalPath, demoTenantId);

            log.info("DemoModeFilter: Original path: {}, Modified path: {}", originalPath, modifiedPath);

            // Build the modified request
            ServerHttpRequest modifiedRequest = originalRequest.mutate()
                    .path(modifiedPath)
                    .build();

            // Modify the gRPC session to use demo tenant ID
            GetUserSessionPermissionsResponse originalSession = exchange.getAttribute("grpcSession");
            if (originalSession != null) {
                GetUserSessionPermissionsResponse modifiedSession = createDemoSession(originalSession, demoTenantId);
                exchange.getAttributes().put("grpcSession", modifiedSession);
                log.info("DemoModeFilter: Modified gRPC session with demo tenant ID");
            }
            GetUserSessionPermissionsResponse grpcResponse = exchange.getAttribute("grpcSession");
            log.info("DemoModeFilter: Grpc tenant id: {}", grpcResponse.getCloudsecure().getTenantId());


            ServerWebExchange modifiedExchange = exchange.mutate()
                    .request(modifiedRequest)
                    .build();

            return chain.filter(modifiedExchange);
        };
    }

    private String replaceTenantInPath(String path, String demoTenantId) {
        Matcher matcher = TENANT_PATH_PATTERN.matcher(path);
        if (matcher.find()) {
            String originalTenantId = matcher.group(1);
            log.info("DemoModeFilter: Replacing tenant {} with demo tenant {}", originalTenantId, demoTenantId);
            return matcher.replaceFirst("/tenant/" + demoTenantId + "/");
        }
        log.warn("DemoModeFilter: No tenant found in path: {}", path);
        return path;
    }

    private GetUserSessionPermissionsResponse createDemoSession(GetUserSessionPermissionsResponse originalSession, String demoTenantId) {
        // Create a new CloudsecurePermissions with demo tenant ID
        CloudsecurePermissions.Builder cloudsecureBuilder = originalSession.getCloudsecure().toBuilder()
                .setTenantId(demoTenantId);

        // Create a new session response with modified cloudsecure permissions
        return originalSession.toBuilder()
                .setCloudsecure(cloudsecureBuilder.build())
                .build();
    }

    @Getter
    @Setter
    public static class Config {
        private String message;
    }
}
