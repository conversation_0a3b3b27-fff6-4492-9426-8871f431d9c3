package com.illumio.data.filter;

import authservice.GetServiceAccountPermissionsResponse;
import authservice.GetUserSessionPermissionsResponse;
import com.illumio.data.service.JwtService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpMethod;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.HashMap;
import java.util.Map;

import static com.illumio.data.constants.Constants.GATEWAY_TOKEN;

@Slf4j
@Component
public class InsightsSearchFilter extends AbstractGatewayFilterFactory<InsightsSearchFilter.Config> {

    private final JwtService jwtService;
    @Value("${demo.tenant.id}")
    private String demoTenantId;

    private static final String DEMO_HEADER = "X-ILLUMIO-DEMO";

    public InsightsSearchFilter(JwtService jwtService) {
        super(Config.class);
        this.jwtService = jwtService;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return ((exchange, chain) -> {
            log.info("Inside Insights search filter, number of available processors: {}", Runtime.getRuntime().availableProcessors());
            if (exchange.getRequest().getMethod() == HttpMethod.OPTIONS) {
                log.info("Skipping filter for OPTIONS");
                return chain.filter(exchange);
            }
            String authKey = exchange.getRequest().getHeaders().getFirst("X-Auth-Key");
            if (authKey != null && !authKey.isEmpty()) {
                log.info("Auth key is present in request header, authKey = {}", authKey);
                return chain.filter(exchange);
            }
            log.info("InsightsQueryFilter : {}", config.getMessage() );
            GetUserSessionPermissionsResponse grpcResponse = exchange.getAttribute("grpcSession");
            GetServiceAccountPermissionsResponse serviceAccountResponse = exchange.getAttribute("serviceAccountSession");
            if (grpcResponse == null && serviceAccountResponse == null) {
                log.warn("Session not found");
                return chain.filter(exchange);
            }

            String demoModeHeader = exchange.getRequest().getHeaders().getFirst(DEMO_HEADER);
            log.info("DemoModeFilter: Demo mode header value: {}", demoModeHeader);

            String tenant_id;
            if ("true".equalsIgnoreCase(demoModeHeader)) {
                log.info("DemoModeFilter: Demo mode enabled, continuing updating the tenant id");
                tenant_id = demoTenantId;
            }
            else {
                if (grpcResponse != null) {
                    tenant_id = grpcResponse.getCloudsecure().getTenantId();
                }
                else {
                    tenant_id = serviceAccountResponse.getCloudsecure().getTenantId();
                }
            }
            Map<String, Object> jwtClaims = new HashMap<>();
            jwtClaims.put("tenant", tenant_id);
            if (serviceAccountResponse != null) {
                String apikey = exchange.getRequest().getHeaders().getFirst("X-api-key");
                String apiSecret = exchange.getRequest().getHeaders().getFirst("X-api-secret");
                jwtClaims.put("X-api-key", apikey);
                jwtClaims.put("X-api-secret", apiSecret);
            }
            String claims;
            try {
                claims = jwtService.generatePermissionsJwt(jwtClaims, 10);
            } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
                throw new RuntimeException(e);
            }

            ServerHttpRequest mutatedRequest = exchange.getRequest().mutate()
                    .header(GATEWAY_TOKEN, claims)
                    .header("jwt", claims)
                    .build();
            ServerWebExchange mutatedExchange = exchange.mutate()
                    .request(mutatedRequest)
                    .build();
            return chain.filter(mutatedExchange);
        });
    }

    @Getter
    @Setter
    public static class Config {
        private String message;
    }
}
