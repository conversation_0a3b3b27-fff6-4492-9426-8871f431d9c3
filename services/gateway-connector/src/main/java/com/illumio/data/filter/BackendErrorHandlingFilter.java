package com.illumio.data.filter;

import com.illumio.data.service.ErrorHandlingService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class BackendErrorHandlingFilter extends AbstractGatewayFilterFactory<BackendErrorHandlingFilter.Config> {

    private final ErrorHandlingService errorHandlingService;

    public BackendErrorHandlingFilter(ErrorHandlingService errorHandlingService) {
        super(Config.class);
        this.errorHandlingService = errorHandlingService;
    }

    @Override
    public GatewayFilter apply(Config config) {
        /**
         *  The GlobalErrorWebExceptionHandler.handle() method won't be called when BackendErrorHandlingFilter
         *  is present because of how Spring Cloud Gateway's error handling chain works.
         */
        return (exchange, chain) -> {
            return chain.filter(exchange)
                    .onErrorResume(throwable -> {
                        log.error("Backend error in route [{}]: {}",
                                config.getServiceName(), throwable.getMessage(), throwable);

                        // Create error context from config
                        ErrorHandlingService.ErrorContext context = new ErrorHandlingService.ErrorContext(config.getServiceName())
                                .withServiceInfo(config.isIncludeServiceInfo(), config.getHealthCheckUrl())
                                .withErrorDetails(config.isIncludeErrorDetails());

                        return errorHandlingService.handleError(exchange, throwable, context);
                    });
        };
    }

    @Getter
    @Setter
    public static class Config {
        private String serviceName = "backend-service";
        private boolean includeServiceInfo = false;
        private boolean includeErrorDetails = false;
        private String healthCheckUrl = "";
    }
}
