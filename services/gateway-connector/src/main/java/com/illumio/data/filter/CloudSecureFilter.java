package com.illumio.data.filter;

import authservice.GetUserSessionPermissionsResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.util.JsonFormat;
import com.illumio.data.service.JwtService;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;

import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.HashMap;

import java.util.Map;

import static com.illumio.data.constants.Constants.GATEWAY_TOKEN;

@Slf4j
@Component
public class CloudSecureFilter extends AbstractGatewayFilterFactory<CloudSecureFilter.Config> {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final JwtService jwtService;
    private final Counter cloudSecureCounter;

    public CloudSecureFilter(JwtService jwtService, MeterRegistry meterRegistry)  {
        super(Config.class);// <== REQUIRED
        this.jwtService = jwtService;
        this.cloudSecureCounter = meterRegistry.counter("cloudsecure_counter");
    }

    @Override
    public GatewayFilter apply(Config config) {
        return ((exchange, chain) -> {
            GetUserSessionPermissionsResponse grpcResponse = exchange.getAttribute("grpcSession");
            String csi_token = exchange.getRequest().getHeaders().getFirst("X-csi");
            if (grpcResponse == null) {
                log.warn("gRPC session not found in request attributes");
                return chain.filter(exchange);
            }

            try {
                // === Extract target FQDN and port from gRPC response ===

                ServerHttpRequest request = exchange.getRequest();
                String existingPath = request.getURI().getRawPath();
                log.info("Current api path is: {}", existingPath);


                // === Clone headers from original request ===

                String json = JsonFormat.printer()
                        .preservingProtoFieldNames()
                        .print(grpcResponse);

                boolean permissions_excluded = false;
                Map<String, Object> jwtClaims;
                try {
                    jwtClaims = objectMapper.readValue(
                            json,
                            new TypeReference<HashMap<String,Object>>() {}   // ensures HashMap
                    );
                } catch (JsonProcessingException e) {
                    log.error("Error parsing JSON {}", e.getMessage());
                    throw new RuntimeException(e);
                }
                    // Convert gRPC message to JSON and encrypt to JWE
                jwtClaims.put("permissions_excluded", permissions_excluded);

                log.info("Sending response to cloudsecure : {}", jwtClaims);

                // === Mutate request with new URI and headers ===
                String jwt = jwtService.generatePermissionsJwt(jwtClaims, grpcResponse.getCore().getSessionTimeoutMinutes());

                ServerHttpRequest mutatedRequest = exchange.getRequest().mutate()
                        .header(GATEWAY_TOKEN, jwt)
                        .build();
                ServerWebExchange mutatedExchange = exchange.mutate()
                        .request(mutatedRequest)
                        .build();
                return chain.filter(mutatedExchange);

            } catch (Exception e) {
                log.error("Cloudsecure filter failed {}", e.getMessage());
                return Mono.error(e);
            }
        });
    }

    public static class Config {
        private String message;
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
