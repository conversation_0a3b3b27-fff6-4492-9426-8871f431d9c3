package com.illumio.data.config;

import authservice.ReactorAuthServiceGrpc;
import com.illumio.data.properties.GrpcClientProperties;
import io.grpc.ManagedChannel;
import io.grpc.netty.GrpcSslContexts;
import io.grpc.netty.NettyChannelBuilder;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.net.ssl.SSLException;
import java.io.ByteArrayInputStream;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

@Configuration
public class GrpcClientConfig {

    private final GrpcClientProperties properties;

    @Value("${MTLS_CERT}")
    private String mtlsCert;

    @Value("${CA_CERT}")
    private String caCert;

    @Value("${MTLS_KEY}")
    private String mtlsKey;

    @Value("${keepAliveTime}")
    private long keepAliveTime;

    @Value("${keepAliveTimeOut}")
    private long keepAliveTimeOut;

    public GrpcClientConfig(GrpcClientProperties properties) {
        this.properties = properties;
    }

    @Bean
    public ManagedChannel authServiceChannel() throws SSLException {
        NettyChannelBuilder channelBuilder = NettyChannelBuilder
            .forAddress(properties.getHost(), properties.getPort());

        SslContext sslContext;
        if (!properties.isEnableMtls()){
            if (properties.isUseTls()) {
                // Use TLS for production
                sslContext = GrpcSslContexts.forClient()
                    .trustManager(InsecureTrustManagerFactory.INSTANCE)
                    .build();
                channelBuilder.sslContext(sslContext);
            } else {
                // Use plaintext for testing/development
                channelBuilder.usePlaintext();
            }
        }
        // use MTLS
        else {

            sslContext = GrpcSslContexts.forClient()
                .keyManager(
                    new ByteArrayInputStream(Base64.getDecoder().decode(mtlsCert)),
                    new ByteArrayInputStream(Base64.getDecoder().decode(mtlsKey)))
                .trustManager(new ByteArrayInputStream(Base64.getDecoder().decode(caCert)))
                .build();
            channelBuilder.sslContext(sslContext);
        }
        return channelBuilder
            .keepAliveTime(keepAliveTime, TimeUnit.SECONDS)
            .keepAliveTimeout(keepAliveTimeOut, TimeUnit.SECONDS)
            .keepAliveWithoutCalls(true)
            .build();
    }

    @Bean
    public ReactorAuthServiceGrpc.ReactorAuthServiceStub authServiceStub(ManagedChannel channel) {
        return ReactorAuthServiceGrpc.newReactorStub(channel);
    }
}

