package com.illumio.data.service;

import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;

@Slf4j
public class DateTimeUtils {
    public static String convertToIso8601(String lastUpdatedAt, String timezone) {
        if (lastUpdatedAt == null || lastUpdatedAt.isBlank()) {
            return null;
        }

        try {
            // Remove trailing "(Pacific Daylight Time)" or similar
            String cleaned = lastUpdatedAt.replaceAll(" \\(.*\\)$", "");

            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern(
                    "EEE MMM dd yyyy HH:mm:ss 'GMT'Z", Locale.ENGLISH);

            ZonedDateTime zonedDateTime = ZonedDateTime.parse(cleaned, inputFormatter);
            ZonedDateTime targetZoneTime = zonedDateTime.withZoneSameInstant(ZoneId.of(timezone));

            return targetZoneTime.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
        } catch (Exception e) {
            // Optional: log error here if needed
            log.error("Error converting to ISO 8601", e);
            return null;
        }
    }

    public static String getExpiry(String timezone , int days_to_expire){
        ZoneId zone = ZoneId.of(timezone);
        ZonedDateTime nowInZone = ZonedDateTime.now(zone);
        ZonedDateTime expiry = nowInZone.plusDays(days_to_expire);
        return expiry.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
    }

    public static boolean isCacheExpiringSoon(String lastUpdatedAt, String timezone, long ttlInSeconds, long bufferInSeconds) {
        String isoString = convertToIso8601(lastUpdatedAt, timezone);
        if (isoString == null) {
            return true; // Fail-safe: refresh cache
        }

        ZonedDateTime cachedAt = ZonedDateTime.parse(isoString);
        ZonedDateTime now = ZonedDateTime.now(ZoneId.of(timezone));

        long secondsElapsed = java.time.Duration.between(cachedAt, now).getSeconds();

        // If it's about to expire (within buffer), return true
        return secondsElapsed >= (ttlInSeconds - bufferInSeconds);
    }


}

