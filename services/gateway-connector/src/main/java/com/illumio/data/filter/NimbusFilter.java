package com.illumio.data.filter;

import authservice.GetServiceAccountPermissionsResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.JsonFormat;
import com.illumio.data.config.JwtClaimsMapper;
import com.illumio.data.service.JwtService;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.HashMap;
import java.util.Map;
import static com.illumio.data.constants.Constants.GATEWAY_TOKEN;
import static com.illumio.data.constants.Constants.PERMISSIONS_SIZE;

@Slf4j
@Component
public class NimbusFilter extends AbstractGatewayFilterFactory<NimbusFilter.Config> {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final JwtService jwtService;
    private final Counter nimbusCounter;

    public NimbusFilter(JwtService jwtService, MeterRegistry meterRegistry) {
        super(Config.class);
        this.jwtService = jwtService;
        this.nimbusCounter = meterRegistry.counter("gateway.nimbus.counter");
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            nimbusCounter.increment();
            GetServiceAccountPermissionsResponse response = exchange.getAttribute("serviceAccountSession");
            ServerHttpRequest request = exchange.getRequest();
            String existingPath = request.getURI().getRawPath();
            log.info("Current api path is: {}", existingPath);
            HttpHeaders newHeaders = new HttpHeaders();
            newHeaders.putAll(exchange.getRequest().getHeaders());
            String json;
            try {
                json = JsonFormat.printer()
                        .preservingProtoFieldNames()
                        .includingDefaultValueFields()
                        .print(response);
            } catch (InvalidProtocolBufferException e) {
                log.error("Error parsing JSON", e);
                throw new RuntimeException(e);
            }
            Map<String, Object> jwtClaims;
            try {
                jwtClaims = objectMapper.readValue(
                        json,
                        new TypeReference<HashMap<String,Object>>() {}   // ensures HashMap
                );
            } catch (JsonProcessingException e) {
                log.error("Error parsing JSON {}", e.getMessage());
                throw new RuntimeException(e);
            }
            boolean permissions_excluded = false;
            if (json.getBytes(StandardCharsets.UTF_8).length > PERMISSIONS_SIZE) {

                log.warn("response > 4KB, skipping permissions as there are too many ...");
                Map<String, Object> coreClaimsMap = JwtClaimsMapper.mapServiceAccountDetails(response);
                jwtClaims.put("core", coreClaimsMap);
                permissions_excluded = true;
            }
            jwtClaims.put("perm_excluded", permissions_excluded);

            try {
                String jwt = jwtService.generatePermissionsJwt(jwtClaims, 10);
                newHeaders.set(GATEWAY_TOKEN, jwt);
                ServerHttpRequest mutatedRequest = exchange.getRequest().mutate()
                        .header(GATEWAY_TOKEN, jwt)
                        .build();
                ServerWebExchange mutatedExchange = exchange.mutate()
                        .request(mutatedRequest)
                        .build();
                return chain.filter(mutatedExchange);
            } catch (NoSuchAlgorithmException e) {
                log.error("No such algorithm exception {}", e.getMessage());
                throw new RuntimeException(e);
            } catch (InvalidKeySpecException e) {
                log.error("Invalid key spec exception {}", e.getMessage());
                throw new RuntimeException(e);
            }
        };
    }

    @Getter
    @Setter
    public static class Config {
        private String message;
    }

}

