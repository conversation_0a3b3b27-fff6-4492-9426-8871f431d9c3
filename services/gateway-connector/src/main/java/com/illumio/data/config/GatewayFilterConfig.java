package com.illumio.data.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import javax.net.ssl.SSLException;
import java.time.Duration;

@Slf4j
@Configuration
public class GatewayFilterConfig {

    @Value("${maximumIdleTime}")
    Duration maxIdleTime;

    @Value("${maximumLifeTime}")
    Duration lifeTime;

    @Value("${acquiringTimeOut}")
    Duration acquiringTimeOut;

    @Value("${evictInBackground}")
    Duration evictInBackground;

    @Value(("${maximumConnections}"))
    int maximumConnections;

    @Value("${connectTimeOut}")
    int connectTimeOut;

    @Value("${readHandlerTimeout}")
    int readHandlerTimeout;

    @Value("${writeHandlerTimeout}")
    int writeHandlerTimeout;


    @Bean
    public ConnectionProvider customConnectionProvider() {
        ConnectionProvider provider = ConnectionProvider.builder("gateway-elastic-pool")
                .lifo()
                .maxConnections(maximumConnections) // Unlimited per host
                .maxIdleTime(maxIdleTime)// Close idle connections after 30s
                .maxLifeTime(lifeTime)
                .pendingAcquireTimeout(acquiringTimeOut) // Timeout waiting for connection
                .evictInBackground(evictInBackground)// Check for bad connections every 30s
                .metrics(true)
                .build();
        log.info("Custom connection provider: {}", provider.maxConnections());
        log.info("Default maximum connections: {}", ConnectionProvider.DEFAULT_POOL_MAX_CONNECTIONS);
        return provider;
    }

    @Bean
    @Primary
    public HttpClient httpClient(ConnectionProvider connectionProvider) {
        log.info("Number of available processors: {}", Runtime.getRuntime().availableProcessors());
        return HttpClient.create(connectionProvider)
                .keepAlive(true)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, connectTimeOut)// 10s connect timeout
                .option(ChannelOption.SO_KEEPALIVE, true)
                .doOnChannelInit((obs, ch, addr) -> ch.pipeline().addLast("sslExceptionCloser",
                new io.netty.channel.ChannelDuplexHandler() {
                    @Override public void exceptionCaught(io.netty.channel.ChannelHandlerContext ctx, Throwable cause) {
                        if (cause instanceof SSLException ||
                                cause instanceof io.netty.channel.unix.Errors.NativeIoException ||
                                cause instanceof java.nio.channels.ClosedChannelException ||
                                cause.getClass().getName().equals("io.netty.handler.ssl.SslClosedEngineException") ||
                                (cause.getClass().getSimpleName().contains("SSLHandshakeException"))) {
                            log.warn("SSL-related error caught, closing channel: {}", cause.toString());
                            ctx.close();
                        }
                        else {
                            ctx.fireExceptionCaught(cause); // propagate to next
                        }
                    }
                }))
                .doOnConnected(conn -> conn
                        .addHandlerLast(new ReadTimeoutHandler(readHandlerTimeout))   // 15s read timeout
                        .addHandlerLast(new WriteTimeoutHandler(writeHandlerTimeout)))
                .secure(sslContextSpec -> {
                    try {
                        sslContextSpec.sslContext(
                                SslContextBuilder.forClient()
                                        .trustManager(InsecureTrustManagerFactory.INSTANCE)
                                        .build()
                        )
                                .closeNotifyFlushTimeout(Duration.ofSeconds(5))
                                .closeNotifyReadTimeout(Duration.ofSeconds(5));
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to build insecure SSL context", e);
                    }
                });
    }

    @Bean
    public WebClient insecureWebClient(HttpClient httpClient) {
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }
}