package com.illumio.data.properties;


import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "grpc.authservice")
public class GrpcClientProperties {
    private String host;
    private int port;
    private boolean useTls = true; // Default to true for production
    private boolean enableMtls;
}
