package com.illumio.data.exception;

import com.illumio.data.service.ErrorHandlingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

@Slf4j
@Component
@Order(-1) // Higher priority than default error handler

/**
 * When GlobalErrorWebExceptionHandler IS called:
     * The global handler only gets called for errors that are NOT caught by filters:
     * Errors in routes WITHOUT BackendErrorHandlingFilter
     * Errors that occur BEFORE the filter chain (e.g., routing errors, malformed requests)
     * Errors in the filter setup itself (e.g., filter configuration issues)
     * Unhandled exceptions in other parts of the application
 */
public class GlobalErrorWebExceptionHandler implements ErrorWebExceptionHandler {

    private final ErrorHandlingService errorHandlingService;

    public GlobalErrorWebExceptionHandler(ErrorHandlingService errorHandlingService) {
        this.errorHandlingService = errorHandlingService;
    }

    @Override
    public Mono<Void> handle(ServerWebExchange exchange, Throwable ex) {
        // Use shared error handling service with global context
        ErrorHandlingService.ErrorContext context = new ErrorHandlingService.ErrorContext("unknown-service")
                .withServiceInfo(false, "")
                .withErrorDetails(false);

        return errorHandlingService.handleError(exchange, ex, context);
    }
}
