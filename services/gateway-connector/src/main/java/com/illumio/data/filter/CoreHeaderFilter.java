package com.illumio.data.filter;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.OrderedGatewayFilter;
import org.springframework.cloud.gateway.filter.RouteToRequestUrlFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.stereotype.Component;
import org.springframework.http.server.reactive.ServerHttpRequest;
import java.net.URI;
import static com.illumio.data.constants.Constants.SCHEME_HTTP;
import static com.illumio.data.constants.Constants.SCHEME_HTTPS;
import static com.illumio.data.constants.Constants.SCHEME_DELIMITER;
import org.springframework.web.util.UriComponentsBuilder;

import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.GATEWAY_REQUEST_URL_ATTR;

@Slf4j
@Component
public class CoreHeaderFilter extends AbstractGatewayFilterFactory<CoreHeaderFilter.Config> {

    public CoreHeaderFilter() {
        super(Config.class); // <-- REQUIRED so SCG binds args into Config
    }
    @Getter
    @Setter
    public static class Config {
        private String message;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return new OrderedGatewayFilter((exchange, chain) -> {
          String pce_fqdn = exchange.getRequest().getHeaders().getFirst("X-PCE-FQDN");
          if (pce_fqdn != null && !pce_fqdn.isEmpty()) {
            ServerHttpRequest request = exchange.getRequest();
            URI original = request.getURI();

            String candidate = pce_fqdn.trim();
            boolean looksLikeUrl = candidate.startsWith(SCHEME_HTTP + SCHEME_DELIMITER) || candidate.startsWith(SCHEME_HTTPS + SCHEME_DELIMITER);

            UriComponentsBuilder builder;
            if (looksLikeUrl) {
                builder = UriComponentsBuilder.fromHttpUrl(candidate);
            } else {
                String originalScheme = original.getScheme();
                String schemeToUse = (originalScheme != null && !originalScheme.isEmpty()) ? originalScheme : SCHEME_HTTPS;
                URI parsed = URI.create(schemeToUse + SCHEME_DELIMITER + candidate);
                builder = UriComponentsBuilder.newInstance()
                        .scheme(schemeToUse)
                        .host(parsed.getHost());
                if (parsed.getPort() != -1) {
                    builder.port(parsed.getPort());
                }
            }

            // Preserve path and query from the original request
            builder.path(original.getRawPath());
            if (original.getRawQuery() != null && !original.getRawQuery().isEmpty()) {
                builder.query(original.getRawQuery());
            }

            URI targetUri = builder.build(true).toUri();
            exchange.getAttributes().put(GATEWAY_REQUEST_URL_ATTR, targetUri);
          }
          return chain.filter(exchange.mutate().request(exchange.getRequest()).build());

        }, RouteToRequestUrlFilter.ROUTE_TO_URL_FILTER_ORDER+1);

    }

}
