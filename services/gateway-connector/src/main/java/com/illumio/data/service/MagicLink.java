package com.illumio.data.service;



import java.util.HashMap;
import java.util.Map;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.time.Instant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MagicLink {
    private final JwtService jwtService;
    
    @Value("${jwtExpiryDays:7}")
    private int expiryDays;

    public MagicLink(JwtService jwtService) {
        this.jwtService = jwtService;
    }

    public String generateJwtToken(String tenantId, String userId){
        Map<String, Object> claims = new HashMap<>();
        claims.put("tenant", tenantId);
        claims.put("sub", userId);

        String Jwtclaims;
        try {
            Jwtclaims = jwtService.generatePermissionsJwt(claims, expiryDays*24*60);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
            throw new RuntimeException(e);
        }

        return Jwtclaims;
    }
}
