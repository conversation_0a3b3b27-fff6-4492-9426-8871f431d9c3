package com.illumio.data.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;

@RestController
public class QosController {

    @GetMapping("/Qos")
    public Mono<String> Qos(){
        return Mono.just("Qos");
    }

    @PostMapping("/Qos")
    public Mono<ServerResponse> Qos(ServerRequest request) {
        return ServerResponse.ok().body(Mono.just("Qos"), String.class);
    }

}
