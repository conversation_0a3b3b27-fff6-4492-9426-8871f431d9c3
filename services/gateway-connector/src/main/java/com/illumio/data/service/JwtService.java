package com.illumio.data.service;

import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Duration;
import java.util.Base64;
import java.util.Date;
import java.util.Map;

@Component
@Slf4j
public class JwtService {

    @Value("${JWT_SECRET}")
    private String secretKey;

    @Value("${JWT_ALGORITHM}")
    private String algorithm;

    private PrivateKey getPrivateKey() throws NoSuchAlgorithmException, InvalidKeySpecException {
        String secret = secretKey.replaceAll("\\s", "");
        byte[] outerDecoded = Base64.getDecoder().decode(secret);

        String pem = new String(outerDecoded, StandardCharsets.UTF_8);

        // Step 2: Strip headers and footers
        String privateKeyPEM = pem
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s", "");

        // Step 3: Decode the inner Base64 content to get DER bytes
        byte[] der = Base64.getDecoder().decode(privateKeyPEM);

        // Step 4: Generate PrivateKey instance
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(der);
        KeyFactory kf = KeyFactory.getInstance("RSA");
        return kf.generatePrivate(keySpec);
    }

    private Key getSigningKey() throws NoSuchAlgorithmException, InvalidKeySpecException {
        if ("RS256".equalsIgnoreCase(algorithm)) {
            return getPrivateKey();
        } else if ("HS256".equalsIgnoreCase(algorithm)) {
            byte[] keyBytes = Base64.getDecoder().decode(secretKey.replaceAll("\\s", ""));
            return Keys.hmacShaKeyFor(keyBytes);
        } else {
            throw new IllegalArgumentException("Unsupported JWT algorithm: " + algorithm);
        }
    }

    public String generatePermissionsJwt(Map<String, Object> permissions, int timeoutMinutes) throws NoSuchAlgorithmException, InvalidKeySpecException {
        Duration duration = Duration.ofMinutes(timeoutMinutes);
        long expirationMillis = System.currentTimeMillis() + duration.toMillis();
        Key signingKey = getSigningKey();

        SignatureAlgorithm sigAlg = SignatureAlgorithm.forName(algorithm);

        return Jwts.builder()
                .setClaims(permissions)
                .setIssuedAt(new Date())
                .setExpiration(new Date(expirationMillis))
                .signWith(signingKey, sigAlg)
                .compact();
    }
}

