package com.illumio.data.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import static com.illumio.data.constants.Constants.EXCLUDE_SUBDOMAINS;

@Slf4j
@Component
public class AsgardFilter extends AbstractGatewayFilterFactory<AsgardFilter.Config> {

    public AsgardFilter() {
        super(Config.class); // <== REQUIRED
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            log.info("AsgardFilter message: {}", config.getMessage());

            ServerHttpRequest request = exchange.getRequest();
            String subdomain = extractSubdomain(request);
            log.info("AsgardFilter subdomain: {}", subdomain);
            if (subdomain != null) {
                log.info("Extracted subdomain: {}", subdomain);

                ServerHttpRequest mutatedRequest = request.mutate()
                        .header("X-tenant-subdomain", subdomain)
                        .build();

                return chain.filter(exchange.mutate().request(mutatedRequest).build());
            }

            return chain.filter(exchange);
        };
    }

    private String extractSubdomain(ServerHttpRequest request) {
        String host = request.getURI().getHost();
        if (host == null) return null;

        try {
            String[] parts = host.split("\\.");
            if (EXCLUDE_SUBDOMAINS.contains(parts[0])){
                return null;
            }
            return parts[0];
        } catch (Exception e) {
            log.error("Error extracting subdomain from host: {}", host, e);
        }

        return null;
    }

    public static class Config {
        private String message;
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
    }
}
