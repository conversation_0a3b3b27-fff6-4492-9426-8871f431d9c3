package com.illumio.data.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.netty.channel.AbstractChannel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.ConnectException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeoutException;

@Slf4j
@Service
public class ErrorHandlingService {

    private final ObjectMapper objectMapper;
    private final Counter serviceUnavailableCounter;
    private final Counter connectionRefusedCounter;
    private final Counter timeoutCounter;
    private final Counter generalErrorCounter;

    public ErrorHandlingService(MeterRegistry meterRegistry) {
        this.objectMapper = new ObjectMapper();
        
        // Initialize metrics
        this.serviceUnavailableCounter = Counter.builder("gateway.backend.service.unavailable")
                .description("Count of backend service unavailable errors")
                .register(meterRegistry);
                
        this.connectionRefusedCounter = Counter.builder("gateway.backend.connection.refused")
                .description("Count of connection refused errors")
                .register(meterRegistry);
                
        this.timeoutCounter = Counter.builder("gateway.backend.timeout")
                .description("Count of backend timeout errors")
                .register(meterRegistry);
                
        this.generalErrorCounter = Counter.builder("gateway.backend.error")
                .description("Count of general backend errors")
                .register(meterRegistry);
    }

    /**
     * Handle backend errors and return appropriate response
     */
    public Mono<Void> handleError(ServerWebExchange exchange, Throwable ex, ErrorContext context) {
        ServerHttpResponse response = exchange.getResponse();
        
        if (response.isCommitted()) {
            return Mono.error(ex);
        }

        // Extract request information
        String requestId = exchange.getRequest().getHeaders().getFirst("X-Request-ID");
        String path = exchange.getRequest().getPath().value();
        String method = exchange.getRequest().getMethod().name();
        
        log.error("Handling error for request [{}] {} {} in service [{}]: {}", 
                requestId, method, path, context.getServiceName(), ex.getMessage(), ex);

        // Create error response
        ErrorResponse errorResponse = createErrorResponse(ex, requestId, path, context);
        
        // Update metrics
        updateMetrics(ex);
        
        // Set response headers
        response.setStatusCode(errorResponse.getHttpStatus());
        response.getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        response.getHeaders().add("X-Error-Source", "gateway");
        
        if (errorResponse.getRetryAfter() != null) {
            response.getHeaders().add("Retry-After", errorResponse.getRetryAfter().toString());
        }
        
        // Create response body
        String responseBody = serializeErrorResponse(errorResponse, context);
        DataBuffer dataBuffer = response.bufferFactory().wrap(responseBody.getBytes(StandardCharsets.UTF_8));
        
        return response.writeWith(Mono.just(dataBuffer));
    }

    /**
     * Create error response based on exception type
     */
    private ErrorResponse createErrorResponse(Throwable ex, String requestId, String path, ErrorContext context) {
        ErrorResponse errorResponse = new ErrorResponse();
        errorResponse.setRequestId(requestId);
        errorResponse.setPath(path);
        errorResponse.setTimestamp(Instant.now().toString());
        errorResponse.setServiceName(context.getServiceName());
        
        if (isConnectionRefused(ex)) {
            errorResponse.setHttpStatus(HttpStatus.SERVICE_UNAVAILABLE);
            errorResponse.setErrorCode("SERVICE_UNAVAILABLE");
            errorResponse.setMessage(String.format("The %s service is currently unavailable. Please try again later.", 
                    context.getServiceName()));
            errorResponse.setRetryAfter(30);
            
        } else if (isTimeout(ex)) {
            errorResponse.setHttpStatus(HttpStatus.GATEWAY_TIMEOUT);
            errorResponse.setErrorCode("GATEWAY_TIMEOUT");
            errorResponse.setMessage(String.format("Request to %s service timed out. Please try again.", 
                    context.getServiceName()));
            errorResponse.setRetryAfter(15);
            
        } else {
            errorResponse.setHttpStatus(HttpStatus.INTERNAL_SERVER_ERROR);
            errorResponse.setErrorCode("INTERNAL_ERROR");
            errorResponse.setMessage(String.format("An unexpected error occurred while communicating with %s service.", 
                    context.getServiceName()));
        }
        
        return errorResponse;
    }

    /**
     * Check if error is connection refused
     */
    public boolean isConnectionRefused(Throwable ex) {
        return ex instanceof ConnectException ||
               (ex.getCause() instanceof ConnectException) ||
               ex.getMessage().contains("Connection refused") ||
               ex.getMessage().contains("finishConnect(..) failed");
    }

    /**
     * Check if error is timeout
     */
    public boolean isTimeout(Throwable ex) {
        return ex instanceof TimeoutException ||
               ex.getMessage().contains("timeout") ||
               ex.getMessage().contains("Timeout");
    }

    /**
     * Check if error is retryable
     */
    public boolean isRetryableError(Throwable ex) {
        return isConnectionRefused(ex) || isTimeout(ex);
    }

    /**
     * Update metrics based on error type
     */
    private void updateMetrics(Throwable ex) {
        generalErrorCounter.increment();
        
        if (isConnectionRefused(ex)) {
            connectionRefusedCounter.increment();
            serviceUnavailableCounter.increment();
        } else if (isTimeout(ex)) {
            timeoutCounter.increment();
        }
    }

    /**
     * Serialize error response to JSON
     */
    private String serializeErrorResponse(ErrorResponse errorResponse, ErrorContext context) {
        try {
            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("error", true);
            responseMap.put("error_code", errorResponse.getErrorCode());
            responseMap.put("message", errorResponse.getMessage());
            responseMap.put("timestamp", errorResponse.getTimestamp());
            responseMap.put("path", errorResponse.getPath());
            responseMap.put("request_id", errorResponse.getRequestId());
            
            if (errorResponse.getRetryAfter() != null) {
                responseMap.put("retry_after_seconds", errorResponse.getRetryAfter());
            }
            
            // Add service info if enabled
            if (context.isIncludeServiceInfo()) {
                responseMap.put("service_info", Map.of(
                    "name", context.getServiceName(),
                    "status", "unavailable",
                    "health_check_url", context.getHealthCheckUrl()
                ));
            }
            
            // Add error details if enabled
            if (context.isIncludeErrorDetails()) {
                responseMap.put("error_details", Map.of(
                    "type", errorResponse.getClass().getSimpleName(),
                    "service", errorResponse.getServiceName()
                ));
            }
            
            responseMap.put("support_info", Map.of(
                "contact", "<EMAIL>",
                "documentation", "https://docs.illumio.com/gateway-errors"
            ));
            
            return objectMapper.writeValueAsString(responseMap);
            
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize error response", e);
            return String.format("{\"error\":true,\"message\":\"Service unavailable\",\"timestamp\":\"%s\"}", 
                   Instant.now().toString());
        }
    }

    /**
     * Error response data structure
     */
    public static class ErrorResponse {
        private HttpStatus httpStatus;
        private String errorCode;
        private String message;
        private String timestamp;
        private String requestId;
        private String path;
        private String serviceName;
        private Integer retryAfter;

        // Getters and setters
        public HttpStatus getHttpStatus() { return httpStatus; }
        public void setHttpStatus(HttpStatus httpStatus) { this.httpStatus = httpStatus; }
        
        public String getErrorCode() { return errorCode; }
        public void setErrorCode(String errorCode) { this.errorCode = errorCode; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public String getTimestamp() { return timestamp; }
        public void setTimestamp(String timestamp) { this.timestamp = timestamp; }
        
        public String getRequestId() { return requestId; }
        public void setRequestId(String requestId) { this.requestId = requestId; }
        
        public String getPath() { return path; }
        public void setPath(String path) { this.path = path; }
        
        public String getServiceName() { return serviceName; }
        public void setServiceName(String serviceName) { this.serviceName = serviceName; }
        
        public Integer getRetryAfter() { return retryAfter; }
        public void setRetryAfter(Integer retryAfter) { this.retryAfter = retryAfter; }
    }

    /**
     * Context for error handling configuration
     */
    public static class ErrorContext {
        private String serviceName = "backend-service";
        private boolean includeServiceInfo = false;
        private boolean includeErrorDetails = false;
        private String healthCheckUrl = "";

        public ErrorContext(String serviceName) {
            this.serviceName = serviceName;
        }

        public ErrorContext withServiceInfo(boolean include, String healthCheckUrl) {
            this.includeServiceInfo = include;
            this.healthCheckUrl = healthCheckUrl;
            return this;
        }

        public ErrorContext withErrorDetails(boolean include) {
            this.includeErrorDetails = include;
            return this;
        }

        // Getters
        public String getServiceName() { return serviceName; }
        public boolean isIncludeServiceInfo() { return includeServiceInfo; }
        public boolean isIncludeErrorDetails() { return includeErrorDetails; }
        public String getHealthCheckUrl() { return healthCheckUrl; }
    }
}
