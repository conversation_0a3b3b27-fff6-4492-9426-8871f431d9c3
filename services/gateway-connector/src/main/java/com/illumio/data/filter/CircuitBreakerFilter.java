package com.illumio.data.filter;

import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.reactor.circuitbreaker.operator.CircuitBreakerOperator;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.ConnectException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.concurrent.TimeoutException;

@Slf4j
@Component
public class CircuitBreakerFilter extends AbstractGatewayFilterFactory<CircuitBreakerFilter.Config> {

    private final MeterRegistry meterRegistry;
    private final Counter circuitBreakerOpenCounter;
    private final Counter circuitBreakerHalfOpenCounter;

    public CircuitBreakerFilter(MeterRegistry meterRegistry) {
        super(Config.class);
        this.meterRegistry = meterRegistry;
        
        this.circuitBreakerOpenCounter = Counter.builder("gateway.circuit.breaker.open")
                .description("Count of circuit breaker open events")
                .register(meterRegistry);
                
        this.circuitBreakerHalfOpenCounter = Counter.builder("gateway.circuit.breaker.half.open")
                .description("Count of circuit breaker half-open events")
                .register(meterRegistry);
    }

    @Override
    public GatewayFilter apply(Config config) {
        CircuitBreaker circuitBreaker = createCircuitBreaker(config);
        
        return (exchange, chain) -> {
            return chain.filter(exchange)
                    .transformDeferred(CircuitBreakerOperator.of(circuitBreaker))
                    .onErrorResume(throwable -> {
                        log.error("Circuit breaker error for service [{}]: {}", 
                                config.getServiceName(), throwable.getMessage());
                        return handleCircuitBreakerError(exchange, throwable, config);
                    });
        };
    }

    private CircuitBreaker createCircuitBreaker(Config config) {
        CircuitBreakerConfig circuitBreakerConfig = CircuitBreakerConfig.custom()
                .failureRateThreshold(config.getFailureRateThreshold())
                .waitDurationInOpenState(Duration.ofSeconds(config.getWaitDurationInOpenState()))
                .slidingWindowSize(config.getSlidingWindowSize())
                .minimumNumberOfCalls(config.getMinimumNumberOfCalls())
                .recordException(this::isRecordableException)
                .build();

        CircuitBreaker circuitBreaker = CircuitBreaker.of(config.getServiceName(), circuitBreakerConfig);
        
        // Register event listeners for metrics
        circuitBreaker.getEventPublisher()
                .onStateTransition(event -> {
                    log.info("Circuit breaker state transition: {} -> {} for service: {}", 
                            event.getStateTransition().getFromState(),
                            event.getStateTransition().getToState(),
                            config.getServiceName());
                    
                    switch (event.getStateTransition().getToState()) {
                        case OPEN:
                            circuitBreakerOpenCounter.increment();
                            break;
                        case HALF_OPEN:
                            circuitBreakerHalfOpenCounter.increment();
                            break;
                    }
                });

        return circuitBreaker;
    }

    private boolean isRecordableException(Throwable throwable) {
        return throwable instanceof ConnectException ||
               throwable instanceof TimeoutException ||
               throwable.getMessage().contains("Connection refused");
    }

    private Mono<Void> handleCircuitBreakerError(ServerWebExchange exchange, Throwable throwable, Config config) {
        String errorResponse = String.format(
                "{\"error\":true,\"error_code\":\"CIRCUIT_BREAKER_OPEN\"," +
                "\"message\":\"Service %s is currently unavailable due to repeated failures. Please try again later.\"," +
                "\"service\":\"%s\",\"retry_after_seconds\":60}",
                config.getServiceName(), config.getServiceName()
        );

        exchange.getResponse().setStatusCode(HttpStatus.SERVICE_UNAVAILABLE);
        exchange.getResponse().getHeaders().add("Content-Type", MediaType.APPLICATION_JSON_VALUE);
        exchange.getResponse().getHeaders().add("Retry-After", "60");

        DataBuffer buffer = exchange.getResponse().bufferFactory()
                .wrap(errorResponse.getBytes(StandardCharsets.UTF_8));

        return exchange.getResponse().writeWith(Mono.just(buffer));
    }

    @Getter
    @Setter
    public static class Config {
        private String serviceName = "backend-service";
        private float failureRateThreshold = 50.0f;
        private int waitDurationInOpenState = 60;
        private int slidingWindowSize = 10;
        private int minimumNumberOfCalls = 5;
    }
}
