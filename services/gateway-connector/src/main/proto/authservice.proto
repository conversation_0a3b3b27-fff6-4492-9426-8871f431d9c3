syntax = "proto3";

package authservice;

option java_multiple_files = true;
option java_package = "authservice";
option java_outer_classname = "AuthSessionProto";

enum Color_Blind {
  normal = 0;
  deficient = 1;
}

enum Type {
  local = 0;
  external = 1;
}

message CoreAuthHeader {
  string Authorization = 1;
  string x_csi = 2;
}



message UserSessionAccessRestriction {
  string name = 1;
  repeated string enforcement_exclusions = 2;
  repeated string ips = 3;
}


message Active_Session {
  string id = 1;
  string log_in_time = 2;
  string log_in_ip = 3;
}

message EffectiveGroups {
  string id = 1;
  string name = 2;
}

message AccessRestrictionInfo {
  string id = 1;
  string name = 2;
}

message Role {
  string id = 1;
  string name = 2;
}

message ScopeObject {
  string type = 1;
  string value = 2;
}

message RoleAndScope {
  Role role = 1;
  repeated ScopeObject scope = 2;
}

message CoreSessionPermissions {
  string user_id = 1;
  string user_uuid = 2;
  string username = 3;
  int32 org_id = 4;
  int32 user_org_id = 5;
  string pce_fqdn = 6;
  string org_uuid = 7;
  bool locked = 8;
  int32 session_timeout_minutes = 9;
  repeated RoleAndScope permissions = 10;
  string user_type = 11;
  UserSessionAccessRestriction access_restriction = 12;
}

message CloudsecurePermissions {
  string tenant_id = 1;
  string user_id = 2;
  repeated string permissions = 3;
}
message GetUserSessionPermissionsRequest {
  string csi = 1;
  string xff = 2;
  string referrer = 3;
  string last_polled_at = 4;
}

message CreatedBy {
  string id = 1;
  string name = 2;
  string type = 3;
  string email = 4;
}

message GetUserSessionPermissionsResponse {
  string iss = 1;
  repeated string aud = 2;
  string sub = 3;
  string sub_type = 4;
  string email = 5;
  CoreSessionPermissions core = 6;
  CloudsecurePermissions cloudsecure = 7;
  string perm_last_updated = 8;
}

//to get the user details
message GetUserRequest {
  string id = 1;
}

message ServiceAccountActor {
  string sub = 1;
  string type = 2;
  string api_key_id = 3;
  string name = 4;
  int32 exp = 5;
}

message GetUserResponse {
  string id = 1;
  string first_name = 2;
  string last_name = 3;
  string email = 4;
  string company_name = 5;
  string tenant_id = 6;
  string display_name = 7;
  bool deleted = 8;
  bool locked = 9;
  string time_zone = 10;
  Color_Blind color_blind = 11;
  Type type = 12;
  repeated Active_Session active_sessions = 13;
  string last_active_time = 14;
  string last_login_time = 15;
  repeated EffectiveGroups effective_group = 16;
  string avatar_id = 17;
  repeated RoleAndScope roles_and_scopes = 18;
  string pceId = 19;
  AccessRestrictionInfo access_restriction = 20;
  CreatedBy created_by = 21;
  CreatedBy updated_by = 22;
}


// For insights query service
message CloudAuthHeader {
  string x_tenant_id = 1;
  string cookie = 2;
}

message SessionCoreProduct {
  string org_id = 1;
  string org_uuid = 2;
  CoreAuthHeader auth_header = 3;
}
message GetServiceAccountPermissionsRequest {
  string key = 1;
  string secret = 2;
  string last_polled_at = 3;
}

message CloudsecureServiceAccountPermissions {
  string id = 1;
  string key_id = 2;
  string tenant_id = 3;
  repeated string permissions = 4;
}

message CoreRole {
  string href = 1;
}

message CorePermission {
  CoreRole role = 1;
  repeated ScopeObject scope = 2;
}

message CoreServiceAccountPermissions {
  int32 org_id = 1;
  string pce_fqdn = 2;
  string org_uuid = 3;
  int32 session_timeout_minutes = 4;
  repeated CorePermission permissions = 5;
  UserSessionAccessRestriction access_restriction = 6;
}


message GetServiceAccountPermissionsResponse {
  string iss = 1;
  repeated string aud = 2;
  string sub = 3;
  string sub_type = 4;
  string svc_name = 5;
  string svc_managing = 6;
  ServiceAccountActor act = 7;
  CoreServiceAccountPermissions core = 8;
  CloudsecureServiceAccountPermissions cloudsecure = 9;
  string perm_last_updated = 10;
}

message SessionCloudProduct {
  string tenant_id = 1;
  CloudAuthHeader auth_header = 2;
}
message SessionProducts {
  SessionCoreProduct core = 1;
  SessionCloudProduct cloudsecure = 2;
}

// gRPC Service
service AuthService {
  rpc GetUserSessionPermissions(GetUserSessionPermissionsRequest) returns (GetUserSessionPermissionsResponse) {}
  rpc GetServiceAccountPermissions(GetServiceAccountPermissionsRequest) returns (GetServiceAccountPermissionsResponse) {}
  rpc GetUser(GetUserRequest) returns (GetUserResponse) {}
}
