# Default values for gateway-connector.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# This will set the replicaset count more information can be found here: https://kubernetes.io/docs/concepts/workloads/controllers/replicaset/
replicaCount: 1

# This sets the container image more information can be found here: https://kubernetes.io/docs/concepts/containers/images/
image:
  repositoryBase: illum.azurecr.io/
  repositoryName: gateway-connector
  tag:          # value given at helm deployment
  pullPolicy: Always

# This is for the secrets for pulling an image from a private repository more information can be found here: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
imagePullSecrets: []
# This is to override the chart name.
nameOverride: ""
fullnameOverride: ""

CA_CERT: _DO_NOT_COMMIT
MTLS_CERT: _DO_NOT_COMMIT
MTLS_KEY: _DO_NOT_COMMIT

# This section builds out the service account more information can be found here: https://kubernetes.io/docs/concepts/security/service-accounts/
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

# This is for setting Kubernetes Annotations to a Pod.
# For more information checkout: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
podAnnotations: {}

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"



podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

jwt:
  secret: _DO_NOT_COMMIT


# This is for setting up a service more information can be found here: https://kubernetes.io/docs/concepts/services-networking/service/
service:
  type: ClusterIP
  port: 8080

configMapData:
  jwtExpiryDays: 7

  maximumIdleTime: 30
  maximumLifeTime: 5
  acquiringTimeOut: 10
  evictInBackground: 30

  server:
    port: 8080

  cache:
    maximum-size: 1000
    expire-after-write: 10
    expire-service-account-permissions: 5

  demo:
    tenant:
      id: "39e868b6-fdfc-4118-b664-a7d4b04728e8"
  
  resource-insights:
    auth-key:
      enabled: false
      value: _DO_NOT_COMMIT
    tenant-mapping:
      sourceTenantId: "_DO_NOT_COMMIT"
      targetTenantId: "_DO_NOT_COMMIT"

  spring:
    application:
      name: gateway-connector

    cloud:
      gateway:
        routes:
          - id: resource-insights-service
            uri: http://localhost:8080
            predicates:
              - Path=/api/v1/resource-insights**
            filters:
              - name: ResourceInsightsFilter
                args:
                  message: "Filter for Resource Insights Service"
          - id: core-service
            uri: http://core-service:8080
            predicates:
              - Path=/core/**
            filters:
              - name: PermissionFilter
              - name: CoreFilter
                args:
                  message: "Routing core"
              - StripPrefix=1
          - id: magic-link-auth-service
            uri: http://localhost:8080
            predicates:
              - Path=/api/magiclink/**
              - Header=user_uuid, .+
              - Header=uc_session, .+
              - Header=ucid, .+
              - Header=X-csi, .+

            filters:
              - name: MagicLinkAuthFilter
                args:
                  message: "Magic link auth"
        forwarded:
          enabled: true

  grpc:
    authservice:
      host: census-grpc.console.sunnyvale.ilabs.io
      port: 443

  management:
    endpoints:
      web:
        exposure:
          include: health,info
    endpoint:
      health:
        show-details: always
        probes:
          enabled: true

  logging:
    level:
      root: INFO
      org.springframework.web.reactive.function.client.ExchangeFunctions: DEBUG
      org.springframework.cloud.gateway: TRACE


ingress:
  tlsSecretName: "gateway-tls"
  annotations: {}
  ingressClassName: "nginx"
  enabled: false
  certManager:
    enabled: false
    clusterIssuer: "cert-manager-letsencrypt-prod-route53"
  hosts:
    - host: placeholder
      paths:
        - path: /core(/|$)(.*)
          pathType: Prefix
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  requests:
    cpu: "200m"       # NOT just 200
    memory: "256Mi"   # NOT just 256
  limits:
    cpu: "500m"
    memory: "512Mi"
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

#This is to setup the liveness and readiness probes more information can be found here: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/

# This section is for setting up autoscaling more information can be fougnd here: https://kubernetes.io/docs/concepts/workloads/autoscaling/
autoscaling:
  enabled: false
  minReplicas: 2
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}
