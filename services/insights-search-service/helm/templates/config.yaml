apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "iss.fullname" . }}-env-configmap
  labels:
    {{- include "iss.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.ROOT}}
    jwt:
      secret: "{{.Values.jwt.secret}}"
    server:
      port:
        8081
    spring:
      application:
        name: insights-search-service
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: reactive
    
    insights-config:
      kustoConfig:
        clusterUri: "{{.Values.insightsConfig.kustoConfig.clusterUri}}"
        database: "{{.Values.insightsConfig.kustoConfig.database}}"
        rawTable: "{{.Values.insightsConfig.kustoConfig.rawTable}}"
        aggregatedTable: "{{.Values.insightsConfig.kustoConfig.aggregatedTable}}"
        useIngestionTime: "{{.Values.insightsConfig.kustoConfig.useIngestionTime}}"
        isManagedIdentity: "{{.Values.insightsConfig.kustoConfig.isManagedIdentity}}"
        azureClientId: "{{.Values.insightsConfig.kustoConfig.azureClientId}}"
        azureClientSecret: "{{.Values.insightsConfig.kustoConfig.azureClientSecret}}"
        azureTenantId: "{{.Values.insightsConfig.kustoConfig.azureTenantId}}"
      riskyServiceConfig:
        riskyServiceFilePath: "{{.Values.insightsConfig.riskyServiceConfig.riskyServiceFilePath}}"
      jwtConfig:
        enableJwt: "{{.Values.insightsConfig.jwtConfig.enableJwt}}"
        isSymmetricKey: "{{.Values.insightsConfig.jwtConfig.isSymmetricKey}}"
      redis-config:
        host: "{{.Values.insightsConfig.redisConfig.host}}"
        port: "{{.Values.insightsConfig.redisConfig.port}}"
        password: "{{.Values.insightsConfig.redisConfig.password}}"
        use-ssl: "{{.Values.insightsConfig.redisConfig.useSsl}}"
        command-timeout-ms: "{{.Values.insightsConfig.redisConfig.commandTimeoutMs}}"
        enableLandingPageCache: "{{.Values.insightsConfig.redisConfig.enableLandingPageCache}}"
        ttlInMinutes: "{{.Values.insightsConfig.redisConfig.ttlInMinutes}}"
        inClusterMode: "{{.Values.insightsConfig.redisConfig.inClusterMode}}"
        clusterHosts: "{{.Values.insightsConfig.redisConfig.clusterHosts}}"
      paginationConfig:
        maxPageSize: "{{.Values.insightsConfig.pagination.maxPageSize}}"
        defaultPageSize: "{{.Values.insightsConfig.pagination.defaultPageSize}}"
        defaultPageNumber: "{{.Values.insightsConfig.pagination.defaultPageNumber}}"
        topResultHardLimit: "{{.Values.insightsConfig.pagination.topResultHardLimit}}"
        enableScatterGather: "{{.Values.insightsConfig.pagination.enableScatterGather}}"


