{{- if .Values.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "iss.fullname" . }}
  labels:
    app: {{ include "iss.name" . }}
  annotations:
    {{- if .Values.ingress.certManager.enabled }}
    cert-manager.io/cluster-issuer: {{ .Values.ingress.certManager.clusterIssuer }}
    {{- end }}
    {{- with .Values.ingress.annotations}}
      {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  ingressClassName: {{ .Values.ingress.ingressClassName }}
  rules:
    - host: {{ .Values.ingress.fqdn }}
      http:
        paths:
          - path: {{ .Values.ingress.path }}
            pathType: Prefix
            backend:
              service:
                name: {{ include "iss.fullname" . }}
                port:
                  number: 8081
  tls:
    - hosts:
        - {{ .Values.ingress.fqdn }}
        {{- if .Values.ingress.proxy_fqdn }}
        - {{ .Values.ingress.proxy_fqdn }}
        {{- end }}
      secretName: {{ .Values.ingress.tlsSecretName }}
{{- end }}
