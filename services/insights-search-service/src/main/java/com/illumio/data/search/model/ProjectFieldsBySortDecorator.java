package com.illumio.data.search.model;

import com.illumio.data.model.ProjectFields;
import com.illumio.data.model.SortByFields;

import java.util.ArrayList;
import java.util.List;

public class ProjectFieldsBySortDecorator extends ProjectFields {

    private final ProjectFields projectFields;
    private final List<SortByFields> sortByFields;

    public ProjectFieldsBySortDecorator(ProjectFields projectFields, List<SortByFields> sortByFields) {
        this.projectFields = projectFields;
        this.sortByFields = sortByFields;
    }

    @Override
    public List<String> getProjectFields() {
        List<String> fields = this.projectFields.getProjectFields();
        if (fields == null || sortByFields == null) { return fields; }

        List<String> deepCopy = new ArrayList<>(fields);
        for (SortByFields sortByField : sortByFields) {
            String adjustedField = sortByField.getAdjustedField();

            if (!deepCopy.contains(adjustedField)) {
                deepCopy.add(adjustedField);
            }
        }

        return deepCopy;
    }
}
