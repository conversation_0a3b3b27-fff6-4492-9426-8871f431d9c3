package com.illumio.data.search.model;

import static com.illumio.data.model.TimeRangeSplitter.DATE_TIME_FORMATTER;

import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.TimeRangeSplitter;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.deepPagination.PaginationType;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.tuple.Pair;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Slf4j
public class SearchRequestCache {

    public static final List<Fields> CACHE_SORTING_FIELDS = List.of(
        Fields.INGESTION_TIME,
        Fields.FLOWS,
        Fields.BYTES
    );

    public static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy_MM_dd_HH");

    /**
     *
     *
     * @param payload: search payload
     * @return true iff
     * 1. time range 24 hours
     * 2. only single sorting field and in {@link SearchRequestCache#CACHE_SORTING_FIELDS}
     * 3. selected fields match {@link Fields#getFieldsForUI}
     * 4. only get top rows, no pagination
     * 5. No sorting
     */
    public static boolean isLandingSearchPayload(SearchRequestPayload payload) {
        if (payload == null) { return false; }

        TimeFrame currentTimeFrame = payload.getCurrentTimeFrame();
        if (currentTimeFrame == null) { return false; }

        // only landing page is cacheable
        if (payload.getPagination().getPaginationType() != PaginationType.FIRST) {
            return false;
        }

        long hourDiff = TimeRangeSplitter.hourDifference(currentTimeFrame.getStartTime(), currentTimeFrame.getEndTime());
        if (TimeRangeSplitter.HOURS_IN_DAY != hourDiff && TimeRangeSplitter.HOURS_IN_WEEK != hourDiff) {
            log.info("Not landing page for time range:{}, payload:{}", currentTimeFrame, payload);
            return false;
        }

        // empty sort fields already translate to ingest_time()
        // only support single sort
        if (payload.getSortByFields() == null || payload.getSortByFields().size() != 1) {
            log.info("Not landing page for sort :{}, payload:{}", payload.getSortByFields(), payload);
            return false;
        }

        SortByFields sortByFields = payload.getSortByFields().get(0);
        Optional<Fields> first = CACHE_SORTING_FIELDS.stream()
            .filter(fields -> fields.getFieldKey().equalsIgnoreCase(sortByFields.getField()))
            .findFirst();

        if (first.isEmpty()) {
            log.info("Not landing page for sort :{}, payload:{}", payload.getSortByFields(), payload);
            return false;
        }

        // empty selection fields are fine, has default columns to pick
        // selection fields are default
        if (payload.getProjectFields() != null) {
            if (payload.getProjectFields().getProjectFields().size() != Fields.getFieldsForUI().size()) {
                log.info("Not landing page for project fields size,  payload:{}", payload);
                return false;
            }

            Set<String> defaultFieldKeys = Fields.getFieldsKeyForUI();
            for(String key: payload.getProjectFields().getProjectFields()) {
                if (!defaultFieldKeys.contains(key)) {
                    log.info("Not landing page for project key:{}, payload:{}", key, payload);
                    return false;
                }
            }
        }

        // check filters
        if (payload.getFilters() != null && !payload.getFilters().isEmpty()) {
            return false;
        }

        // check base filter
        if (payload.getBaseFilter() != null) {
            return false;
        }

        // check pagination
        return (payload.getPagination() != null
            && payload.getPagination().getTopResultHardLimit() > 0);
    }

    /**
     *
     * @param payload
     * @param tenantID
     * @return the landing page Redis key pair
     * left is the hash key , right is the field key
     */
    public static Optional<Pair<String, String>> getLandingPageRedisKey(SearchRequestPayload payload, String tenantID) {
        if(!isLandingSearchPayload(payload)) {
            return Optional.empty();
        }

        String startTimeFormat = formatter.format(
            LocalDateTime.parse(payload.getCurrentTimeFrame().getStartTime(), DATE_TIME_FORMATTER));

        String endTimeFormat = formatter.format(
            LocalDateTime.parse(payload.getCurrentTimeFrame().getEndTime(), DATE_TIME_FORMATTER));

        // Due to 1 day or 1 week for landing page, the redis key need to include both start/end date
        String hashKey = "%s_%s-%s".formatted(tenantID, startTimeFormat, endTimeFormat);

        SortByFields sortByFields = payload.getSortByFields().get(0);
        String fieldKey = "%s_%s".formatted(sortByFields.getField(), sortByFields.getOrder());


        return Optional.of(Pair.of(hashKey, fieldKey));
    }
}
