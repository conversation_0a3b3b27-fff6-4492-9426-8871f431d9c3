package com.illumio.data.search.response.annotator;

import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.generalFilters.IpOverviewFilter;
import com.illumio.data.search.model.SearchRequestPayload;
import com.illumio.data.search.response.SearchResponse;
import com.illumio.data.search.response.SearchResponseAnnotator;
import com.illumio.data.utils.IpType;
import com.illumio.data.utils.IpUtils;

import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * This annotator is used to annotate the search response for IP Overview
 */
@Slf4j
public class IpOverviewAnnotator implements SearchResponseAnnotator {

    public static final List<String> COLUMNS = List.of(
        "ip_address",
        "ip_address_type",
        "ip_company",
        "ip_category",
        "location",
        "severity",
        "city",
        "country",
        "description"
    );

    public static final List<String> COLUMNS_TYPES = List.of(
        "string",
        "string",
        "string",
        "string[]",
        "string",
        "string",
        "string",
        "string",
        "string"
    );

    public static final List<String> COLUMNS_DISPLAY_NAMES = List.of(
        "IP Address",
        "IP Address Type",
        "IP Company",
        "IP Category",
        "Region",
        "IP Severity",
        "City",
        "Country",
        "Description"
    );

    private Object getDataRawValue(SearchResponse searchResponse, String fieldKey){
        if (searchResponse.getData().isEmpty()) return null;

        int index = -1;
        for(int i=0; i<searchResponse.getColumns().size(); i++){
            if (searchResponse.getColumns().get(i).equals(fieldKey)) {
                index = i;
                break;
            }
        }

        if (index < 0) return null;
        return searchResponse.getData().get(0).get(index);
    }

    private boolean isSrcIP(RequestPayload payload, SearchResponse searchResponse){
        if (payload instanceof SearchRequestPayload searchRequestPayload){
            IpOverviewFilter.IpOverviewUnit ipOverviewUnit = searchRequestPayload.getIpOverviewUnit();
            // for general IP search, we need to check the IP address in result to determine if it's src or dest
            if (ipOverviewUnit.isSearchSrcIP() && ipOverviewUnit.isSearchDestIP()){
                Object srcIP = getDataRawValue(searchResponse, Fields.SOURCE_IP.getFieldKey());

                if (srcIP == null) return false;
                return ipOverviewUnit.getOriginalSearchValues().contains(srcIP.toString());
            }

            return ipOverviewUnit.isSearchSrcIP();
        }
        return true;
    }

    private String getIPAddressType(String ipAddress){
        IpType ipType = IpUtils.getIpType(ipAddress);
        if (ipType == IpType.UNKNOWN) {
            log.error("Unknown IP type for {}", ipAddress);
        }
        return ipType.name();
    }

    private String getIP(RequestPayload payload, SearchResponse searchResponse){
        Object result;
        if (isSrcIP(payload, searchResponse)){
            result = getDataRawValue(searchResponse, Fields.SOURCE_IP.getFieldKey());
        }
        else {
            result = getDataRawValue(searchResponse, Fields.DESTINATION_IP.getFieldKey());
        }

        return result == null? "": result.toString();
    }

    private String getIPCompany(RequestPayload payload, SearchResponse searchResponse){
        Object result;
        if (isSrcIP(payload, searchResponse)){
            result = getDataRawValue(searchResponse, Fields.SOURCE_CLOUD_PROVIDER.getFieldKey());
        }
        else {
            result = getDataRawValue(searchResponse, Fields.DESTINATION_CLOUD_PROVIDER.getFieldKey());
        }
        return result == null? "": result.toString();
    }

    private String[] getIPCategory(RequestPayload payload, SearchResponse searchResponse){
        Object wellKnown;
        if (isSrcIP(payload, searchResponse)){
            wellKnown = getDataRawValue(searchResponse, Fields.SOURCE_CLOUD_PROVIDER.getFieldKey());
        }
        else {
            wellKnown = getDataRawValue(searchResponse, Fields.DESTINATION_CLOUD_PROVIDER.getFieldKey());
        }

        String[] result = new String[2];
        String severity = getSeverity(payload, searchResponse);
        try {
            int tmp = Integer.parseInt(severity);
            if (tmp >= 2){
                result[0] = "Malicious";
            } else {
                result[0] = "";
            }
        }
        catch (NumberFormatException e) {
            result[0] = "";
        }

        result[1] = wellKnown != null && wellKnown.toString().equalsIgnoreCase("true") ? "Known Internet" : "Unknown Internet";
        return result;
    }

    private String getGeoLocation(RequestPayload payload, SearchResponse searchResponse){
        Object result;
        if (isSrcIP(payload, searchResponse)){
            result = getDataRawValue(searchResponse, Fields.SOURCE_REGION.getFieldKey());
        }
        else {
            result = getDataRawValue(searchResponse, Fields.DESTINATION_REGION.getFieldKey());
        }
        return result == null? "": result.toString();
    }

    private String getCity(RequestPayload payload, SearchResponse searchResponse){
        Object result;
        if (isSrcIP(payload, searchResponse)){
            result = getDataRawValue(searchResponse, Fields.SOURCE_CITY.getFieldKey());
        }
        else {
            result = getDataRawValue(searchResponse, Fields.DESTINATION_CITY.getFieldKey());
        }
        return result == null? "": result.toString();
    }

    private String getCountry(RequestPayload payload, SearchResponse searchResponse){
        Object result;
        if (isSrcIP(payload, searchResponse)){
            result = getDataRawValue(searchResponse, Fields.SOURCE_COUNTRY.getFieldKey());
        }
        else {
            result = getDataRawValue(searchResponse, Fields.DESTINATION_COUNTRY.getFieldKey());
        }
        return result == null? "": result.toString();
    }

    private String getSeverity(RequestPayload payload, SearchResponse searchResponse){
        Object result;
        if (isSrcIP(payload, searchResponse)){
            result = getDataRawValue(searchResponse, Fields.SOURCE_THREAT_LEVEL.getFieldKey());
        }
        else {
            result =  getDataRawValue(searchResponse, Fields.DESTINATION_THREAT_LEVEL.getFieldKey());
        }

        return result == null? "0": result.toString();
    }

    private List<Object> buildMinimalIPInfor(RequestPayload payload){
        if (!(payload instanceof SearchRequestPayload searchRequestPayload)) return null;
        if (searchRequestPayload.getIpOverviewUnit() == null
            || searchRequestPayload.getIpOverviewUnit().getOriginalSearchValues() == null
            || searchRequestPayload.getIpOverviewUnit().getOriginalSearchValues().isEmpty()) {
            return null;
        }
        
        String ip = searchRequestPayload.getIpOverviewUnit().getOriginalSearchValues().get(0);
        return List.of(
            ip,
            getIPAddressType(ip),
            "",
            new String[]{"", ""},
            "",
            "0",
            "",
            "",
            ""
        );
    }

    @Override
    public SearchResponse annotate(RequestPayload payload, SearchResponse searchResponse) {
        if (payload == null) { return searchResponse; }

        /**
         * need to map the database result to UI
         * 1. only pick the first row of data
         * 2. transfer the data to UI format
         */

        if (searchResponse.getData().size() > 0){
            String ip = getIP(payload, searchResponse);
            List<Object> data = List.of(
                ip,
                getIPAddressType(ip),
                getIPCompany(payload, searchResponse),
                getIPCategory(payload, searchResponse),
                getGeoLocation(payload, searchResponse),
                getSeverity(payload, searchResponse),
                getCity(payload, searchResponse),
                getCountry(payload, searchResponse),
                "" // TBD: description
            );
            searchResponse.setData(List.of(data));
        }
        else {
            List<Object> minimalInfor = buildMinimalIPInfor(payload);
            if (minimalInfor != null){
                searchResponse.setData(List.of(minimalInfor));
            }
        }

        searchResponse.setPagination(null);
        searchResponse.setSortByFields(null);
        searchResponse.setColumns(COLUMNS);
        searchResponse.setColumnTypes(COLUMNS_TYPES);
        searchResponse.setColumnDisplayNames(COLUMNS_DISPLAY_NAMES);
        return searchResponse;
    }
}
