package com.illumio.data.search.model;

import com.illumio.data.model.ProjectFields;
import com.illumio.data.model.constants.Fields;

import java.util.ArrayList;
import java.util.List;

public class ProjectFieldsServiceDecorator extends ProjectFields {

    public static final List<Fields> additional = List.of(Fields.PORT, Fields.PROTOCOL);

    /**
     * Need to remove the Project column "Service" and add "Port/Protocol" columns if NOT specified
     *
     * Reason: "Service" is NOT valid column in DB
     * need to derive the value of "Service" based on "Port/Protocol" which in DB
     */
    private final ProjectFields projectFields;

    public ProjectFieldsServiceDecorator(ProjectFields projectFields) {
        this.projectFields = projectFields;
    }

    @Override
    public List<String> getProjectFields() {
        List<String> fields = this.projectFields.getProjectFields();
        if (fields == null || fields.isEmpty() ||
            !fields.contains(Fields.SERVICE.getFieldKey())) {
            return fields;
        }

        List<String> deepCopy = new ArrayList<>(fields);
        deepCopy.remove(Fields.SERVICE.getFieldKey());

        for (Fields field : additional) {
            if (!fields.contains(field.getFieldKey())){
                deepCopy.add(field.getFieldKey());
            }
        }
        return deepCopy;
    }
}
