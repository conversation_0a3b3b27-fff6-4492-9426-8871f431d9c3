package com.illumio.data.search.response.annotator;

import com.fasterxml.jackson.databind.node.ArrayNode;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.search.model.AggregationRequestPayload;
import com.illumio.data.search.response.SearchResponse;
import com.illumio.data.search.response.SearchResponseAnnotator;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
/**
 * This annotator is used to annotate the search response for aggregation
 */
public class AggregationAnnotator implements SearchResponseAnnotator {

    public static final List<String> COLUMNS = List.of(
        // the name should be dynamically build
        Fields.AGGREGATE_FIELD.getFieldKey(),
        Fields.TIME_SERIES.getTableColumnName().toLowerCase()
    );

    public static final List<String> COLUMNS_TYPES = List.of(
        "string",
        "[string, number][]"
    );

    public static final List<String> COLUMNS_DISPLAY_NAMES = List.of(
        Fields.AGGREGATE_FIELD.getFieldDisplayName(),
        Fields.TIME_SERIES.getFieldDisplayName()
    );

    private List<List<Object>> buildAggregationData(AggregationRequestPayload payload, SearchResponse searchResponse){
        List<List<Object>> result = new ArrayList<>();

        /**
         * the getAggregatedBy order may NOT be in sync with the data order
         * need to loop through all data rows to find the matching one
         */
        for(int i=0; i<payload.getAggregationField().getAggregatedBy().size(); i++){
            String aggBy = payload.getAggregationField().getAggregatedBy().get(i);
            String aggregationName = payload.getSumName(aggBy);
            // find the correct row
            List<Integer> dataIndexList = getRowIndexWithColumnNotNull(searchResponse, aggregationName);
            for (int dataIndex: dataIndexList) {
                List<Object> dataRow = searchResponse.getData().get(dataIndex);

                List<Object> adjustedRow = new ArrayList<>();

                // add group by fields values
                for(String groupByField : payload.getAggregationField().getGroupBy()){
                    int index = searchResponse.getColumns().indexOf(groupByField);
                    if (index < 0 ){
                        log.error("Can not find group by field {} in the result set", groupByField);
                    }
                    else {
                        adjustedRow.add(dataRow.get(index));
                    }
                }

                adjustedRow.add(aggBy); // aggregated by
                adjustedRow.add(buildAggregationRow(searchResponse, dataRow, aggregationName));
                result.add(adjustedRow);
            }
        }

        return result;
    }

    private List<Integer> getRowIndexWithColumnNotNull(SearchResponse searchResponse, String columnName){
        int index = searchResponse.getColumns().indexOf(columnName);
        if (index < 0) {
            log.error("Can not find column {} in the result set", columnName);
            return Collections.emptyList();
        }

        List<Integer> indexList = new ArrayList<>();

        for(int i=0; i<searchResponse.getData().size(); i++){
            if (searchResponse.getData().get(i).get(index) != null) {
                indexList.add(i);
            }
        }

        if (indexList.isEmpty()) {
            log.error("Can not find row with column {} not null", columnName);
        }
        return indexList;
    }

    /**
     * transfer the database output to UI format
     *

     [
         ["2025-02-23T00:00:00.0000000Z", 100],
         ["2025-02-23T00:01:00.0000000Z", 200]
     ]

     * @param searchResponse
     * @param dataRow
     * @param aggregationName
     * @return
     */
    private List<Object> buildAggregationRow(SearchResponse searchResponse, List<Object> dataRow, String aggregationName){
        int index = searchResponse.getColumns().indexOf(aggregationName);
        if (index < 0) {
            log.error("Can not find aggregation column {} in the result set", aggregationName);
            return Collections.emptyList();
        }

        int timeSeriesIndex = searchResponse.getColumns().indexOf(Fields.INGESTION_TIME.getFieldKey());
        if (timeSeriesIndex < 0) {
            log.error("Can not find inject time index in the result set");
            return Collections.emptyList();
        }

        List<Object> result = new ArrayList<>();
        Object aggObj = dataRow.get(index);
        Object timeSeriesObj = dataRow.get(timeSeriesIndex);

        if (aggObj instanceof ArrayNode arrayNode && timeSeriesObj instanceof ArrayNode timeSeriesArrayNode){
            if (arrayNode.size() != timeSeriesArrayNode.size()){
                log.error("Aggregation and time series array size does not match");
                return Collections.emptyList();
            }

            for(int i=0; i<arrayNode.size(); i++){
                List<Object> unit = new ArrayList<>();
                unit.add(timeSeriesArrayNode.get(i).asText());
                unit.add(arrayNode.get(i).longValue());
                result.add(unit);
            }
        } else {
            log.error("Can not build aggregation row for {} and {}", aggObj, timeSeriesObj);
        }

        return result;
    }

    /**
     * Need to transfer the database output to UI format
     *
     "data": [
        ["10.1.1.1", "FLOWS",
            [
                ["2025-02-23T00:00:00.0000000Z", 100],
                ["2025-02-23T00:01:00.0000000Z", 200]
            ],
            [
                 ["2025-02-23T00:00:00.0000000Z", 10000],
                 ["2025-02-23T00:01:00.0000000Z", 20000]
            ]
        ],
        ["10.2.1.1", "BYTES",
            [
                ["2025-02-23T00:00:00.0000000Z", 400],
                ["2025-02-23T00:01:00.0000000Z", 500]
            ],
            [
                 ["2025-02-23T00:00:00.0000000Z", 40000],
                 ["2025-02-23T00:01:00.0000000Z", 50000]
            ]
        ]
     ],
     * @param payload
     * @param searchResponse
     * @return
     */
    @Override
    public SearchResponse annotate(RequestPayload payload, SearchResponse searchResponse) {
        if (payload == null) { return searchResponse; }

        if (!(payload instanceof AggregationRequestPayload)){
            return searchResponse;
        }
        AggregationRequestPayload aggregationPayload = (AggregationRequestPayload) payload;

        // build the data
        if (searchResponse.getData() == null || searchResponse.getData().isEmpty()) {
            return searchResponse;
        }

        searchResponse.setData(buildAggregationData(aggregationPayload, searchResponse));

        // dynamically build the columns based on the payload
        List<String> combinedColumns = new ArrayList<>();
        List<String> combinedColumnTypes = new ArrayList<>();
        List<String> combinedColumnDisplayNames = new ArrayList<>();
        aggregationPayload.getAggregationField().getGroupBy().stream()
            .forEach( field -> {
                combinedColumns.add(field);
                combinedColumnTypes.add(Fields.getFieldByFieldKey(field).getFieldType());
                combinedColumnDisplayNames.add(Fields.getFieldByFieldKey(field).getFieldDisplayName());
            });
        combinedColumns.addAll(COLUMNS);
        combinedColumnTypes.addAll(COLUMNS_TYPES);
        combinedColumnDisplayNames.addAll(COLUMNS_DISPLAY_NAMES);

        searchResponse.setPagination(null);
        searchResponse.setSortByFields(null);
        searchResponse.setColumns(combinedColumns);
        searchResponse.setColumnTypes(combinedColumnTypes);
        searchResponse.setColumnDisplayNames(combinedColumnDisplayNames);

        return searchResponse;
    }
}
