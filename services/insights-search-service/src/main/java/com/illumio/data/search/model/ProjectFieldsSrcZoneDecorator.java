package com.illumio.data.search.model;

import com.illumio.data.model.ProjectFields;
import com.illumio.data.model.constants.Fields;

import java.util.ArrayList;
import java.util.List;

public class ProjectFieldsSrcZoneDecorator extends ProjectFields {

    public static final List<Fields> additional = List.of(Fields.SOURCE_RESOURCE_ID, Fields.SOURCE_CLOUD_PROVIDER);

    /**
     * Need to remove the Project column "SourceZone" and add "SrcResId/SrcCloudProvider" columns if NOT specified
     *
     * Reason: "SourceZone" content is NOT valid in DB
     * need to derive the value of "SourceZone" based on "SrcResId/SrcCloudProvider" which in DB
     */
    private final ProjectFields projectFields;

    public ProjectFieldsSrcZoneDecorator(ProjectFields projectFields) {
        this.projectFields = projectFields;
    }

    @Override
    public List<String> getProjectFields() {
        List<String> fields = this.projectFields.getProjectFields();
        if (fields == null || fields.isEmpty() ||
            !fields.contains(Fields.SOURCE_ZONE.getFieldKey())) {
            return fields;
        }

        List<String> deepCopy = new ArrayList<>(fields);
        deepCopy.remove(Fields.SOURCE_ZONE.getFieldKey());

        for (Fields field : additional) {
            if (!fields.contains(field.getFieldKey())){
                deepCopy.add(field.getFieldKey());
            }
        }
        return deepCopy;
    }
}

