package com.illumio.data.search.model;

import com.illumio.data.model.Pagination;
import com.illumio.data.model.SortByFields;

/**
 * Annotate the sorting logic if in pagination in reverse order
 *
 */
public class OppositeSortDecorator extends SortByFields {

    private final SortByFields sortByFields;

    public OppositeSortDecorator(SortByFields sortByFields, Pagination pagination) {
        if (pagination.inReverseTraverse()) {
            this.sortByFields = sortByFields.getOppositeSortByFields();
        } else {
            this.sortByFields = sortByFields;
        }
    }

    @Override
    public String buildKQL(){
        return this.sortByFields.buildKQL();
    }
}