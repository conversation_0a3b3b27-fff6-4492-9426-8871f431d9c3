package com.illumio.data.search.controller;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeRangeSplitter;
import com.illumio.data.search.model.SearchRequestPayload;
import com.illumio.data.service.JWTService;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.function.TriFunction;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;

import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * This class is used to validate the input for the controller
 */
@Slf4j
public class ControllerInputValidator {

    /**
     * common validation for some the controller input
     * @param tenantId
     * @param payload
     * @param headers
     * @param config
     * @param jwtService
     * @param onValid
     * @return
     */
    public static Mono<ResponseEntity<Object>> validateInput(
        String tenantId,
        SearchRequestPayload payload,
        MultiValueMap<String, String> headers,
        InsightsServiceConfiguration config,
        JWTService jwtService,
        TriFunction<String, SearchRequestPayload, MultiValueMap<String, String>, Mono<ResponseEntity<Object>>> onValid) {

        // Validate mandatory parameters
        if (tenantId == null || tenantId.trim().isEmpty()) {
            log.error("Missing required parameter: tenantID");
            return Mono.just(
                errorResponse(
                    HttpStatus.BAD_REQUEST,
                    "Bad Request",
                    "Missing required parameter: tenantID."));
        }

        // validate time range
        try{
            payload.getCurrentTimeFrame().setStartTime(
                TimeRangeSplitter.roundToHour(payload.getCurrentTimeFrame().getStartTime()));
            payload.getCurrentTimeFrame().setEndTime(TimeRangeSplitter.roundToHour(payload.getCurrentTimeFrame().getEndTime()));
        } catch(Exception e){
            return Mono.just(
                errorResponse(
                    HttpStatus.BAD_REQUEST,
                    "Bad Request",
                    "Invalid time range: " + e.getMessage()));
        }


        if (config.getJwtConfig() != null && config.getJwtConfig().getEnableJwt() != null
            && config.getJwtConfig().getEnableJwt()){
            log.info("JWT enabled with header {}, with symmetric mode: {}", headers, config.getJwtConfig().getIsSymmetricKey());
            Pair<Boolean, String> jwtValidation = jwtService.validateJWT(headers, tenantId, config.getJwtConfig().getIsSymmetricKey());
            log.info("JWT validation result {}", jwtValidation);
            if (!jwtValidation.getLeft()) {
                return Mono.just(
                    errorResponse(
                        HttpStatus.UNAUTHORIZED,
                        "Unauthorized",
                        "Invalid JWT: " + jwtValidation.getRight()));
            }
        } else {
            log.info("JWT disabled");
        }

        Pair<Boolean, String> wellFormatted = isWellFormatted(payload);
        if (!wellFormatted.getLeft()) {
            return Mono.just(
                errorResponse(
                    HttpStatus.BAD_REQUEST,
                    "Bad Request",
                    "Invalid request payload: " + wellFormatted.getRight()));
        }

        return onValid.apply(tenantId, payload, headers);
    }

    public static ResponseEntity<Object> errorResponse(HttpStatus status, String error, String message) {
        return ResponseEntity.status(status).body(Map.of("error", error, "message", message));
    }

    public static Pair<Boolean, String> isWellFormatted(SearchRequestPayload payload) {
        Pair<Boolean, String> wellFormatted;
        if (payload.getBaseFilter() != null) {
            wellFormatted = payload.getBaseFilter().isWellFormatted();
            if (!wellFormatted.getLeft()) {
                return wellFormatted;
            }
        }

        if (payload.getProjectFields() != null) {
            wellFormatted = payload.getProjectFields().isWellFormatted();
            if (!wellFormatted.getLeft()) {
                return wellFormatted;
            }
        }

        if (payload.getSortByFields() != null) {
            for (SortByFields sortByField : payload.getSortByFields()) {
                wellFormatted = sortByField.isWellFormatted();
                if (!wellFormatted.getLeft()) {
                    return wellFormatted;
                }
            }
        }

        return Pair.of(true, null);
    }
}
