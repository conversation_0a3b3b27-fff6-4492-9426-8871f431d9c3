package com.illumio.data.search.response.annotator;

import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.deepPagination.AnchorRows;
import com.illumio.data.model.deepPagination.DpMetaData;
import com.illumio.data.model.deepPagination.DpTargetPageInfor;
import com.illumio.data.search.response.SearchResponse;
import com.illumio.data.search.response.SearchResponseAnnotator;

import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
public class DeepPaginationAnnotator implements SearchResponseAnnotator {

    @Override
    public SearchResponse annotate(RequestPayload payload, SearchResponse searchResponse) {
        if (payload == null) { return searchResponse; }

        this.skipRows(payload, searchResponse);
        this.reverseOrder(payload, searchResponse);
        this.annotateWithCurrentPage(payload, searchResponse);
        return searchResponse;
    }

    private void skipRows(RequestPayload payload, SearchResponse searchResponse){
        DpTargetPageInfor targetPage = payload.getPagination().getTargetPage();
        if (targetPage== null|| targetPage.getRowCountToSkip() == 0) return;

        List<List<Object>> tmpData = searchResponse.getData();
        if (tmpData.size() <= targetPage.getRowCountToSkip()){
            searchResponse.getData().clear();
            return;
        }

        for(int i=0; i<targetPage.getRowCountToSkip(); i++){
            searchResponse.getData().remove(tmpData.get(0));
        }
    }

    private void reverseOrder(RequestPayload payload, SearchResponse searchResponse){
        if (!payload.getPagination().inReverseTraverse()){
            return;
        }

        List<List<Object>> tmpData = searchResponse.getData();
        Collections.reverse(tmpData);
    }

    private int getPrimarySortingIndex(RequestPayload payload,
        SearchResponse response){
        // only pick the first sorting fields as that is the primary one
        SortByFields primary = payload.getSortByFields().get(0);
        List<String> tmpColumns = response.getColumns();

        int index = IntStream.range(0, tmpColumns.size())
            .filter(i -> primary.getAdjustedField().equals(tmpColumns.get(i)))
            .findFirst()
            .orElse(-1);

        if (index < 0) {
            log.error("can not find sorting column {} in the result set", primary.getField());
        }
        return index;
    }

    private Optional<Integer> getTotalPages(RequestPayload payload, SearchResponse response){
        List<String> tmpColumns = response.getColumns();
        List<List<Object>> tmpData = response.getData();

        if (tmpColumns.isEmpty() || tmpData.isEmpty()) return Optional.empty();

        int index = IntStream.range(0, tmpColumns.size())
            .filter(i -> Pagination.TOTAL_COUNT.equals(tmpColumns.get(i)))
            .findFirst()
            .orElse(-1);

        if (index < 0 || tmpData.size() <= index) {
            log.error("can not find total page : {} in the result set", Pagination.TOTAL_COUNT);
            return Optional.empty();
        }

        String totalCount = "N/A";
        try {
            List<Object> firstRow = tmpData.get(0);
            totalCount = firstRow.get(index).toString();
            int totalCountInt = Integer.parseInt(totalCount);
            int rowsPerPage = payload.getPagination().getRowLimit();
            int totalPages = totalCountInt / rowsPerPage;

            if (totalCountInt % rowsPerPage != 0) {totalPages++;}

            return Optional.of(totalPages);
        }
        catch(NumberFormatException e){
            log.error("can not get totalPages from value {}", totalCount);
            return Optional.empty();
        }
    }

    private void annotateWithCurrentPage(RequestPayload payload,
        SearchResponse response){
        if (payload.getPagination() == null || payload.getPagination().getTargetPage() == null) return;

        List<List<Object>> tmpData = response.getData();
        if (tmpData == null || tmpData.isEmpty()) return;

        int primarySortingIndex = getPrimarySortingIndex(payload, response);
        if (primarySortingIndex < 0 || primarySortingIndex >= tmpData.size()) {
            log.error("sorting index out of bound, index {}, data size {}", primarySortingIndex, tmpData.size());
            return;
        }

        Object firstValueObj = tmpData.get(0).get(primarySortingIndex);
        boolean isFirstValueNull = (firstValueObj == null);
        String firstValue = isFirstValueNull ? null : firstValueObj.toString();
        int firstDupCount=0;

        Object lastValueObj = tmpData.get(tmpData.size() - 1).get(primarySortingIndex);
        boolean isLastValueNull = (lastValueObj == null);
        String lastValue = isLastValueNull ? null : lastValueObj.toString();
        int lastDupCount=0;

        for(List<Object> row : tmpData){
            Object valueObj = row.get(primarySortingIndex);
            boolean isValueNull = (valueObj == null);
            String value = isValueNull ? null : valueObj.toString();

            // For null values, we need to check if the value is null
            // For non-null values, we can use equals
            if((isFirstValueNull && isValueNull) || (!isFirstValueNull && !isValueNull && value.equals(firstValue))){
                firstDupCount++;
            }
            if((isLastValueNull && isValueNull) || (!isLastValueNull && !isValueNull && value.equals(lastValue))){
                lastDupCount++;
            }
        }

        AnchorRows current = AnchorRows.builder()
            .pageNumber(payload.getPagination().getTargetPage().getTargetPageNumber())
            .firstValue(firstValue)
            .lastValue(lastValue)
            .firstDupCount(firstDupCount)
            .lastDupCount(lastDupCount)
            .build();

        Pagination pagination = response.getPagination();
        DpMetaData dpMetaData = pagination.getDpMetaData();
        if (dpMetaData == null){
            int tp = response.getPagination().getTotalPages();
            pagination.setDpMetaData(
                DpMetaData.builder()
                    .primarySortBy(payload.getSortByFields().get(0))
                    .anchorRows(List.of(current))
                    .currPage(1)
                    .totalPages(tp)
                    .build()
            );
        } else {
            // remove possible in range meta data
            dpMetaData.setAnchorRows(
                dpMetaData.getAnchorRows().stream()
                    .filter(anchorRows -> Math.abs(anchorRows.getPageNumber()) < Math.abs(current.getPageNumber()))
                    .collect(Collectors.toList())
            );
            dpMetaData.getAnchorRows().add(current);
            dpMetaData.setCurrPage(current.getPageNumber());
        }

        int curPage = pagination.getDpMetaData().getCurrPage();
        if (curPage < 0) {
            // curPage = -1 -> last page
            pagination.setPageNumber(pagination.getDpMetaData().getTotalPages()+1+curPage);
        } else {
            pagination.setPageNumber(pagination.getDpMetaData().getCurrPage());
        }

    }
}