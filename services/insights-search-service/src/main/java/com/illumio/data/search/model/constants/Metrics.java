package com.illumio.data.search.model.constants;

import lombok.Getter;

@Getter
public enum Metrics {
    REDIS_HIT_COUNT("custom_search_redis_hit"),
    REDIS_MISS_COUNT("custom_search_redis_miss"),
    REDIS_GET_ERROR_COUNT("custom_search_redis_get_error"),
    REDIS_SET_ERROR_COUNT("custom_search_redis_set_error"),

    REDIS_QUERY_SUCCESS_LATENCY_METRIC("custom_search_redis_query_success_latency_seconds"),
    REDIS_QUERY_FAIL_LATENCY_METRIC("custom_search_redis_query_fail_latency_seconds"),


    API_REQUEST_COUNT_METRIC("custom_search_api_request_count_total"),
    API_REQUEST_DURATION_METRIC("custom_search_api_request_duration_seconds"),
    API_RESPONSE_SIZE_METRIC("custom_search_api_response_payload_size_bytes"),
    <PERSON><PERSON><PERSON>_QUERY_LATENCY_METRIC("custom_search_kusto_query_latency_seconds");

    private final String value;
    Metrics(String value) {
        this.value = value;
    }

}