package com.illumio.data.search.configuration;

import com.illumio.data.configuration.InsightsServiceConfiguration;

import io.lettuce.core.internal.HostAndPort;
import io.lettuce.core.resource.ClientResources;
import io.lettuce.core.resource.DefaultClientResources;
import io.lettuce.core.resource.DnsResolvers;
import io.lettuce.core.resource.MappingSocketAddressResolver;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.ReactiveRedisConnectionFactory;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.ReactiveRedisOperations;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class RedisConfiguration {

    private final InsightsServiceConfiguration insightsServiceConfig;

    @Bean
    @Primary
    public ReactiveRedisConnectionFactory reactiveRedisConnectionFactory() {
        boolean inClusterMode = insightsServiceConfig.getRedisConfig().getInClusterMode() == null ||
            insightsServiceConfig.getRedisConfig().getInClusterMode();

        boolean useSSL = insightsServiceConfig.getRedisConfig().getUseSsl();

        if (inClusterMode && useSSL){
            /**
             * when using Redis Cluster with SSL in Azure, a common problem is that Redis nodes advertise
             * their private IP addresses, but the SSL certificate is issued for a hostname (e.g.,
             * our-redis-name.redis.cache.windows.net).
             *
             * This leads to a certificate validation error, because the SAN (Subject Alternative Name)
             * in the SSL certificate doesn’t match the IP address returned by the Redis cluster.
             *
             * You need to use a custom MappingSocketAddressResolver to map the internal IPs back
             * to the correct hostname. This tricks the client into using the proper hostnames for SSL validation.
             */
            log.info(
                    "Redis in cluster mode with SSL, with hosts: {}",
                    insightsServiceConfig.getRedisConfig().getClusterHosts());
            RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration(
                Arrays.asList(
                    insightsServiceConfig.getRedisConfig().getClusterHosts().split(",")
                )
            );
            String firstHost = insightsServiceConfig.getRedisConfig().getClusterHosts().split(",")[0].split(":")[0];
            clusterConfig.setPassword(insightsServiceConfig.getRedisConfig().getPassword());
            ClientResources clientResources = this.clientResourcesForSSLInClusterModeOnAzure(firstHost);

            LettuceClientConfiguration clientConfig = LettuceClientConfiguration.builder()
                .clientResources(clientResources)
                .commandTimeout(
                    Duration.ofMillis(insightsServiceConfig.getRedisConfig().getCommandTimeoutMs()))
                .useSsl()
                .build();

            return new LettuceConnectionFactory(clusterConfig, clientConfig);
        }
        // Redis in cluster mode but without SSL
        else if (inClusterMode) {
            log.info("Redis in cluster mode WITHOUT SSL, with hosts: {}", insightsServiceConfig.getRedisConfig().getClusterHosts());
            List<RedisNode> nodes =
                Arrays.stream( insightsServiceConfig.getRedisConfig().getClusterHosts().split(","))
                .map(addr -> {
                    String[] parts = addr.split(":");
                    return new RedisNode(parts[0], Integer.parseInt(parts[1]));
                })
                .toList();

            RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration();
            clusterConfig.setClusterNodes(nodes);

            clusterConfig.setPassword(insightsServiceConfig.getRedisConfig().getPassword());
            LettuceClientConfiguration.LettuceClientConfigurationBuilder builder = getLettuceClientConfigurationBuilder();

            return new LettuceConnectionFactory(clusterConfig, builder.build());
        } else {
            // standalone mode
            log.info("Redis in standalone mode, with host: {}", insightsServiceConfig.getRedisConfig().getHost());

            RedisStandaloneConfiguration redisStandaloneConfiguration =
                    new RedisStandaloneConfiguration();

            redisStandaloneConfiguration.setHostName(
                    insightsServiceConfig.getRedisConfig().getHost());
            redisStandaloneConfiguration.setPort(insightsServiceConfig.getRedisConfig().getPort());
            redisStandaloneConfiguration.setPassword(
                    insightsServiceConfig.getRedisConfig().getPassword());

            LettuceClientConfiguration.LettuceClientConfigurationBuilder builder =
                    getLettuceClientConfigurationBuilder();

            return new LettuceConnectionFactory(redisStandaloneConfiguration, builder.build());
        }
    }

    private ClientResources clientResourcesForSSLInClusterModeOnAzure(String configuredHost)  {
        Function<HostAndPort, HostAndPort> mappingFunction = new Function<HostAndPort, HostAndPort>() {
            @Override
            public HostAndPort apply(HostAndPort hostAndPort) {
                // Redis input hostText is **********, after validate that is a correct IP address
                // return the configuredHost name instead of IP
                log.debug("Redis input hostText is {}", hostAndPort.hostText);
                try {
                    InetAddress[] addresses = DnsResolvers.JVM_DEFAULT.resolve(hostAndPort.getHostText());

                    for(InetAddress address: addresses){
                        if (address.getHostAddress().equals(hostAndPort.hostText) ||
                            address.getHostName().equals(hostAndPort.hostText)
                        ){
                            log.debug("Redis build with host {}, port {}", configuredHost, hostAndPort.getPort());
                            return HostAndPort.of(configuredHost, hostAndPort.getPort());
                        }
                    }
                } catch (UnknownHostException e) {
                    log.error("Redis get unknown host ", e);
                }

                log.error("Redis NOT able to map the IP back to host, return original {}", hostAndPort);
                return hostAndPort;
            }
        };

        MappingSocketAddressResolver resolver =
            MappingSocketAddressResolver.create(
                DnsResolvers.UNRESOLVED, // Don't resolve immediately, let mapping handle it
                mappingFunction);

        return DefaultClientResources.builder().socketAddressResolver(resolver).build();
    }

    private LettuceClientConfiguration.LettuceClientConfigurationBuilder getLettuceClientConfigurationBuilder() {
        DefaultClientResources clientResources = DefaultClientResources.create();
        LettuceClientConfiguration.LettuceClientConfigurationBuilder builder =
                LettuceClientConfiguration.builder();
        builder.clientResources(clientResources);
        builder.commandTimeout(
                Duration.ofMillis(insightsServiceConfig.getRedisConfig().getCommandTimeoutMs()));
        if (insightsServiceConfig.getRedisConfig().getUseSsl()) {
            builder.useSsl();
        }
        return builder;
    }

    @Bean
    public ReactiveRedisOperations<String, String> reactiveRedisOperations(ReactiveRedisConnectionFactory factory) {
        RedisSerializationContext<String, String> serializationContext = RedisSerializationContext
            .<String, String>newSerializationContext()
            .key(new StringRedisSerializer())
            .value(new StringRedisSerializer())
            .hashKey(new StringRedisSerializer())
            .hashValue(new StringRedisSerializer())
            .build();
        return new ReactiveRedisTemplate<>(factory, serializationContext);
    }
}
