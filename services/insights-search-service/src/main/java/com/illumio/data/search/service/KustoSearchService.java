package com.illumio.data.search.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.search.KustoSearchQueryClient;
import com.illumio.data.search.model.SearchRequestCache;
import com.illumio.data.search.model.SearchRequestPayload;
import com.illumio.data.search.response.SearchResponse;
import com.illumio.data.service.RiskyServiceInfo;

import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import reactor.core.publisher.Mono;

import java.time.temporal.ChronoUnit;
import java.util.Optional;

@Service
@Slf4j
@AllArgsConstructor
@Import({RiskyServiceInfo.class})
public class KustoSearchService {

    private final KustoSearchQueryClient kustoQueryClient;

    private final RiskyServiceInfo riskyServiceInfo;

    private final CacheService cacheService;

    private final InsightsServiceConfiguration config;

    public Mono<ResponseEntity<Object>> getSearchResult(
            SearchRequestPayload payload, String tenantId) {
        return this.relayWithCache(payload, tenantId)
                .map(ResponseEntity::ok)
                .map(
                        response ->
                                ResponseEntity.status(response.getStatusCode()) // Preserve original
                                        // status
                                        .headers(response.getHeaders()) // Preserve headers
                                        .body((Object) response.getBody()) // Cast Subclass to
                        // Object
                        )
                .onErrorResume(
                        error -> {
                            log.error(
                                    "Search service error processing request: {}",
                                    error.getMessage(),
                                    error);
                            return Mono.just(
                                    ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                                            .body(error.getMessage()));
                        });
    }

    @SneakyThrows
    public SearchResponse parseFromJson(String json) {
        return new ObjectMapper().readValue(json, SearchResponse.class);
    }

    @SneakyThrows
    public String parseToJson(SearchResponse searchResponse) {
        return new ObjectMapper().writeValueAsString(searchResponse);
    }

    private Mono<SearchResponse> relayWithCache(SearchRequestPayload payload, String tenantId){

        if (!config.getRedisConfig().getEnableLandingPageCache()){
            log.info("Landing page cache disabled, directly get result from Kusto Database");
            return callKusto(payload, tenantId);
        }

        Optional<Pair<String, String>> redisKeys = SearchRequestCache.getLandingPageRedisKey(payload,tenantId);
        // not landing page, no cache
        if(redisKeys.isEmpty()) {
            return callKusto(payload, tenantId);
        }

        return cacheService.getHashFieldValue(redisKeys.get().getLeft(), redisKeys.get().getRight(), tenantId)
            .flatMap(json -> {
                log.info("Search service cache hit key:{}", redisKeys.get());
                return Mono.fromCallable(() -> parseFromJson(json));
            })
            .onErrorResume( ex -> {
                log.error("Content stored in key:{}, can not parse as JSON to SearchResponse", redisKeys.get(), ex);
                return callKustoWithSetCache(payload, redisKeys.get(), tenantId);
            })
            // cache miss
            .switchIfEmpty(
                /**
                 * need to use defer() otherwise, the KQL will always get called
                 */
                Mono.defer(() -> callKustoWithSetCache(payload, redisKeys.get(), tenantId))
            );
    }

    private Mono<SearchResponse> callKustoWithSetCache(SearchRequestPayload payload, Pair<String, String> redisKeys, String tenantID) {
        log.info("Search service set redis key: {} after get payload:{} from Kusto", redisKeys, payload);
        return this.callKusto(payload, tenantID).flatMap(
            response ->
                cacheService.setHashFieldValue(tenantID, redisKeys.getLeft(), redisKeys.getRight(), parseToJson(response), config.getRedisConfig().getTtlInMinutes())
                    .thenReturn(response)
        );
    }

    private Mono<SearchResponse> callKusto(SearchRequestPayload payload, String tenantId) {
        // no pagination, scatter and gather
        if (payload.getPagination() != null
                && payload.getPagination().getEnableScatterGather()) {
            return this.kustoQueryClient.getSearchResults(
                    payload,
                    riskyServiceInfo,
                    ChronoUnit.DAYS, tenantId);

        }
        // with pagination
        else {
            return this.kustoQueryClient.getSearchResults(
                    payload, new SearchResponse(), riskyServiceInfo, tenantId);
        }
    }
}
