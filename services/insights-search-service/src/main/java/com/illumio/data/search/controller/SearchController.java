package com.illumio.data.search.controller;

import static com.illumio.data.search.controller.ControllerInputValidator.errorResponse;

import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.ProjectFields;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeRangeSplitter;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.deepPagination.PaginationType;
import com.illumio.data.model.generalFilters.IpOverviewFilter;
import com.illumio.data.model.generalFilters.ServiceFilter;
import com.illumio.data.search.KustoSearchQueryClient;
import com.illumio.data.search.model.AggregationRequestPayload;
import com.illumio.data.search.model.SearchRequestPayload;
import com.illumio.data.search.model.constants.MetricStatusLabel;
import com.illumio.data.search.response.SearchResponse;
import com.illumio.data.search.response.annotator.AggregationAnnotator;
import com.illumio.data.search.response.annotator.IpOverviewAnnotator;
import com.illumio.data.search.service.CacheService;
import com.illumio.data.search.service.KustoSearchService;
import com.illumio.data.search.service.MetricRecordService;
import com.illumio.data.service.JWTService;
import com.illumio.data.service.RequestPayloadValidation;
import com.illumio.data.service.RiskyServiceInfo;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.context.annotation.Import;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import reactor.core.publisher.Mono;

import java.time.format.DateTimeParseException;
import java.util.List;

@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/api/v1/search/tenant/{tenantId}")
@Import({RequestPayloadValidation.class, JWTService.class})
public class SearchController {
    private final KustoSearchService kustoSearchService;
    private final KustoSearchQueryClient kustoQueryClient;
    private final InsightsServiceConfiguration config;
    private final CacheService cacheService;
    private final MeterRegistry meterRegistry;
    private final MetricRecordService metricRecordService;

    private final RiskyServiceInfo riskyServiceInfo;

    private final JWTService jwtService;

    private void preprocessSearchRequestPayload(SearchRequestPayload payload, String tenantId) {
        // based on config, update the payload
        if (config.getKustoConfig().getRawTable() != null) {
            payload.setTableName(config.getKustoConfig().getRawTable());
        }
        if (config.getKustoConfig().getUseIngestionTime() != null) {
            payload.setUseIngestionTime(config.getKustoConfig().getUseIngestionTime());
        }

        payload.setTenantId(tenantId);

        // default to ingest time desc
        if (payload.getSortByFields() == null || payload.getSortByFields().isEmpty()) {
            payload.setSortByFields(
                    List.of(
                            SortByFields.builder()
                                    .field(Fields.INGESTION_TIME.getFieldKey())
                                    .order("desc")
                                    .build()));
        }

        // default project columns
        if (payload.getProjectFields() == null || payload.getProjectFields().getProjectFields() == null
            || payload.getProjectFields().getProjectFields().isEmpty()) {
            List<String> defaultProjectFields = Fields.getFieldsForUI()
                .stream()
                .map(Fields::getFieldKey)
                .toList();

            payload.setProjectFields(
                ProjectFields.builder()
                    .projectFields(defaultProjectFields)
                    .build()
            );
        }

        // round start/end time to exact hour
        payload.getCurrentTimeFrame().setStartTime(TimeRangeSplitter.roundToHour(payload.getCurrentTimeFrame().getStartTime()));
        payload.getCurrentTimeFrame().setEndTime(TimeRangeSplitter.roundToHour(payload.getCurrentTimeFrame().getEndTime()));

        // Pagination default values
        if (payload.getPagination() == null) {
            payload.setPagination(Pagination.builder().build());
        }
        if (payload.getPagination().getRowLimit() < 1) {
            payload.getPagination().setRowLimit(config.getPaginationConfig().getDefaultPageSize());
        }
        if (payload.getPagination().getRowLimit() > config.getPaginationConfig().getMaxPageSize()) {
            payload.getPagination().setRowLimit(config.getPaginationConfig().getMaxPageSize());
        }
        if (payload.getPagination().getPageNumber() < 1) {
            payload.getPagination()
                    .setPageNumber(config.getPaginationConfig().getDefaultPageNumber());
        }

        // deprecated the topResultHardLimit, use rowLimit instead
        payload.getPagination().setTopResultHardLimit(payload.getPagination().getRowLimit());

        if (payload.getPagination().getTopResultHardLimit() == null ||
            payload.getPagination().getTopResultHardLimit() > config.getPaginationConfig().getTopResultHardLimit()) {
            payload.getPagination().setTopResultHardLimit(config.getPaginationConfig().getTopResultHardLimit());
        }

        if (payload.getPagination().getEnableScatterGather() == null){
            payload.getPagination().setEnableScatterGather(config.getPaginationConfig().getEnableScatterGather());
        }

        // default to first page
        if (payload.getPagination().getPaginationType() == null) {
            this.setDefaultPaginationType(payload);
        }

        if (payload.getPagination().getDpMetaData() != null && !payload.getPagination().getDpMetaData().sanityCheck()){
            log.error("Deep pagination is NOT well formatted {}", payload.getPagination().getDpMetaData());
            this.setDefaultPaginationType(payload);
        }

        payload.getPagination().buildTargetPage();
    }

    private void setDefaultPaginationType(SearchRequestPayload payload) {
        payload.getPagination().setPaginationType(PaginationType.FIRST);
        payload.getPagination().setDpMetaData(null);
    }

    public Mono<ResponseEntity<Object>> getWidgetDataActual(String tenantId,
        SearchRequestPayload payload,
        MultiValueMap<String, String> headers){
        Timer.Sample timer = Timer.start(meterRegistry);

        this.preprocessSearchRequestPayload(payload, tenantId);

        return kustoSearchService
            .getSearchResult(payload, tenantId)
            .doOnSubscribe(subscription -> log.info("Request payload: {}", payload))
            .doOnSuccess(
                result -> {
                    log.info("Successfully processed request for {} ", payload);
                    // recording metrics for successful response
                    metricRecordService.recordTimeAndCountMetrics(
                        meterRegistry,
                        timer,
                        MetricStatusLabel.SUCCESS.getValue(),
                        "Time taken to process request per tenantId",
                        tenantId);
                    metricRecordService.recordSizeMetrics(
                        meterRegistry, result, tenantId, "success");
                })
            .onErrorResume(
                error -> {
                    log.error("Error processing request: ", error);
                    // recording metrics for error response
                    metricRecordService.recordTimeAndCountMetrics(
                        meterRegistry, timer,
                        MetricStatusLabel.ERROR.getValue(),
                        "Error request duration per tenantId",
                        tenantId);
                    if (error instanceof IllegalArgumentException
                        || error instanceof DateTimeParseException) {
                        ResponseEntity<Object> badRequestResponse =
                            errorResponse(
                                HttpStatus.BAD_REQUEST,
                                "Bad Request",
                                error.getMessage());
                        metricRecordService.recordSizeMetrics(
                            meterRegistry,
                            badRequestResponse,
                            tenantId,
                            MetricStatusLabel.ERROR.getValue());
                        return Mono.just(badRequestResponse);
                    }
                    ResponseEntity<Object> serverErrorResponse =
                        errorResponse(
                            HttpStatus.INTERNAL_SERVER_ERROR,
                            "Internal Server Error",
                            "Something went wrong: " + error.getMessage());
                    metricRecordService.recordSizeMetrics(
                        meterRegistry,
                        serverErrorResponse,
                        tenantId,
                        MetricStatusLabel.ERROR.getValue());
                    return Mono.just(serverErrorResponse);
                });
    }


    @PostMapping("/search")
    public Mono<ResponseEntity<Object>> getWidgetData(
            @PathVariable String tenantId, @RequestBody SearchRequestPayload payload,
            @RequestHeader MultiValueMap<String, String> headers) {

        log.debug("Processing search request for tenant {}", tenantId);

        // make service info available,
        ServiceFilter.riskyServiceInfo = riskyServiceInfo;

        return ControllerInputValidator.validateInput(
            tenantId,
            payload,
            headers,
            config,
            jwtService,
            this::getWidgetDataActual
        );

    }


    @GetMapping("/getAvailableFields")
    public Mono<ResponseEntity<Object>> getAvailableFields() {
        List<Fields> availableFields = Fields.getFieldsForUI();
        return Mono.just(ResponseEntity.ok(availableFields));
    }

    @PostMapping("/testJwt")
    public Mono<ResponseEntity<Object>> testJwt(
        @PathVariable String tenantId, @RequestBody SearchRequestPayload payload,
        @RequestHeader MultiValueMap<String, String> headers) {
        log.info("JWT test with header {}, with symmetric mode {}", headers, config.getJwtConfig().getIsSymmetricKey());

        Pair<Boolean, String> jwtValidation = jwtService.validateJWT(headers, tenantId, config.getJwtConfig().getIsSymmetricKey());

        log.info("JWT test validation result {}", jwtValidation);
        if (!jwtValidation.getLeft()) {
            return Mono.just(
                errorResponse(
                    HttpStatus.UNAUTHORIZED,
                    "Unauthorized",
                    "Invalid JWT: " + jwtValidation.getRight()));
        }

        return Mono.just(ResponseEntity.ok("Valid JWT"));
    }

    @GetMapping("/redis/{hashKey}/{fieldKey}/{fieldValue}")
    public Mono<ResponseEntity<Object>> redis( @PathVariable String hashKey, @PathVariable String fieldKey, @PathVariable String fieldValue) {
        String a = cacheService.getHashFieldValue("testTanant", "af84345d-0f7a-43ad-b19f-dcce53af2b9d_2025_03_27_08", "bytes_desc").block();
        return cacheService.setHashFieldValue("testTanant", hashKey, fieldKey, fieldValue)
            .map(ResponseEntity::ok)
            ;
    }

    private Mono<ResponseEntity<Object>> getIpOverviewActual(String tenantId,
        SearchRequestPayload payload, MultiValueMap<String, String> headers){

        this.preprocessSearchRequestPayload(payload, tenantId);

        /**
         * if the filter is source_ip or destination_ip, use it directly
         * else if filter is "ip" + traffic_direction
         *   -> with INBOUND, use source_ip
         *   -> with OUTBOUND, use destination_ip
         * else if filter is "ip"
         *   -> use both source_ip and destination_ip, KQL with or relationship
         */

        IpOverviewFilter.IpOverviewUnit ipOverviewUnit = IpOverviewFilter.mapFromLegacyFilter(payload.getFilters());
        if (ipOverviewUnit.getError() != null) {
            return Mono.just(
                errorResponse(
                    HttpStatus.BAD_REQUEST,
                    "Bad Request",
                    "Invalid request payload: " + ipOverviewUnit.getError()));
        }

        // update the filters based on the preprocess
        payload.setFilters(null);
        payload.setBaseFilter(ipOverviewUnit.getFilter().get());
        payload.setIpOverviewUnit(ipOverviewUnit);
        payload.setProjectFields(ProjectFields.builder().projectFields(ipOverviewUnit.getProjectFields()).build());

        return this.kustoQueryClient.getSearchResults(payload, new SearchResponse(List.of(new IpOverviewAnnotator())), this.riskyServiceInfo, tenantId)
            .map(ResponseEntity::ok)
            .map(
                response ->
                    ResponseEntity.status(response.getStatusCode()) // Preserve original
                        // status
                        .headers(response.getHeaders()) // Preserve headers
                        .body((Object) response.getBody()) // Cast Subclass to
                // Object
            )
            .onErrorResume(
                error -> {
                    log.error("Error processing request: ", error);

                    ResponseEntity<Object> serverErrorResponse =
                        errorResponse(
                            HttpStatus.INTERNAL_SERVER_ERROR,
                            "Internal Server Error",
                            "Something went wrong: " + error.getMessage());
                    metricRecordService.recordSizeMetrics(
                        meterRegistry,
                        serverErrorResponse,
                        tenantId,
                        MetricStatusLabel.ERROR.getValue());
                    return Mono.just(serverErrorResponse);
                });
    }

    @PostMapping("/insights/ipOverview")
    public Mono<ResponseEntity<Object>> getIpOverview(
        @PathVariable String tenantId, @RequestBody SearchRequestPayload payload,
        @RequestHeader MultiValueMap<String, String> headers) {

        // make service info available,
        ServiceFilter.riskyServiceInfo = riskyServiceInfo;

        return ControllerInputValidator.validateInput(
            tenantId,
            payload,
            headers,
            config,
            jwtService,
            this::getIpOverviewActual
        );
    }

    @PostMapping("/aggregate/timeseries")
    public Mono<ResponseEntity<Object>> getAggregatedData(
        @PathVariable String tenantId, @RequestBody AggregationRequestPayload payload,
        @RequestHeader MultiValueMap<String, String> headers) {

        // make service info available,
        ServiceFilter.riskyServiceInfo = riskyServiceInfo;

        return ControllerInputValidator.validateInput(
            tenantId,
            payload,
            headers,
            config,
            jwtService,
            this::getAggregatedDataActual
        );
    }

    private Mono<ResponseEntity<Object>> getAggregatedDataActual(String tenantId,
        SearchRequestPayload payload, MultiValueMap<String, String> headers){

        this.preprocessSearchRequestPayload(payload, tenantId);

        if (!(payload instanceof AggregationRequestPayload)) {
            return Mono.just(
                errorResponse(
                    HttpStatus.BAD_REQUEST,
                    "Bad Request",
                    "Invalid request payload: " + "Not an AggregationRequestPayload"));
        }

        AggregationRequestPayload aggregationPayload = (AggregationRequestPayload) payload;
        Pair<Boolean, String> wellFormatted = aggregationPayload.getAggregationField()
            .isWellFormatted();
        if (!wellFormatted.getLeft()) {
            return Mono.just(
                errorResponse(
                    HttpStatus.BAD_REQUEST,
                    "Bad Request",
                    "Invalid request payload: " + wellFormatted.getRight()));
        }

        return this.kustoQueryClient.getSearchResults(aggregationPayload,
                new SearchResponse(List.of(new AggregationAnnotator())),
                this.riskyServiceInfo, tenantId)
            .map(ResponseEntity::ok)
            .map(
                response ->
                    ResponseEntity.status(response.getStatusCode()) // Preserve original
                        // status
                        .headers(response.getHeaders()) // Preserve headers
                        .body((Object) response.getBody()) // Cast Subclass to
                // Object
            )
            .onErrorResume(
                error -> {
                    log.error("Error processing request: ", error);

                    ResponseEntity<Object> serverErrorResponse =
                        errorResponse(
                            HttpStatus.INTERNAL_SERVER_ERROR,
                            "Internal Server Error",
                            "Something went wrong: " + error.getMessage());
                    metricRecordService.recordSizeMetrics(
                        meterRegistry,
                        serverErrorResponse,
                        tenantId,
                        MetricStatusLabel.ERROR.getValue());
                    return Mono.just(serverErrorResponse);
                });
    }
}
