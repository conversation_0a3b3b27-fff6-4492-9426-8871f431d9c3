package com.illumio.data.search.model;

import com.illumio.data.model.ProjectFields;
import com.illumio.data.model.constants.Fields;

import java.util.ArrayList;
import java.util.List;

public class ProjectFieldsDestZoneDecorator extends ProjectFields {
    public static final List<Fields> additional = List.of(Fields.DESTINATION_RESOURCE_ID, Fields.DESTINATION_CLOUD_PROVIDER);

    /**
     * Need to remove the Project column "DestinationZone" and add "DestResId/DestCloudProvider" columns if NOT specified
     *
     * Reason: "DestinationZone" content is NOT valid in DB
     * need to derive the value of "DestinationZone" based on "DestResId/DestCloudProvider" which in DB
     */
    private final ProjectFields projectFields;

    public ProjectFieldsDestZoneDecorator(ProjectFields projectFields) {
        this.projectFields = projectFields;
    }

    @Override
    public List<String> getProjectFields() {
        List<String> fields = this.projectFields.getProjectFields();
        if (fields == null || fields.isEmpty() ||
            !fields.contains(Fields.DESTINATION_ZONE.getFieldKey())) {
            return fields;
        }

        List<String> deepCopy = new ArrayList<>(fields);
        deepCopy.remove(Fields.DESTINATION_ZONE.getFieldKey());

        for (Fields field : additional) {
            if (!fields.contains(field.getFieldKey())){
                deepCopy.add(field.getFieldKey());
            }
        }
        return deepCopy;
    }
}

