package com.illumio.data.search.response;

import static com.illumio.data.model.constants.Fields.getFieldByTableColumnName;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.SrcDestZoneFilter;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.constants.Fields;
import com.illumio.data.model.constants.KustoInternal;
import com.illumio.data.model.constants.SeverityLevel;
import com.illumio.data.search.model.SearchRequestPayload;
import com.illumio.data.search.response.annotator.DeepPaginationAnnotator;
import com.illumio.data.service.RiskyServiceInfo;
import com.microsoft.azure.kusto.data.KustoResultColumn;
import com.microsoft.azure.kusto.data.KustoResultSetTable;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Data
@Slf4j
public class SearchResponse implements ResponseBuilder<SearchResponse> {
    TimeFrame currentTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @JsonIgnore
    private final List<SearchResponseAnnotator> annotators;

    public SearchResponse() {
        this(List.of(new DeepPaginationAnnotator()));
    }
    public SearchResponse(List<SearchResponseAnnotator> annotators) {
        this.annotators = annotators;
    }

    private String deriveZone(String resID, String cloudProvider) {
        if (resID != null && !resID.isEmpty()) {
            return "%s%s".formatted(SrcDestZoneFilter.INTERNAL_ZONE_PREFIX, cloudProvider);
        }

        if (cloudProvider != null) {
            return SrcDestZoneFilter.CLOUD_PROVIDER_MAP.getOrDefault(
                    cloudProvider.toLowerCase(), SrcDestZoneFilter.UNKNOWN_ZONE);
        }

        return SrcDestZoneFilter.UNKNOWN_ZONE;
    }

    private void annotateWithServiceName(
            RequestPayload payload,
            RiskyServiceInfo riskyServiceInfo,
            List<String> tmpColumns,
            List<String> tmpColumnTypes,
            List<String> tmpColumnDisplayNames,
            List<List<Object>> tmpData) {

        // always annotated with service information

        if (!(payload instanceof SearchRequestPayload)) return;

        // meta data for service
        tmpColumns.add(Fields.SERVICE.getFieldKey());
        tmpColumnTypes.add(Fields.SERVICE.getFieldType());
        tmpColumnDisplayNames.add(Fields.SERVICE.getFieldDisplayName());

        // meta data for service Severity
        tmpColumns.add(Fields.SEVERITY.getFieldKey());
        tmpColumnTypes.add(Fields.SEVERITY.getFieldType());
        tmpColumnDisplayNames.add(Fields.SEVERITY.getFieldDisplayName());

        int portIndex = -1;
        int protocolIndex = -1;

        for (int i = 0; i < tmpColumns.size(); i++) {
            if (tmpColumns.get(i).equals(Fields.PORT.getFieldKey())) {
                portIndex = i;
            }
            if (tmpColumns.get(i).equals(Fields.PROTOCOL.getFieldKey())) {
                protocolIndex = i;
            }
        }

        if (portIndex != -1 && protocolIndex != -1) {
            for (List<Object> row : tmpData) {
                Object portObj = row.get(portIndex);
                Object protocolObj = row.get(protocolIndex);
                if (portObj == null || protocolObj == null) {
                    row.add("Unknown"); // service name
                    row.add(-1); // Severity
                } else {
                    int port = Integer.parseInt(portObj.toString());
                    String protocol =
                            protocolObj
                                    .toString()
                                    .toUpperCase(); // the resource file protocol is in Uppercase

                    RiskyServiceInfo.ServiceInfo serviceInfo =
                            riskyServiceInfo
                                    .getPortProtoToServiceInfoMap()
                                    .get(new RiskyServiceInfo.PortProtocolPair(port, protocol));

                    if (serviceInfo != null) {
                        row.add(serviceInfo.getService());
                        row.add(SeverityLevel.fromString(serviceInfo.getSeverity()));
                    } else {
                        row.add("Unknown"); // service can not be decided
                        row.add(-1); // Severity
                    }
                }
            }

        } else {
            tmpData.forEach(row -> {
                row.add("Unknown"); // service can not be decided
                row.add(-1); // Severity
            });
        }
    }

    private void annotateWithZone(SearchRequestPayload searchRequestPayload,
        List<String> tmpColumns,
        List<String> tmpColumnTypes,
        List<String> tmpColumnDisplayNames,
        List<List<Object>> tmpData,
        Fields zone,
        Fields resID,
        Fields cloudProvider) {
        /**
         * Original search payload do NOT request zone information
         */
        if (searchRequestPayload.getProjectFields() != null
            && searchRequestPayload.getProjectFields().getProjectFields() != null &&
            !searchRequestPayload.getProjectFields().getProjectFields().contains(zone.getFieldKey())){
            return;
        }

        // meta data add two more fields
        tmpColumns.add(zone.getFieldKey());
        tmpColumnTypes.add(zone.getFieldType());
        tmpColumnDisplayNames.add(zone.getFieldDisplayName());
        

        int resIDIndex = -1;
        int cloudProviderIndex = -1;
        for (int i = 0; i < tmpColumns.size(); i++) {
            if (tmpColumns.get(i).equals(resID.getFieldKey())) {
                resIDIndex = i;
            }
            if (tmpColumns.get(i).equals(cloudProvider.getFieldKey())) {
                cloudProviderIndex = i;
            }
        }
        
        // derive src zone based on database output
        for (List<Object> tmpRow : tmpData) {
            Object resIDObj = resIDIndex == -1 ? null : tmpRow.get(resIDIndex);
            Object cloudProviderObj =
                cloudProviderIndex == -1
                    ? null
                    : tmpRow.get(cloudProviderIndex);

            tmpRow.add(deriveZone(resIDObj == null? "": resIDObj.toString(),
                cloudProviderObj == null ? "": cloudProviderObj.toString()));
        }

    }

    /**
     * derived the src/dest zone information from the Database result set
     *
     * @param payload
     * @param tmpColumns
     * @param tmpColumnTypes
     * @param tmpColumnDisplayNames
     * @param tmpData
     */
    private void annotateWithSrcDestZone(
            RequestPayload payload,
            List<String> tmpColumns,
            List<String> tmpColumnTypes,
            List<String> tmpColumnDisplayNames,
            List<List<Object>> tmpData) {

        if (!(payload instanceof SearchRequestPayload searchRequestPayload)) return;

        this.annotateWithZone(searchRequestPayload, tmpColumns,
            tmpColumnTypes, tmpColumnDisplayNames, tmpData,
            Fields.SOURCE_ZONE,
            Fields.SOURCE_RESOURCE_ID,
            Fields.SOURCE_CLOUD_PROVIDER
        );

        this.annotateWithZone(searchRequestPayload, tmpColumns,
            tmpColumnTypes, tmpColumnDisplayNames, tmpData,
            Fields.DESTINATION_ZONE,
            Fields.DESTINATION_RESOURCE_ID,
            Fields.DESTINATION_CLOUD_PROVIDER
        );
    }

    @Override
    public SearchResponse buildResponse(
            KustoResultSetTable resultSetTable,
            RequestPayload payload,
            RiskyServiceInfo riskyServiceInfo) {
        SearchResponse response = new SearchResponse();

        // Set Request Specific Metadata
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        List<String> tmpColumns = new ArrayList<>();
        List<String> tmpColumnTypes = new ArrayList<>();
        List<String> tmpColumnDisplayNames = new ArrayList<>();
        List<List<Object>> tmpData = new ArrayList<>();

        KustoResultColumn[] columns = resultSetTable.getColumns();

        int totalRowIndex = -1;
        for (int i = 0; i < columns.length; i++) {
            KustoResultColumn column = columns[i];
            String columnName = column.getColumnName();
            String columnType = column.getColumnType();

            // do NOT show totalRow in the output
            if (columnName.equals(Pagination.TOTAL_COUNT)) {
                totalRowIndex = i;
                continue;
            }

            // the return name is different than the project name
            if (KustoInternal.RETURN_TO_PROJECT_COLUMN.containsKey(columnName)) {
                columnName = KustoInternal.RETURN_TO_PROJECT_COLUMN.get(columnName);
            }

            Fields fields = getFieldByTableColumnName(columnName);
            if (fields == null) {
                tmpColumns.add(columnName);
                tmpColumnTypes.add(columnType);
                tmpColumnDisplayNames.add(columnName);
            } else {
                tmpColumns.add(fields.getFieldKey());
                tmpColumnTypes.add(fields.getFieldType());
                tmpColumnDisplayNames.add(fields.getFieldDisplayName());
            }
        }

        while (resultSetTable.next()) {
            List<Object> dataRow = new ArrayList<>();
            for (int i = 0; i < columns.length; i++) {
                // do NOT show totalRow in the output
                if (i == totalRowIndex) {
                    continue;
                }

                dataRow.add(resultSetTable.getObject(i));
            }
            tmpData.add(dataRow);

            // Set total pages = (int) ceil(total_rows / rows per page). Set this only once in the
            // response.
            if (totalRowIndex >= 0 && resultSetTable.isLast()) {
                response.pagination.setTotalPages(
                        (int)Math.ceil( (double) resultSetTable.getInt(totalRowIndex)
                                                / response.pagination.getRowLimit()));

                response.pagination.setTotalRows(resultSetTable.getInt(totalRowIndex));
            }
        }

        this.annotateWithSrcDestZone(
                payload, tmpColumns, tmpColumnTypes, tmpColumnDisplayNames, tmpData);
        this.annotateWithServiceName(
                payload, riskyServiceInfo, tmpColumns, tmpColumnTypes, tmpColumnDisplayNames, tmpData);

        response.setData(tmpData);
        response.setColumns(tmpColumns);
        response.setColumnTypes(tmpColumnTypes);
        response.setColumnDisplayNames(tmpColumnDisplayNames);

        // for none pagination
        response.pagination.setTopResultRows(response.data.size());

        for(SearchResponseAnnotator annotator: this.annotators){
            response = annotator.annotate(payload, response);
        }

        return response;
    }
}
