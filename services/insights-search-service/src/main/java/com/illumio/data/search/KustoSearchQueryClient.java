package com.illumio.data.search;

import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.configuration.InsightsServiceConfiguration;
import com.illumio.data.configuration.KustoClientConfiguration;
import com.illumio.data.search.model.SearchRequestPayload;
import com.illumio.data.search.model.constants.MetricStatusLabel;
import com.illumio.data.search.response.SearchResponse;
import com.illumio.data.search.response.SearchResponseCombiner;
import com.illumio.data.search.service.MetricRecordService;
import com.illumio.data.service.RiskyServiceInfo;
import com.microsoft.azure.kusto.data.Client;
import com.microsoft.azure.kusto.data.KustoOperationResult;
import com.microsoft.azure.kusto.data.KustoResultSetTable;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Component;

import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
@Slf4j
@Import({
    InsightsServiceConfiguration.class,
    KustoClientConfiguration.class,
    RiskyServiceInfo.class
})
public class KustoSearchQueryClient {
    private static final Scheduler KUSTO_SCHEDULER =
            Schedulers.newBoundedElastic(10, 100, "kusto-scheduler", 60, true);

    private final Client kustoClient;
    private final String database;
    private final MeterRegistry meterRegistry;
    private final MetricRecordService metricRecordService;

    public KustoSearchQueryClient(
            Client kustoClient, InsightsServiceConfiguration inventoryConfig, MeterRegistry meterRegistry, MetricRecordService metricRecordService) {
        this.kustoClient = kustoClient;
        this.database = inventoryConfig.getKustoConfig().getDatabase();
        this.meterRegistry = meterRegistry;
        this.metricRecordService = metricRecordService;
    }

    /**
     * Scatter and gather, split the query based on time unit and do parallel process
     */
    public Mono<SearchResponse> getSearchResults(
        SearchRequestPayload payload,
        RiskyServiceInfo riskyServiceInfo,
        ChronoUnit unit,
        String tenantId) {

        List<SearchRequestPayload> partitionList = payload.partitionByUnit(unit);

        List<Mono<SearchResponse>> scatter = partitionList
            .stream()
            .map(subPayload -> getSearchResults(subPayload, new SearchResponse(), riskyServiceInfo, tenantId))
            .collect(Collectors.toList());

        // Combine the result, pick the top limit rows
        Mono<SearchResponse> zip = Mono.zip(scatter, result -> {
            return Stream.of(result)
                .map(obj -> (SearchResponse)obj )
                    .collect(Collectors.collectingAndThen(
                    Collectors.toList(),
                        list -> {
                            log.info("All results fetched from Kusto for payload {}", payload);
                            return SearchResponseCombiner.combine(list, payload);
                        }));
        });

        return zip;
    }

    public Mono<SearchResponse> getSearchResults(
            SearchRequestPayload payload,
            ResponseBuilder<SearchResponse> responseBuilder,
            RiskyServiceInfo riskyServiceInfo,
            String tenantId
    ) {

        return Mono.fromSupplier(
                        () -> {
                            // transfer "service" filter to port/protocol
                            payload.replaceServiceFilters(
                                    riskyServiceInfo.getServicetoServiceInfoMap());
                            log.info("KQL: \n {} \n\n for payload {}", payload.buildKQL(), payload);
                            return payload.buildKQL();
                        })
                .flatMap(
                        query -> {
                                Timer.Sample queryTimer = Timer.start(meterRegistry);

                                return Mono.fromCallable(() -> executeQuery(query))
                                        .subscribeOn(KUSTO_SCHEDULER)
                                        .publishOn(Schedulers.boundedElastic())
                                        .flatMap(
                                                kustoResultSetTable ->
                                                        Mono.fromCallable(
                                                                () -> {
                                                                    log.info("Partial results fetched from Kusto for payload {}", payload);
                                                                    metricRecordService
                                                                            .recordQueryLatency(
                                                                                    meterRegistry,
                                                                                    queryTimer,
                                                                                    MetricStatusLabel
                                                                                            .SUCCESS
                                                                                            .getValue(),
                                                                                    tenantId);
                                                                    return responseBuilder
                                                                            .buildResponse(
                                                                                    kustoResultSetTable,
                                                                                    payload,
                                                                                    riskyServiceInfo);
                                                                }));
                                    })
                .onErrorResume(
                        error -> {
                            log.error("Kusto DB Error processing request: ", error);
                            return Mono.error(
                                    new RuntimeException("Kusto Database can not process request"));
                        });
    }

    @SneakyThrows
    public KustoResultSetTable executeQuery(String query) {
        KustoOperationResult kustoOperationResult = kustoClient.execute(database, query);
        return kustoOperationResult.getPrimaryResults();
    }
}
