package com.illumio.data.search.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.search.model.constants.Metrics;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Slf4j
@AllArgsConstructor
@Service
public class MetricRecordService {
    private final ObjectMapper objectMapper;
    /**
     * Records the API response latency and API request count metrics
     */
    public void recordTimeAndCountMetrics(MeterRegistry meterRegistry, Timer.Sample timer, String status,
                                          String description, String tenantId){
        // backward compatible
        recordTimeAndCountMetrics(meterRegistry,timer,status,description,tenantId, Metrics.API_REQUEST_DURATION_METRIC.getValue(),
            Metrics.API_REQUEST_COUNT_METRIC.getValue());
    }

    public void recordTimeAndCountMetrics(MeterRegistry meterRegistry, Timer.Sample timer, String status,
        String description, String tenantId, String durationMetricString, String counterMetricsString){
        // Timer metric
        Tags tags = buildCommonTags(tenantId, status);
        timer.stop(Timer.builder(durationMetricString)
            .description(description)
            .tags(tags)
            .register(meterRegistry));

        // Counter metric
        meterRegistry.counter(counterMetricsString, tags).increment();
    }

    /**
     * Records API response size metrics
     */
    public void recordSizeMetrics(MeterRegistry meterRegistry, ResponseEntity<Object> response,
                                  String tenantId,
                                  String status){
        int responseSizeBytes = 0;
        if(response!=null && response.getBody()!=null){
            try {
                String responseBody = objectMapper.writeValueAsString(response.getBody());
                responseSizeBytes = responseBody.getBytes(StandardCharsets.UTF_8).length;
            } catch (JsonProcessingException e) {
                log.warn("Failed to serialize response body for metrics", e);
            }
        }
        meterRegistry
                .summary(
                        Metrics.API_RESPONSE_SIZE_METRIC.getValue(),
                        buildCommonTags(tenantId, status))
                .record(responseSizeBytes);
    }

    /**
     * Records the Kusto Query Latency metrics
     */
    public void recordQueryLatency(MeterRegistry meterRegistry, Timer.Sample sample,
                                   String status, String tenantId) {
        Tags tags = buildCommonTags(tenantId, status);

        // Query Latency metric
        sample.stop(Timer.builder(Metrics.KUSTO_QUERY_LATENCY_METRIC.getValue())
                .description("Time taken to execute Kusto query")
                .tags(tags)
                .register(meterRegistry));
    }

    private Tags buildCommonTags(String tenantId, String status) {

        return Tags.of("tenantId", tenantId,
                "status", status);
    }
}

