package com.illumio.data.search.model;

import com.illumio.data.model.ProjectFields;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.constants.Fields;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class ProjectFieldsBySortDecoratorTest {
    @Test
    void testKQLWithSort() {
        ProjectFields pf =
            ProjectFields.builder()
                .projectFields(
                    List.of("port", "service", "source_ip", "source_zone"))
                .build();

        ProjectFieldsServiceDecorator decorator =
            new ProjectFieldsServiceDecorator(
                new ProjectFieldsSrcZoneDecorator(
                    new ProjectFieldsDestZoneDecorator(pf)));

        SortByFields sbBytes = SortByFields.builder()
            .field(Fields.BYTES.getFieldKey())
            .order("asc")
            .build();

        SortByFields sb = SortByFields.builder()
            .field(Fields.DESTINATION_IP.getFieldKey())
            .order("asc")
            .build();

        SortByFields sbPort = SortByFields.builder()
            .field(Fields.DESTINATION_RESOURCE_ID.getFieldKey())
            .order("desc")
            .build();

        ProjectFieldsBySortDecorator decorator2 = new ProjectFieldsBySortDecorator(
            decorator, List.of(sb, sbPort, sbBytes)
        );

        String kql = decorator2.buildKQL();

        // the field name transfer to the column names based on Fields
        Assertions.assertEquals("| project Port, SrcIP, SrcResId, SrcCloudProvider, Proto, DestIP, DestResId, SentBytes", kql);
    }
}
