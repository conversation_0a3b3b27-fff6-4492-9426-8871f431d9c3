package com.illumio.data.search.request;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.search.model.SearchRequestPayload;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class SearchPayLoadTest {

    @Test
    public void testJsonToPayLoad() throws Exception {
        String json = """
{
  "currentTimeFrame":{
    "startTime":"2025-02-07T08:40:00Z",
    "endTime":"2025-03-01T07:59:59Z"
  },
  "sortByFields": [{
    "field":"flows",
    "order":"desc"
  }],
  "pagination": {
    "pageNumber": 1,
    "rowLimit": 10,
    "topResultHardLimit": 5
  }
}
""";
        ObjectMapper mapper = new ObjectMapper();
        RequestPayload req = mapper.readValue(json, RequestPayload.class);


        Assertions.assertNotNull(req);
        Assertions.assertEquals(1, req.getPagination().getPageNumber());
    }

    @Test
    public void testJsonToSearchPayLoad() throws Exception {
        String json =
            """
{
  "currentTimeFrame":{
    "startTime":"2025-02-21T08:40:00Z",
    "endTime":"2025-05-01T07:59:59Z"
  },
   "baseFilter":           {
 	"operand":"greater","field":"flows","value":"322","includeEqual":true
 },
  "sortByFields": [
  {
    "field":"flows",
    "order":"desc"
  }],
"projectFields": {
    "projectFields":["source_ip"]
   },

  "pagination": {
        "pageNumber": 6,
        "rowLimit": 5,
        "totalPages": 8,
        "topResultHardLimit": 5,
        "topResultRows": 6,
        "enableScatterGather": false,
        "dpMetaData": {
            "primarySortBy": {
                "field": "flows",
                "order": "desc"
            },
            "totalPages": 8,
            "currPage": -3,
            "anchorRows": [
                {
                    "pageNumber": -1,
                    "firstValue": "363",
                    "lastValue": "322",
                    "firstDupCount": 1,
                    "lastDupCount": 1
                },
                {
                    "pageNumber": -2,
                    "firstValue": "401",
                    "lastValue": "367",
                    "firstDupCount": 1,
                    "lastDupCount": 1
                },
                {
                    "pageNumber": -3,
                    "firstValue": "449",
                    "lastValue": "401",
                    "firstDupCount": 1,
                    "lastDupCount": 1
                }
            ]
        },
        "paginationType": "NEXT"
    }
}
""";
        ObjectMapper mapper = new ObjectMapper();
        SearchRequestPayload req = mapper.readValue(json, SearchRequestPayload.class);

        Assertions.assertEquals(6, req.getPagination().getPageNumber());
    }
}