package com.illumio.data.search.model;

import com.illumio.data.model.Pagination;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.model.deepPagination.PaginationType;
import com.illumio.data.model.generalFilters.GreaterFilter;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

public class SearchRequestCacheTest {
    @Test
    void testIsLandingPage() {
        TimeFrame currentTime =
                TimeFrame.builder()
                        .startTime("2025-02-15T08:00:00Z")
                        .endTime("2025-02-16T08:59:59Z")
                        .build();

        SortByFields sortByFields = SortByFields.builder().field("flows").order("asc").build();

        Pagination pagination = Pagination.builder()
            .paginationType(PaginationType.FIRST)
            .topResultHardLimit(1000).build();

        SearchRequestPayload searchRequestPayload =
                new SearchRequestPayload(
                        currentTime,
                        null,
                        Collections.emptyList(),
                        List.of(sortByFields),
                        pagination,
                        Collections.emptyList(),
                        null,
                    null);

        boolean isLandingPage = SearchRequestCache.isLandingSearchPayload(searchRequestPayload);
        Optional<Pair<String, String>> redisKeys =
                SearchRequestCache.getLandingPageRedisKey(
                        searchRequestPayload, "af84345d-0f7a-43ad-b19f-dcce53af2b9d");
        Assertions.assertTrue(isLandingPage);
        Assertions.assertTrue(redisKeys.isPresent());
        Assertions.assertEquals(
                "af84345d-0f7a-43ad-b19f-dcce53af2b9d_2025_02_15_08-2025_02_16_08", redisKeys.get().getKey());
        Assertions.assertEquals("flows_asc", redisKeys.get().getValue());

        // No cache for baseFilter specified
        GreaterFilter ports = new GreaterFilter();
        ports.setField( "port");
        ports.setValue( "21000");
        searchRequestPayload =
            new SearchRequestPayload(
                currentTime,
                null,
                Collections.emptyList(),
                List.of(sortByFields),
                pagination,
                Collections.emptyList(),
                null,
                ports);

        isLandingPage = SearchRequestCache.isLandingSearchPayload(searchRequestPayload);
        Assertions.assertFalse(isLandingPage);
    }
}
