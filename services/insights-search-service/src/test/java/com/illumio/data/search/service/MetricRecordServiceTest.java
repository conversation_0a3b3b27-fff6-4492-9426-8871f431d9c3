package com.illumio.data.search.service;


import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.search.model.constants.Metrics;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.simple.SimpleMeterRegistry;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.ResponseEntity;

class MetricRecordServiceTest {

    private MetricRecordService metricRecordService;
    private MeterRegistry meterRegistry;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setup() {
        objectMapper = new ObjectMapper();
        meterRegistry = new SimpleMeterRegistry(); // Simple registry for unit tests
        metricRecordService = new MetricRecordService(objectMapper);
    }

    @Test
    void testRecordTimeAndCountMetrics() {
        Timer.Sample sample = Timer.start(meterRegistry);
        metricRecordService.recordTimeAndCountMetrics(meterRegistry, sample, "success", "API latency", "tenant1");

        // Validate counter
        double count =
                meterRegistry
                        .get(Metrics.API_REQUEST_COUNT_METRIC.getValue())
                        .tag("tenantId", "tenant1")
                        .counter()
                        .count();
        assertEquals(1.0, count);

        // Validate timer
        Timer timer =
                meterRegistry
                        .get(Metrics.API_REQUEST_DURATION_METRIC.getValue())
                        .tag("tenantId", "tenant1")
                        .timer();
        assertEquals(1, timer.count());
    }

    @Test
    void testRecordSizeMetrics_withValidBody() {
        String sampleResponse = "{\"key\":\"value\"}";
        ResponseEntity<Object> response = ResponseEntity.ok().body(sampleResponse);

        metricRecordService.recordSizeMetrics(meterRegistry, response, "tenant1", "success");

        double recorded =
                meterRegistry
                        .get(Metrics.API_RESPONSE_SIZE_METRIC.getValue())
                        .tag("tenantId", "tenant1")
                        .summary()
                        .count();

        // Should record one value
        assertEquals(1, recorded);
    }

    @Test
    void testRecordQueryLatency() {
        Timer.Sample sample = Timer.start(meterRegistry);
        metricRecordService.recordQueryLatency(meterRegistry, sample, "success", "testTenant");

        Timer timer =
                meterRegistry
                        .get(Metrics.KUSTO_QUERY_LATENCY_METRIC.getValue())
                        .tag("tenantId", "testTenant")
                        .timer();
        assertEquals(1, timer.count());
    }

    @Test
    void testRecordSizeMetrics_withInvalidJson() {
        ResponseEntity<Object> response = ResponseEntity.ok(new Object() {
            // ObjectMapper will likely fail to serialize this anonymous class in strict mode
        });

        metricRecordService.recordSizeMetrics(meterRegistry, response ,"tenant1", "error");

        // We expect no exception and summary recorded as 0
        double count =
                meterRegistry
                        .get(Metrics.API_RESPONSE_SIZE_METRIC.getValue())
                        .tag("tenantId", "tenant1")
                        .summary()
                        .count();

        assertEquals(1, count); // still counts a record, but size would be 0
    }
}