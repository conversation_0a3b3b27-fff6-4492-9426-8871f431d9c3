package com.illumio.data.search.response;

import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.ProjectFields;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.illumio.data.search.model.SearchRequestPayload;
import com.illumio.data.service.RiskyServiceInfo;
import com.microsoft.azure.kusto.data.KustoResultColumn;
import com.microsoft.azure.kusto.data.KustoResultSetTable;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class SearchResponseCombinerTest {

    private KustoResultColumn[] getColumns() {
        List<KustoResultColumn> columns = new ArrayList<>();

        columns.add(new KustoResultColumn("DestIP", "string", 0));
        columns.add(new KustoResultColumn("Port", "int", 1));
        columns.add(new KustoResultColumn("Proto", "string", 2));
        columns.add(new KustoResultColumn("FlowCount", "int", 3));

        return columns.toArray(new KustoResultColumn[columns.size()]);
    }

    private RiskyServiceInfo getMockedServiceInfo(){
        RiskyServiceInfo serviceInfo = Mockito.mock(RiskyServiceInfo.class);
        when(serviceInfo.getPortProtoToServiceInfoMap()).thenReturn(Collections.emptyMap());
        return serviceInfo;
    }

    private KustoResultSetTable createMock1(){
        KustoResultSetTable mockset1 = Mockito.mock(KustoResultSetTable.class);

        when(mockset1.getColumns()).thenReturn(getColumns());

        // Mock behavior: Simulate 3 rows of data
        when(mockset1.next()).thenReturn(true, true, true, true, true, false); // 5 rows, then false to stop
        when(mockset1.getObject(0)).thenReturn("ip1", "ip3", "ip5", "ip7", "ip9");
        when(mockset1.getObject(1)).thenReturn(41, 43, 45, 47, 49);
        when(mockset1.getObject(2)).thenReturn("proto1", "proto3", "proto5", "proto7", "proto9");
        when(mockset1.getObject(3)).thenReturn(41000, 43000, 45000, 47000, 49000);

        return mockset1;
    }

    private KustoResultSetTable createMock2(){
        KustoResultSetTable mockset2 = Mockito.mock(KustoResultSetTable.class);

        when(mockset2.getColumns()).thenReturn(getColumns());

        // Mock behavior: Simulate 3 rows of data
        when(mockset2.next()).thenReturn(true, true, true, true, true, false); // 5 rows, then false to stop
        when(mockset2.getObject(0)).thenReturn("ip2", "ip4", "ip6", "ip8", "ip10");
        when(mockset2.getObject(1)).thenReturn(42, 44, 46, 48, 50);
        when(mockset2.getObject(2)).thenReturn("proto2", "proto4", "proto6", "proto8", "proto10");
        when(mockset2.getObject(3)).thenReturn(42000, 44000, 46000, 48000, 50000);

        return mockset2;
    }

    /**
     * Create mock data with same flow count but different port
     * and use multiple sorting method for port
     * @return
     */
    private KustoResultSetTable createMock3(){
        KustoResultSetTable mockset3 = Mockito.mock(KustoResultSetTable.class);

        when(mockset3.getColumns()).thenReturn(getColumns());

        // Mock behavior: Simulate 3 rows of data
        when(mockset3.next()).thenReturn(true, true, true, true, true, false); // 5 rows, then false to stop
        when(mockset3.getObject(0)).thenReturn("ip3_1", "ip3_3", "ip3_5", "ip3_7", "ip3_9");
        when(mockset3.getObject(1)).thenReturn(48, 46, 44, 42, 40);
        when(mockset3.getObject(2)).thenReturn("proto1", "proto3", "proto5", "proto7", "proto9");
        when(mockset3.getObject(3)).thenReturn(41000, 43000, 45000, 47000, 49000);

        return mockset3;
    }

    private SearchRequestPayload createMockPayload(){
        SearchRequestPayload mock = Mockito.mock(SearchRequestPayload.class);

        when (mock.getPagination()).thenReturn(Pagination.builder()
                .topResultHardLimit(4)
            .build());
        return mock;
    }

    private SearchResponse buildResponse(KustoResultSetTable mockset){
        SearchResponse searchResponse = new SearchResponse();

        SearchResponse result =
            searchResponse.buildResponse(
                mockset,
                new SearchRequestPayload(
                    TimeFrame.builder().build(),
                    null,
                    Collections.emptyList(),
                    List.of(SortByFields.builder()
                        .field("flows")
                        .order("asc")
                        .build()),
                    Pagination.builder()
                        .topResultHardLimit(4)
                        .build(),
                    Collections.emptyList(),
                    ProjectFields.builder().build(),
                    null
                ),
                getMockedServiceInfo());
        return result;
    }

    @Test
    public void testSearchResponseToJson() throws Exception {
        SearchResponse res1 = buildResponse(createMock1());
        ObjectMapper mapper = new ObjectMapper();

        // Serialize: Object -> JSON String
        String json = mapper.writeValueAsString(res1);
        System.out.println(json);


        SearchResponse converted = mapper.readValue(json, SearchResponse.class);
        List<List<Object>> data = converted.getData();
        Assertions.assertEquals(5, data.size());
        Assertions.assertEquals(8, data.get(0).size());

        Assertions.assertEquals("ip1", data.get(0).get(0));
        Assertions.assertEquals(43, data.get(1).get(1));
        Assertions.assertEquals("proto5", data.get(2).get(2));
        Assertions.assertEquals(47000, data.get(3).get(3));
    }

    @Test
    public void testMockResponseAsc() throws Exception {
        SearchResponse res1 = buildResponse(createMock1());

        List<List<Object>> data = res1.getData();
        Assertions.assertEquals(5, data.size());
        Assertions.assertEquals(8, data.get(0).size());

        Assertions.assertEquals("ip1", data.get(0).get(0));
        Assertions.assertEquals(43, data.get(1).get(1));
        Assertions.assertEquals("proto5", data.get(2).get(2));
        Assertions.assertEquals(47000, data.get(3).get(3));

        SearchResponse res2 = buildResponse(createMock2());

        data = res2.getData();
        Assertions.assertEquals(5, data.size());
        Assertions.assertEquals(8, data.get(0).size());

        Assertions.assertEquals("ip2", data.get(0).get(0));
        Assertions.assertEquals(44, data.get(1).get(1));
        Assertions.assertEquals("proto6", data.get(2).get(2));
        Assertions.assertEquals(48000, data.get(3).get(3));

        int limit = 4;
        SearchResponse combined = SearchResponseCombiner.combine(List.of(res1, res2), createMockPayload());

        Assertions.assertEquals(limit, combined.getData().size());
        data = combined.getData();
        Assertions.assertEquals(limit, data.size());
        Assertions.assertEquals(8, data.get(0).size());

        Assertions.assertEquals("ip1", data.get(0).get(0));
        Assertions.assertEquals(42, data.get(1).get(1));
        Assertions.assertEquals("proto3", data.get(2).get(2));
        Assertions.assertEquals(44000, data.get(3).get(3));
    }

    private SearchResponse buildResponseDesc(KustoResultSetTable mockset){
        SearchResponse searchResponse = new SearchResponse();

        SearchResponse result =
            searchResponse.buildResponse(
                mockset,
                new SearchRequestPayload(
                    TimeFrame.builder().build(),
                    null,
                    Collections.emptyList(),
                    List.of(SortByFields.builder()
                        .field("flows")
                        .order("desc")
                        .build()),
                    Pagination.builder()
                        .topResultHardLimit(4)
                        .build(),
                    Collections.emptyList(),
                    ProjectFields.builder().build(),
                    null
                ),
                getMockedServiceInfo());
        return result;
    }

    @Test
    public void testMockResponseDesc() throws Exception {
        // the mock data order is not ascending, but will not affect the combiner result
        SearchResponse res1 = buildResponseDesc(createMock1());
        SearchResponse res2 = buildResponseDesc(createMock2());

        int limit = 4;
        SearchResponse combined = SearchResponseCombiner.combine(List.of(res1, res2), createMockPayload());

        Assertions.assertEquals(limit, combined.getData().size());
        List<List<Object>> data = combined.getData();
        Assertions.assertEquals(limit, data.size());
        Assertions.assertEquals(8, data.get(0).size());

        Assertions.assertEquals("ip10", data.get(0).get(0));
        Assertions.assertEquals(49, data.get(1).get(1));
        Assertions.assertEquals("proto8", data.get(2).get(2));
        Assertions.assertEquals(47000, data.get(3).get(3));
    }

    private SearchResponse buildResponseWithDoubleSort(KustoResultSetTable mockset){
        SearchResponse searchResponse = new SearchResponse();

        SearchResponse result =
            searchResponse.buildResponse(
                mockset,
                new SearchRequestPayload(
                    TimeFrame.builder().build(),
                    null,
                    Collections.emptyList(),
                    List.of(
                        SortByFields.builder()
                        .field("flows")
                        .order("asc")
                        .build(),

                        SortByFields.builder()
                            .field("port")
                            .order("desc")
                            .build()
                        ),
                    Pagination.builder()
                        .topResultHardLimit(4)
                        .build(),
                    Collections.emptyList(),
                    ProjectFields.builder().build(),
                    null
                ),
                getMockedServiceInfo());
        return result;
    }

    @Test
    public void testMockResponseWithTwoSort() throws Exception {
        SearchResponse res1 = buildResponseWithDoubleSort(createMock1());
        SearchResponse res3 = buildResponseWithDoubleSort(createMock3());

        int limit = 4;
        SearchResponse combined = SearchResponseCombiner.combine(List.of(res1, res3), createMockPayload());

        Assertions.assertEquals(limit, combined.getData().size());
        List<List<Object>> data = combined.getData();
        Assertions.assertEquals(limit, data.size());
        Assertions.assertEquals(8, data.get(0).size());

        // sort by flow count (index3) asc, then port (index1) desc
        Assertions.assertEquals("ip3_1", data.get(0).get(0));
        Assertions.assertEquals(48, data.get(0).get(1));
        Assertions.assertEquals("proto1", data.get(0).get(2));
        Assertions.assertEquals(41000, data.get(0).get(3));

        // sort by flow count (index3) asc, then port (index1) desc
        Assertions.assertEquals("ip1", data.get(1).get(0));
        Assertions.assertEquals(41, data.get(1).get(1));
        Assertions.assertEquals("proto1", data.get(1).get(2));
        Assertions.assertEquals(41000, data.get(1).get(3));

        // sort by flow count (index3) asc, then port (index1) desc
        Assertions.assertEquals("ip3_3", data.get(2).get(0));
        Assertions.assertEquals(46, data.get(2).get(1));
        Assertions.assertEquals("proto3", data.get(2).get(2));
        Assertions.assertEquals(43000, data.get(2).get(3));

        // sort by flow count (index3) asc, then port (index1) desc
        Assertions.assertEquals("ip3", data.get(3).get(0));
        Assertions.assertEquals(43, data.get(3).get(1));
        Assertions.assertEquals("proto3", data.get(3).get(2));
        Assertions.assertEquals(43000, data.get(3).get(3));
    }
}
