package com.illumio.data.search.model;

import com.illumio.data.model.ProjectFields;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class ProjectFieldsZoneDecoratorTest {
    @Test
    void testKQLWithServiceAndSrcZone() {
        ProjectFields pf =
            ProjectFields.builder()
                .projectFields(List.of("port", "service", "source_ip", "destination_ip", "source_zone"))
                .build();

        ProjectFieldsServiceDecorator decorator =
            new ProjectFieldsServiceDecorator(
                new ProjectFieldsSrcZoneDecorator(
                    new ProjectFieldsDestZoneDecorator(pf)));
        String kql = decorator.buildKQL();

        // the field name transfer to the column names based on Fields
        Assertions.assertEquals("| project Port, SrcIP, DestIP, SrcResId, SrcCloudProvider, Proto", kql);
    }

    @Test
    void testKQLWithServiceAndBothZones() {
        ProjectFields pf =
            ProjectFields.builder()
                .projectFields(List.of("port", "service", "source_ip", "destination_ip", "source_zone", "destination_zone"))
                .build();

        ProjectFieldsServiceDecorator decorator =
            new ProjectFieldsServiceDecorator(
                new ProjectFieldsSrcZoneDecorator(
                    new ProjectFieldsDestZoneDecorator(pf)));
        String kql = decorator.buildKQL();

        // the field name transfer to the column names based on Fields
        Assertions.assertEquals("| project Port, SrcIP, DestIP, DestResId, DestCloudProvider, SrcResId, SrcCloudProvider, Proto", kql);
    }

}
