package com.illumio.data.search.pagination;

import com.illumio.data.model.SortByFields;
import com.illumio.data.model.deepPagination.AnchorRows;
import com.illumio.data.model.deepPagination.DpMetaData;
import com.illumio.data.model.deepPagination.DpTargetPageInfor;
import com.illumio.data.model.deepPagination.PaginationType;
import com.microsoft.azure.kusto.data.KustoResultColumn;
import com.microsoft.azure.kusto.data.KustoResultSetTable;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class PaginationTest {
    private static final int PAGE_SIZE = 3;

    @Test
    public void testFirstPageValidation() {
        // Create test data with duplicate values
        KustoResultSetTable firstPage = KustoDBSimulator.createDBResult(false, Integer.MIN_VALUE, Integer.MAX_VALUE, PAGE_SIZE);
        Pair<KustoResultColumn[], List<List<Object>>> transferResult = KustoDBSimulator.transferDBResult(firstPage);
        List<List<Object>> data = transferResult.getValue();

        // Create pagination metadata for first page
        DpMetaData metaData = DpMetaData.builder()
            .totalPages(5)
            .currPage(1)
            .primarySortBy(SortByFields.builder()
                .field("flows")
                .order("asc")
                .build())
            .anchorRows(List.of(
                AnchorRows.builder()
                    .pageNumber(1)
                    .firstValue("41000")
                    .lastValue("41000")
                    .firstDupCount(3)
                    .lastDupCount(3)
                    .build()
            ))
            .build();

        // Validate first page
        Assertions.assertTrue(metaData.sanityCheck());
        
        // Test first page navigation
        DpTargetPageInfor firstPageInfo = metaData.buildTargetPageInfor(PaginationType.FIRST);
        Assertions.assertEquals(1, firstPageInfo.getTargetPageNumber());
        Assertions.assertNull(firstPageInfo.getAdditionalFilter());
        Assertions.assertEquals(0, firstPageInfo.getRowCountToSkip());
        Assertions.assertFalse(firstPageInfo.isOutOfRange());

        // Validate data
        Assertions.assertEquals(3, data.size());
        Assertions.assertEquals(41000, data.get(0).get(1));
        Assertions.assertEquals(41000, data.get(1).get(1));
        Assertions.assertEquals(41000, data.get(2).get(1));
    }

    @Test
    public void testLastPageValidation() {
        // Create test data for last page
        KustoResultSetTable lastPage = KustoDBSimulator.createDBResult(true, Integer.MIN_VALUE, Integer.MAX_VALUE, PAGE_SIZE);
        Pair<KustoResultColumn[], List<List<Object>>> transferResult = KustoDBSimulator.transferDBResult(lastPage);
        List<List<Object>> data = transferResult.getValue();

        // Create pagination metadata for last page
        DpMetaData metaData = DpMetaData.builder()
            .totalPages(5)
            .currPage(-1)
            .primarySortBy(SortByFields.builder()
                .field("flows")
                .order("desc")
                .build())
            .anchorRows(List.of(
                AnchorRows.builder()
                    .pageNumber(-1)
                    .firstValue("53000")
                    .lastValue("51000")
                    .firstDupCount(1)
                    .lastDupCount(1)
                    .build()
            ))
            .build();

        // Validate last page
        Assertions.assertTrue(metaData.sanityCheck());

        // Test last page navigation
        DpTargetPageInfor lastPageInfo = metaData.buildTargetPageInfor(PaginationType.LAST);
        Assertions.assertEquals(-1, lastPageInfo.getTargetPageNumber());
        Assertions.assertNull(lastPageInfo.getAdditionalFilter());
        Assertions.assertEquals(0, lastPageInfo.getRowCountToSkip());
        Assertions.assertFalse(lastPageInfo.isOutOfRange());

        // Validate data
        Assertions.assertEquals(3, data.size());
        Assertions.assertEquals(53000, data.get(0).get(1));
        Assertions.assertEquals(52000, data.get(1).get(1));
        Assertions.assertEquals(51000, data.get(2).get(1));
    }

    @Test
    public void testPrevNextPageWithDuplicates() {
        // Create test data with duplicate values (49000 appears multiple times)
        KustoResultSetTable middlePage = KustoDBSimulator.createDBResult(false, 49000, 49000, PAGE_SIZE);
        Pair<KustoResultColumn[], List<List<Object>>> transferResult = KustoDBSimulator.transferDBResult(middlePage);
        List<List<Object>> data = transferResult.getValue();

        // Create pagination metadata with multiple pages including duplicates
        DpMetaData metaData = DpMetaData.builder()
            .totalPages(5)
            .currPage(3)
            .primarySortBy(SortByFields.builder()
                .field("flows")
                .order("asc")
                .build())
            .anchorRows(List.of(
                AnchorRows.builder()
                    .pageNumber(1)
                    .firstValue("41000")
                    .lastValue("41000")
                    .firstDupCount(3)
                    .lastDupCount(3)
                    .build(),
                AnchorRows.builder()
                    .pageNumber(2)
                    .firstValue("49000")
                    .lastValue("49000")
                    .firstDupCount(3)
                    .lastDupCount(3)
                    .build(),
                AnchorRows.builder()
                    .pageNumber(3)
                    .firstValue("49000")
                    .lastValue("49000")
                    .firstDupCount(3)
                    .lastDupCount(3)
                    .build()
            ))
            .build();

        // Validate metadata
        Assertions.assertTrue(metaData.sanityCheck());

        // Test next page navigation, need to check duplicated value of 49000 from Page 2-3,
        DpTargetPageInfor nextPageInfo = metaData.buildTargetPageInfor(PaginationType.NEXT);
        Assertions.assertEquals(4, nextPageInfo.getTargetPageNumber());
        Assertions.assertNotNull(nextPageInfo.getAdditionalFilter());
        Assertions.assertEquals("FlowCount  >=  49000", nextPageInfo.getAdditionalFilter().buildKQL().trim());
        Assertions.assertEquals(6, nextPageInfo.getRowCountToSkip()); // Skip all 49000 duplicates
        Assertions.assertTrue(nextPageInfo.isOutOfRange());

        // Test previous page navigation, need to check duplicated value of 49000 from Page 1
        DpTargetPageInfor prevPageInfo = metaData.buildTargetPageInfor(PaginationType.PREV);
        Assertions.assertEquals(2, prevPageInfo.getTargetPageNumber());
        Assertions.assertNotNull(prevPageInfo.getAdditionalFilter());
        Assertions.assertEquals("((FlowCount  >=  49000 ) and (FlowCount  <=  49000 ))", prevPageInfo.getAdditionalFilter().buildKQL());
        Assertions.assertEquals(0, prevPageInfo.getRowCountToSkip()); // Skip 41000 duplicates
        Assertions.assertFalse(prevPageInfo.isOutOfRange());

        // Validate data
        Assertions.assertEquals(3, data.size());
        Assertions.assertEquals(49000, data.get(0).get(1));
        Assertions.assertEquals(49000, data.get(1).get(1));
        Assertions.assertEquals(49000, data.get(2).get(1));
    }

    @Test
    public void testPrevNextPageWithDuplicatesStartsWithLastPage() {
        // Create test data with duplicate values (49000 appears multiple times)
        KustoResultSetTable middlePage = KustoDBSimulator.createDBResult(true, 49000, 50000, PAGE_SIZE);
        Pair<KustoResultColumn[], List<List<Object>>> transferResult = KustoDBSimulator.transferDBResult(middlePage);
        List<List<Object>> data = transferResult.getValue();

        // Create pagination metadata with multiple pages including duplicates
        DpMetaData metaData = DpMetaData.builder()
            .totalPages(5)
            .currPage(-3)
            .primarySortBy(SortByFields.builder()
                .field("flows")
                .order("asc")
                .build())
            .anchorRows(List.of(
                AnchorRows.builder()
                    .pageNumber(-1)
                    .firstValue("51000")
                    .lastValue("53000")
                    .firstDupCount(1)
                    .lastDupCount(1)
                    .build(),
                AnchorRows.builder()
                    .pageNumber(-2)
                    .firstValue("49000")
                    .lastValue("50000")
                    .firstDupCount(2)
                    .lastDupCount(1)
                    .build(),
                AnchorRows.builder()
                    .pageNumber(-3)
                    .firstValue("49000")
                    .lastValue("49000")
                    .firstDupCount(3)
                    .lastDupCount(3)
                    .build()
            ))
            .build();

        // Validate metadata
        Assertions.assertTrue(metaData.sanityCheck());

        // Test next page navigation, need to check duplicated value of 50000 from last page,
        DpTargetPageInfor nextPageInfo = metaData.buildTargetPageInfor(PaginationType.NEXT);
        Assertions.assertEquals(-2, nextPageInfo.getTargetPageNumber());
        Assertions.assertNotNull(nextPageInfo.getAdditionalFilter());
        Assertions.assertEquals("((FlowCount  >=  49000 ) and (FlowCount  <=  50000 ))", nextPageInfo.getAdditionalFilter().buildKQL().trim());
        Assertions.assertEquals(0, nextPageInfo.getRowCountToSkip()); // Skip all 49000 duplicates
        Assertions.assertFalse(nextPageInfo.isOutOfRange());

        // Test previous page navigation, need to check duplicated value of 49000 from Page -2 and -3
        DpTargetPageInfor prevPageInfo = metaData.buildTargetPageInfor(PaginationType.PREV);
        Assertions.assertEquals(-4, prevPageInfo.getTargetPageNumber());
        Assertions.assertNotNull(prevPageInfo.getAdditionalFilter());
        Assertions.assertEquals("FlowCount  <=  49000 ", prevPageInfo.getAdditionalFilter().buildKQL());
        Assertions.assertEquals(5, prevPageInfo.getRowCountToSkip()); // Skip 41000 duplicates
        Assertions.assertTrue(prevPageInfo.isOutOfRange());

        // Validate data
        Assertions.assertEquals(3, data.size());
        Assertions.assertEquals(50000, data.get(0).get(1));
        Assertions.assertEquals(49000, data.get(1).get(1));
        Assertions.assertEquals(49000, data.get(2).get(1));
    }

}