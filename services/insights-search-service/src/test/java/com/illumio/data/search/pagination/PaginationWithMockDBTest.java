package com.illumio.data.search.pagination;

import static com.illumio.data.mockKusto.MockKustoFlowTable.FLOW_TABLE;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

import com.illumio.data.mockKusto.MockKustoDatabase;
import com.illumio.data.mockKusto.MockKustoFlowTable;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public class PaginationWithMockDBTest {
    private MockKustoDatabase db;

    @BeforeEach
    void setUp() {
        db = MockKustoFlowTable.createKustoDatabase();
    }

    @Test
    void testDataIngestion() throws IOException {
        String query =
            FLOW_TABLE
                + " | where SrcIP in (\"************\") "
                + " | project SrcIP, SrcId, SentBytes "
                + " | sort by  SentBytes desc";
        List<Map<String, Object>> result = db.executeQuery(query);
        assertFalse(result.isEmpty());
        assertEquals(30, result.size());
    }
}
