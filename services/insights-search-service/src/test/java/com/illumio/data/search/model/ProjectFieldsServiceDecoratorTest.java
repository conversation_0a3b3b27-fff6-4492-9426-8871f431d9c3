package com.illumio.data.search.model;

import com.illumio.data.model.ProjectFields;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class ProjectFieldsServiceDecoratorTest {
    @Test
    void testKQLWithoutService() {
        ProjectFields pf =
            ProjectFields.builder()
                .projectFields(List.of("port", "source_ip", "destination_ip"))
                .build();

        ProjectFieldsServiceDecorator decorator = new ProjectFieldsServiceDecorator(pf);
        String kql = decorator.buildKQL();

        // the field name transfer to the column names based on Fields
        Assertions.assertEquals("| project Port, SrcIP, DestIP", kql);
    }

    @Test
    void testKQLWithService1() {
        ProjectFields pf =
            ProjectFields.builder()
                .projectFields(List.of("port", "service", "source_ip", "destination_ip"))
                .build();

        ProjectFieldsServiceDecorator decorator = new ProjectFieldsServiceDecorator(pf);
        String kql = decorator.buildKQL();

        // the field name transfer to the column names based on Fields
        Assertions.assertEquals("| project Port, SrcIP, DestIP, Proto", kql);
    }

    @Test
    void testKQLWithService2() {
        ProjectFields pf =
            ProjectFields.builder()
                .projectFields(List.of("port", "service", "source_ip", "destination_ip", "proto"))
                .build();

        ProjectFieldsServiceDecorator decorator = new ProjectFieldsServiceDecorator(pf);
        String kql = decorator.buildKQL();

        // the field name transfer to the column names based on Fields
        Assertions.assertEquals("| project Port, SrcIP, DestIP, Proto", kql);
    }

    @Test
    void testKQLWithService3() {
        ProjectFields pf =
            ProjectFields.builder()
                .projectFields(List.of("service", "source_ip", "destination_ip"))
                .build();

        ProjectFieldsServiceDecorator decorator = new ProjectFieldsServiceDecorator(pf);
        String kql = decorator.buildKQL();

        // the field name transfer to the column names based on Fields
        Assertions.assertEquals("| project SrcIP, DestIP, Port, Proto", kql);
    }
}
