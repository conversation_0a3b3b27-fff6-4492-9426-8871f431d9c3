apiVersion: v1
kind: Secret
metadata:
  name: {{ include "LawSink.fullname" . }}-env-secrets
  labels:
    {{- include "LawSink.labels" . | nindent 4 }}
type: Opaque
stringData:
  LAWSINK_KAFKACOMMONCONFIG_SASLJAASCONFIG: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.eventhub.password }}";
  LAWSINK_AZURECONFIG_AZURECLIENTID: "{{ .Values.lawSink.azure_config.azure_client_id }}"
  LAWSINK_AZURECONFIG_AZURECLIENTSECRET: "{{ .Values.lawSink.azure_config.azure_client_secret }}"
  LAWSINK_AZURECONFIG_AZURETENANTID: "{{ .Values.lawSink.azure_config.azure_tenant_id }}"
