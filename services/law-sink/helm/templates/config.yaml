apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "LawSink.fullname" . }}-env-configmap
  labels:
    {{- include "LawSink.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: "{{.Values.logging.level.root}}"
        org:
          apache:
            kafka: INFO
    
    spring:
      application:
        name: law-sink
      output:
        ansi:
          enabled: ALWAYS
    
    law-sink:
      kafka-common-config:
        bootstrapServers: "{{.Values.lawSink.kafkaCommonConfig.bootstrapServers}}"
        isSasl: true
        saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.eventhub.password }}";
    
      kafka-receiver-config:
        topic: "{{ .Values.lawSink.kafkaReceiverConfig.topic }}"
        groupId: "{{ .Values.lawSink.kafkaReceiverConfig.groupId }}"
        autoOffsetReset: latest
        requestTimeoutMs: "{{.Values.lawSink.kafkaReceiverConfig.requestTimeoutMs}}"
        maxPollRecords: "{{.Values.lawSink.kafkaReceiverConfig.maxPollRecords}}"
        maxPartitionFetchBytes: "{{.Values.lawSink.kafkaReceiverConfig.maxPartitionFetchBytes}}"
        maxDeferredCommits: "{{.Values.lawSink.kafkaReceiverConfig.maxDeferredCommits}}"
    
      data-collection-configs:
        - data-collection-endpoint: "{{.Values.lawSink.data_collection_config.data_collection_endpoint}}"
          data-collection-rule-id: "{{.Values.lawSink.data_collection_config.data_collection_rule_id}}"
          data-collection-rule-stream-name: "{{.Values.lawSink.data_collection_config.data_collection_rule_stream_name}}"
    
      logs-ingestion-api-config:
        max-concurrency: "{{.Values.lawSink.logsIngestionApiConfig.maxConcurrency}}"
        httpLogLevelDetail: "{{.Values.lawSink.logsIngestionApiConfig.httpLogLevelDetail}}"
    
      azure-config:
        is-workload-identity: "{{.Values.lawSink.azure_config.is_workload_identity}}"
        workload-identity:
          client-id: "{{.Values.lawSink.azure_config.client_id}}"

    insights:
      kafka-common-config:
        bootstrapServers: "{{.Values.insights.kafkaCommonConfig.bootstrapServers}}"
        isSasl: true
        saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.eventhub.password }}";

      kafka-receiver-config:
        topic: "{{ .Values.insights.kafkaReceiverConfig.topic }}"
        groupId: "{{ .Values.insights.kafkaReceiverConfig.groupId }}"
        autoOffsetReset: latest
        requestTimeoutMs: "{{.Values.insights.kafkaReceiverConfig.requestTimeoutMs}}"
        maxPollRecords: "{{.Values.insights.kafkaReceiverConfig.maxPollRecords}}"
        maxPartitionFetchBytes: "{{.Values.insights.kafkaReceiverConfig.maxPartitionFetchBytes}}"
        maxDeferredCommits: "{{.Values.insights.kafkaReceiverConfig.maxDeferredCommits}}"

      data-collection-configs:
        - data-collection-endpoint: "{{.Values.insights.data_collection_config.data_collection_endpoint}}"
          data-collection-rule-id: "{{.Values.insights.data_collection_config.data_collection_rule_id}}"
          data-collection-rule-stream-name: "{{.Values.insights.data_collection_config.data_collection_rule_stream_name}}"
    
      logs-ingestion-api-config:
        max-concurrency: "{{.Values.insights.logsIngestionApiConfig.maxConcurrency}}"
        httpLogLevelDetail: "{{.Values.insights.logsIngestionApiConfig.httpLogLevelDetail}}"