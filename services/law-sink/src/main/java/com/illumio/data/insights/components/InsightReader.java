package com.illumio.data.insights.components;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.insights.pojo.Insight;
import com.illumio.data.pojo.DecoratedFlow;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
@Slf4j
public class InsightReader {
    private final ObjectMapper objectMapper;

    public Mono<Insight> readValue(String value) {
        return Mono.fromCallable(() -> readValueFromString(value));
    }

    @SneakyThrows
    public Insight readValueFromString(String value) {
        try {
            return objectMapper.readValue(value, Insight.class);
        } catch (JsonProcessingException e) {
            log.error("Couldn't process json", e);
            throw e;
        }
    }
}
