package com.illumio.data.insights.configuration;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "insights")
@Data
public class InsightsConfiguration {
    private final KafkaCommonConfig kafkaCommonConfig;
    private final KafkaReceiverConfig kafkaReceiverConfig;
    private final List<DataCollectionConfig> dataCollectionConfigs;
    private final AzureConfig azureConfig;
    private final LogsIngestionApiConfig logsIngestionApiConfig;

    @Configuration
    @Getter
    @Setter
    public static class KafkaCommonConfig {
        private String bootstrapServers;
        private Boolean isSasl = false;
        private String saslJaasConfig;
    }

    @Configuration
    @Getter
    @Setter
    public static class KafkaReceiverConfig {
        private String topic;
        private String groupId;
        private String autoOffsetReset;
        private Integer requestTimeoutMs;
        private Integer maxPollRecords;
        private Integer maxPartitionFetchBytes;
        private Integer maxDeferredCommits = 100;
    }

    @Getter
    @Setter
    public static class DataCollectionConfig {
        private String dataCollectionEndpoint;
        private String dataCollectionRuleId;
        private String dataCollectionRuleStreamName;
    }

    @Configuration
    @Getter
    @Setter
    public static class AzureConfig {
        private String azureClientId;
        private String azureClientSecret;
        private String azureTenantId;
        private Boolean isManagedIdentity;
        private String managedIdentityClientId;
        private Boolean isWorkloadIdentity;
        private WorkloadIdentity workloadIdentity;

        @Configuration
        @Getter
        @Setter
        public static class WorkloadIdentity {
            private String clientId;
            private String tenantId;
            private String tokenFilePath;
        }
    }

    @Configuration
    @Getter
    @Setter
    public static class LogsIngestionApiConfig {
        private Integer maxConcurrency;
        private String httpLogLevelDetail = "basic";
    }
}
