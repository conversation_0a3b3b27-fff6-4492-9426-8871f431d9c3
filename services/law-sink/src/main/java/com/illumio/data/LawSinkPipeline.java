package com.illumio.data;

import com.illumio.data.components.DecoratedFlowReader;
import com.illumio.data.components.DecoratedFlowDCRRoundRobinSelector;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverRecord;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

@Slf4j
@Component
@RequiredArgsConstructor
public class LawSinkPipeline {

    private final KafkaReceiver<String, String> kafkaReceiver;
    private final DecoratedFlowReader decoratedFlowReader;
    private final DecoratedFlowDCRRoundRobinSelector decoratedFlowDCRRoundRobinSelector;

    private Disposable disposable;

    public void start() {
        this.disposable =
                kafkaReceiver
                        .receiveBatch()
                        .publishOn(Schedulers.boundedElastic())
                        .flatMap(this::processFlux)
                        .subscribe();
    }

    public Mono<Void> processFlux(Flux<ReceiverRecord<String, String>> receiverRecordBatch) {
        return receiverRecordBatch
                .doOnNext(
                        receiverRecord ->
                                log.info(
                                        "Processing offset: {}, key: {}, value: {}",
                                        receiverRecord.receiverOffset(),
                                        receiverRecord.key(),
                                        receiverRecord.value()))
                .flatMap(
                        receiverRecord ->
                                decoratedFlowReader
                                        .readValue(receiverRecord.value())
                                        .doOnError(
                                                throwable -> {
                                                    log.error(
                                                            "Couldn't parse offset: {}, value: {}",
                                                            receiverRecord.receiverOffset(),
                                                            receiverRecord.value(),
                                                            throwable);
                                                    receiverRecord.receiverOffset().acknowledge();
                                                    log.error(
                                                            "Acknowledged {} after error while parsing",
                                                            receiverRecord.receiverOffset());
                                                })
                                        .onErrorResume(throwable -> Mono.empty())
                                        .map(
                                                insight ->
                                                        Tuples.of(
                                                                insight,
                                                                receiverRecord.receiverOffset())))
                .collectList()
                .doOnNext(
                        tuple2s ->
                                log.info(
                                        "Received Batch Size: {} Batch: {}",
                                        tuple2s.size(),
                                        tuple2s.stream().map(Tuple2::getT1).toList()))
                .flatMap(
                        tuple2s ->
                                decoratedFlowDCRRoundRobinSelector
                                        .upload(tuple2s.stream().map(Tuple2::getT1).toList())
                                        .doOnSuccess(
                                                __ ->
                                                        tuple2s.forEach(
                                                                tuple -> {
                                                                    log.info(
                                                                            "Acknowledged {} after successfully processing",
                                                                            tuple.getT2());
                                                                    tuple.getT2().acknowledge();
                                                                })))
                .onErrorComplete();
    }

    public void stop() {
        this.disposable.dispose();
    }
}
