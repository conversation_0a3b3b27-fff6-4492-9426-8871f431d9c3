package com.illumio.data.components;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.pojo.DecoratedFlow;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
@Builder
@Slf4j
public class DecoratedFlowReader {
    private final ObjectMapper objectMapper;

    public Mono<DecoratedFlow> readValue(String value) {
        return Mono.fromCallable(() -> readValueFromString(value));
    }

    @SneakyThrows
    public DecoratedFlow readValueFromString(String value) {
        try {
            return objectMapper.readValue(value, DecoratedFlow.class);
        } catch (JsonProcessingException e) {
            log.error("Couldn't process json", e);
            throw e;
        }
    }
}
