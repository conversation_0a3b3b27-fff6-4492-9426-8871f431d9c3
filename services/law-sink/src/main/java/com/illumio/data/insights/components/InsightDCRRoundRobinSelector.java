package com.illumio.data.insights.components;

import com.azure.core.credential.TokenCredential;
import com.azure.core.http.policy.HttpLogDetailLevel;
import com.azure.core.http.policy.HttpLogOptions;
import com.azure.monitor.ingestion.LogsIngestionAsyncClient;
import com.azure.monitor.ingestion.LogsIngestionClientBuilder;
import com.azure.monitor.ingestion.models.LogsUploadOptions;
import com.illumio.data.components.DecoratedFlowDCRRoundRobinSelector;
import com.illumio.data.insights.configuration.InsightsConfiguration;
import com.illumio.data.insights.pojo.Insight;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.metrics.LongHistogram;
import io.opentelemetry.api.metrics.Meter;

import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * TODO Merge common code with @{@link com.illumio.data.components.DecoratedFlowDCRRoundRobinSelector}
 */
@Component
@Slf4j
public class InsightDCRRoundRobinSelector {

    private final AtomicInteger atomicInteger;
    private final List<ClientDataCollectionTuple> clients;
    private final LongHistogram responseTimeDoubleHistogram;
    private final LogsUploadOptions logsUploadOptions;

    public InsightDCRRoundRobinSelector(InsightsConfiguration insightsConfiguration,
                                        TokenCredential tokenCredential,
                                        OpenTelemetry openTelemetry) {
        this.atomicInteger = new AtomicInteger(0);
        this.clients = initializeClients(insightsConfiguration, tokenCredential);
        log.info("Initialized InsightDCRRoundRobinSelector with {} clients", clients.size());
        this.responseTimeDoubleHistogram = initializeHistogram(openTelemetry);
        this.logsUploadOptions = initializeLogsUploadOptions(insightsConfiguration);
    }

    private List<ClientDataCollectionTuple> initializeClients(InsightsConfiguration insightsConfiguration, TokenCredential tokenCredential) {
        List<ClientDataCollectionTuple> clientDataCollectionTupleList = new ArrayList<>();
        for (int i = 0; i < insightsConfiguration.getDataCollectionConfigs().size(); i++) {
            InsightsConfiguration.DataCollectionConfig dataCollectionConfig = insightsConfiguration.getDataCollectionConfigs().get(i);
            Objects.requireNonNull(dataCollectionConfig.getDataCollectionEndpoint(), "data-collection-configs.data-collection-endpoint must not be null. Please check index " + i);
            Objects.requireNonNull(dataCollectionConfig.getDataCollectionRuleId(), "data-collection-configs.data-collection-rule-id must not be null. Please check index " + i);
            Objects.requireNonNull(dataCollectionConfig.getDataCollectionRuleStreamName(), "data-collection-configs.data-collection-stream-name must not be null. Please check index " + i);
            LogsIngestionClientBuilder logsIngestionClientBuilder = new LogsIngestionClientBuilder();
            logsIngestionClientBuilder.endpoint(dataCollectionConfig.getDataCollectionEndpoint());
            logsIngestionClientBuilder.credential(tokenCredential);
            if (null != insightsConfiguration.getLogsIngestionApiConfig()
                    && null
                            != insightsConfiguration
                                    .getLogsIngestionApiConfig()
                                    .getHttpLogLevelDetail()) {
                logsIngestionClientBuilder.httpLogOptions(
                        new HttpLogOptions()
                                .setLogLevel(
                                        HttpLogDetailLevel.valueOf(
                                                insightsConfiguration
                                                        .getLogsIngestionApiConfig()
                                                        .getHttpLogLevelDetail()
                                                        .toUpperCase())));
            }
            final LogsIngestionAsyncClient asyncClient = logsIngestionClientBuilder.buildAsyncClient();
            clientDataCollectionTupleList.add(ClientDataCollectionTuple.builder()
                    .logsIngestionAsyncClient(asyncClient)
                    .dataCollectionConfig(dataCollectionConfig)
                    .build());
        }
        return clientDataCollectionTupleList;
    }

    private LongHistogram initializeHistogram(OpenTelemetry openTelemetry) {
        Meter meter = openTelemetry.getMeter("law_sink");
        return meter.histogramBuilder("insights_logs_ingestion_api_latency").ofLongs().build();
    }

    private LogsUploadOptions initializeLogsUploadOptions(InsightsConfiguration insightsConfiguration) {
        Integer maxConcurrency =  Optional.ofNullable(insightsConfiguration.getLogsIngestionApiConfig())
                .map(InsightsConfiguration.LogsIngestionApiConfig::getMaxConcurrency)
                .orElse(1);
        log.info("Initialized Insights logs ingestion api max concurrency to: {}", maxConcurrency);

        return new LogsUploadOptions()
                .setMaxConcurrency(maxConcurrency)
                .setLogsUploadErrorConsumer(
                        uploadLogsError -> {
                            log.warn(
                                    "Error uploading logs {}",
                                    uploadLogsError.getResponseException().getMessage());
                            log.warn(
                                    "Total logs failed to upload = {}",
                                    uploadLogsError.getFailedLogs().size());
                            // throw the exception here to abort uploading remaining logs
                            // throw uploadLogsError.getResponseException();
                        });
    }

    public Mono<Void> upload(List<Insight> insights) {
        return timeUpload(insights);
    }

    private Mono<Void> timeUpload(List<Insight> insights) {
        Instant start = Instant.now();
        return uploadInternal(insights)
                .doOnSuccess(__ -> responseTimeDoubleHistogram.record(
                Instant.now().toEpochMilli() - start.toEpochMilli()));
    }

    private Mono<Void> uploadInternal(List<Insight> insights) {
        ClientDataCollectionTuple clientDataCollectionTuple = getNext();
        LogsIngestionAsyncClient client = clientDataCollectionTuple.logsIngestionAsyncClient();
        List<Object> logs = new ArrayList<>(insights.size());
        logs.addAll(insights);
        return client.upload(clientDataCollectionTuple.dataCollectionConfig().getDataCollectionRuleId(),
                        clientDataCollectionTuple.dataCollectionConfig().getDataCollectionRuleStreamName(),
                        logs,
                        logsUploadOptions)
                .doOnSuccess(__ -> log.info(
                        "Uploaded log to DCE: {}, DCR Rule ID: {}, DCR Stream Name: {}, Size of Batch: {}",
                        clientDataCollectionTuple.dataCollectionConfig()
                                .getDataCollectionEndpoint(),
                        clientDataCollectionTuple.dataCollectionConfig()
                                .getDataCollectionRuleId(),
                        clientDataCollectionTuple.dataCollectionConfig()
                                .getDataCollectionRuleStreamName(),
                        insights.size()));
    }

    public ClientDataCollectionTuple getNext() {
        int index = atomicInteger.getAndIncrement() % clients.size();
        ClientDataCollectionTuple clientDataCollectionTuple = clients.get(index);
        return clientDataCollectionTuple;
    }

    @Builder
    public record ClientDataCollectionTuple(LogsIngestionAsyncClient logsIngestionAsyncClient,
                                             InsightsConfiguration.DataCollectionConfig dataCollectionConfig) {
    }
}
