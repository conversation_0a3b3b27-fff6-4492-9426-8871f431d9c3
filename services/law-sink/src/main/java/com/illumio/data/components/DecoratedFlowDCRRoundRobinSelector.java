package com.illumio.data.components;

import com.azure.core.credential.TokenCredential;
import com.azure.core.http.policy.HttpLogDetailLevel;
import com.azure.core.http.policy.HttpLogOptions;
import com.azure.monitor.ingestion.LogsIngestionAsyncClient;
import com.azure.monitor.ingestion.LogsIngestionClientBuilder;
import com.azure.monitor.ingestion.models.LogsUploadOptions;
import com.illumio.data.configuration.LawSinkConfiguration;
import com.illumio.data.pojo.DecoratedFlow;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.metrics.LongHistogram;
import io.opentelemetry.api.metrics.Meter;

import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * TODO Merge common code with {@link com.illumio.data.insights.components.InsightDCRRoundRobinSelector}
 */
@Component
@Slf4j
public class DecoratedFlowDCRRoundRobinSelector {

    private final AtomicInteger atomicInteger;
    private final List<ClientDataCollectionTuple> clients;
    private final LongHistogram responseTimeDoubleHistogram;
    private final LogsUploadOptions logsUploadOptions;

    public DecoratedFlowDCRRoundRobinSelector(LawSinkConfiguration lawSinkConfiguration,
                                              TokenCredential tokenCredential,
                                              OpenTelemetry openTelemetry) {
        this.atomicInteger = new AtomicInteger(0);
        this.clients = initializeClients(lawSinkConfiguration, tokenCredential);
        log.info("Initialized DecoratedFlowDCRRoundRobinSelector with {} clients", clients.size());
        this.responseTimeDoubleHistogram = initializeHistogram(openTelemetry);
        this.logsUploadOptions = initializeLogsUploadOptions(lawSinkConfiguration);
    }

    private List<ClientDataCollectionTuple> initializeClients(LawSinkConfiguration lawSinkConfiguration, TokenCredential tokenCredential) {
        List<ClientDataCollectionTuple> clientDataCollectionTupleList = new ArrayList<>();
        for (int i = 0; i < lawSinkConfiguration.getDataCollectionConfigs().size(); i++) {
            LawSinkConfiguration.DataCollectionConfig dataCollectionConfig = lawSinkConfiguration.getDataCollectionConfigs().get(i);
            Objects.requireNonNull(dataCollectionConfig.getDataCollectionEndpoint(), "data-collection-configs.data-collection-endpoint must not be null. Please check index " + i);
            Objects.requireNonNull(dataCollectionConfig.getDataCollectionRuleId(), "data-collection-configs.data-collection-rule-id must not be null. Please check index " + i);
            Objects.requireNonNull(dataCollectionConfig.getDataCollectionRuleStreamName(), "data-collection-configs.data-collection-stream-name must not be null. Please check index " + i);

            LogsIngestionClientBuilder logsIngestionClientBuilder = new LogsIngestionClientBuilder();
            logsIngestionClientBuilder.endpoint(dataCollectionConfig.getDataCollectionEndpoint());
            logsIngestionClientBuilder.credential(tokenCredential);
            if (null != lawSinkConfiguration.getLogsIngestionApiConfig()
                    && null
                            != lawSinkConfiguration
                                    .getLogsIngestionApiConfig()
                                    .getHttpLogLevelDetail()) {
                logsIngestionClientBuilder.httpLogOptions(
                        new HttpLogOptions()
                                .setLogLevel(
                                        HttpLogDetailLevel.valueOf(
                                                lawSinkConfiguration
                                                        .getLogsIngestionApiConfig()
                                                        .getHttpLogLevelDetail()
                                                        .toUpperCase())));
            }
            final LogsIngestionAsyncClient asyncClient = logsIngestionClientBuilder.buildAsyncClient();
            clientDataCollectionTupleList.add(ClientDataCollectionTuple.builder()
                    .logsIngestionAsyncClient(asyncClient)
                    .dataCollectionConfig(dataCollectionConfig)
                    .build());
        }
        return clientDataCollectionTupleList;
    }

    private LongHistogram initializeHistogram(OpenTelemetry openTelemetry) {
        Meter meter = openTelemetry.getMeter("law_sink");
        return meter.histogramBuilder("logs_ingestion_api_latency").ofLongs().build();
    }

    private LogsUploadOptions initializeLogsUploadOptions(LawSinkConfiguration lawSinkConfiguration) {
        Integer maxConcurrency =  Optional.ofNullable(lawSinkConfiguration.getLogsIngestionApiConfig())
                .map(LawSinkConfiguration.LogsIngestionApiConfig::getMaxConcurrency)
                .orElse(1);
        log.info("Initialized DecoratedFlows logs ingestion api max concurrency to: {}", maxConcurrency);

        return new LogsUploadOptions()
                .setMaxConcurrency(maxConcurrency)
                .setLogsUploadErrorConsumer(
                        uploadLogsError -> {
                            log.warn(
                                    "Error uploading logs {}",
                                    uploadLogsError.getResponseException().getMessage());
                            log.warn(
                                    "Total logs failed to upload = {}",
                                    uploadLogsError.getFailedLogs().size());
                            // throw the exception here to abort uploading remaining logs
                            // throw uploadLogsError.getResponseException();
                        });
    }

    public Mono<Void> upload(List<DecoratedFlow> decoratedFlows) {
        return timeUpload(decoratedFlows);
    }

    private Mono<Void> timeUpload(List<DecoratedFlow> decoratedFlows) {
        Instant start = Instant.now();
        return uploadInternal(decoratedFlows)
                .doOnSuccess(__ -> responseTimeDoubleHistogram.record(
                Instant.now().toEpochMilli() - start.toEpochMilli()));
    }

    private Mono<Void> uploadInternal(List<DecoratedFlow> decoratedFlows) {
        ClientDataCollectionTuple clientDataCollectionTuple = getNext();
        LogsIngestionAsyncClient client = clientDataCollectionTuple.logsIngestionAsyncClient();
        List<Object> logs = new ArrayList<>(decoratedFlows.size());
        logs.addAll(decoratedFlows);
        return client.upload(clientDataCollectionTuple.dataCollectionConfig().getDataCollectionRuleId(),
                        clientDataCollectionTuple.dataCollectionConfig().getDataCollectionRuleStreamName(),
                        logs,
                        logsUploadOptions)
                .doOnSuccess(__ -> log.info(
                        "Uploaded log to DCE: {}, DCR Rule ID: {}, DCR Stream Name: {}, Size of Batch: {}",
                        clientDataCollectionTuple.dataCollectionConfig()
                                .getDataCollectionEndpoint(),
                        clientDataCollectionTuple.dataCollectionConfig()
                                .getDataCollectionRuleId(),
                        clientDataCollectionTuple.dataCollectionConfig()
                                .getDataCollectionRuleStreamName(),
                        decoratedFlows.size()));
    }

    public ClientDataCollectionTuple getNext() {
        int index = atomicInteger.getAndIncrement() % clients.size();
        ClientDataCollectionTuple clientDataCollectionTuple = clients.get(index);
        return clientDataCollectionTuple;
    }

    @Builder
    public record ClientDataCollectionTuple(LogsIngestionAsyncClient logsIngestionAsyncClient,
                                             LawSinkConfiguration.DataCollectionConfig dataCollectionConfig) {
    }
}
