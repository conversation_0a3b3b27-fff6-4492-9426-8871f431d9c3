package com.illumio.data.insights.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Jacksonized
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class Insight {
    @JsonProperty("IllumioTenantId")
    private String IllumioTenantId;
    @JsonProperty("StartTime")
    private String StartTime;
    @JsonProperty("EndTime")
    private String EndTime;
    @JsonProperty("TrafficStatus")
    private String TrafficStatus;
    @JsonProperty("FlowCount")
    private Long FlowCount;
    @JsonProperty("RiskyFlowCount")
    private Long RiskyFlowCount;
    @JsonProperty("ByteCount")
    private Long ByteCount;
    @JsonProperty("RiskyByteCount")
    private Long RiskyByteCount;
    @JsonProperty("SourceCloud")
    private String SourceCloud;
    @JsonProperty("DestinationCloud")
    private String DestinationCloud;
    @JsonProperty("SourceRegion")
    private String SourceRegion;
    @JsonProperty("DestinationRegion")
    private String DestinationRegion;
    @JsonProperty("SourceAccount")
    private String SourceAccount;
    @JsonProperty("DestinationAccount")
    private String DestinationAccount;
    @JsonProperty("SourceOrg")
    private String SourceOrg;
    @JsonProperty("DestinationOrg")
    private String DestinationOrg;
    @JsonProperty("SourceAccountName")
    private String SourceAccountName;
    @JsonProperty("DestinationAccountName")
    private String DestinationAccountName;
    @JsonProperty("DestinationIPAddress")
    private String DestinationIPAddress;
    @JsonProperty("DestinationDomain")
    private String DestinationDomain;
    @JsonProperty("DestinationThreatLevel")
    private Integer DestinationThreatLevel;
    @JsonProperty("DestinationIsWellKnown")
    private String DestinationIsWellKnown;
}
