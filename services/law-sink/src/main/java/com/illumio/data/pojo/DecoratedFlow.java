package com.illumio.data.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Jacksonized
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class DecoratedFlow {
    @JsonProperty("SrcIP")
    private String SrcIP;
    @JsonProperty("SrcId")
    private String SrcId;
    @JsonProperty("DestIP")
    private String DestIP;
    @JsonProperty("DestId")
    private String DestId;
    @JsonProperty("Port")
    private Integer Port;
    @JsonProperty("Proto")
    private String Proto;
    @JsonProperty("SentBytes")
    private Long SentBytes;
    @JsonProperty("ReceivedBytes")
    private Long ReceivedBytes;
    @JsonProperty("IllumioTenantId")
    private String IllumioTenantId;
    @JsonProperty("SrcSubId")
    private String SrcSubId;
    @JsonProperty("SrcResId")
    private String SrcResId;
    @JsonProperty("SrcVnetId")
    private String SrcVnetId;
    @JsonProperty("SrcUserName")
    private String SrcUserName;
    @JsonProperty("DestSubId")
    private String DestSubId;
    @JsonProperty("DestResId")
    private String DestResId;
    @JsonProperty("DestVnetId")
    private String DestVnetId;
    @JsonProperty("DestUserName")
    private String DestUserName;
    @JsonProperty("SrcMetadata")
    private String SrcMetadata;
    @JsonProperty("DestMetadata")
    private String DestMetadata;
    @JsonProperty("SrcTenantId")
    private String SrcTenantId;
    @JsonProperty("SrcRegion")
    private String SrcRegion;
    @JsonProperty("DestTenantId")
    private String DestTenantId;
    @JsonProperty("DestRegion")
    private String DestRegion;
    @JsonProperty("CSSrcId")
    private String CSSrcId;
    @JsonProperty("CSDestId")
    private String CSDestId;
}
