package com.illumio.data.insights.configuration;

import lombok.RequiredArgsConstructor;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class InsightsKafkaReceiverConfiguration {
    private final InsightsConfiguration insightsConfiguration;

    @Bean
    public KafkaReceiver<String, String> insightsKafkaReceiver() {
        Map<String, Object> consumerProps = new HashMap<>();
        consumerProps.put(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                insightsConfiguration.getKafkaCommonConfig().getBootstrapServers());
        consumerProps.put(
                ConsumerConfig.GROUP_ID_CONFIG,
                insightsConfiguration.getKafkaReceiverConfig().getGroupId());
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(
                CommonClientConfigs.REQUEST_TIMEOUT_MS_CONFIG,
                insightsConfiguration.getKafkaReceiverConfig().getRequestTimeoutMs());
        consumerProps.put(
                ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,
                insightsConfiguration.getKafkaReceiverConfig().getAutoOffsetReset());
        if (null != insightsConfiguration.getKafkaCommonConfig().getIsSasl()
                && insightsConfiguration.getKafkaCommonConfig().getIsSasl()) {
            consumerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            consumerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            consumerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    insightsConfiguration.getKafkaCommonConfig().getSaslJaasConfig());
        }
        consumerProps.put(
                ConsumerConfig.MAX_POLL_RECORDS_CONFIG,
                insightsConfiguration.getKafkaReceiverConfig().getMaxPollRecords());
        consumerProps.put(
                ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG,
                insightsConfiguration.getKafkaReceiverConfig().getMaxPartitionFetchBytes());

        ReceiverOptions<String, String> receiverOptions =
                ReceiverOptions.<String, String>create(consumerProps)
                        .maxDeferredCommits(
                                insightsConfiguration
                                        .getKafkaReceiverConfig()
                                        .getMaxDeferredCommits())
                        .subscription(
                                Collections.singleton(
                                        insightsConfiguration.getKafkaReceiverConfig().getTopic()));

        return KafkaReceiver.create(receiverOptions);
    }
}
