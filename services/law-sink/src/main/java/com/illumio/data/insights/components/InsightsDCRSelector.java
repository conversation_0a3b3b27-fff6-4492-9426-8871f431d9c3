// Note this is unused. This is needed if a map of insight type to DCR is needed.
// We decided against this, and are using a single DCR and single LAW table to represent an insight with a wide schema.
/*
package com.illumio.data.insights.components;

import com.azure.core.credential.TokenCredential;
import com.azure.monitor.ingestion.LogsIngestionAsyncClient;
import com.azure.monitor.ingestion.LogsIngestionClientBuilder;
import com.azure.monitor.ingestion.models.LogsUploadOptions;
import com.illumio.data.insights.configuration.InsightsConfiguration;
import com.illumio.data.insights.pojo.Insight;

import io.opentelemetry.api.OpenTelemetry;

import lombok.Builder;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import reactor.core.publisher.Mono;

import java.util.*;

@Component
@Slf4j
public class InsightsDCRSelector {

    private final Map<String, ClientDataCollectionTuple> clients;
    private final LogsUploadOptions logsUploadOptions;

    public InsightsDCRSelector(
            InsightsConfiguration insightsConfiguration,
            TokenCredential tokenCredential,
            OpenTelemetry openTelemetry) {
        this.clients = initializeClients(insightsConfiguration, tokenCredential);
        log.info("Initialized InsightsDCRSelector with {} clients", clients.size());
        this.logsUploadOptions = initializeLogsUploadOptions(insightsConfiguration);
    }

    private Map<String, ClientDataCollectionTuple> initializeClients(
            InsightsConfiguration insightsConfiguration, TokenCredential tokenCredential) {
        Map<String, ClientDataCollectionTuple> clientDataCollectionTupleList = new HashMap<>();
        for (var entry : insightsConfiguration.getDataCollectionConfigs().entrySet()) {
            InsightsConfiguration.DataCollectionConfig dataCollectionConfig = entry.getValue();
            Objects.requireNonNull(
                    dataCollectionConfig.getDataCollectionEndpoint(),
                    "insights.data-collection-config-map.data-collection-endpoint must not be null. Please check key "
                            + entry.getKey());
            Objects.requireNonNull(
                    dataCollectionConfig.getDataCollectionRuleId(),
                    "insights.data-collection-config-map.data-collection-rule-id must not be null. Please check key "
                            + entry.getKey());
            Objects.requireNonNull(
                    dataCollectionConfig.getDataCollectionRuleStreamName(),
                    "insights.data-collection-config-map.data-collection-stream-name must not be null. Please check key "
                            + entry.getKey());
            final LogsIngestionAsyncClient asyncClient =
                    new LogsIngestionClientBuilder()
                            .endpoint(dataCollectionConfig.getDataCollectionEndpoint())
                            .credential(tokenCredential)
                            .buildAsyncClient();
            clientDataCollectionTupleList.put(
                    entry.getKey(),
                    ClientDataCollectionTuple.builder()
                            .logsIngestionAsyncClient(asyncClient)
                            .dataCollectionConfig(dataCollectionConfig)
                            .build());
        }
        return clientDataCollectionTupleList;
    }


    private LogsUploadOptions initializeLogsUploadOptions(
            InsightsConfiguration insightsConfiguration) {
        Integer maxConcurrency =
                Optional.ofNullable(insightsConfiguration.getLogsIngestionApiConfig())
                        .map(InsightsConfiguration.LogsIngestionApiConfig::getMaxConcurrency)
                        .orElse(1);
        log.info("Initialized insights logs ingestion api max concurrency to: {}", maxConcurrency);

        return new LogsUploadOptions()
                .setMaxConcurrency(maxConcurrency)
                .setLogsUploadErrorConsumer(
                        uploadLogsError -> {
                            log.warn(
                                    "Error uploading logs {}",
                                    uploadLogsError.getResponseException().getMessage());
                            log.warn(
                                    "Total logs failed to upload = {}",
                                    uploadLogsError.getFailedLogs().size());
                            // throw the exception here to abort uploading remaining logs
                            // throw uploadLogsError.getResponseException();
                        });
    }

    public Mono<Void> upload(Insight insight) {
        ClientDataCollectionTuple tuple2 = clients.get(insight.getInsightType());
        if (null == tuple2) {
            log.warn(
                    "InsightType {} not supported. "
                            + "Please check that insights.data-collection-config-map contains this insight type. "
                            + "Skipping ingestion of: {}",
                    insight.getInsightType(),
                    insight);
        }
        List<Object> logs = new ArrayList<>();
        logs.add(insight);
        return tuple2.logsIngestionAsyncClient()
                .upload(
                        tuple2.dataCollectionConfig().getDataCollectionRuleId(),
                        tuple2.dataCollectionConfig().getDataCollectionRuleStreamName(),
                        logs,
                        logsUploadOptions)
                .doOnSuccess(
                        __ ->
                                log.info(
                                        "Uploaded insight {} to DCE: {}, DCR Rule ID: {}, DCR Stream Name: {}",
                                        insight.getInsightType(),
                                        tuple2.dataCollectionConfig().getDataCollectionEndpoint(),
                                        tuple2.dataCollectionConfig().getDataCollectionRuleId(),
                                        tuple2.dataCollectionConfig()
                                                .getDataCollectionRuleStreamName()));
    }

    @Builder
    public record ClientDataCollectionTuple(
            LogsIngestionAsyncClient logsIngestionAsyncClient,
            InsightsConfiguration.DataCollectionConfig dataCollectionConfig) {}
}
*/
