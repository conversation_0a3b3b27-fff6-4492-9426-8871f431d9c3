package com.illumio.data;

import com.illumio.data.insights.components.InsightsPipeline;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ApplicationListener {
    private final LawSinkPipeline lawSinkPipeline;
    private final InsightsPipeline insightsPipeline;

    @EventListener(ApplicationReadyEvent.class)
    public void handleReadyEvent() {
        log.info("Starting law-sink consumer");
        lawSinkPipeline.start();
        log.info("Starting insights consumer");
        insightsPipeline.start();
    }

    @EventListener(ContextClosedEvent.class)
    public void handleClosedEvent() {
        log.info("Stopping consumers");
        lawSinkPipeline.stop();
        insightsPipeline.stop();
        log.info("Stopped consumers");
    }
}
