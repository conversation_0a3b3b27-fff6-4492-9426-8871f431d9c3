package com.illumio.data.configuration;

import com.azure.core.credential.TokenCredential;
import com.azure.identity.*;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Optional;

@Configuration
@RequiredArgsConstructor
public class AzureCredentialsConfig {
    private final LawSinkConfiguration lawSinkConfiguration;

    @Bean
    public TokenCredential tokenCredential() {
        if (Optional.ofNullable(lawSinkConfiguration.getAzureConfig())
                .map(LawSinkConfiguration.AzureConfig::getIsWorkloadIdentity)
                .orElse(false)) {
            TokenCredential tokenCredential = getWorkloadIdentity();
            return tokenCredential;
        } else if (Optional.ofNullable(lawSinkConfiguration.getAzureConfig())
            .map(LawSinkConfiguration.AzureConfig::getIsManagedIdentity)
            .orElse(false)) {
            TokenCredential tokenCredential = new ManagedIdentityCredentialBuilder()
                    .clientId(lawSinkConfiguration.getAzureConfig().getManagedIdentityClientId())
                    .build();
            return tokenCredential;
        } else {
            ClientSecretCredential clientSecretCredential =
                    new ClientSecretCredentialBuilder()
                            .clientId(lawSinkConfiguration.getAzureConfig().getAzureClientId())
                            .clientSecret(
                                    lawSinkConfiguration.getAzureConfig().getAzureClientSecret())
                            .tenantId(lawSinkConfiguration.getAzureConfig().getAzureTenantId())
                            .build();
            return clientSecretCredential;
        }
    }

    private TokenCredential getWorkloadIdentity() {
        LawSinkConfiguration.AzureConfig.WorkloadIdentity workloadIdentity =
                lawSinkConfiguration.getAzureConfig().getWorkloadIdentity();

        TokenCredential tokenCredential =
                new WorkloadIdentityCredentialBuilder()
                        .clientId(workloadIdentity.getClientId())
                        .tenantId(workloadIdentity.getTenantId())
                        .tokenFilePath(workloadIdentity.getTokenFilePath())
                        .build();
        return tokenCredential;
    }
}
