package com.illumio.data.insights.components;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverRecord;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

@Component
@RequiredArgsConstructor
@Slf4j
public class InsightsPipeline {
    private final KafkaReceiver<String, String> insightsKafkaReceiver;
    private final InsightReader insightReader;
    private final InsightDCRRoundRobinSelector insightDCRRoundRobinSelector;

    private Disposable disposable;

    public void start() {
        this.disposable =
                insightsKafkaReceiver
                        .receiveBatch()
                        .publishOn(Schedulers.boundedElastic())
                        .flatMap(this::processInsight)
                        .subscribe();
    }

    public Mono<Void> processInsight(Flux<ReceiverRecord<String, String>> receiverRecordBatch) {
        return receiverRecordBatch
                .doOnNext(
                        receiverRecord ->
                                log.info(
                                        "Processing offset: {}, key: {}, value: {}",
                                        receiverRecord.receiverOffset(),
                                        receiverRecord.key(),
                                        receiverRecord.value()))
                .flatMap(
                        receiverRecord ->
                                insightReader
                                        .readValue(receiverRecord.value())
                                        .doOnError(
                                                throwable -> {
                                                    log.error(
                                                            "Couldn't parse offset: {}, value: {}",
                                                            receiverRecord.receiverOffset(),
                                                            receiverRecord.value(),
                                                            throwable);
                                                    receiverRecord.receiverOffset().acknowledge();
                                                    log.error(
                                                            "Acknowledged {} after error while parsing",
                                                            receiverRecord.receiverOffset());
                                                })
                                        .onErrorResume(throwable -> Mono.empty())
                                        .map(
                                                insight ->
                                                        Tuples.of(
                                                                insight,
                                                                receiverRecord.receiverOffset())))
                .collectList()
                .flatMap(
                        tuple2s ->
                                insightDCRRoundRobinSelector
                                        .upload(tuple2s.stream().map(Tuple2::getT1).toList())
                                        .doOnSuccess(
                                                __ ->
                                                        tuple2s.forEach(
                                                                tuple -> {
                                                                    log.info(
                                                                            "Acknowledged {} after successfully processing",
                                                                            tuple.getT2());
                                                                    tuple.getT2().acknowledge();
                                                                })))
                .onErrorComplete();
    }

    public void stop() {
        this.disposable.dispose();
    }
}
