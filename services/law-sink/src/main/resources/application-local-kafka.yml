logging:
  level:
    ROOT: INFO
    org:
      apache:
        kafka: INFO

spring:
  application:
    name: law-sink
  output:
    ansi:
      enabled: ALWAYS

law-sink:
  kafka-common-config:
    bootstrapServers: localhost:9092
    isSasl: false
    saslJaasConfig: _DO_NOT_COMMIT_

  kafka-receiver-config:
    topic: illumio-decorated-flows
    groupId: decorated-flows-group-2
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880
    maxDeferredCommits: "100"

  data-collection-configs:
    - data-collection-endpoint: https://decoratedflow-dce-jcqd.eastus-1.ingest.monitor.azure.com
      data-collection-rule-id: dcr-dba3118aa7c64115a2abde797a8b9e76
      data-collection-rule-stream-name: Custom-DecoratedFlow_CL

  azure-config:
    azure-client-id: _DO_NOT_COMMIT_
    azure-client-secret: _DO_NOT_COMMIT_
    azure-tenant-id: _DO_NOT_COMMIT_

  logs-ingestion-api-config:
    max-concurrency: 1024
    httpLogLevelDetail: body_and_headers

insights:
  kafka-common-config:
    bootstrapServers: localhost:9092
    isSasl: false
    saslJaasConfig: _DO_NOT_COMMIT_

  kafka-receiver-config:
    topic: illumio-insights
    groupId: insights-group
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880
    maxDeferredCommits: "100"

  data-collection-configs:
    - data-collection-endpoint: https://insights-dce-w409.eastus-1.ingest.monitor.azure.com
      data-collection-rule-id: dcr-6865b95edd864a719198cb6cbbaf8e92
      data-collection-rule-stream-name: Custom-Test1

  logs-ingestion-api-config:
    max-concurrency: 1024
    httpLogLevelDetail: body_and_headers