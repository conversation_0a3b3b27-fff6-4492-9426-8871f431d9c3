logging:
  level:
    ROOT: INFO

spring:
  application:
    name: law-sink
  output:
    ansi:
      enabled: ALWAYS

law-sink:
  kafka-common-config:
    bootstrapServers: arch-eventhub.servicebus.windows.net:9093
    isSasl: true
    saslJaasConfig: _DO_NOT_COMMIT_

  kafka-receiver-config:
    topic: illumio-decorated-flows
    groupId: decorated-flows-group-2
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880

  data-collection-configs:
    - data-collection-endpoint: https://decoratedflow-dce-jcqd.eastus-1.ingest.monitor.azure.com
      data-collection-rule-id: dcr-dba3118aa7c64115a2abde797a8b9e76
      data-collection-rule-stream-name: Custom-DecoratedFlow_CL
    - data-collection-endpoint: https://decoratedflow-dce-jcqd.eastus-1.ingest.monitor.azure.com
      data-collection-rule-id: dcr-61e10003d5f04657b0ed6f8e368c56a2
      data-collection-rule-stream-name: Custom-DecoratedFlow_CL

  azure-config:
    azure-client-id: _DO_NOT_COMMIT_
    azure-client-secret: _DO_NOT_COMMIT_
    azure-tenant-id: _DO_NOT_COMMIT_
    is-workload-identity: false
    workload_identity:
      client-id: client
      tenant-id: tenant
      token-file-path: /foo/bar.txt

  logs-ingestion-api-config:
    max-concurrency: 1024