package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import reactor.test.StepVerifier;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.*;

class DecoratedFlowReaderTest {
    ObjectMapper objectMapper = new ObjectMapper();
    DecoratedFlowReader decoratedFlowReader;

    @BeforeEach
    void setup() {
        decoratedFlowReader = new DecoratedFlowReader(objectMapper);
    }

    @SneakyThrows
    public static String readFromStream(InputStream inputStream) {
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        for (int length; (length = inputStream.read(buffer)) != -1; ) {
            result.write(buffer, 0, length);
        }
        // StandardCharsets.UTF_8.name() > JDK 7
        return result.toString("UTF-8");
    }

    @Test
    void parsesValidJson() {
        String json = readFromStream(
                Objects.requireNonNull(
                        this.getClass()
                                .getClassLoader()
                                .getResourceAsStream("unit-test-examples/DecoratedFlow.json")));

        StepVerifier.create(decoratedFlowReader.readValue(json))
                .expectSubscription()
                .assertNext(decoratedFlow -> {
                    assertEquals("********", decoratedFlow.getSrcIP());
                    assertEquals("********", decoratedFlow.getDestIP());
                })
                .verifyComplete();
    }

    @Test
    void parsesInvalidJson() {
        StepVerifier.create(decoratedFlowReader.readValue("invalid json"))
                .expectSubscription()
                .expectError()
                .verify();
    }
}