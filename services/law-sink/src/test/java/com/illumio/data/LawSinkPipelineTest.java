package com.illumio.data;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.DecoratedFlowReader;
import com.illumio.data.components.DecoratedFlowDCRRoundRobinSelector;
import lombok.SneakyThrows;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOffset;
import reactor.kafka.receiver.ReceiverRecord;
import reactor.test.StepVerifier;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.Collections;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class LawSinkPipelineTest {
    @Mock
    KafkaReceiver<String, String> kafkaReceiver;

    DecoratedFlowReader decoratedFlowReader;

    ObjectMapper objectMapper;

    LawSinkPipeline lawSinkPipeline;

    @Mock
    DecoratedFlowDCRRoundRobinSelector decoratedFlowDCRRoundRobinSelector;

    @Mock
    ReceiverOffset receiverOffset;

    @SneakyThrows
    public static String readFromStream(InputStream inputStream) {
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        for (int length; (length = inputStream.read(buffer)) != -1; ) {
            result.write(buffer, 0, length);
        }
        // StandardCharsets.UTF_8.name() > JDK 7
        return result.toString("UTF-8");
    }


    @BeforeEach
    void setup() {
        objectMapper = new ObjectMapper();
        decoratedFlowReader = DecoratedFlowReader.builder()
                .objectMapper(objectMapper)
                .build();
        lawSinkPipeline = new LawSinkPipeline(kafkaReceiver, decoratedFlowReader, decoratedFlowDCRRoundRobinSelector);
    }

    @Test
    void testInvalidRecordValue() {
        ConsumerRecord<String, String> consumerRecord = new ConsumerRecord<>(
                "topic", 0, 0, "key", "value");
        ReceiverRecord<String, String> receiverRecord = new ReceiverRecord<>(consumerRecord, receiverOffset);
        Flux<ReceiverRecord<String, String>> flux = Flux.fromIterable(Collections.singletonList(receiverRecord));
        StepVerifier.create(Mono.just(consumerRecord)
                .flatMap(r -> lawSinkPipeline.processFlux(flux))
        )
                .expectSubscription()
                .expectNextCount(0)
                .verifyComplete();
    }

    @Test
    void testEmptyJson() {
        when(decoratedFlowDCRRoundRobinSelector.upload(any())).thenReturn(Mono.empty());
        ConsumerRecord<String, String> consumerRecord = new ConsumerRecord<>(
                "topic", 0, 0, "key", "{}");
        ReceiverRecord<String, String> receiverRecord = new ReceiverRecord<>(consumerRecord, receiverOffset);
        Flux<ReceiverRecord<String, String>> flux = Flux.fromIterable(Collections.singletonList(receiverRecord));
        StepVerifier.create(Mono.just(consumerRecord)
                        .flatMap(r -> lawSinkPipeline.processFlux(flux))
                )
                .expectSubscription()
                .expectNextCount(0)
                .verifyComplete();
    }

    @Test
    void testValidJson() {
        String valueJson = readFromStream(Objects.requireNonNull(this.getClass()
                .getClassLoader()
                .getResourceAsStream("unit-test-examples/DecoratedFlow.json")));
        when(decoratedFlowDCRRoundRobinSelector.upload(any())).thenReturn(Mono.empty());
        ConsumerRecord<String, String> consumerRecord = new ConsumerRecord<>(
                "topic", 0, 0, "key", valueJson);
        ReceiverRecord<String, String> receiverRecord = new ReceiverRecord<>(consumerRecord, receiverOffset);
        Flux<ReceiverRecord<String, String>> flux = Flux.fromIterable(Collections.singletonList(receiverRecord));
        StepVerifier.create(Mono.just(consumerRecord)
                        .flatMap(r -> lawSinkPipeline.processFlux(flux))
                )
                .expectSubscription()
                .expectNextCount(0)
                .verifyComplete();
    }
}