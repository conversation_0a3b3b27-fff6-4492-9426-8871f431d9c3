package com.illumio.data.components;

import static org.junit.jupiter.api.Assertions.*;

import com.azure.core.credential.TokenCredential;
import com.illumio.data.configuration.LawSinkConfiguration;

import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.OpenTelemetry;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

@ExtendWith(MockitoExtension.class)
class DecoratedFlowDCRRoundRobinSelectorTest {
    TokenCredential tokenCredential = tokenRequestContext -> null;;
    OpenTelemetry openTelemetry = GlobalOpenTelemetry.get();
    

    @Test
    void testWithTwoDCRs() {
        var dcr1 = new LawSinkConfiguration.DataCollectionConfig();
        dcr1.setDataCollectionEndpoint("https://dce1.ingest.monitor.azure.com");
        dcr1.setDataCollectionRuleId("1");
        dcr1.setDataCollectionRuleStreamName("1");

        var dcr2 = new LawSinkConfiguration.DataCollectionConfig();
        dcr2.setDataCollectionEndpoint("https://dce2.ingest.monitor.azure.com");
        dcr2.setDataCollectionRuleId("2");
        dcr2.setDataCollectionRuleStreamName("2");

        List<LawSinkConfiguration.DataCollectionConfig> dataCollectionConfigs = List.of(
                dcr1, dcr2
        );
        LawSinkConfiguration lawSinkConfiguration = new LawSinkConfiguration(
                null,
                null,
                dataCollectionConfigs,
                null,
                null

        );
        DecoratedFlowDCRRoundRobinSelector decoratedFlowDCRRoundRobinSelector = new DecoratedFlowDCRRoundRobinSelector(lawSinkConfiguration,
                tokenCredential, openTelemetry);

        var clientDataCollectionTuple = decoratedFlowDCRRoundRobinSelector.getNext();
        assertEquals("https://dce1.ingest.monitor.azure.com", clientDataCollectionTuple.dataCollectionConfig().getDataCollectionEndpoint());

        clientDataCollectionTuple = decoratedFlowDCRRoundRobinSelector.getNext();
        assertEquals("https://dce2.ingest.monitor.azure.com", clientDataCollectionTuple.dataCollectionConfig().getDataCollectionEndpoint());

        clientDataCollectionTuple = decoratedFlowDCRRoundRobinSelector.getNext();
        assertEquals("https://dce1.ingest.monitor.azure.com", clientDataCollectionTuple.dataCollectionConfig().getDataCollectionEndpoint());

        clientDataCollectionTuple = decoratedFlowDCRRoundRobinSelector.getNext();
        assertEquals("https://dce2.ingest.monitor.azure.com", clientDataCollectionTuple.dataCollectionConfig().getDataCollectionEndpoint());
    }

    @Test
    void testWithThreeDCRs() {
        var dcr1 = new LawSinkConfiguration.DataCollectionConfig();
        dcr1.setDataCollectionEndpoint("https://dce1.ingest.monitor.azure.com");
        dcr1.setDataCollectionRuleId("1");
        dcr1.setDataCollectionRuleStreamName("1");

        var dcr2 = new LawSinkConfiguration.DataCollectionConfig();
        dcr2.setDataCollectionEndpoint("https://dce2.ingest.monitor.azure.com");
        dcr2.setDataCollectionRuleId("2");
        dcr2.setDataCollectionRuleStreamName("2");

        var dcr3 = new LawSinkConfiguration.DataCollectionConfig();
        dcr3.setDataCollectionEndpoint("https://dce3.ingest.monitor.azure.com");
        dcr3.setDataCollectionRuleId("3");
        dcr3.setDataCollectionRuleStreamName("3");

        List<LawSinkConfiguration.DataCollectionConfig> dataCollectionConfigs = List.of(
               dcr1, dcr2, dcr3
        );
        LawSinkConfiguration lawSinkConfiguration = new LawSinkConfiguration(
                null,
                null,
                dataCollectionConfigs,
                null,
                null
        );
        DecoratedFlowDCRRoundRobinSelector decoratedFlowDCRRoundRobinSelector = new DecoratedFlowDCRRoundRobinSelector(lawSinkConfiguration,
                tokenCredential, openTelemetry);

        var clientDataCollectionTuple = decoratedFlowDCRRoundRobinSelector.getNext();
        assertEquals("https://dce1.ingest.monitor.azure.com", clientDataCollectionTuple.dataCollectionConfig().getDataCollectionEndpoint());

        clientDataCollectionTuple = decoratedFlowDCRRoundRobinSelector.getNext();
        assertEquals("https://dce2.ingest.monitor.azure.com", clientDataCollectionTuple.dataCollectionConfig().getDataCollectionEndpoint());

        clientDataCollectionTuple = decoratedFlowDCRRoundRobinSelector.getNext();
        assertEquals("https://dce3.ingest.monitor.azure.com", clientDataCollectionTuple.dataCollectionConfig().getDataCollectionEndpoint());

        clientDataCollectionTuple = decoratedFlowDCRRoundRobinSelector.getNext();
        assertEquals("https://dce1.ingest.monitor.azure.com", clientDataCollectionTuple.dataCollectionConfig().getDataCollectionEndpoint());

        clientDataCollectionTuple = decoratedFlowDCRRoundRobinSelector.getNext();
        assertEquals("https://dce2.ingest.monitor.azure.com", clientDataCollectionTuple.dataCollectionConfig().getDataCollectionEndpoint());

        clientDataCollectionTuple = decoratedFlowDCRRoundRobinSelector.getNext();
        assertEquals("https://dce3.ingest.monitor.azure.com", clientDataCollectionTuple.dataCollectionConfig().getDataCollectionEndpoint());
    }
}