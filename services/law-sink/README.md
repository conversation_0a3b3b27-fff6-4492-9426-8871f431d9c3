# LAW (Log Analytics Workspace) Sink Connector
A sink data connector that receives data from event hub, and sends data to LAW.

# Running locally
1. Modify application.yaml
    1. Data Collection Config
    2. Kafka Receiver Config
2. Create src/main/resources/secrets.yml and fill in secrets:
```yaml
law-sink:
  kafka-common-config:
    saslJaasConfig: _DO_NOT_COMMIT_
  azure-config:
    azure-client-id: _DO_NOT_COMMIT_
    azure-client-secret: _DO_NOT_COMMIT_
    azure-tenant-id: _DO_NOT_COMMIT_

```
3. Use IntelliJ Run Configuration:
- law-sink/LawSinkApplication