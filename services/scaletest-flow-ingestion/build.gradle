plugins {
    id 'org.springframework.boot'
    id 'com.google.cloud.tools.jib'
}

dependencies {
    implementation project(":commons:azure-commons")
    implementation project(":commons:utility-commons")
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'com.azure:azure-storage-blob'
    implementation("com.azure:azure-identity")
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    implementation 'com.azure:azure-core-amqp'
}

apply from: "${project.rootDir}/gradle/scripts/jib-build.gradle"