logging:
  level:
    ROOT: INFO

spring:
  application:
    name: scaletest-flow-ingestion
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive
server:
  port: 8081

scaletest-flow-ingestion:

  azure-config:
    azure-client-id: _DO_NOT_COMMIT_
    azure-client-secret: _DO_NOT_COMMIT_
    azure-tenant-id: tenant
    subscription-id: _DO_NOT_COMMIT_
    resource-grp: _DO_NOT_COMMIT_

  run-config:
    nsg-log-config:
      enabled: true
      append-enabled: false
      scale-config:
        ingest-size-mb: 1
    flow-log-config:
      enabled: false
      append-enabled: false
      scale-config:
        ingest-size-mb: 1

  storage-account-endpoint: https://inglogtestnav1.blob.core.windows.net/
  target-container-name: ""
  file-input:
    nsg-file-class-path: files/00h-nsg-PT1H.txt
    flow-file-class-path: files/23h-flow-1-PT1H.txt
    create-files-folder-path: files/create-folder
    test-files-folder-name: files
    nsg-append-folder-path: files/append-folder/nsg
    flow-append-folder-path: files/append-folder/flow

  target-config:
    flow-log-container-name: insights-logs-flowlogflowevent
    nsg-log-container-name: insights-logs-networksecuritygroupflowevent
    file-name: PT1H.json
