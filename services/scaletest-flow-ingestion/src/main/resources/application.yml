logging:
  level:
    ROOT: INFO

spring:
  application:
    name: scaletest-flow-ingestion
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive
server:
  port: 8081

scaletest-flow-ingestion:

  azure-config:
    azure-client-id: _DO_NOT_COMMIT_
    azure-client-secret: _DO_NOT_COMMIT_
    azure-tenant-id: _DO_NOT_COMMIT_
    subscription-id: fd72c36d-cdc1-4fe9-9c31-13f3e670b601
    resource-grp: arch-log-ing-rg

  run-config:
    nsg-log-config:
      enabled: true
      append-enabled: true
      scale-config:
        ingest-size-mb: 5
    flow-log-config:
      enabled: true
      append-enabled: true
      scale-config:
        ingest-size-mb: 5

  storage-account-endpoint: https://ingestlogscaletest.blob.core.windows.net/
  target-container-name: testcontainer
  file-input:
    nsg-file-class-path: files/create-folder/00h-nsg-PT1H.txt
    flow-file-class-path: files/create-folder/23h-flow-1-PT1H.txt
    create-files-folder-path: files/create-folder
    test-files-folder-name: files
    nsg-append-folder-path: files/append-folder/nsg
    flow-append-folder-path: files/append-folder/flow

  target-config:
    flow-log-container-name: insights-logs-flowlogflowevent
    nsg-log-container-name: insights-logs-networksecuritygroupflowevent
    file-name: PT1H.json