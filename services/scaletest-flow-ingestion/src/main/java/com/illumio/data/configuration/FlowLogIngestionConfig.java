package com.illumio.data.configuration;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import static com.illumio.data.util.Constants.FLOW_LOG_INGESTION_CONFIG_PREFIX;

@Configuration
@ConfigurationProperties(prefix = FLOW_LOG_INGESTION_CONFIG_PREFIX)
@Data
@Getter
@Setter
public class FlowLogIngestionConfig {
    private String targetContainerName;
    private String storageAccountEndpoint;
    private final FileInput fileInput;
    private final AzureConfig azureConfig;
    private final TargetConfig targetConfig;
    private final RunConfig runConfig;

    @Configuration
    @Getter
    @Setter
    public static class FileInput {
        private String nsgFileClassPath;
        private String flowFileClassPath;
        private String testFilesFolderName;
        private String createFilesFolderPath;
        private String nsgAppendFolderPath;
        private String flowAppendFolderPath;
    }

    @Configuration
    @Getter
    @Setter
    public static class AzureConfig {
        private String azureClientId;
        private String azureClientSecret;
        private String azureTenantId;
        private String subscriptionId;
        private String resourceGrp;
    }

    @Configuration
    @Getter
    @Setter
    public static class RunConfig {
        private NSGLogConfig  nsgLogConfig;
        private FlowLogConfig flowLogConfig;
    }

    @Configuration
    @Getter
    @Setter
    public static class NSGLogConfig{
        private boolean enabled;
        private boolean appendEnabled;
        private ScaleConfig scaleConfig;
    }

    @Configuration
    @Getter
    @Setter
    public static class FlowLogConfig {
        private boolean enabled;
        private boolean appendEnabled;
        private ScaleConfig scaleConfig;
    }

    @Configuration
    @Getter
    @Setter
    public static class ScaleConfig {
        private double ingestSizeMb;
    }

    @Configuration
    @Getter
    @Setter
    public static class TargetConfig {
        private String flowLogContainerName;
        private String nsgLogContainerName;
        private String fileName;
    }
}
