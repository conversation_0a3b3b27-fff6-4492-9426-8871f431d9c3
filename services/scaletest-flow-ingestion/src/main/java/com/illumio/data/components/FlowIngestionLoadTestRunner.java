package com.illumio.data.components;

import com.illumio.data.FileUtil;
import com.illumio.data.configuration.FlowLogIngestionConfig;
import com.illumio.data.util.RandomMacAddrGenerator;
import com.illumio.data.util.TimePartitionedPathGenerator;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import reactor.core.Disposable;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@AllArgsConstructor
public class FlowIngestionLoadTestRunner implements Runnable {

    private AzureBlobUploadService azureBlobUploadService;
    private FlowLogIngestionConfig flowLogIngestionConfig;
    private String sourceFilePath;
    private Disposable disposable;

    @Override
    public void run() {
        cycle();
    }

    public void stop() {
        this.disposable.dispose();
    }

    // file upload initiator - the scope of this method is TBD
    public void cycle() {

        final Resource nsgAppendFileFolderResource =
                new ClassPathResource(
                        flowLogIngestionConfig.getFileInput().getNsgAppendFolderPath());
        final Resource flowAppendFileFolderResource =
                new ClassPathResource(
                        flowLogIngestionConfig.getFileInput().getFlowAppendFolderPath());

        // while(true) {
        // sourceFilePaths.parallelStream().forEach(sourceFilePath -> {

        if (sourceFilePath.contains("nsg")) {
            String nsgTargetFilePath = generateNSGTargetFilePath();
            azureBlobUploadService.uploadFile(
                    sourceFilePath,
                    nsgTargetFilePath,
                    flowLogIngestionConfig.getTargetConfig().getNsgLogContainerName());
            log.info("Uploaded NSG file {}", nsgTargetFilePath);
            try {
                TimeUnit.SECONDS.sleep(2);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            if (flowLogIngestionConfig.getRunConfig().getNsgLogConfig().isAppendEnabled()) {
                try {
                    List<String> nsgFilePaths =
                            FileUtil.readFolderPath(nsgAppendFileFolderResource.getURI().getPath());
                    for (String nsgFilePath : nsgFilePaths) {

                        azureBlobUploadService.uploadFile(
                                nsgFilePath,
                                nsgTargetFilePath,
                                flowLogIngestionConfig.getTargetConfig().getNsgLogContainerName());
                        log.info("Appended NSG file {}", nsgFilePath);
                    }

                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        } else if (sourceFilePath.contains("flow")) {

            String flowTargetFilePath = generateFlowLogTargetFilePath();
            azureBlobUploadService.uploadFile(
                    sourceFilePath,
                    flowTargetFilePath,
                    flowLogIngestionConfig.getTargetConfig().getFlowLogContainerName());
            log.info("Uploaded FLOW file {}", flowTargetFilePath);

            try {
                if (flowLogIngestionConfig.getRunConfig().getFlowLogConfig().isAppendEnabled()) {
                    List<String> flowFilePaths =
                            FileUtil.readFolderPath(
                                    flowAppendFileFolderResource.getURI().getPath());
                    for (String flowFilePath : flowFilePaths) {
                        azureBlobUploadService.uploadFile(
                                flowFilePath,
                                flowTargetFilePath,
                                flowLogIngestionConfig.getTargetConfig().getFlowLogContainerName());
                        log.info("Appended Flow file {}", flowFilePath);
                    }
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
        // });
        // }
    }

    static String readFile(String path) throws IOException {
        byte[] encoded = Files.readAllBytes(Paths.get(path));
        return new String(encoded, StandardCharsets.UTF_8);
    }

    public String generateTargetFilePath() {
        return TimePartitionedPathGenerator.generateTimePartitionedPathStructure()
                + "/macAddress="
                + RandomMacAddrGenerator.generateRandomMACAddress()
                + "/"
                + flowLogIngestionConfig.getTargetConfig().getFileName();
    }

    public String generateNSGTargetFilePath() {
        return "resourceId=/SUBSCRIPTIONS/"
                + flowLogIngestionConfig.getAzureConfig().getSubscriptionId()
                + "/RESOURCEGROUPS/"
                + flowLogIngestionConfig.getAzureConfig().getResourceGrp()
                + "/PROVIDERS/MICROSOFT.NETWORK/NETWORKSECURITYGROUPS/ABC-DEV-NSG-3-4/"
                + generateTargetFilePath();
    }

    public String generateFlowLogTargetFilePath() {
        return "flowLogResourceID=/"
                + flowLogIngestionConfig.getAzureConfig().getSubscriptionId()
                + "_NETWORKWATCHERRG/NETWORKWATCHER_EASTUS_VNETFLOWLOGTEST-ABC-TEST-FLOWLOG/"
                + generateTargetFilePath();
    }
}
