package com.illumio.data.components;

import com.illumio.data.FileUtil;
import com.illumio.data.configuration.FlowLogIngestionConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import reactor.core.Disposable;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.IntStream;

@Slf4j
@Service
public class FlowIngestionLoadProducer implements CommandLineRunner {

    private final AzureBlobUploadService azureBlobUploadService;
    private final ExecutorService executorService;
    private final int numberOfProcessors;
    private final FlowLogIngestionConfig flowLogIngestionConfig;
    private Disposable disposable;

    @Autowired
    public FlowIngestionLoadProducer(AzureBlobUploadService azureBlobUploadService,
                                     FlowLogIngestionConfig flowLogIngestionConfig){
        this.azureBlobUploadService = azureBlobUploadService;
        this.flowLogIngestionConfig = flowLogIngestionConfig;
        numberOfProcessors = Runtime.getRuntime().availableProcessors() -1;// Leaving one processor for os related tasks

        //executorService = Executors.newFixedThreadPool(numberOfProcessors * 10) ;
        // Create a queue to hold up to 10,000 tasks
        BlockingQueue<Runnable> taskQueue = new ArrayBlockingQueue<>(10000);

        // Define a RejectionHandler to handle tasks when the queue is full
        RejectedExecutionHandler rejectionHandler = new ThreadPoolExecutor.AbortPolicy(); // This will throw an exception when the queue is full

        executorService = new ThreadPoolExecutor(
                100,        // Core thread pool size
                1000000,         // Maximum thread pool size
                60L,       // Keep alive time for idle threads
                TimeUnit.MILLISECONDS, // Time unit for keep alive time
                taskQueue,           // Task queue with a capacity of 10,000
                rejectionHandler
        );

    }

    @Override
    public void run(String... args) throws Exception{
        generateFiles();
    }

    private void generateFiles() {

        final Resource createFileFolderResource = new ClassPathResource(flowLogIngestionConfig.getFileInput().getCreateFilesFolderPath());

        try {
            List<String> filePaths = FileUtil.readFolderPath(createFileFolderResource.getURI().getPath());
            for (String filePath: filePaths) {
                double fileSizeInKb = (double) (Files.size(Path.of(filePath)) / 1024);
                if (filePath.contains("nsg") && flowLogIngestionConfig.getRunConfig().getNsgLogConfig().isEnabled()) {
                    double numberOfNSGFilesToIngest =
                            (flowLogIngestionConfig.getRunConfig().getNsgLogConfig().getScaleConfig().getIngestSizeMb() *1024) / fileSizeInKb;
                    submitTask(numberOfNSGFilesToIngest, filePath);
                } else if (filePath.contains("flow") && flowLogIngestionConfig.getRunConfig().getFlowLogConfig().isEnabled()) {
                    double numberOfFlowFilesToIngest =
                            (flowLogIngestionConfig.getRunConfig().getFlowLogConfig().getScaleConfig().getIngestSizeMb() * 1024) / fileSizeInKb;
                    submitTask(numberOfFlowFilesToIngest, filePath);
                }
            }

            // Shutdown the executor and wait for all tasks to complete
            executorService.shutdown();
            executorService.awaitTermination(5, TimeUnit.MINUTES);  // Adjust timeout based on your needs
            System.exit(0);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (InterruptedException e) {
            log.error("InterruptedException {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    private void submitTask(double numOfTasks, String filePath) {
        IntStream.range(0, (int) numOfTasks).forEach(i -> {
            log.info("submit task {}", i);
            Runnable task = new FlowIngestionLoadTestRunner(azureBlobUploadService, flowLogIngestionConfig,
                    filePath, disposable);
            executorService.submit(task);
        });
        log.info("completed task submission {}", numOfTasks);
    }
}
