package com.illumio.data.util;

import java.util.Random;

public class RandomMacAddrGenerator {

    public static String generateRandomMACAddress() {
        Random random = new Random();

        // Create a byte array of 6 bytes for the MAC address
        byte[] macAddr = new byte[6];

        // Generate random bytes for the MAC address
        random.nextBytes(macAddr);

        // To make sure it's a unicast MAC address (first byte's least significant bit should be 0)
        macAddr[0] = (byte) (macAddr[0] & (byte) 254);

        // Convert byte array to MAC address format without colons
        StringBuilder macAddress = new StringBuilder();
        for (byte b : macAddr) {
            macAddress.append(String.format("%02X", b)); // No colon separator
        }

        return macAddress.toString();
    }
}
