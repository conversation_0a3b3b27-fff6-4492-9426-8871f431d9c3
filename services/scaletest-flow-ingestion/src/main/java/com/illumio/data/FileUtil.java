package com.illumio.data;

import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Stream;

@Slf4j
@UtilityClass
public class FileUtil {

    @Autowired
    private ResourceLoader resourceLoader;

    @SneakyThrows
    public File getFileFromClassPath(final String classPath) {
        if (null != classPath) {
            final URL fileURL = FileUtil.class.getResource("");

            if (null == fileURL) {
                log.info("Couldn't find file at class path {}", classPath);
            } else {
                return new File(classPath);
            }
        }

        return null;
    }

    public List<String> readFolderPath(String folderPath) {

        List<String> filePaths = new LinkedList<>();

        try (Stream<Path> paths = Files.walk(Paths.get(folderPath))) {
            paths.filter(Files::isRegularFile) // Filter only regular files (not directories)
                    .forEach(path -> filePaths.add(path.toString()));
        } catch (IOException e) {
            log.error("Couldn't read from input folder {}", folderPath , e);
            throw new RuntimeException(e);
        }

        return filePaths;
    }
}
