package com.illumio.data.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class TimePartitionedPathGenerator {

    public static String generateTimePartitionedPathStructure() {

        // Get the current date and time
        LocalDateTime now = LocalDateTime.now();

        // Create a formatter for the directory structure
        DateTimeFormatter yearFormatter = DateTimeFormatter.ofPattern("yyyy");
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("MM");
        DateTimeFormatter dayFormatter = DateTimeFormatter.ofPattern("dd");
        DateTimeFormatter hourFormatter = DateTimeFormatter.ofPattern("HH");
        DateTimeFormatter minuteFormatter = DateTimeFormatter.ofPattern("mm");

        // Generate the directory structure
        String yearDir = now.format(yearFormatter);
        String monthDir = now.format(monthFormatter);
        String dayDir = now.format(dayFormatter);
        String hourDir = now.format(hourFormatter);
        String minuteDir = now.format(minuteFormatter);

        // Combine to form the full directory structure
        return  "y=" + yearDir + "/m=" + monthDir + "/d=" + dayDir + "/h=" + hourDir + "/m=00" ;
    }
}
