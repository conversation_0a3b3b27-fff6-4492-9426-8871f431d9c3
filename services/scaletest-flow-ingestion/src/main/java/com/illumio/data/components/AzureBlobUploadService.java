package com.illumio.data.components;

import com.azure.storage.blob.*;
import com.azure.storage.blob.models.ParallelTransferOptions;
import com.azure.storage.blob.options.BlobUploadFromFileOptions;
import com.illumio.data.configuration.AzureBlobClientService;
import com.illumio.data.configuration.FlowLogIngestionConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class AzureBlobUploadService {

    private final AzureBlobClientService azureBlobServiceClient;
    private final FlowLogIngestionConfig flowLogIngestionConfig;

    public void uploadFile(String sourceFilePath, String targetFileName, String containerName) {

        BlobAsyncClient blobClient = getBlobAsyncClient(targetFileName, containerName);

        BlobUploadFromFileOptions blobUploadFromFileOptions = new BlobUploadFromFileOptions(sourceFilePath);
        log.info("Uploading to {} {}", targetFileName, containerName);
        final ParallelTransferOptions parallelTransferOptions = new ParallelTransferOptions()
                .setBlockSizeLong((long) (4 * 1024 * 1024));
        blobUploadFromFileOptions.setParallelTransferOptions(parallelTransferOptions);
        blobClient.uploadFromFileWithResponse(blobUploadFromFileOptions)
                .subscribe(
                        success -> log.info("Successfully uploaded sourceFilePath={} to target storage account.", sourceFilePath),
                        error-> log.error("Error occurred while uploading file {}: ", error.getMessage())
                );
    }

    private BlobAsyncClient getBlobAsyncClient(String targetFileName, String containerName) {
        final BlobServiceAsyncClient blobServiceAsyncClient = azureBlobServiceClient.getBlobServiceAsyncClient();
        final BlobContainerAsyncClient containerClient = blobServiceAsyncClient
                .getBlobContainerAsyncClient(containerName);
        containerClient.createIfNotExists();

        // Upload the file to the container
        return containerClient.getBlobAsyncClient(targetFileName);
    }
}
