package com.illumio.data.configuration;

import com.azure.identity.ClientSecretCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class AzureCredentialsConfig {
    private final FlowLogIngestionConfig flowLogIngestionConfig;

    @Bean
    public ClientSecretCredential clientSecretCredential() {
        return new ClientSecretCredentialBuilder()
                .clientId(flowLogIngestionConfig.getAzureConfig().getAzureClientId())
                .clientSecret(
                        flowLogIngestionConfig.getAzureConfig().getAzureClientSecret())
                .tenantId(flowLogIngestionConfig.getAzureConfig().getAzureTenantId())
                .build();
    }
}
