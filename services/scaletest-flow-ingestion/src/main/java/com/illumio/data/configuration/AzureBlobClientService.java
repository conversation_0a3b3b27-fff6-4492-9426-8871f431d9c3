package com.illumio.data.configuration;

import com.azure.core.util.HttpClientOptions;
import com.azure.identity.ClientSecretCredential;
import com.azure.storage.blob.BlobServiceAsyncClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;

import com.azure.core.http.HttpClient;
import java.time.Duration;

@Configuration
@RequiredArgsConstructor
public class AzureBlobClientService {

    private BlobServiceAsyncClient blobServiceAsyncClient;
    private final ClientSecretCredential clientSecretCredential;

    private final FlowLogIngestionConfig flowLogIngestionConfig;

    public BlobServiceAsyncClient getBlobServiceAsyncClient() {
        if(blobServiceAsyncClient == null) {
            blobServiceAsyncClient = blobServiceAsyncClient();
        }
        return blobServiceAsyncClient;
    }

    private BlobServiceAsyncClient blobServiceAsyncClient() {

        // Create an HttpClient with the custom connection provider
        HttpClientOptions clientOptions = new HttpClientOptions()
                .setMaximumConnectionPoolSize(10000)
                .setConnectTimeout(Duration.ofSeconds(60));
        HttpClient httpClient = HttpClient.createDefault(clientOptions);

        return new BlobServiceClientBuilder()
                .endpoint(flowLogIngestionConfig.getStorageAccountEndpoint())
                .httpClient(httpClient)
                .credential(clientSecretCredential)
                .buildAsyncClient();
    }

}
