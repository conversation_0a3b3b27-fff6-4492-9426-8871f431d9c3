package com.illumio.data;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.r2dbc.core.DatabaseClient;
import org.springframework.test.context.TestPropertySource;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

@SpringBootTest(classes = LabelRecommendationApplication.class)
@AutoConfigureMockMvc
@TestPropertySource(locations = "classpath:application.yml")
class LabelRecommendationApplicationTest {

    @Autowired
    private DatabaseClient databaseClient;

    @Test
    void contextLoads() {

    }

    @Test
    public void testTableSchema() {
        Flux<String> columns = databaseClient.sql("SELECT column_name FROM information_schema.columns WHERE table_name = 'resource_service_map'")
                .map(row -> row.get("column_name", String.class))
                .all();
        StepVerifier.create(columns)
                .expectNext("resource_id", "service_id", "service_label", "tenant_id", "created_at")
                .verifyComplete();
    }
}