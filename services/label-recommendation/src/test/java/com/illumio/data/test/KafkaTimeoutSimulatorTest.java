package com.illumio.data.test;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Test class to run the Kafka timeout simulation
 * This test helps reproduce the consumer stall issue where:
 * "when the client received request timeout, the flux was closed.
 * Even though the new consumer instance was created, it is not processing anymore."
 */
class KafkaTimeoutSimulatorTest {

    private KafkaTimeoutSimulator simulator;

    @BeforeEach
    void setUp() {
        simulator = new KafkaTimeoutSimulator();
    }
    
    /**
     * Test that demonstrates the consumer timeout and stall behavior
     * Run this test while <PERSON><PERSON><PERSON> is running to observe the issue
     */
    @Test
    void testConsumerTimeoutStall() throws InterruptedException {
        System.out.println("=== Starting Kafka Timeout Simulation Test ===");
        System.out.println("Make sure Kafka is running on localhost:9092");
        System.out.println("This test will run for 3 minutes to demonstrate the stall behavior");
        
        // Start the simulation
        simulator.demonstrateStallIssue();
        
        // Let the test run for 3 minutes to observe the stall behavior
        Thread.sleep(180000);
        
        // Stop the simulation
        simulator.stopSimulation();
        
        System.out.println("=== Timeout Simulation Test Completed ===");
    }
    
    /**
     * Test that demonstrates producer timeout behavior
     */
    @Test
    void testProducerTimeout() throws InterruptedException {
        System.out.println("=== Starting Producer Timeout Test ===");
        
        // Start producer timeout simulation
        simulator.simulateProducerTimeout();
        
        // Wait for 30 seconds to observe producer timeout behavior
        Thread.sleep(30000);
        
        System.out.println("=== Producer Timeout Test Completed ===");
    }
}
