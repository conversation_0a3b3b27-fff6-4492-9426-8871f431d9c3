package com.illumio.data;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.DecoratedFlowReader;
import com.illumio.data.components.EnrichedFlowWriter;
import com.illumio.data.components.LabelLookup;
import com.illumio.data.components.PortFilter;
import com.illumio.data.configuration.LabelRecommendationConfig;
import com.illumio.data.configuration.MetricsConfiguration;
import com.illumio.data.util.MetricsUtil;
import lombok.SneakyThrows;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.Test;
import org.mockito.Answers;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOffset;
import reactor.kafka.receiver.ReceiverRecord;
import reactor.kafka.sender.KafkaSender;
import reactor.test.StepVerifier;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = {MetricsConfiguration.class, MetricsUtil.class})
class LabelRecommendationPipelineTest {
    @Mock
    KafkaReceiver<String, String> kafkaReceiver;
    DecoratedFlowReader decoratedFlowReader;
    @Mock
    LabelLookup labelLookup;
    @Mock
    EnrichedFlowWriter enrichedFlowWriter;
    @Mock
    KafkaSender<String, String> kafkaSender;
    @Mock
    PortFilter portFilter;
    @Autowired
    MetricsUtil metricsUtil;

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    LabelRecommendationConfig labelRecommendationConfig;

    @SneakyThrows
    public static String readFromStream(InputStream inputStream) {
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        for (int length; (length = inputStream.read(buffer)) != -1; ) {
            result.write(buffer, 0, length);
        }
        // StandardCharsets.UTF_8.name() > JDK 7
        return result.toString(StandardCharsets.UTF_8);
    }

    /**
     * Expects to drop the record in case of parsing error.
     */
    @Test
    void testBadRecord() {
        LabelRecommendationPipeline labelRecommendationPipeline = setupPipelineWithoutInsights();
        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", "no-json");
        ReceiverOffset receiverOffset = mock(ReceiverOffset.class);
        ReceiverRecord<String, String> receiverRecord =
                new ReceiverRecord<>(consumerRecord, receiverOffset);

        StepVerifier.create(
                        Flux.just(consumerRecord)
                                .flatMap(
                                        labelRecommendationPipeline::processConsumerRecord))
                .expectSubscription()
                .thenAwait(Duration.ofSeconds(1))
                .expectNextCount(1)
                .verifyComplete();
    }

    /**
     * Expects the json to be returned without decoration when CSSrcId field is missing.
     */
    @Test
    void testEmptyJson() {
        LabelRecommendationPipeline labelRecommendationPipeline = setupPipelineWithoutInsights();
        JsonNode jsonNode = mock(JsonNode.class);
        when(labelLookup.enrichWithLabel(any(JsonNode.class))).thenReturn(Mono.just(jsonNode));
        when(enrichedFlowWriter.writeTreeAsString(any())).thenReturn(Mono.just("json"));
        final String value = readFromStream(
                Objects.requireNonNull(this.getClass()
                        .getClassLoader()
                        .getResourceAsStream("unit-test-examples/empty-json.json")));
        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);
        ReceiverOffset receiverOffset = mock(ReceiverOffset.class);
        ReceiverRecord<String, String> receiverRecord = new ReceiverRecord<>(consumerRecord, receiverOffset);

        StepVerifier.create(Flux.just(consumerRecord)
                        .flatMap(labelRecommendationPipeline::processConsumerRecord)
                )
                .expectSubscription()
                .expectNextCount(1)
                .verifyComplete();
    }

    /**
     * Expects the json to be returned with decoration when CSSrcId field is available.
     */
    @Test
    void testOneRecord() {
        LabelRecommendationPipeline labelRecommendationPipeline = setupPipelineWithoutInsights();
        JsonNode jsonNode = mock(JsonNode.class);
        when(labelLookup.enrichWithLabel(any(JsonNode.class))).thenReturn(Mono.just(jsonNode));
        when(enrichedFlowWriter.writeTreeAsString(any())).thenReturn(Mono.just("json"));
        final String value = readFromStream(
                Objects.requireNonNull(this.getClass()
                        .getClassLoader()
                        .getResourceAsStream("unit-test-examples/1-record.json")));

        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);
        ReceiverOffset receiverOffset = mock(ReceiverOffset.class);
        ReceiverRecord<String, String> receiverRecord = new ReceiverRecord<>(consumerRecord, receiverOffset);

        StepVerifier.create(Flux.just(consumerRecord)
                        .flatMap(labelRecommendationPipeline::processConsumerRecord)
                )
                .expectSubscription()
                .expectNextCount(1)
                .verifyComplete();
    }

    /**
     * When Insights are enabled, expects two sender records to be created when the Port:Proto is risky
     * One sender record for existing ml-classification
     * Another sender record for the new insights
     */
    @Test
    @SneakyThrows
    void testRiskyRecord() {
        LabelRecommendationPipeline labelRecommendationPipeline = setupPipelineWithInsights();
        when(portFilter.test(any(JsonNode.class))).thenReturn(true);
        final String value = readFromStream(
                Objects.requireNonNull(this.getClass()
                        .getClassLoader()
                        .getResourceAsStream("unit-test-examples/risky-flow.json")));

        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);

        ObjectMapper objectMapper = new ObjectMapper();
        // We can skip enrichment for this specific test as it is trivial
        when(labelLookup.enrichWithLabel(any(JsonNode.class))).thenReturn(Mono.just(objectMapper.readTree(value)));
        when(enrichedFlowWriter.writeTreeAsString(any())).thenReturn(Mono.just("json"));

        StepVerifier.create(Flux.just(consumerRecord)
                        .flatMap(labelRecommendationPipeline::processConsumerRecord)
                )
                .expectSubscription()
                .assertNext(senderRecord -> assertEquals(senderRecord.topic(), "ml-classification-flows"))
                .assertNext(senderRecord -> assertEquals(senderRecord.topic(), "insights"))
                .verifyComplete();
    }

    /**
     * When Insights are enabled, expects one sender record to be created when the Port:Proto is not risky
     * One sender record for existing ml-classification
     * No sender record for the new insights
     */
    @Test
    @SneakyThrows
    void testNonRiskyRecord() {
        LabelRecommendationPipeline labelRecommendationPipeline = setupPipelineWithInsights();
        when(portFilter.test(any(JsonNode.class))).thenReturn(false);
        final String value = readFromStream(
                Objects.requireNonNull(this.getClass()
                        .getClassLoader()
                        .getResourceAsStream("unit-test-examples/risky-flow.json")));

        ConsumerRecord<String, String> consumerRecord =
                new ConsumerRecord<>("topic", 0, 0, "key", value);

        ObjectMapper objectMapper = new ObjectMapper();
        // We can skip enrichment for this specific test as it is trivial
        when(labelLookup.enrichWithLabel(any(JsonNode.class))).thenReturn(Mono.just(objectMapper.readTree(value)));
        when(enrichedFlowWriter.writeTreeAsString(any())).thenReturn(Mono.just("json"));

        StepVerifier.create(Flux.just(consumerRecord)
                        .flatMap(labelRecommendationPipeline::processConsumerRecord)
                )
                .expectSubscription()
                .assertNext(senderRecord -> assertEquals(senderRecord.topic(), "ml-classification-flows"))
                .verifyComplete();
    }

    private LabelRecommendationPipeline setupPipelineWithoutInsights() {
        ObjectMapper objectMapper = new ObjectMapper();
        decoratedFlowReader = new DecoratedFlowReader(objectMapper);
        when(labelRecommendationConfig.getKafkaSenderConfig().getTopic()).thenReturn("ml-classification-flows");
        return new LabelRecommendationPipeline(
                        kafkaReceiver,
                        decoratedFlowReader,
                        labelLookup,
                        enrichedFlowWriter,
                        kafkaSender,
                        portFilter,
                        labelRecommendationConfig,
                        metricsUtil);
    }

    private LabelRecommendationPipeline setupPipelineWithInsights() {
        ObjectMapper objectMapper = new ObjectMapper();
        decoratedFlowReader = new DecoratedFlowReader(objectMapper);
        when(labelRecommendationConfig.getKafkaSenderConfig().getTopic()).thenReturn("ml-classification-flows");
        when(labelRecommendationConfig.getInsightsConfig().getIsEnabled()).thenReturn(true);
        when(labelRecommendationConfig.getInsightsConfig().getOutboundTopic()).thenReturn("insights");
        return new LabelRecommendationPipeline(
                kafkaReceiver,
                decoratedFlowReader,
                labelLookup,
                enrichedFlowWriter,
                kafkaSender,
                portFilter,
                labelRecommendationConfig,
                metricsUtil);
    }
}