package com.illumio.data.test;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;

import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;
import reactor.kafka.sender.SenderRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Utility class to simulate Kafka timeout scenarios for testing
 * This helps reproduce the issue where "when the client received request timeout, 
 * the flux was closed. Even though the new consumer instance was created, 
 * it is not processing anymore."
 */
@Slf4j
public class KafkaTimeoutSimulator {
    
    private final AtomicInteger consumerInstanceCounter = new AtomicInteger(0);
    private volatile Disposable currentSubscription;
    
    /**
     * Simulates the timeout scenario by creating a consumer with very short timeouts
     * and then introducing delays that exceed those timeouts
     */
    public void simulateConsumerTimeout() {
        log.info("Starting Kafka timeout simulation...");
        
        // Create consumer with very short timeouts
        KafkaReceiver<String, String> receiver = createTimeoutProneReceiver();
        
        // Start consuming with retry logic (similar to your pipeline)
        currentSubscription = receiver
                .receiveAutoAck()
                .publishOn(Schedulers.boundedElastic())
                .doOnSubscribe(subscription -> {
                    int instanceId = consumerInstanceCounter.incrementAndGet();
                    log.info("Consumer instance {} started", instanceId);
                })
                .doOnNext(records -> {
                    log.info("Received batch of {} records", records.count());
                    // Simulate processing delay that might cause timeout
                    try {
                        Thread.sleep(2000); // 2 second delay
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                })
                .flatMap(records -> records)
                .doOnNext(record -> {
                    log.debug("Processing record: {}", record.value());
                    // Simulate slow processing that exceeds max.poll.interval.ms
                    simulateSlowProcessing();
                })
                .retryWhen(
                    Retry.backoff(5, Duration.ofSeconds(3))
                        .doBeforeRetry(retrySignal -> {
                            log.warn("Consumer error, retrying... Attempt: {}, Error: {}", 
                                retrySignal.totalRetries() + 1, 
                                retrySignal.failure().getMessage());
                        })
                )
                .doOnError(error -> {
                    log.error("Consumer error: {}", error.getMessage(), error);
                })
                .doOnComplete(() -> {
                    log.warn("Consumer completed unexpectedly");
                })
                .doOnCancel(() -> {
                    log.warn("Consumer cancelled");
                })
                .subscribe(
                    record -> log.info("Successfully processed record from partition {} offset {}", 
                        record.partition(), record.offset()),
                    error -> log.error("Final consumer error: {}", error.getMessage(), error),
                    () -> log.info("Consumer stream completed")
                );
    }
    
    /**
     * Creates a Kafka receiver with timeout-prone configuration
     */
    private KafkaReceiver<String, String> createTimeoutProneReceiver() {
        Map<String, Object> consumerProps = new HashMap<>();
        consumerProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "localhost:9092");
        consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, "timeout-test-group");
        consumerProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        
        // Very aggressive timeout settings to trigger timeouts quickly
        consumerProps.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, 3000);      // 3 seconds
        consumerProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 6000);      // 6 seconds  
        consumerProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 1000);   // 1 second
        consumerProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 10000);   // 10 seconds
        consumerProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 10);          // Small batches
        
        // Enable detailed logging
        consumerProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        
        ReceiverOptions<String, String> receiverOptions = ReceiverOptions
                .<String, String>create(consumerProps)
                .subscription(Collections.singleton("decorated-flow-v1"));
                
        return KafkaReceiver.create(receiverOptions);
    }
    
    /**
     * Simulates slow processing that might exceed max.poll.interval.ms
     */
    private void simulateSlowProcessing() {
        try {
            // Simulate processing that takes longer than max.poll.interval.ms (10 seconds)
            Thread.sleep(12000); // 12 seconds - this should cause timeout
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Processing interrupted");
        }
    }
    
    /**
     * Simulates network issues by creating a producer that will timeout
     */
    public void simulateProducerTimeout() {
        log.info("Starting producer timeout simulation...");
        
        KafkaSender<String, String> sender = createTimeoutProneSender();
        
        // Try to send messages that will timeout
        Flux.range(1, 10)
            .map(i -> SenderRecord.create(
                new ProducerRecord<>("test-topic", "key-" + i, "value-" + i),
                i))
            .as(sender::send)
            .doOnNext(result -> {
                if (result.exception() != null) {
                    log.error("Send failed: {}", result.exception().getMessage());
                } else {
                    log.info("Send successful: {}", result.correlationMetadata());
                }
            })
            .doOnError(error -> log.error("Producer error: {}", error.getMessage(), error))
            .subscribe();
    }
    
    /**
     * Creates a Kafka sender with timeout-prone configuration
     */
    private KafkaSender<String, String> createTimeoutProneSender() {
        Map<String, Object> producerProps = new HashMap<>();
        producerProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "localhost:9092");
        producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        
        // Timeout-prone settings
        producerProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, 3000);      // 3 seconds
        producerProps.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, 5000);     // 5 seconds
        producerProps.put(ProducerConfig.LINGER_MS_CONFIG, 0);                  // Send immediately
        
        SenderOptions<String, String> senderOptions = SenderOptions.create(producerProps);
        return KafkaSender.create(senderOptions);
    }
    
    /**
     * Stops the current simulation
     */
    public void stopSimulation() {
        if (currentSubscription != null && !currentSubscription.isDisposed()) {
            log.info("Stopping timeout simulation...");
            currentSubscription.dispose();
        }
    }
    
    /**
     * Test method to demonstrate the issue
     */
    public void demonstrateStallIssue() {
        log.info("=== Demonstrating Consumer Stall Issue ===");
        log.info("This will show how a consumer can stall after timeout even with retry logic");
        
        simulateConsumerTimeout();
        
        // Let it run for a while to observe the behavior
        Mono.delay(Duration.ofMinutes(2))
            .doOnNext(tick -> log.info("Simulation running... Check logs for timeout behavior"))
            .subscribe();
    }
}
