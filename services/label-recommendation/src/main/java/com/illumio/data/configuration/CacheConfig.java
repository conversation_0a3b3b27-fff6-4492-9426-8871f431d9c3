package com.illumio.data.configuration;

import com.github.benmanes.caffeine.cache.AsyncCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.cache.CaffeineCacheMetrics;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
@Slf4j
public class CacheConfig {

    private final LabelRecommendationConfig labelRecommendationConfig;
    private final MeterRegistry meterRegistry;

    @Bean
    public AsyncCache<String, String> asyncCache() {
        AsyncCache<String, String> localCache = Caffeine.newBuilder()
                .maximumSize(labelRecommendationConfig.getCacheConfig().getMaxKeys())
                .expireAfterWrite(labelRecommendationConfig.getCacheConfig().getLocalTtlDuration())
                .recordStats()
                .buildAsync();
        CaffeineCacheMetrics.monitor(meterRegistry, localCache, "local-cache");
        return localCache;
    }

    @PreDestroy
    public void cleanUp() {
        AsyncCache<String, String> cache = asyncCache();
        log.info("Invalidating the local cache");
        cache.synchronous().invalidateAll();
        log.info("Invalidated the local cache");
    }
}
