package com.illumio.data.configuration;

import lombok.RequiredArgsConstructor;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class KafkaReceiverConfiguration {
    private final LabelRecommendationConfig labelRecommendationConfig;

    @Bean
    public KafkaReceiver<String, String> kafkaReceiver() {
        Map<String, Object> consumerProps = new HashMap<>();
        consumerProps.put(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                labelRecommendationConfig.getKafkaCommonConfig().getBootstrapServers());
        consumerProps.put(
                ConsumerConfig.GROUP_ID_CONFIG,
                labelRecommendationConfig.getKafkaReceiverConfig().getGroupId());
        consumerProps.put(
                ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,
                labelRecommendationConfig.getKafkaReceiverConfig().getAutoOffsetReset());
        consumerProps.put(
                ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG,
                StringDeserializer.class);
        consumerProps.put(
                ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,
                StringDeserializer.class);
        consumerProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG,
                labelRecommendationConfig.getKafkaReceiverConfig().getHeartbeatIntervalMs());
        consumerProps.put(
                ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG,
                labelRecommendationConfig.getKafkaReceiverConfig().getRequestTimeoutMs());
        consumerProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG,
                labelRecommendationConfig.getKafkaReceiverConfig().getSessionTimeoutMs());
        consumerProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG,
                labelRecommendationConfig.getKafkaReceiverConfig().getMaxPollIntervalMs());
        consumerProps.put(
                ConsumerConfig.MAX_POLL_RECORDS_CONFIG,
                labelRecommendationConfig.getKafkaReceiverConfig().getMaxPollRecords());
        consumerProps.put(
                ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG,
                labelRecommendationConfig.getKafkaReceiverConfig().getMaxPartitionFetchBytes());
        consumerProps.put(ConsumerConfig.PARTITION_ASSIGNMENT_STRATEGY_CONFIG,
                labelRecommendationConfig.getKafkaReceiverConfig().getPartitionAssignmentStrategy());

        if (null != labelRecommendationConfig.getKafkaCommonConfig().getIsSasl()
                && labelRecommendationConfig.getKafkaCommonConfig().getIsSasl()) {
            consumerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            consumerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            consumerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    labelRecommendationConfig.getKafkaCommonConfig().getSaslJaasConfig());
        }
        if (null != labelRecommendationConfig.getKafkaReceiverConfig().getGroupInstanceId()) {
            consumerProps.put(ConsumerConfig.GROUP_INSTANCE_ID_CONFIG,
                    labelRecommendationConfig.getKafkaReceiverConfig().getGroupInstanceId());
        }
        consumerProps.put(CommonClientConfigs.METADATA_MAX_AGE_CONFIG,
                labelRecommendationConfig.getKafkaCommonConfig().getMetadataMaxAgeMs());
        consumerProps.put(CommonClientConfigs.CONNECTIONS_MAX_IDLE_MS_CONFIG,
                labelRecommendationConfig.getKafkaCommonConfig().getConnectionMaxIdleMs());

        ReceiverOptions<String, String> receiverOptions =
                ReceiverOptions.<String, String>create(consumerProps)
                        .subscription(
                                Collections.singleton(
                                        labelRecommendationConfig.getKafkaReceiverConfig().getTopic()));

        return KafkaReceiver.create(receiverOptions);
    }
}
