package com.illumio.data.configuration;

import io.r2dbc.spi.ConnectionFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.r2dbc.ConnectionFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.dao.DataAccessException;
import org.springframework.data.r2dbc.config.AbstractR2dbcConfiguration;
import org.springframework.jdbc.core.JdbcOperations;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.SingleConnectionDataSource;
import org.springframework.r2dbc.connection.init.ConnectionFactoryInitializer;
import org.springframework.r2dbc.connection.init.ResourceDatabasePopulator;
import org.springframework.r2dbc.connection.init.ScriptStatementFailedException;
import org.springframework.r2dbc.connection.init.UncategorizedScriptException;

import java.util.Optional;

@Configuration
@RequiredArgsConstructor
@Slf4j
public class PostgresConfig extends AbstractR2dbcConfiguration {

    private final LabelRecommendationConfig labelRecommendationConfig;

    @Bean
    @Primary
    public ConnectionFactory connectionFactory() {
        this.ensureDatabaseExists();
        return ConnectionFactoryBuilder
                .withUrl(labelRecommendationConfig.getDatasourceConfig().getUrl())
                .username(labelRecommendationConfig.getDatasourceConfig().getUsername())
                .password(labelRecommendationConfig.getDatasourceConfig().getPassword())
                .build();
    }

    @Bean
    public ConnectionFactoryInitializer initializer(ConnectionFactory connectionFactory) {
        ConnectionFactoryInitializer initializer = new ConnectionFactoryInitializer();
        initializer.setConnectionFactory(connectionFactory);
        ResourceDatabasePopulator populator = new ResourceDatabasePopulator(new ClassPathResource("schema.sql"));
        initializer.setDatabasePopulator(populator);
        try {
            log.info("Initializing database with the schema and tables");
            initializer.afterPropertiesSet();
            log.info("Successfully initialized database schema and tables");
        } catch (ScriptStatementFailedException | UncategorizedScriptException e) {
            log.error("Error initializing database schema and tables", e);
            System.exit(1);
        }
        return initializer;
    }

    private void ensureDatabaseExists() {
        String databaseName = labelRecommendationConfig.getDbInitConfig().getDatabase();
        log.info("Checking if the database {} exists", databaseName);
        try(SingleConnectionDataSource dataSource =
                new SingleConnectionDataSource(
                        labelRecommendationConfig.getDbInitConfig().getUrl(),
                        labelRecommendationConfig.getDbInitConfig().getUsername(),
                        labelRecommendationConfig.getDbInitConfig().getPassword(),
                        true)) {
            JdbcOperations jdbcOperations = new JdbcTemplate(dataSource);
            try {
                Optional<Integer> count = Optional.ofNullable(jdbcOperations.queryForObject(
                        "SELECT count(*) FROM pg_database WHERE datname = ?",
                        Integer.class,
                        databaseName));

                if (count.isPresent() && count.get() == 1) {
                    log.info("The database {} exists.", databaseName);
                } else {
                    log.info("The database {} does not exist. Creating the database as part of initialization.", databaseName);
                    jdbcOperations.execute(String.format("CREATE DATABASE %s", databaseName));
                    log.info("Successfully created the database {} as part of initialization.", databaseName);
                }
            } catch (DataAccessException e) {
                log.error("Error ensuring that the database {} exists.", databaseName, e);
                System.exit(1);
            }
        }
    }
}