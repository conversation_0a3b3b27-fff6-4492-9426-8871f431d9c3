package com.illumio.data.configuration;

import io.micrometer.core.instrument.MeterRegistry;
import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.metrics.LongCounter;
import io.opentelemetry.api.metrics.Meter;
import io.opentelemetry.instrumentation.micrometer.v1_5.OpenTelemetryMeterRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MetricsConfiguration {

    public static final String INSTRUMENTATION_NAME = "label-recommendation";

    /*
     * The application uses otel java agent jar, hence,
     * we can access the OpenTelemetry instance via GlobalOpenTelemetry
     */
    @Bean
    public OpenTelemetry openTelemetry() {
        return GlobalOpenTelemetry.get();
    }

    @Bean
    public Meter meter(OpenTelemetry openTelemetry) {
        return openTelemetry.getMeter(INSTRUMENTATION_NAME);
    }

    /*
     * Add Micrometer MeterRegistry
     * Helps to track local cache metrics which are not provided out-of-the-box by otel
     */
    @Bean
    MeterRegistry meterRegistry(OpenTelemetry openTelemetry) {
        return OpenTelemetryMeterRegistry.builder(openTelemetry).build();
    }

    private LongCounter createCounter(OpenTelemetry openTelemetry, String name, String description) {
        Meter meter = this.meter(openTelemetry);
        return meter.counterBuilder(name)
            .setDescription(description)
            .build();
    }

    @Bean
    public LongCounter labelRecommendationEvent(OpenTelemetry openTelemetry) {
        return createCounter(openTelemetry, "label_recommendation_events", "Used to count the number of Label Recommendation events");
    }

    @Bean
    public LongCounter labelRecommendationWrongEvent(OpenTelemetry openTelemetry) {
        return createCounter(openTelemetry, "label_recommendation_wrong_events", "Used to count the number of malformed events that reached Label Recommendation");
    }
}
