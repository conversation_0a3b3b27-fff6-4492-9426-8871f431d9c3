package com.illumio.data.components;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
@Builder
@Slf4j
public class DecoratedFlowReader {
    private final ObjectMapper objectMapper;

    public Mono<JsonNode> readTree(String value) {
        return Mono.fromCallable(() -> {
            JsonNode jsonNode = objectMapper.readTree(value);
            return jsonNode;
        });
    }
}
