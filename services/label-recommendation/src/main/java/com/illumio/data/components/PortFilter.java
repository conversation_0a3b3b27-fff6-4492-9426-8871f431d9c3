package com.illumio.data.components;

import com.fasterxml.jackson.databind.JsonNode;
import com.illumio.data.configuration.LabelRecommendationConfig;
import com.illumio.data.model.PortProtocolPair;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ResourceUtils;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;
import java.util.function.Predicate;
import java.util.stream.Stream;

@Component
@Slf4j
public class PortFilter implements Predicate<JsonNode> {
    private final Set<PortProtocolPair> riskyPorts;

    public PortFilter(LabelRecommendationConfig config) {
        this.riskyPorts = new HashSet<>();
        if (Optional.ofNullable(config.getInsightsConfig())
                .map(LabelRecommendationConfig.InsightsConfig::getIsEnabled)
                .orElse(false)) {
            this.loadRiskyPorts(config.getInsightsConfig().getRiskyPortsFilePath());
        }
    }

    @Override
    public boolean test(JsonNode jsonNode) {
        if (jsonNode.isObject() && jsonNode.has("Port") && jsonNode.has("Proto")) {
            return riskyPorts.contains(
                    new PortProtocolPair(
                            jsonNode.get("Port").asInt(),
                            jsonNode.get("Proto").asText().toUpperCase()));
        }
        return false;
    }

    // Method to read and load the CSV file containing risky ports lookup table
    private void loadRiskyPorts(String filePath) {
        // For containerized env, file will be picked up from the mounted volume
        // For local env, file will be picked up from resources folder
        if (filePath.startsWith("classpath:")) {
            try {
                filePath = ResourceUtils.getFile(filePath).getPath();
            } catch (FileNotFoundException e) {
                throw new RuntimeException("Error in loading the risky ports lookup table. Please verify that the file exists.", e);
            }
        }
        try (BufferedReader reader = Files.newBufferedReader(Paths.get(filePath))) {
            // Read the file lines in parallel and process
            Stream<String> lines = reader.lines().parallel();
            lines.skip(1).forEach(line -> {
                try {
                    String[] parts = line.split(",");
                    riskyPorts.add(new PortProtocolPair(Integer.parseInt(parts[0]), parts[1].toUpperCase()));
                } catch (NumberFormatException | NullPointerException e) {
                    log.warn("Poorly formatted risky port/protocol pair. Check line: {}", line);
                }
            });
        } catch (IOException | NullPointerException e) {
            throw new RuntimeException("Error in loading the risky ports lookup table", e);
        }
    }
}
