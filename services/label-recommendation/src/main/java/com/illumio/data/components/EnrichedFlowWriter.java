package com.illumio.data.components;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class EnrichedFlowWriter {
    private final ObjectMapper objectMapper;

    public Mono<String> writeTreeAsString(JsonNode jsonNode) {
        return Mono.fromCallable(
                () -> {
                    String result = objectMapper.writeValueAsString(jsonNode);
                    return result;
                });
    }
}
