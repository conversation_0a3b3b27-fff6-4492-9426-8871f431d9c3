package com.illumio.data;

import static com.illumio.data.util.FlowDataValidator.extractTenantId;

import com.illumio.data.components.EnrichedFlowWriter;
import com.illumio.data.components.DecoratedFlowReader;
import com.illumio.data.components.LabelLookup;
import com.illumio.data.components.PortFilter;
import com.illumio.data.configuration.LabelRecommendationConfig;
import com.illumio.data.util.FlowDataValidator;
import com.illumio.data.util.MetricsUtil;
import com.fasterxml.jackson.databind.JsonNode;
import io.opentelemetry.api.common.Attributes;
import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.stereotype.Component;

import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.kafka.sender.SenderResult;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.NoSuchElementException;
import java.util.Optional;

@Slf4j
@Component
public class LabelRecommendationPipeline {
    private final KafkaReceiver<String, String> kafkaReceiver;
    private final DecoratedFlowReader decoratedFlowReader;
    private final LabelLookup labelLookup;
    private final EnrichedFlowWriter enrichedFlowWriter;
    private final KafkaSender<String, String> kafkaSender;
    private final String sinkTopic;
    private final boolean isInsightsEnabled;
    private final String insightsTopic;
    private final PortFilter portFilter;
    private final MetricsUtil metricsUtil;

    private Disposable disposable;

    public LabelRecommendationPipeline(
            KafkaReceiver<String, String> kafkaReceiver,
            DecoratedFlowReader decoratedFlowReader,
            LabelLookup labelLookup,
            EnrichedFlowWriter enrichedFlowWriter,
            KafkaSender<String, String> kafkaSender,
            PortFilter portFilter,
            LabelRecommendationConfig labelRecommendationConfig,
            MetricsUtil metricsUtil) {
        this.kafkaReceiver = kafkaReceiver;
        this.decoratedFlowReader = decoratedFlowReader;
        this.labelLookup = labelLookup;
        this.enrichedFlowWriter = enrichedFlowWriter;
        this.kafkaSender = kafkaSender;
        this.sinkTopic = getSinkTopic(labelRecommendationConfig);
        this.isInsightsEnabled = checkIsInsightsEnabled(labelRecommendationConfig);
        this.insightsTopic = getInsightsTopic(labelRecommendationConfig);
        this.portFilter = portFilter;
        this.metricsUtil = metricsUtil;
    }

    /**
     * Fail early if sink topic is not defined.
     *
     * @param labelRecommendationConfig object which contains application specific configuration
     * @return Configured downstream Kafka topic name
     */
    private String getSinkTopic(LabelRecommendationConfig labelRecommendationConfig) {
        return Optional.of(labelRecommendationConfig)
                .map(LabelRecommendationConfig::getKafkaSenderConfig)
                .map(LabelRecommendationConfig.KafkaSenderConfig::getTopic)
                .orElseThrow(
                        () ->
                                new NoSuchElementException(
                                        "No topic configured. Please make sure "
                                                + "labelRecommendation.kafkaSenderConfig.topic is set."));
    }

    private boolean checkIsInsightsEnabled(LabelRecommendationConfig labelRecommendationConfig) {
        return Optional.ofNullable(labelRecommendationConfig.getInsightsConfig())
                .map(LabelRecommendationConfig.InsightsConfig::getIsEnabled)
                .orElse(false);
    }

    /**
     * Fail early if the insights feature is enabled and the insights topic is not defined.
     *
     * @param labelRecommendationConfig object which contains application specific configuration
     * @return Configured downstream Kafka topic name for insights
     */
    private String getInsightsTopic(LabelRecommendationConfig labelRecommendationConfig) {
        if (checkIsInsightsEnabled(labelRecommendationConfig)) {
            return Optional.of(labelRecommendationConfig)
                    .map(LabelRecommendationConfig::getInsightsConfig)
                    .map(LabelRecommendationConfig.InsightsConfig::getOutboundTopic)
                    .orElseThrow(() ->
                            new NoSuchElementException(
                                    "No insights topic configured. Please make sure "
                                            + "labelRecommendation.insightsConfig.outboundTopic is set."));
        } else {
            return null;
        }
    }

    public void start() {
        this.disposable = startInternal().subscribe();
    }

    public Flux<SenderResult<String>> startInternal() {
        return kafkaSender
                .send(
                        kafkaReceiver
                                .receiveAutoAck()
                                .publishOn(Schedulers.boundedElastic())
                                .retryWhen(
                                        Retry.backoff(5, Duration.ofSeconds(3L))
                                                .doBeforeRetry(
                                                        retrySignal ->
                                                                log.warn(
                                                                        "Error receiving from kafka {}",
                                                                        retrySignal.toString())))
                                .concatMap(r -> r)
                                .flatMap(this::processConsumerRecord))
                .retryWhen(
                        Retry.backoff(5, Duration.ofSeconds(3L))
                                .doBeforeRetry(
                                        retrySignal ->
                                                log.warn(
                                                        "Error sending to kafka {}",
                                                        retrySignal.toString())));
    }

    public Flux<SenderRecord<String, String, String>> processConsumerRecord(
            ConsumerRecord<String, String> consumerRecord) {
        return Mono.just(consumerRecord)
                .doOnNext(__ -> log.debug("Processing record {}", consumerRecord))
                .publishOn(Schedulers.parallel())
                .flatMap(
                        __ ->
                                decoratedFlowReader
                                        .readTree(consumerRecord.value())
                                        .doOnError(
                                                throwable ->
                                                        log.error(
                                                                "Error parsing record {}",
                                                                consumerRecordString(
                                                                        consumerRecord),
                                                                throwable))
                                        .flatMap(labelLookup::enrichWithLabel)
                                        .doOnError(___ -> log.error("Error parsing the record or adding label to the record.")))
                .flatMapMany(enrichedRecord -> {
                    // Extract tenant ID for outgoing metrics
                    String tenantId = FlowDataValidator.extractTenantId(enrichedRecord);
                    Attributes tenantAttributes = Attributes.builder()
                            .put(FlowDataValidator.INSIGHTS_TENANT_ID, tenantId)
                            .build();

                    Mono<SenderRecord<String, String, String>> classificationRecord =
                            Mono.just(enrichedRecord)
                                    .flatMap(enrichedFlowWriter::writeTreeAsString)
                                    .onErrorReturn(consumerRecord.value())
                                    .flatMap(enrichedRecordString -> createSenderRecord(sinkTopic, consumerRecord, enrichedRecordString))
                                    .doOnNext(enrichedClassificationRecordString ->
                                            log.debug("Sending json record: {} to topic: {}", enrichedClassificationRecordString, sinkTopic));
                    Mono<SenderRecord<String, String, String>> insightsRecord = Mono.empty();
                    if (isInsightsEnabled) {
                        insightsRecord = Mono.just(enrichedRecord)
                                .filter(portFilter)
                                .flatMap(enrichedFlowWriter::writeTreeAsString)
                                .onErrorResume(__ -> Mono.empty())
                                .flatMap(enrichedRecordString -> createSenderRecord(insightsTopic, consumerRecord, enrichedRecordString))
                                .doOnNext(enrichedInsightRecordString ->
                                        log.debug("Sending json record: {} to topic: {}", enrichedInsightRecordString, insightsTopic));
                    }

                    metricsUtil.incrementLabelRecommendationOutgoingEvent(tenantAttributes);
                    return Flux.concat(classificationRecord, insightsRecord);
                })
                .doOnError(throwable -> {
                    log.error(
                        "Error enriching classification flow with label lookup {}: {}",
                        consumerRecordString(consumerRecord),
                        consumerRecord.value(),
                        throwable);
                    metricsUtil.incrementRecordsWithErrors();
                })
                .onErrorResume(____ ->
                        createSenderRecord(sinkTopic, consumerRecord, consumerRecord.value())
                                .doOnNext(originalRecord -> log.debug("Sending json record: {} to topic: {}", originalRecord, sinkTopic))
                );
    }

    private Mono<SenderRecord<String, String, String>> createSenderRecord(
            String topic, ConsumerRecord<String, String> consumerRecord, String enrichedRecordString) {
        return Mono.just(
                SenderRecord.create(
                        new ProducerRecord<>(
                                topic,
                                consumerRecord.key(),
                                enrichedRecordString),
                        consumerRecordString(consumerRecord)));
    }

    private String consumerRecordString(ConsumerRecord<String, String> consumerRecord) {
        return consumerRecord.topic()
                + "-"
                + consumerRecord.partition()
                + "@"
                + consumerRecord.offset();
    }

    public void stop() {
        this.disposable.dispose();
    }
}
