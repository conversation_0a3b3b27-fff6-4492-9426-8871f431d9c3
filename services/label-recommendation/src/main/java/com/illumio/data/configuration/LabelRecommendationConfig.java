package com.illumio.data.configuration;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
@ConfigurationProperties(prefix = "label-recommendation")
@Data
public class LabelRecommendationConfig {
    private final KafkaCommonConfig kafkaCommonConfig;
    private final KafkaReceiverConfig kafkaReceiverConfig;
    private final KafkaSenderConfig kafkaSenderConfig;
    private final DbInitConfig dbInitConfig;
    private final DatasourceConfig datasourceConfig;
    private final RedisConfig redisConfig;
    private final CacheConfig cacheConfig;
    private final InsightsConfig insightsConfig;

    @Configuration
    @Getter
    @Setter
    public static class KafkaCommonConfig {
        private String bootstrapServers;
        private Boolean isSasl = false;
        private String saslJaasConfig;
        private Integer metadataMaxAgeMs;
        private Integer connectionMaxIdleMs;
    }

    @Configuration
    @Getter
    @Setter
    public static class KafkaReceiverConfig {
        private String topic;
        private String groupId;
        private String groupInstanceId;
        private String autoOffsetReset;
        private Integer heartbeatIntervalMs;
        private Integer requestTimeoutMs;
        private Integer sessionTimeoutMs;
        private Integer maxPollIntervalMs;
        private Integer maxPollRecords;
        private Integer maxPartitionFetchBytes;
        private String partitionAssignmentStrategy;
    }

    @Configuration
    @Getter
    @Setter
    public static class KafkaSenderConfig {
        private String topic;
        private Integer maxRequestSize;
        private Integer requestTimeoutMs;
        private Integer metadataMaxIdleMs;
        private Integer deliveryTimeoutMs;
        private Integer batchSizeKb;
        private Long bufferMemoryMb;
        private Long lingerMs;
    }

    @Configuration
    @Getter
    @Setter
    public static class DbInitConfig {
        private String url;
        private String database;
        private String username;
        private String password;
    }

    @Configuration
    @Getter
    @Setter
    public static class DatasourceConfig {
        private String url;
        private String username;
        private String password;
        private String sslMode;
    }

    @Configuration
    @Getter
    @Setter
    public static class RedisConfig {
        private String host;
        private Integer port;
        private String password;
        private Boolean useSsl;
        private Long commandTimeoutMs;
    }

    @Configuration
    @Getter
    @Setter
    public static class CacheConfig {
        private Duration localTtlDuration;
        private Long maxKeys;
    }

    @Configuration
    @Getter
    @Setter
    public static class InsightsConfig {
        public Boolean isEnabled;
        public String outboundTopic;
        public String riskyPortsFilePath;
    }
}
