logging:
  level:
    ROOT: DEBUG
    org:
      apache:
        kafka: INFO
spring:
  application:
    name: label-recommendation
  main:
    web-application-type: none
  output:
    ansi:
      enabled: ALWAYS

label-recommendation:
  kafka-common-config:
    bootstrap-servers: localhost:9092
    is-sasl: false
    sasl-jaas-config: _DO_NOT_COMMIT_
    metadata-max-age-ms: 180000
    connection-max-idle-ms: 180000
  kafka-receiver-config:
    topic: ip-classification-flows
    group-id: label-recommendation-group
    auto-offset-reset: latest
    heartbeat-interval-ms: 3000
    request-timeout-ms: 60000
    session-timeout-ms: 60000
    max-poll-interval-ms: 300000
    max-poll-records: 5000
    max-partition-fetch-bytes: 5242880
    partition-assignment-strategy: "org.apache.kafka.clients.consumer.CooperativeStickyAssignor"
  kafka-sender-config:
    topic: ml-classification-flows
    max-request-size: 1000000
    request-timeout-ms: 60000
    metadata-max-idle-ms: 180000
    delivery-timeout-ms: 300000
    linger-ms: 10
    buffer-memory-mb: 32
    batch-size-kb: 16
  db-init-config:
    url: *****************************************
    username: _DO_NOT_COMMIT_
    password: _DO_NOT_COMMIT_
    database: autolabel_output
  datasource-config:
    url: r2dbc:pool:postgresql://localhost:5432/autolabel_output
    username: _DO_NOT_COMMIT_
    password: _DO_NOT_COMMIT_
    ssl-mode: REQUIRE
  redis-config:
    host: localhost
    port: 6379
    password: _DO_NOT_COMMIT_
    use-ssl: false
    command-timeout-ms: 120000
  cache-config:
    local-ttl-duration: PT3H
    max-keys: 12000
  insights-config:
    is-enabled: true
    outbound-topic: insights
    risky-ports-file-path: "classpath:port_protocol.csv"
