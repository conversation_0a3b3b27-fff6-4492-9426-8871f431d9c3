CREATE SCHEMA IF NOT EXISTS auto_label_worker_output;

CREATE TABLE IF NOT EXISTS auto_label_worker_output.resource_service_map
(
    resource_id character varying(80) NOT NULL,
    service_id character varying(80) NOT NULL,
    service_label character varying(80) NOT NULL,
    tenant_id character varying(80) NOT NULL,
    created_at timestamp with time zone NOT NULL DEFAULT NOW(),
    CONSTRAINT resource_service_map_pkey PRIMARY KEY (resource_id)
)