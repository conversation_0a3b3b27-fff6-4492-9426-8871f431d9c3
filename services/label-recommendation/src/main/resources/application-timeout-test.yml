# Configuration file for testing Kafka timeouts
# Use this profile to test timeout scenarios: --spring.profiles.active=timeout-test

logging:
  level:
    ROOT: DEBUG
    org:
      apache:
        kafka: DEBUG
    reactor:
      kafka: DEBUG
    com:
      illumio:
        data: DEBUG

spring:
  application:
    name: label-recommendation
  main:
    web-application-type: none
  output:
    ansi:
      enabled: ALWAYS

label-recommendation:
  kafka-common-config:
    bootstrap-servers: localhost:9092
    is-sasl: false
    # Reduced metadata refresh to detect issues faster
    metadata-max-age-ms: 5000
    connection-max-idle-ms: 10000
  kafka-receiver-config:
    topic: ip-classification-flows
    group-id: label-recommendation-timeout-test
    auto-offset-reset: latest
    # Very aggressive timeout settings for testing
    heartbeat-interval-ms: 1000      # 1 second (was 3000)
    request-timeout-ms: 5000         # 5 seconds (was 60000) - This will timeout quickly
    session-timeout-ms: 10000        # 10 seconds (was 60000)
    max-poll-interval-ms: 15000      # 15 seconds (was 300000)
    max-poll-records: 100            # Reduced for faster processing
    max-partition-fetch-bytes: 1048576  # 1MB (was 5MB)
    partition-assignment-strategy: "org.apache.kafka.clients.consumer.CooperativeStickyAssignor"
  kafka-sender-config:
    topic: ml-classification-flows
    max-request-size: 1000000
    request-timeout-ms: 5000         # 5 seconds (was 60000) - This will timeout quickly
    metadata-max-idle-ms: 10000      # 10 seconds (was 180000)
    delivery-timeout-ms: 15000       # 15 seconds (was 300000)
    linger-ms: 0                     # Send immediately
    buffer-memory-mb: 8              # Reduced buffer
    batch-size-kb: 4                 # Smaller batches
  db-init-config:
    url: *****************************************
    username: _DO_NOT_COMMIT_
    password: _DO_NOT_COMMIT_
    database: autolabel_output
  datasource-config:
    url: r2dbc:pool:postgresql://localhost:5432/autolabel_output
    username: _DO_NOT_COMMIT_
    password: _DO_NOT_COMMIT_
    ssl-mode: REQUIRE
  redis-config:
    host: localhost
    port: 6379
    password: _DO_NOT_COMMIT_
    use-ssl: false
    command-timeout-ms: 5000         # 5 seconds (was 120000)
  cache-config:
    local-ttl-duration: PT1M         # 1 minute (was 3 hours)
    max-keys: 1000                   # Reduced cache size
  insights-config:
    is-enabled: true
    outbound-topic: insights
    risky-ports-file-path: "classpath:port_protocol.csv"
