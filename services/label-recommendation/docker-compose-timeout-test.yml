# Docker Compose setup for testing Kafka timeouts
# This setup includes tools to simulate network issues

version: '3.8'

services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    hostname: zookeeper
    container_name: zookeeper-timeout-test
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - kafka-timeout-test

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    hostname: kafka
    container_name: kafka-timeout-test
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
      - "9997:9997"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: 'zookeeper:2181'
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS: 0
      KAFKA_JMX_PORT: 9997
      KAFKA_JMX_HOSTNAME: localhost
      # Kafka settings that can help trigger timeouts
      KAFKA_REPLICA_FETCH_MAX_WAIT_MS: 500
      KAFKA_REPLICA_FETCH_MIN_BYTES: 1
    networks:
      - kafka-timeout-test

  # Network chaos tool to simulate network issues
  pumba:
    image: gaiaadm/pumba:0.9.0
    container_name: pumba-chaos
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    command: --log-level info --interval 30s netem --duration 10s --tc-image gaiadocker/iproute2 delay --time 5000 kafka-timeout-test
    networks:
      - kafka-timeout-test
    profiles:
      - chaos  # Only start with --profile chaos

  # Kafka UI for monitoring
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui-timeout-test
    depends_on:
      - kafka
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    networks:
      - kafka-timeout-test

  # Tool to generate test messages
  kafka-producer:
    image: confluentinc/cp-kafka:7.4.0
    container_name: kafka-producer-test
    depends_on:
      - kafka
    command: >
      bash -c "
        echo 'Waiting for Kafka to be ready...'
        sleep 30
        echo 'Creating test topic...'
        kafka-topics --create --topic ip-classification-flows --bootstrap-server kafka:29092 --partitions 3 --replication-factor 1 || true
        echo 'Producing test messages...'
        for i in {1..1000}; do
          echo '{\"id\": \"'$$i'\", \"message\": \"Test message '$$i'\", \"timestamp\": \"'$$(date -Iseconds)'\"}' | kafka-console-producer --topic ip-classification-flows --bootstrap-server kafka:29092
          sleep 1
        done
      "
    networks:
      - kafka-timeout-test
    profiles:
      - producer  # Only start with --profile producer

networks:
  kafka-timeout-test:
    driver: bridge

# Usage:
# 1. Start basic Kafka: docker-compose -f docker-compose-timeout-test.yml up -d
# 2. Start with producer: docker-compose -f docker-compose-timeout-test.yml --profile producer up -d
# 3. Start with chaos testing: docker-compose -f docker-compose-timeout-test.yml --profile chaos up -d
# 4. View Kafka UI: http://localhost:8080
