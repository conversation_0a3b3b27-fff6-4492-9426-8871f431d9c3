# Label Recommendation Service

## About

This label recommendation service is the latter half of the ML auto-labeling solution that:

- Reads the records from the `ip-classification-flows` topic
- Enriches the records with the labels stored in the Redis cache falling back to PostgreSQL on external cache miss
- Writes the enriched records to `ml-classification-flows` topic

## Design and Implementation

https://confluence.illum.io/display/ARCH/Label+Recommendation+Service

## Build and Test

> TODO: Add build and test instructions

1. Update the `application-local.yml` to include Kafka producer, Kafka consumer, Redis and PostgreSQL configuration
2. Update the runtime configuration to include env variable `spring.profiles.active=local`
3. `./gradlew clean bootRun`

## Deployment

> TODO: Add deployment instructions