apiVersion: v1
kind: Secret
metadata:
  name: {{ include "LabelRecommendation.fullname" . }}-env-secrets
  labels:
    {{- include "LabelRecommendation.labels" . | nindent 4 }}
type: Opaque
stringData:
  LABELRECOMMENDATION_KAFKACOMMONCONFIG_SASLJAASCONFIG: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.eventhub.password }}";
  LABELRECOMMENDATION_DATASOURCECONFIG_USERNAME: {{ .Values.labelRecommendation.postgresConfig.username | quote}}
  LABELRECOMMENDATION_DATASOURCECONFIG_PASSWORD: {{ .Values.labelRecommendation.postgresConfig.password | quote}}
  LABELRECOMMENDATION_DBINITCONFIG_USERNAME: {{ .Values.labelRecommendation.dbInitConfig.username | quote}}
  LABELRECOMMENDATION_DBINITCONFIG_PASSWORD: {{ .Values.labelRecommendation.dbInitConfig.password | quote}}

