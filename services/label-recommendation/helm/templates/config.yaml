apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "LabelRecommendation.fullname" . }}-env-configmap
  labels:
    {{- include "LabelRecommendation.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{ .Values.logging.level.root }}
        org:
          apache:
            kafka: {{ .Values.logging.level.org.apache.kafka }}
    spring:
      application:
        name: label-recommendation
      main:
        web-application-type: none
      output:
        ansi:
          enabled: ALWAYS

    label-recommendation:
      kafka-common-config:
        bootstrap-servers: {{ .Values.labelRecommendation.kafkaCommonConfig.bootstrapServers }}
        is-sasl: {{ .Values.labelRecommendation.kafkaCommonConfig.isSasl }}
        sasl-jaas-config: ${LABELRECOMMENDATION_KAFKACOMMONCONFIG_SASLJAASCONFIG}
        metadata-max-age-ms: {{ .Values.labelRecommendation.kafkaCommonConfig.metadataMaxAgeMs }}
        connection-max-idle-ms: {{ .Values.labelRecommendation.kafkaCommonConfig.connectionMaxIdleMs }}
      kafka-receiver-config:
        topic: {{ .Values.labelRecommendation.kafkaReceiverConfig.topic }}
        group-id: {{ .Values.labelRecommendation.kafkaReceiverConfig.groupId }}
        auto-offset-reset: {{ .Values.labelRecommendation.kafkaReceiverConfig.autoOffsetReset }}
        heartbeat-interval-ms: {{ .Values.labelRecommendation.kafkaReceiverConfig.heartbeatIntervalMs }}
        request-timeout-ms: {{ .Values.labelRecommendation.kafkaReceiverConfig.requestTimeoutMs }}
        session-timeout-ms: {{ .Values.labelRecommendation.kafkaReceiverConfig.sessionTimeoutMs }}
        max-poll-interval-ms: {{ .Values.labelRecommendation.kafkaReceiverConfig.maxPollIntervalMs }}
        max-poll-records: {{ .Values.labelRecommendation.kafkaReceiverConfig.maxPollRecords }}
        max-partition-fetch-bytes: {{ .Values.labelRecommendation.kafkaReceiverConfig.maxPartitionFetchBytes }}
        partition-assignment-strategy: {{ .Values.labelRecommendation.kafkaReceiverConfig.partitionAssignmentStrategy }}
      kafka-sender-config:
        topic: {{ .Values.labelRecommendation.kafkaSenderConfig.topic }}
        max-request-size: {{ .Values.labelRecommendation.kafkaSenderConfig.maxRequestSize }}
        request-timeout-ms: {{ .Values.labelRecommendation.kafkaSenderConfig.requestTimeoutMs }}
        metadata-max-idle-ms: {{ .Values.labelRecommendation.kafkaSenderConfig.metadataMaxIdleMs }}
        delivery-timeout-ms: {{ .Values.labelRecommendation.kafkaSenderConfig.deliveryTimeoutMs }}
        linger-ms: {{ .Values.labelRecommendation.kafkaSenderConfig.lingerMs }}
        buffer-memory-mb: {{ .Values.labelRecommendation.kafkaSenderConfig.bufferMemoryMb }}
        batch-size-kb: {{ .Values.labelRecommendation.kafkaSenderConfig.batchSizeKb }}
      db-init-config:
        url: {{ .Values.labelRecommendation.dbInitConfig.url }}
        username: {{ .Values.labelRecommendation.dbInitConfig.username }}
        password: {{ .Values.labelRecommendation.dbInitConfig.password }}
        database: {{ .Values.labelRecommendation.dbInitConfig.svcDbName }}
      datasource-config:
        url: {{ .Values.labelRecommendation.postgresConfig.url }}
        username: {{ .Values.labelRecommendation.postgresConfig.username }}
        password: {{ .Values.labelRecommendation.postgresConfig.password }}
        ssl-mode: {{ .Values.labelRecommendation.postgresConfig.sslMode }}
      redis-config:
        host: {{ .Values.labelRecommendation.redisConfig.host }}
        port: {{ .Values.labelRecommendation.redisConfig.port }}
        password: {{ .Values.labelRecommendation.redisConfig.password }}
        use-ssl: {{ .Values.labelRecommendation.redisConfig.useSsl }}
        command-timeout-ms: {{ .Values.labelRecommendation.redisConfig.commandTimeoutMs }}
      cache-config:
        local-ttl-duration: {{ .Values.labelRecommendation.cacheConfig.localTtlDuration }}
        max-keys: {{ .Values.labelRecommendation.cacheConfig.maxKeys }}
      insights-config:
        is-enabled: {{ .Values.labelRecommendation.insightsConfig.isEnabled }}
        outbound-topic: {{ .Values.labelRecommendation.insightsConfig.outboundTopic }}
        risky-ports-file-path: {{ .Values.labelRecommendation.insightsConfig.riskyPortsFilePath | quote }}
