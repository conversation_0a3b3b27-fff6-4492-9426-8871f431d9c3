apiVersion: v1
kind: Service
metadata:
  name: {{ include "LabelRecommendation.name" . }}
  labels:
    {{- include "LabelRecommendation.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "LabelRecommendation.selectorLabels" . | nindent 4 }}
