plugins {
    id 'org.springframework.boot'
    id 'io.spring.dependency-management'
    id 'com.google.cloud.tools.jib'
}

apply from: "${project.rootDir}/gradle/scripts/jib-build.gradle"
apply from: "${project.rootDir}/gradle/scripts/opentelemetry.gradle"

dependencies {
    // implementation
    implementation 'org.springframework.boot:spring-boot-starter-webflux'

    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.postgresql:postgresql'

    implementation 'org.springframework.boot:spring-boot-starter-data-r2dbc'
    implementation 'io.r2dbc:r2dbc-postgresql'

    implementation 'org.springframework.boot:spring-boot-starter-data-redis-reactive'

    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'com.github.ben-manes.caffeine:caffeine'

    implementation 'org.springframework.kafka:spring-kafka'
    implementation "io.projectreactor.kafka:reactor-kafka"

    implementation 'com.azure:azure-identity'

    implementation 'co.elastic.logging:logback-ecs-encoder'

    implementation("io.opentelemetry:opentelemetry-api")
    implementation 'io.micrometer:micrometer-core'
    implementation("io.opentelemetry.instrumentation:opentelemetry-micrometer-1.5:2.7.0-alpha")

    implementation 'com.google.guava:guava:33.2.1-jre'

    // common package for json validation
    implementation(project(":commons:utility-commons"))

    //jackson
    implementation 'com.fasterxml.jackson.core:jackson-annotations'

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'

    // spring
    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"

    // test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
    testImplementation 'org.springframework.kafka:spring-kafka-test'
    testImplementation 'org.testcontainers:r2dbc'
    testImplementation 'org.testcontainers:postgresql'
    testImplementation 'org.testcontainers:jdbc'
}

jib {
    container {
        environment = [
                "JAVA_TOOL_OPTIONS": "-javaagent:/otel/opentelemetry-javaagent.jar",
                "OTEL_METRICS_EXPORTER": "prometheus",
                "OTEL_TRACES_EXPORTER": "none",
                "OTEL_LOGS_EXPORTER": "none"
        ]
        entrypoint = [
                'java', '-cp', '@/app/jib-classpath-file',
                'com.illumio.data.LabelRecommendationApplication',
                '--spring.config.location=file:/var/resources/application.yml',
                '--spring.profiles.active=prod'
        ]
    }
}