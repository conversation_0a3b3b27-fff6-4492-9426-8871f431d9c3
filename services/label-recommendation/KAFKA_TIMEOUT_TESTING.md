# Kafka Timeout Testing Guide

This guide provides tools and instructions to simulate Kafka client request timeouts in your local environment to reproduce the issue where "when the client received request timeout, the flux was closed. Even though the new consumer instance was created, it is not processing anymore."

## Files Added

- `src/test/java/com/illumio/data/test/KafkaTimeoutSimulator.java` - Programmatic timeout simulation
- `src/test/java/com/illumio/data/test/KafkaTimeoutSimulatorTest.java` - JUnit tests for timeout scenarios
- `src/main/resources/application-timeout-test.yml` - Configuration with aggressive timeouts
- `simulate-kafka-timeout.sh` - Advanced network simulation script (pfctl/dnctl)
- `simulate-kafka-timeout-simple.sh` - Simple timeout simulation script (macOS-friendly)
- `docker-compose-timeout-test.yml` - Docker setup with chaos engineering
- `KAFKA_TIMEOUT_TESTING.md` - This documentation

## Prerequisites

1. **Java 17+** and Gradle
2. **Kafka running locally** on `localhost:9092` (or use Docker setup)
3. **Root access** for network simulation (sudo)
4. **Docker and Docker Compose** (optional, for containerized testing)

## Method 1: Configuration-Based Testing (Easiest)

### Step 1: Run with Aggressive Timeout Configuration

```bash
# Navigate to label-recommendation service
cd services/label-recommendation

# Run the application with timeout-prone configuration
./gradlew bootRun --args='--spring.profiles.active=timeout-test'
```

This configuration uses very short timeouts:
- `request-timeout-ms: 5000` (5 seconds instead of 60)
- `session-timeout-ms: 10000` (10 seconds instead of 60)
- `max-poll-interval-ms: 15000` (15 seconds instead of 5 minutes)

### Step 2: Observe the Behavior

Watch the logs for timeout patterns:
```
ERROR - Error receiving from kafka org.apache.kafka.common.errors.TimeoutException
WARN - Error receiving from Kafka, retrying...
INFO - Consumer instance 1 started
INFO - Consumer instance 2 started  # New instance after timeout
```

## Method 2: Programmatic Testing

### Step 1: Run the JUnit Test

```bash
# Run the timeout simulation test
./gradlew test --tests "com.illumio.data.test.KafkaTimeoutSimulatorTest.testConsumerTimeoutStall"
```

### Step 2: Run Individual Simulations

You can also create an instance of `KafkaTimeoutSimulator` and call:

```java
KafkaTimeoutSimulator simulator = new KafkaTimeoutSimulator();

// Simulate consumer timeout and stall
simulator.demonstrateStallIssue();

// Simulate producer timeout
simulator.simulateProducerTimeout();
```

## Method 3: Network-Level Simulation (macOS Compatible)

### Option A: Advanced Network Simulation (requires pfctl/dnctl)

```bash
# Make script executable
chmod +x simulate-kafka-timeout.sh

# Add 70-second delay (exceeds 60-second timeout)
sudo ./simulate-kafka-timeout.sh latency 70000

# In another terminal, run your application
./gradlew bootRun --args='--spring.profiles.active=local'

# Clean up when done
sudo ./simulate-kafka-timeout.sh cleanup
```

### Option B: Simple Simulation (easier, no complex network config)

```bash
# Make script executable
chmod +x simulate-kafka-timeout-simple.sh

# Stop Kafka temporarily to simulate timeout
./simulate-kafka-timeout-simple.sh kafka-stop 60

# Or block port 9092 (requires sudo)
sudo ./simulate-kafka-timeout-simple.sh port-block 120

# Or create CPU/memory pressure
./simulate-kafka-timeout-simple.sh cpu-load 60
./simulate-kafka-timeout-simple.sh memory-pressure 60 1024

# Run comprehensive test
./simulate-kafka-timeout-simple.sh comprehensive
```

## Method 4: Docker with Chaos Engineering

### Step 1: Start Kafka with Chaos Testing

```bash
# Start Kafka, producer, and chaos engineering
docker-compose -f docker-compose-timeout-test.yml --profile chaos --profile producer up -d

# View Kafka UI at http://localhost:8080
```

### Step 2: Run Your Application

```bash
# Your application will connect to localhost:9092
# The chaos tool will periodically introduce 5-second delays
./gradlew bootRun --args='--spring.profiles.active=local'
```

### Step 3: Monitor and Clean Up

```bash
# View logs
docker-compose -f docker-compose-timeout-test.yml logs -f

# Stop everything
docker-compose -f docker-compose-timeout-test.yml --profile chaos --profile producer down
```

## What to Look For

### 1. Initial Timeout Errors
```
ERROR - Error receiving from kafka org.apache.kafka.common.errors.TimeoutException: Failed to update metadata after 5000 ms.
WARN - Error receiving from Kafka, retrying... org.apache.kafka.common.errors.TimeoutException
```

### 2. Consumer Restart Attempts
```
INFO - Consumer instance 1 started
WARN - Consumer cancelled
INFO - Consumer instance 2 started
```

### 3. The Stall Condition (The Bug)
```
INFO - Consumer instance 3 started
# But no more "Received batch of X records" messages
# Even though messages are available in Kafka
```

### 4. Reactive Stream Issues
```
WARN - Consumer completed unexpectedly
ERROR - Consumer error: reactor.core.Exceptions$ReactiveException
```

## Troubleshooting

### If No Timeouts Occur:
1. **Reduce timeout values further** in `application-timeout-test.yml`
2. **Increase network delays** in simulation scripts
3. **Check Kafka is actually running** on localhost:9092

### If Application Won't Start:
1. **Check database connection** (PostgreSQL)
2. **Check Redis connection**
3. **Verify Kafka topic exists**: `ip-classification-flows`

### If Network Simulation Doesn't Work:
1. **Run as root**: `sudo ./simulate-kafka-timeout.sh`
2. **Use simple script**: Try `./simulate-kafka-timeout-simple.sh` instead
3. **Check macOS version**: dnctl requires macOS 10.10 or later
4. **Verify pfctl**: Make sure pfctl is available (should be on all macOS versions)

## Expected Results

After running these simulations, you should observe:

1. **Timeout exceptions** in the logs
2. **Consumer restarts** with new instance IDs
3. **Message processing stalls** despite new consumer instances
4. **Reactive stream completion/cancellation** events

This will help you reproduce and debug the exact issue you're experiencing in production where consumers stall after timeout events.

## Next Steps

Once you've reproduced the issue:

1. **Analyze the logs** to identify the exact failure pattern
2. **Implement proper error handling** with circuit breakers
3. **Add consumer health checks** to detect stalled consumers
4. **Consider increasing timeout values** in production
5. **Implement manual offset management** for better control

## Cleanup

Always clean up after testing:

```bash
# Network simulation cleanup
sudo ./simulate-kafka-timeout.sh cleanup

# Docker cleanup
docker-compose -f docker-compose-timeout-test.yml down

# Stop any running applications
# Ctrl+C or kill the Gradle process
```
