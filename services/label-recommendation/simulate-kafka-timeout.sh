#!/bin/bash

# Script to simulate Kafka client request timeouts in local environment
# This script uses macOS-compatible network tools (pfctl, dnctl) to introduce delays and packet loss

echo "=== Kafka Timeout Simulation Script (macOS) ==="
echo "This script will help you simulate various network conditions that cause Kafka timeouts"

# Function to check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        echo "Please run this script as root (use sudo) to modify network settings"
        exit 1
    fi
}

# Function to check if dnctl is available (macOS 10.10+)
check_dnctl() {
    if ! command -v dnctl &> /dev/null; then
        echo "Error: dnctl is not available. This script requires macOS 10.10 or later."
        exit 1
    fi
}

# Function to show current network settings
show_current_settings() {
    echo "Current pfctl rules:"
    pfctl -sr 2>/dev/null || echo "No pfctl rules set"
    echo ""
    echo "Current dnctl pipes:"
    dnctl list 2>/dev/null || echo "No dnctl pipes set"
}

# Function to simulate high latency (causes request timeouts)
simulate_high_latency() {
    local delay=${1:-70000}  # Default 70 seconds delay (higher than 60s timeout)
    echo "Adding ${delay}ms delay to localhost traffic using dnctl..."

    # Create a pipe with delay
    dnctl pipe 1 config delay ${delay}ms

    # Create pfctl rules to use the pipe for localhost traffic on port 9092
    cat > /tmp/kafka_delay_rules.conf << EOF
# Delay rules for Kafka timeout simulation
dummynet out proto tcp from any to 127.0.0.1 port 9092 pipe 1
dummynet in proto tcp from 127.0.0.1 port 9092 to any pipe 1
EOF

    # Load the rules
    pfctl -f /tmp/kafka_delay_rules.conf
    pfctl -e

    echo "High latency simulation active. Kafka requests should timeout after 60 seconds."
    echo "To remove: sudo $0 cleanup"
}

# Function to simulate packet loss (causes connection issues)
simulate_packet_loss() {
    local loss_rate=${1:-50}  # Default 50% packet loss
    echo "Adding ${loss_rate}% packet loss to localhost traffic using dnctl..."

    # Create a pipe with packet loss
    dnctl pipe 2 config plr 0.${loss_rate}

    # Create pfctl rules to use the pipe for localhost traffic on port 9092
    cat > /tmp/kafka_loss_rules.conf << EOF
# Packet loss rules for Kafka timeout simulation
dummynet out proto tcp from any to 127.0.0.1 port 9092 pipe 2
dummynet in proto tcp from 127.0.0.1 port 9092 to any pipe 2
EOF

    # Load the rules
    pfctl -f /tmp/kafka_loss_rules.conf
    pfctl -e

    echo "Packet loss simulation active. This may cause connection timeouts."
    echo "To remove: sudo $0 cleanup"
}

# Function to simulate network partitioning
simulate_network_partition() {
    local duration=${1:-120}  # Default 2 minutes
    echo "Simulating network partition for ${duration} seconds..."

    # Create pfctl rules to block Kafka port (9092)
    cat > /tmp/kafka_block_rules.conf << EOF
# Block rules for Kafka network partition simulation
block drop out proto tcp from any to 127.0.0.1 port 9092
block drop in proto tcp from 127.0.0.1 port 9092 to any
EOF

    # Load the rules
    pfctl -f /tmp/kafka_block_rules.conf
    pfctl -e

    echo "Network partition active for ${duration} seconds..."
    echo "Kafka connections should fail and timeout."

    # Wait for specified duration
    sleep ${duration}

    # Remove the rules
    pfctl -d
    rm -f /tmp/kafka_block_rules.conf

    echo "Network partition removed."
}

# Function to clean up all network modifications
cleanup() {
    echo "Cleaning up network modifications..."

    # Disable pfctl
    pfctl -d 2>/dev/null || echo "pfctl was not enabled"

    # Remove dnctl pipes
    dnctl delete 1 2>/dev/null || true
    dnctl delete 2 2>/dev/null || true

    # Remove temporary rule files
    rm -f /tmp/kafka_delay_rules.conf
    rm -f /tmp/kafka_loss_rules.conf
    rm -f /tmp/kafka_block_rules.conf

    echo "Cleanup complete."
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [option]"
    echo "Options:"
    echo "  latency [delay_ms]     - Add network delay (default: 70000ms)"
    echo "  packet-loss [rate%]    - Add packet loss (default: 50%)"
    echo "  partition [duration_s] - Simulate network partition (default: 120s)"
    echo "  status                 - Show current network settings"
    echo "  cleanup               - Remove all network modifications"
    echo ""
    echo "Examples:"
    echo "  $0 latency 80000      # 80 second delay"
    echo "  $0 packet-loss 30     # 30% packet loss"
    echo "  $0 partition 60       # 60 second partition"
    echo "  $0 cleanup            # Clean up all modifications"
}

# Main script logic
case "${1:-help}" in
    "latency")
        check_root
        check_dnctl
        simulate_high_latency $2
        ;;
    "packet-loss")
        check_root
        check_dnctl
        simulate_packet_loss $2
        ;;
    "partition")
        check_root
        simulate_network_partition $2
        ;;
    "status")
        show_current_settings
        ;;
    "cleanup")
        check_root
        cleanup
        ;;
    "help"|*)
        show_usage
        ;;
esac
