#!/bin/bash

# Simple Kafka timeout simulation script for macOS
# This script uses simpler methods that don't require complex network configuration

echo "=== Simple Kafka Timeout Simulation Script (macOS) ==="
echo "This script provides simple methods to simulate Kafka timeouts"

# Function to check if running as root for some operations
check_root_if_needed() {
    if [ "$1" = "true" ] && [ "$EUID" -ne 0 ]; then
        echo "This operation requires root privileges. Please run with sudo."
        exit 1
    fi
}

# Function to show current processes
show_current_status() {
    echo "Current Kafka-related processes:"
    ps aux | grep -E "(kafka|zookeeper)" | grep -v grep || echo "No Kafka processes found"
    echo ""
    echo "Current network connections to port 9092:"
    lsof -i :9092 2>/dev/null || echo "No connections to port 9092"
}

# Function to simulate timeout by stopping <PERSON><PERSON><PERSON> temporarily
simulate_kafka_stop() {
    local duration=${1:-60}  # Default 60 seconds
    echo "Stopping Kafka for ${duration} seconds to simulate timeout..."
    
    # Find Kafka process
    KAFKA_PID=$(ps aux | grep kafka |grep -v kafka-stop | grep -v grep | awk '{print $2}' | head -1)
    
    if [ -z "$KAFKA_PID" ]; then
        echo "No Kafka process found. Please start Kafka first."
        return 1
    fi
    
    echo "Found Kafka process: $KAFKA_PID"
    echo "Stopping Kafka..."
    kill -STOP $KAFKA_PID
    
    echo "Kafka stopped for ${duration} seconds. This will cause client timeouts."
    sleep $duration
    
    echo "Resuming Kafka..."
    kill -CONT $KAFKA_PID
    echo "Kafka resumed."
}

# Function to simulate network issues using localhost port blocking
simulate_port_block() {
    local duration=${1:-120}  # Default 2 minutes
    echo "Blocking port 9092 for ${duration} seconds..."
    
    # Create a simple port blocker using pfctl
    cat > /tmp/kafka_simple_block.conf << 'EOF'
# Simple port blocking for Kafka
block drop out proto tcp from any to 127.0.0.1 port 9092
block drop in proto tcp from 127.0.0.1 port 9092 to any
EOF
    
    # Apply the rules
    sudo pfctl -f /tmp/kafka_simple_block.conf
    sudo pfctl -e
    
    echo "Port 9092 blocked for ${duration} seconds..."
    sleep $duration
    
    # Remove the rules
    sudo pfctl -d
    rm -f /tmp/kafka_simple_block.conf
    
    echo "Port blocking removed."
}

# Function to simulate high CPU load (causes processing delays)
simulate_cpu_load() {
    local duration=${1:-60}  # Default 60 seconds
    echo "Creating high CPU load for ${duration} seconds..."
    echo "This will cause processing delays that may trigger timeouts."
    
    # Start CPU-intensive processes
    for i in {1..4}; do
        yes > /dev/null &
        CPU_PIDS="$CPU_PIDS $!"
    done
    
    echo "High CPU load active for ${duration} seconds..."
    sleep $duration
    
    # Kill the CPU-intensive processes
    for pid in $CPU_PIDS; do
        kill $pid 2>/dev/null || true
    done
    
    echo "CPU load simulation completed."
}

# Function to simulate memory pressure
simulate_memory_pressure() {
    local duration=${1:-60}  # Default 60 seconds
    local size_mb=${2:-1024}  # Default 1GB
    
    echo "Creating memory pressure (${size_mb}MB) for ${duration} seconds..."
    
    # Create memory pressure using dd and /dev/zero
    dd if=/dev/zero of=/tmp/memory_pressure bs=1m count=$size_mb 2>/dev/null &
    MEMORY_PID=$!
    
    echo "Memory pressure active for ${duration} seconds..."
    sleep $duration
    
    # Clean up
    kill $MEMORY_PID 2>/dev/null || true
    rm -f /tmp/memory_pressure
    
    echo "Memory pressure simulation completed."
}

# Function to run a comprehensive timeout test
run_comprehensive_test() {
    echo "=== Running Comprehensive Timeout Test ==="
    echo "This will run multiple timeout scenarios in sequence"
    
    echo "1. Starting with CPU load simulation (30 seconds)..."
    simulate_cpu_load 30
    
    sleep 5
    
    echo "2. Creating memory pressure (30 seconds)..."
    simulate_memory_pressure 30 512
    
    sleep 5
    
    echo "3. Stopping Kafka temporarily (30 seconds)..."
    simulate_kafka_stop 30
    
    echo "=== Comprehensive test completed ==="
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [option] [duration/size]"
    echo "Options:"
    echo "  kafka-stop [duration_s]    - Stop Kafka process temporarily (default: 60s)"
    echo "  port-block [duration_s]    - Block port 9092 (default: 120s, requires sudo)"
    echo "  cpu-load [duration_s]      - Create high CPU load (default: 60s)"
    echo "  memory-pressure [duration_s] [size_mb] - Create memory pressure (default: 60s, 1024MB)"
    echo "  comprehensive             - Run all tests in sequence"
    echo "  status                    - Show current Kafka status"
    echo ""
    echo "Examples:"
    echo "  $0 kafka-stop 30         # Stop Kafka for 30 seconds"
    echo "  $0 cpu-load 45           # High CPU load for 45 seconds"
    echo "  $0 memory-pressure 60 2048 # 2GB memory pressure for 60 seconds"
    echo "  sudo $0 port-block 90    # Block port for 90 seconds"
    echo "  $0 comprehensive          # Run all tests"
}

# Main script logic
case "${1:-help}" in
    "kafka-stop")
        simulate_kafka_stop $2
        ;;
    "port-block")
        check_root_if_needed true
        simulate_port_block $2
        ;;
    "cpu-load")
        simulate_cpu_load $2
        ;;
    "memory-pressure")
        simulate_memory_pressure $2 $3
        ;;
    "comprehensive")
        run_comprehensive_test
        ;;
    "status")
        show_current_status
        ;;
    "help"|*)
        show_usage
        ;;
esac
