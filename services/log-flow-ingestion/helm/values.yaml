# Default values for connector.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
logging:
  level:
    root: DEBUG
server:
  port: 8081
  ssl:
    certificate: ""
    certificatePrivateKey: ""

ports:
- name: admin
  port: 8084
- name: rest
  port: 8081

servicePorts:
- name: rest
  podPort: rest
  servicePort: 8081

service:
  type: ClusterIP

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

replicaCount: 1

image:
  repositoryBase: illum.azurecr.io/
  repositoryName: log-flow-ingestion
  tag:      # value given at helm deployment
  pullPolicy: Always

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 60
  targetMemoryUtilizationPercentage: 60

podAnnotations: {}

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"



podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000



resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

javaToolOptions: "-Xms4096m -Xmx4096m"

nodeSelector: {}

tolerations: []

affinity: {}

ansi:
  enabled: ALWAYS
webApplicationType: reactive
logFlowIngestion:

  httpClientConfig:
    wiretap: false
    readTimeoutMinutes: 20
    writeTimeoutMinutes: 20

  blobServiceClientConfig:
    exponentialRetryConfig:
      maxAttempts: 5
      retryDelaySeconds: 10
      maxRetryDelaySeconds: 30

  transferBufferSizeBytes: 1048576 # 1 MB

  sourceBucketsAwsConfig:

  sourceStorageAccountsAzureConfig:

  targetStorageAccountAzureConfig:
    storageAccountEndpoint:   # should give at deployment time
    authConfig:
      isWorkloadIdentity: true
      workloadIdentity:
        clientId:    # should give at deployment time
  targetContainerName: indexes
  serviceBusSenderConfig:
    namespace: devcsdataeventbus
    queueName: csflowevent
    exponentialRetryConfig:
      maxAttempts: 5
      minSecondsBetween: 1
      clientTimeoutSeconds: 60
      jitterFactor: 0.5
    authConfig:
      isWorkloadIdentity: true
      workloadIdentity:
        clientId:    # should give at deployment time
  launchDarklyConfig:
    sdkKey: ""
extraLabels: {}

ingress:
  enabled: false
  path: "/"
  annotations: {}
  ingressClassName: "nginx"
  certManager:
    enabled: false
    clusterIssuer: "cert-manager-letsencrypt-prod-route53"
  fqdn: ""
  tlsSecretName: "logflowingestion-grpc-tls"
