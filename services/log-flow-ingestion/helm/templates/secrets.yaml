apiVersion: v1
kind: Secret
metadata:
  name: {{ include "LogFlowIngestion.fullname" . }}-env-secrets
  labels:
    {{- include "LogFlowIngestion.labels" . | nindent 4 }}
type: Opaque
stringData:
  LOGFLOWINGESTION_LAUNCHDARKLYCONFIG_SDKKEY: {{ .Values.logFlowIngestion.launchDarklyConfig.sdkKey }}


  {{- range $awsAccount, $awsAccountConfig := .Values.logFlowIngestion.sourceBucketsAwsConfig }}
  LOGFLOWINGESTION_SOURCEBUCKETSAWSCONFIG_{{ $awsAccount }}_AWSACCESSKEY: "{{ $awsAccountConfig.awsAccessKey }}"
  LOGFLOWINGESTION_SOURCEBUCKETSAWSCONFIG_{{ $awsAccount }}_AWSSECRETKEY: "{{ $awsAccountConfig.awsSecretKey }}"
  {{- end }}