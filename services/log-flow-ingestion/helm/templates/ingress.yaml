{{- if .Values.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "LogFlowIngestion.fullname" . }}
  labels:
    {{- include "LogFlowIngestion.labels" . | nindent 4 }}
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    {{- if .Values.ingress.certManager.enabled }}
    cert-manager.io/cluster-issuer: {{ .Values.ingress.certManager.clusterIssuer }}
    {{- end }}
    {{- with .Values.ingress.annotations }}
      {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  ingressClassName: {{ .Values.ingress.ingressClassName }}
  rules:
    - host: {{ .Values.ingress.fqdn }}
      http:
        paths:
          - path: {{ .Values.ingress.path}}
            pathType: Prefix
            backend:
              service:
                name: {{ include "LogFlowIngestion.name" . }}
                port:
                  number: 8081
  tls:
    - hosts:
        - {{ .Values.ingress.fqdn }}
      secretName: {{ .Values.ingress.tlsSecretName }}
{{- end }}
