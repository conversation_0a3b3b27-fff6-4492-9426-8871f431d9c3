apiVersion: v1
kind: Service
metadata:
  name: {{ include "LogFlowIngestion.name" . }}
  labels:
    {{- include "LogFlowIngestion.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "LogFlowIngestion.selectorLabels" . | nindent 4 }}
