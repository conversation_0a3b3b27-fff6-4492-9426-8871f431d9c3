apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "LogFlowIngestion.fullname" . }}
  labels:
    {{- include "LogFlowIngestion.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "LogFlowIngestion.selectorLabels" . | nindent 6 }}
      {{- if .Values.extraLabels }}
        {{ toYaml .Values.extraLabels | nindent 6 }}
      {{- end }}
  template:
    metadata:
      annotations:
      {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.podMetricsAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "LogFlowIngestion.selectorLabels" . | nindent 8 }}
        {{- if .Values.extraLabels }}
          {{ toYaml .Values.extraLabels | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "LogFlowIngestion.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repositoryBase }}{{ .Values.image.repositoryName }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: JAVA_TOOL_OPTIONS
              value: "{{ .Values.javaToolOptions }} -javaagent:/otel/opentelemetry-javaagent.jar"
          envFrom:
            - secretRef:
                name: {{ include "LogFlowIngestion.fullname" . }}-env-secrets
          ports:
          {{- range .Values.ports }}
            - name: {{ .name }}
              containerPort: {{ .port }}
              protocol: TCP
          {{- end }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: {{ include "LogFlowIngestion.fullname" . }}-env-configmap
              mountPath: /var/resources/
            - name: {{ .Values.ingress.tlsSecretName }}
              mountPath: /var/resources/certs/

      volumes:
        - name: {{ include "LogFlowIngestion.fullname" . }}-env-configmap
          configMap:
            name: {{ include "LogFlowIngestion.fullname" . }}-env-configmap
        - name: {{ .Values.ingress.tlsSecretName }}
          secret:
            secretName: {{ .Values.ingress.tlsSecretName }}

      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}

