apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "LogFlowIngestion.fullname" . }}-env-configmap
  labels:
    {{- include "LogFlowIngestion.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
    spring:
      application:
        name: "log-flow-ingestion"
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: reactive
    server:
      port: {{.Values.server.port}}
      ssl:
        enabled: true
        certificate: {{.Values.server.ssl.certificate}}
        certificate-private-key: {{.Values.server.ssl.certificatePrivateKey}}

    log-flow-ingestion:
      http-client-config:
        wiretap: {{.Values.logFlowIngestion.httpClientConfig.wiretap}}
        read-timeout-minutes: {{.Values.logFlowIngestion.httpClientConfig.readTimeoutMinutes}}
        write-timeout-minutes: {{.Values.logFlowIngestion.httpClientConfig.writeTimeoutMinutes}}

      blob-service-client-config:
        exponential-retry-config:
          max-attempts: {{.Values.logFlowIngestion.blobServiceClientConfig.exponentialRetryConfig.maxAttempts}}
          retry-delay-seconds: {{.Values.logFlowIngestion.blobServiceClientConfig.exponentialRetryConfig.retryDelaySeconds}}
          max-retry-delay-seconds: {{.Values.logFlowIngestion.blobServiceClientConfig.exponentialRetryConfig.maxRetryDelaySeconds}}

      transfer-buffer-size-bytes: {{.Values.logFlowIngestion.transferBufferSizeBytes}}

      source-storage-accounts-azure-config:
        {{- range $storageAccount, $storageAccountConfig := .Values.logFlowIngestion.sourceStorageAccountsAzureConfig }}
        {{ $storageAccount }}:
          storage-account-endpoint: {{ $storageAccountConfig.storageAccountEndpoint }}
          auth-config:
            is-workload-identity: {{ $storageAccountConfig.authConfig.isWorkloadIdentity }}
            workload-identity:
              client-id: {{ $storageAccountConfig.authConfig.workloadIdentity.clientId }}
        {{- end }}

      target-storage-account-azure-config:
        storage-account-endpoint: "{{.Values.logFlowIngestion.targetStorageAccountAzureConfig.storageAccountEndpoint}}"
        auth-config:
          is-workload-identity: "{{.Values.logFlowIngestion.targetStorageAccountAzureConfig.authConfig.isWorkloadIdentity}}"
          workload-identity:
            client-id: "{{.Values.logFlowIngestion.targetStorageAccountAzureConfig.authConfig.workloadIdentity.clientId}}"

      target-container-name: "{{.Values.logFlowIngestion.targetContainerName}}"

      service-bus-sender-config:
        namespace: "{{.Values.logFlowIngestion.serviceBusSenderConfig.namespace}}"
        queue-name: "{{.Values.logFlowIngestion.serviceBusSenderConfig.queueName}}"
        exponential-retry-config:
          max-attempts: {{.Values.logFlowIngestion.serviceBusSenderConfig.exponentialRetryConfig.maxAttempts}}
          min-seconds-between: {{.Values.logFlowIngestion.serviceBusSenderConfig.exponentialRetryConfig.minSecondsBetween}}
          jitter-factor: {{.Values.logFlowIngestion.serviceBusSenderConfig.exponentialRetryConfig.jitterFactor}}
          client-timeout-seconds: {{.Values.logFlowIngestion.serviceBusSenderConfig.exponentialRetryConfig.clientTimeoutSeconds}}
        auth-config:
          is-workload-identity: "{{.Values.logFlowIngestion.serviceBusSenderConfig.authConfig.isWorkloadIdentity}}"
          workload-identity:
            client-id: "{{.Values.logFlowIngestion.serviceBusSenderConfig.authConfig.workloadIdentity.clientId}}"
