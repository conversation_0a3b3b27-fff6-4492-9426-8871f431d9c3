logging:
  level:
    ROOT: INFO

spring:
  application:
    name: log-flow-ingestion
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive
server:
  port: 8081
  ssl:
    enabled: true
    certificate: classpath:ssl-cert/log-flow-ingestion-ssl.pem
    certificate-private-key: classpath:ssl-cert/log-flow-ingestion-private-key.pem

log-flow-ingestion:
  launch-darkly-config:
    sdk-key: DO_NOT_COMMIT

  http-client-config:
    wiretap: false
    read-timeout-minutes: 20
    write-timeout-minutes: 20

  blob-service-client-config:
    exponential-retry-config:
      max-attempts: 5
      max-retry-delay-seconds: 30
      retry-delay-seconds: 10

  transfer-buffer-size-bytes: 1048576 # 1MB
  max-azure-upload-block-size-bytes: 1048576 # 1MB

  source-storage-accounts-azure-config:
    inglogpoc:
      storage-account-endpoint: https://inglogpoc.blob.core.windows.net/
      auth-config:
        azure-client-id: DO_NOT_COMMIT
        azure-client-secret: DO_NOT_COMMIT
        azure-tenant-id: DO_NOT_COMMIT
    SA2:
      storage-account-endpoint: https://sa2.blob.core.windows.net/
      auth-config:
        azure-client-id: DO_NOT_COMMIT
        azure-client-secret: DO_NOT_COMMIT
        azure-tenant-id: DO_NOT_COMMIT

  target-storage-account-azure-config:
    storage-account-endpoint: https://inglogpoctarget.blob.core.windows.net/
    auth-config:
      azure-client-id: DO_NOT_COMMIT
      azure-client-secret: DO_NOT_COMMIT
      azure-tenant-id: DO_NOT_COMMIT

  target-container-name: indexes

  service-bus-sender-config:
    queue-name: csflowevent
    exponential-retry-config:
      max-attempts: 5
      min-seconds-between: 1
      jitter-factor: 0.5
      client-timeout-seconds: 60
    auth-config:
      connection-string: Endpoint=sb://devcsdataeventbus.servicebus.windows.net/;SharedAccessKeyName=DO_NOT_COMMIT;SharedAccessKey=DO_NOT_COMMIT