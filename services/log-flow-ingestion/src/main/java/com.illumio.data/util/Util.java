package com.illumio.data.util;

import com.illumio.data.pojo.IngestionBlob;
import lombok.experimental.UtilityClass;

@UtilityClass
public class Util {

    public static String getTargetBlobName(final String sourceBlobName,
                                           final String uniqueParentPath,
                                           final IngestionBlob.BlobSourceType blobSourceType) {
        return String.format("%s/%s/%s", blobSourceType, uniqueParentPath, sourceBlobName);
    }

}
