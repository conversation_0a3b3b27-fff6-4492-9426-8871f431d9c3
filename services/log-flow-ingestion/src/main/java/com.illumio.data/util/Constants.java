package com.illumio.data.util;

public class Constants {

    public static final String LOG_FLOW_INGESTION_CONFIG_PREFIX = "log-flow-ingestion";

    public static final String LOG_FLOW_INGESTION_BASE_API_PATH = "/api/v1/azureIngestor";
    public static final String LOG_FLOW_INGESTION_INGEST_API = "/ingestLogFlowBlob";

    public static final String VALIDATION_CODE_RESPONSE_KEY = "validationResponse";

    public static final String BLOB_COPY_NAME_REGEX_PATTERN =
            "[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}/\\d{4}/\\d{2}/\\d{2}/flowindex-\\d{10}-\\d{10}.db.gz";

    public static final String BLOB_COPY_LOCATION_TYPE = "flowdata";

    public static final String LD_FEATURE_FLAG_ML_AUTOLABEL_ENABLED_TENANTS = "ml-autolabel-enabled";
    public static final String LD_AUTOLABEL_ENABLED_TENANTS_ATTRIBUTE_TENANT = "tenant";

    public static final String AZURE_SUBSCRIPTION_VALIDATION_EVENT_TYPE =
            "Microsoft.EventGrid.SubscriptionValidationEvent";

    public static final String LOG_FLOW_INGESTION_AWS_API_PATH = "/api/v1/awsIngestor";
    public static final String LOG_FLOW_INGESTION_AWS_INGEST_API = "/ingestLogFlowS3";
}
