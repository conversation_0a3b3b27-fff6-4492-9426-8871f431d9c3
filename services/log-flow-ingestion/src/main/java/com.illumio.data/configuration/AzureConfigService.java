package com.illumio.data.configuration;

import com.azure.core.credential.TokenCredential;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

// Separation from AzureBlobServiceClientService as multiple SAs might use the same TokenCredential
@Component
@RequiredArgsConstructor
public class AzureConfigService {

    private TokenCredential targetSACredential;
    private Map<String, TokenCredential> sourceSACredentials;

    private final LogFlowIngestionConfig logFlowIngestionConfig;

    @PostConstruct
    public void init() {
        sourceSACredentials =
                logFlowIngestionConfig.getSourceStorageAccountsAzureConfig().entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        Map.Entry::getKey,
                                        configEntry ->
                                                AzureCredentialFactory.createCredential(
                                                        configEntry.getValue().getAuthConfig())));
        targetSACredential =
                AzureCredentialFactory.createCredential(
                        logFlowIngestionConfig
                                .getTargetStorageAccountAzureConfig()
                                .getAuthConfig());
    }

    public LogFlowIngestionConfig.SAAzureConfig getConfigForTargetSA() {
        return logFlowIngestionConfig.getTargetStorageAccountAzureConfig();
    }

    public LogFlowIngestionConfig.SAAzureConfig getConfigForSourceSA(final String storageAccount) {
        return logFlowIngestionConfig.getSourceStorageAccountsAzureConfig().get(storageAccount);
    }

    public TokenCredential getCredentialForTargetSA() {
        return targetSACredential;
    }

    public TokenCredential getCredentialForSourceSA(final String storageAccount) {
        return sourceSACredentials.get(storageAccount);
    }

    public Set<String> getSourceSANames() {
        return sourceSACredentials.keySet();
    }
}
