package com.illumio.data.configuration;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Configuration
@RequiredArgsConstructor
public class AwsConfigService {

    private final LogFlowIngestionConfig logFlowIngestionConfig;

    private final Map<AwsS3ClientKey, S3Client> s3ClientsByAccount = new ConcurrentHashMap<>();

    public S3Client getS3ClientForAccount(final String accountId, final String region) {
        final AwsS3ClientKey s3ClientKey = new AwsS3ClientKey(accountId, region);
        return s3ClientsByAccount.computeIfAbsent(s3ClientKey, k -> createS3Client(accountId, region));
    }

    private S3Client createS3Client(final String accountId, final String region) {
        final StaticCredentialsProvider credentialsProvider = getS3CredentialsProvider(accountId);

        return S3Client.builder()
                .region(Region.of(region))
                .credentialsProvider(credentialsProvider)
                .build();
    }

    private StaticCredentialsProvider getS3CredentialsProvider(final String accountId) {
        LogFlowIngestionConfig.AwsAccountConfig accountConfig = logFlowIngestionConfig.getSourcebucketsawsconfig().get(accountId);

        if (accountConfig == null ||
                accountConfig.getAwsAccessKey() == null ||
                accountConfig.getAwsSecretKey() == null) {
            throw new IllegalArgumentException("No AWS credentials found for accountId=" + accountId);
        }

        return StaticCredentialsProvider.create(
                AwsBasicCredentials.create(
                        accountConfig.getAwsAccessKey(),
                        accountConfig.getAwsSecretKey()
                )
        );
    }

    @EqualsAndHashCode
    @AllArgsConstructor
    private static class AwsS3ClientKey {
        private String accountId;
        private String region;
    }

}