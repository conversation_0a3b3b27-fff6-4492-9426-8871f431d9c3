package com.illumio.data.configuration;

import com.azure.core.credential.TokenCredential;
import com.azure.core.http.HttpClient;
import com.azure.core.http.netty.NettyAsyncHttpClientBuilder;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import com.azure.storage.common.policy.RequestRetryOptions;
import com.azure.storage.common.policy.RetryPolicyType;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

@Configuration
@RequiredArgsConstructor
public class AzureBlobServiceClientService {

    private BlobServiceClient targetBlobServiceClient;
    private final Map<String, BlobServiceClient> sourceBlobServiceClients = new ConcurrentHashMap<>();

    private final AzureConfigService azureConfigService;
    private final LogFlowIngestionConfig logFlowIngestionConfig;

    public BlobServiceClient getTargetBlobServiceClient() {
        return Optional.ofNullable(targetBlobServiceClient)
                .orElseGet(() -> {
                    targetBlobServiceClient = blobServiceClient(
                            azureConfigService.getConfigForTargetSA().getStorageAccountEndpoint(),
                            azureConfigService.getCredentialForTargetSA());
                    return targetBlobServiceClient;
                });
    }

    public BlobServiceClient getSourceBlobServiceClient(final String storageAccount) {
        if (!azureConfigService.getSourceSANames().contains(storageAccount)) {
            throw new IllegalArgumentException(
                    "Storage account credentials are not configured for storageAccount=" + storageAccount);
        }

        return sourceBlobServiceClients.computeIfAbsent(storageAccount, key -> {
            final LogFlowIngestionConfig.SAAzureConfig saAzureConfig = azureConfigService
                    .getConfigForSourceSA(storageAccount);
            final TokenCredential storageAccountCredential = azureConfigService
                    .getCredentialForSourceSA(storageAccount);
            return blobServiceClient(saAzureConfig.getStorageAccountEndpoint(), storageAccountCredential);
        });
    }

    private BlobServiceClient blobServiceClient(final String endpoint,
                                                                 final TokenCredential credential) {
        final HttpClient httpClient = new NettyAsyncHttpClientBuilder()
                .wiretap(logFlowIngestionConfig.getHttpClientConfig().getWiretap())
                .readTimeout(Duration.ofMinutes(logFlowIngestionConfig.getHttpClientConfig().getReadTimeoutMinutes()))
                .writeTimeout(Duration.ofMinutes(logFlowIngestionConfig.getHttpClientConfig().getWriteTimeoutMinutes()))
                .build();

        final LogFlowIngestionConfig.BlobServiceClientConfig.ExponentialRetryConfig exponentialRetryConfig =
                logFlowIngestionConfig.getBlobServiceClientConfig().getExponentialRetryConfig();
        return new BlobServiceClientBuilder()
                .endpoint(endpoint)
                .credential(credential)
                .retryOptions(new RequestRetryOptions(RetryPolicyType.EXPONENTIAL,
                        exponentialRetryConfig.getMaxAttempts(),
                        null,
                        Duration.ofSeconds(exponentialRetryConfig.getRetryDelaySeconds()),
                        Duration.ofSeconds(exponentialRetryConfig.getMaxRetryDelaySeconds()), null))
                .httpClient(httpClient)
                .buildClient();
    }

}
