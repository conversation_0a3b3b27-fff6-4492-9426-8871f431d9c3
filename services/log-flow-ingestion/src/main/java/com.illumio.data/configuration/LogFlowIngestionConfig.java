package com.illumio.data.configuration;

import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

import static com.illumio.data.util.Constants.LOG_FLOW_INGESTION_CONFIG_PREFIX;

@Configuration
@ConfigurationProperties(prefix = LOG_FLOW_INGESTION_CONFIG_PREFIX)
@Getter
@Setter
public class LogFlowIngestionConfig {

    private final LaunchDarklyConfig launchDarklyConfig = new LaunchDarklyConfig();
    private final Map<String, SAAzureConfig> sourceStorageAccountsAzureConfig = new HashMap<>();
    private final Map<String, AwsAccountConfig> sourcebucketsawsconfig = new HashMap<>();
    private final SAAzureConfig targetStorageAccountAzureConfig = new SAAzureConfig();
    private String targetContainerName;
    private final ServiceBusSenderConfig serviceBusSenderConfig = new ServiceBusSenderConfig();
    private final HttpClientConfig httpClientConfig = new HttpClientConfig();
    private final BlobServiceClientConfig blobServiceClientConfig = new BlobServiceClientConfig();
    private Integer transferBufferSizeBytes;

    @Configuration
    @Getter
    @Setter
    public static class LaunchDarklyConfig {
        private String sdkKey;
    }

    @Configuration
    @Getter
    @Setter
    public static class ServiceBusSenderConfig {
        private String namespace;
        private String queueName;
        private ExponentialRetryConfig exponentialRetryConfig;
        private AzureAuthConfig authConfig;
    }

    @Configuration
    @Getter
    @Setter
    public static class ExponentialRetryConfig {
        private Integer maxAttempts;
        private Integer minSecondsBetween;
        private Integer clientTimeoutSeconds;
        private Double jitterFactor;
    }

    @Configuration
    @Getter
    @Setter
    public static class SAAzureConfig {
        private String storageAccountEndpoint;
        private AzureAuthConfig authConfig;
    }

    @Configuration
    @Getter
    @Setter
    public static class AzureAuthConfig {
        private String azureClientId;
        private String azureClientSecret;
        private String azureTenantId;
        private Boolean isManagedIdentity;
        private String managedIdentityClientId;
        private Boolean isWorkloadIdentity;
        private WorkloadIdentity workloadIdentity;
        private String connectionString;
    }

    @Getter
    @Setter
    public static class AwsAccountConfig {
        private String awsAccessKey;
        private String awsSecretKey;
    }

    @Configuration
    @Getter
    @Setter
    public static class HttpClientConfig {
        private Boolean wiretap;
        private Long readTimeoutMinutes;
        private Long writeTimeoutMinutes;
    }


    @Configuration
    @Getter
    @Setter
    public static class BlobServiceClientConfig {
        private ExponentialRetryConfig exponentialRetryConfig;

        @Configuration
        @Getter
        @Setter
        public static class ExponentialRetryConfig {
            private Integer maxAttempts;
            private Long maxRetryDelaySeconds;
            private Long retryDelaySeconds;
        }
    }
    @Configuration
    @Getter
    @Setter
    public static class WorkloadIdentity {
        private String clientId;
        private String tenantId;
        private String tokenFilePath;
    }

}
