package com.illumio.data.configuration;

import com.launchdarkly.sdk.server.integrations.reactor.LDReactorClient;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.scheduler.Schedulers;

import java.util.UUID;

@Slf4j
@Configuration
@RequiredArgsConstructor
public class LDReactorClientConfig {

    private LDReactorClient ldReactorClient;
    private final LogFlowIngestionConfig logFlowIngestionConfig;

    @Bean
    public LDReactorClient ldReactorClient() {
        if (null == ldReactorClient) {
            ldReactorClient = new LDReactorClient(
                    logFlowIngestionConfig.getLaunchDarklyConfig().getSdkKey(),
                    Schedulers.boundedElastic());
            if (!ldReactorClient.isInitialized()) {
                log.error("LD client not initialized. Flags may serve default values.");
            }
        }
        return ldReactorClient;
    }

    @Bean
    public String ldReactorClientUUID() {
        return UUID.randomUUID().toString();
    }

    @PreDestroy
    @SneakyThrows
    public void closeClient() {
        if (null != ldReactorClient) {
            ldReactorClient.close();
        }
    }

}
