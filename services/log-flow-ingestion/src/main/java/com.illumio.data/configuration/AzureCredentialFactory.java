package com.illumio.data.configuration;

import com.azure.core.credential.TokenCredential;
import com.azure.identity.ClientSecretCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.azure.identity.ManagedIdentityCredentialBuilder;
import com.azure.identity.WorkloadIdentityCredentialBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;
@Slf4j
public class AzureCredentialFactory {

    public static TokenCredential createCredential(final LogFlowIngestionConfig.AzureAuthConfig authConfig) {
        TokenCredential tokenCredential;
        if (Optional.ofNullable(authConfig)
                .map(LogFlowIngestionConfig.AzureAuthConfig::getIsWorkloadIdentity)
                .orElse(false)) {
            tokenCredential = getWorkloadIdentity(authConfig);

        } else if (Optional.ofNullable(authConfig)
                .map(LogFlowIngestionConfig.AzureAuthConfig::getIsManagedIdentity)
                .orElse(false)) {
            tokenCredential = new ManagedIdentityCredentialBuilder()
                    .clientId(authConfig.getManagedIdentityClientId())
                    .build();

        } else {
            ClientSecretCredential clientSecretCredential =
                    new ClientSecretCredentialBuilder()
                            .clientId(authConfig.getAzureClientId())
                            .clientSecret(authConfig.getAzureClientSecret())
                            .tenantId(authConfig.getAzureTenantId())
                            .build();
                return withLogging(clientSecretCredential);
        }
        return withLogging(tokenCredential);
    }

    private static TokenCredential withLogging(TokenCredential tokenCredential) {
        return request -> {
            log.debug("Requesting token for scopes={}", request.getScopes());
            return tokenCredential.getToken(request)
                    .doOnNext(accessToken -> log.debug("Acquired token, expires at={}", accessToken.getExpiresAt()))
                    .doOnError(error -> log.error("Failed to acquire token", error));
        };
    }

    public static TokenCredential getWorkloadIdentity(final LogFlowIngestionConfig.AzureAuthConfig authConfig) {
        final LogFlowIngestionConfig.WorkloadIdentity workloadIdentity =
                authConfig.getWorkloadIdentity();

        return new WorkloadIdentityCredentialBuilder()
                .clientId(workloadIdentity.getClientId())
                .tenantId(workloadIdentity.getTenantId())
                .tokenFilePath(workloadIdentity.getTokenFilePath())
                .build();

    }

}
