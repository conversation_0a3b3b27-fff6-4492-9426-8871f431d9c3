package com.illumio.data.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import java.util.concurrent.*;

@Configuration
public class SchedulerConfig {

    @Bean
    public Scheduler blobTransferScheduler() {
        final int maxThreadPoolQueueSize = 50000;
        final BlockingQueue<Runnable> threadPoolQueue = new ArrayBlockingQueue<>(maxThreadPoolQueueSize);
        final ExecutorService blobTransferExecutorService = new ThreadPoolExecutor(
                10,
                10,
                0,
                TimeUnit.MILLISECONDS,
                threadPoolQueue
        );

        return Schedulers.fromExecutor(blobTransferExecutorService);
    }

}
