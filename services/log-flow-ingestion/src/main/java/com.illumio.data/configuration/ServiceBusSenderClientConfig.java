package com.illumio.data.configuration;

import com.azure.core.amqp.AmqpRetryOptions;
import com.azure.core.credential.TokenCredential;
import com.azure.messaging.servicebus.ServiceBusClientBuilder;
import com.azure.messaging.servicebus.ServiceBusSenderAsyncClient;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.Optional;

import static com.illumio.data.configuration.AzureCredentialFactory.createCredential;

@Configuration
@RequiredArgsConstructor
public class ServiceBusSenderClientConfig {

    private final LogFlowIngestionConfig logFlowIngestionConfig;

    @Bean
    public ServiceBusSenderAsyncClient serviceBusSenderAsyncClient() {
        final ServiceBusClientBuilder.ServiceBusSenderClientBuilder serviceBusSenderClientBuilder =
                Optional.ofNullable(logFlowIngestionConfig.getServiceBusSenderConfig())
                        .map(LogFlowIngestionConfig.ServiceBusSenderConfig::getAuthConfig)
                        .map(LogFlowIngestionConfig.AzureAuthConfig::getIsWorkloadIdentity)
                        .orElse(false)
                ? serviceBusSenderClientBuilderFromWorkloadIdentity()
                : serviceBusSenderClientBuilderFromConnectionString();
        return serviceBusSenderClientBuilder
                .queueName(logFlowIngestionConfig.getServiceBusSenderConfig()
                        .getQueueName())
                .buildAsyncClient();
    }

    private ServiceBusClientBuilder.ServiceBusSenderClientBuilder serviceBusSenderClientBuilderFromConnectionString() {
        final LogFlowIngestionConfig.ExponentialRetryConfig retryConfig =
                logFlowIngestionConfig.getServiceBusSenderConfig().getExponentialRetryConfig();
        return new ServiceBusClientBuilder()
                .connectionString(logFlowIngestionConfig.getServiceBusSenderConfig()
                        .getAuthConfig().getConnectionString())
                .retryOptions(new AmqpRetryOptions()
                        .setTryTimeout(Duration.ofSeconds(retryConfig.getClientTimeoutSeconds())))
                .sender();
    }

    private ServiceBusClientBuilder.ServiceBusSenderClientBuilder serviceBusSenderClientBuilderFromWorkloadIdentity() {
        final String namespace = logFlowIngestionConfig.getServiceBusSenderConfig().getNamespace();
        final TokenCredential tokenCredential =
                createCredential(logFlowIngestionConfig.getServiceBusSenderConfig().getAuthConfig());
        return new ServiceBusClientBuilder()
                .credential(namespace, tokenCredential)
                .sender();
    }

}
