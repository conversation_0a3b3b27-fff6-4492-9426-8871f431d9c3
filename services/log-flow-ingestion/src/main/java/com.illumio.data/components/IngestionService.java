package com.illumio.data.components;

import com.illumio.data.components.azure.ServiceBusSenderService;
import com.illumio.data.pojo.IngestionBlob;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class IngestionService {

    private final IngestionValidationService ingestionValidationService;
    private final BlobTransferService blobTransferService;
    private final ServiceBusSenderService serviceBusSenderService;
    private final Scheduler blobTransferScheduler;

    @Setter
    private Scheduler schedulerOverride;

    public void beginOrQueueBlobIngestion(final IngestionBlob ingestionBlob) {
        final StopWatch ingestionStopWatch = new StopWatch();
        ingestionStopWatch.start();

        ingestionValidationService.shouldIngestBlob(ingestionBlob)
                .filter(Boolean.TRUE::equals)
                .publishOn(Optional.ofNullable(schedulerOverride).orElse(blobTransferScheduler))
                .flatMap(shouldIngest -> triggerBlobIngestion(ingestionBlob, ingestionStopWatch))
                .switchIfEmpty(Mono.empty())
                .doFinally(end -> {
                    if (ingestionStopWatch.isRunning()) {
                        ingestionStopWatch.stop();
                    }
                })
                .subscribeOn(Optional.ofNullable(schedulerOverride).orElse(Schedulers.boundedElastic()))
                .subscribe();
    }

    private Mono<Void> triggerBlobIngestion(final IngestionBlob ingestionBlob, final StopWatch ingestionStopWatch) {
        log.info("Ingesting blobName={} of sizeBytes={} from tenantId={} and cloudProvider={}",
                ingestionBlob.getSourceBlobName(), ingestionBlob.getSizeBytes(),
                ingestionBlob.getTenantId(), ingestionBlob.getBlobSourceType());
        final StopWatch activeIngestionStopWatch = new StopWatch();
        activeIngestionStopWatch.start();

        return blobTransferService.transferBlob(ingestionBlob)
                .publishOn(Optional.ofNullable(schedulerOverride).orElse(Schedulers.boundedElastic()))
                .doOnSuccess(uploadSuccess ->
                        serviceBusSenderService.sendBlobCopyLocationMessage(ingestionBlob)
                                .doOnSuccess(serviceBusSenderService ->
                                        log.info("Successfully ingested blobName={} of sizeBytes={} " +
                                                        "from tenantId={}. activeElapsedTimeMs={}, elapsedTimeMs={}",
                                                ingestionBlob.getTargetBlobName(),
                                                ingestionBlob.getSizeBytes(),
                                                ingestionBlob.getTenantId(),
                                                activeIngestionStopWatch.getTotalTimeMillis(),
                                                ingestionStopWatch.getTotalTimeMillis())
                                )
                                .subscribeOn(Optional.ofNullable(schedulerOverride).orElse(Schedulers.boundedElastic()))
                                .subscribe())
                .doFinally(end -> {
                    if (activeIngestionStopWatch.isRunning()) {
                        activeIngestionStopWatch.stop();
                    }
                    if (ingestionStopWatch.isRunning()) {
                        ingestionStopWatch.stop();
                    }
                });
    }

}