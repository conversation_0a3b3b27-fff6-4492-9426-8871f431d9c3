package com.illumio.data.components;

import com.illumio.data.components.aws.AwsBlobDownloadService;
import com.illumio.data.components.azure.AzureBlobDownloadService;
import com.illumio.data.components.azure.AzureBlobUploadService;
import com.illumio.data.configuration.LogFlowIngestionConfig;
import com.illumio.data.pojo.IngestionBlob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.io.BufferedInputStream;
import java.io.InputStream;

@Slf4j
@Service
@RequiredArgsConstructor
public class BlobTransferService {

    private final AwsBlobDownloadService awsBlobDownloadService;
    private final AzureBlobDownloadService azureBlobDownloadService;
    private final AzureBlobUploadService azureBlobUploadService;
    private final LogFlowIngestionConfig logFlowIngestionConfig;

    @SneakyThrows
    public Mono<Void> transferBlob(final IngestionBlob ingestionBlob) {
        return Mono.fromCallable(() -> {
            final InputStream blobInputStream = getBlobDownloader(ingestionBlob).getBlobInputStream(ingestionBlob);
            final int transferBufferSize = logFlowIngestionConfig.getTransferBufferSizeBytes();
            try (final BufferedInputStream inputStream = new BufferedInputStream(blobInputStream, transferBufferSize)) {
                azureBlobUploadService.uploadBlobToTarget(ingestionBlob, inputStream);
                return null;
            } catch (final Exception ex) {
                log.error("Error occurred while buffering blobName={} of sizeBytes={} from cloudProvider={} and tenantId={}.",
                        ingestionBlob.getTargetBlobName(), ingestionBlob.getSizeBytes(),
                        ingestionBlob.getBlobSourceType(), ingestionBlob.getTenantId(), ex);
                throw ex;
            }
        });
    }

    private BlobDownloadService getBlobDownloader(final IngestionBlob ingestionBlob) {
        return switch (ingestionBlob.getBlobSourceType()) {
            case AWS -> awsBlobDownloadService;
            case AZURE -> azureBlobDownloadService;
        };
    }

}
