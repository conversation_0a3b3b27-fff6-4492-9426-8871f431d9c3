package com.illumio.data.components.aws;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class AwsSnsSubscriptionService {

    private final WebClient webClient;

    public Mono<Void> confirmSubscription(final String subscribeURL, final String topicArn) {
        log.info("Confirming SNS subscription for topicArn={}", topicArn);
        return webClient.get()
                .uri(subscribeURL)
                .retrieve()
                .bodyToMono(Void.class)
                .doOnSuccess(success ->
                        log.info("SNS subscription confirmed successfully for topicArn={}.", topicArn))
                .doOnError(error ->
                        log.error("Error confirming SNS subscription for topicArn={}. ", topicArn, error));
    }
}
