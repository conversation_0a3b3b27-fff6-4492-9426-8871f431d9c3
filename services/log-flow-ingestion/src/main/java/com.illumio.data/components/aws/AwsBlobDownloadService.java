package com.illumio.data.components.aws;

import com.illumio.data.components.BlobDownloadService;
import com.illumio.data.configuration.AwsConfigService;
import com.illumio.data.pojo.IngestionBlob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

@Slf4j
@Service
@RequiredArgsConstructor
public class AwsBlobDownloadService implements BlobDownloadService {

    private final AwsConfigService awsConfigService;

    @SneakyThrows
    public ResponseInputStream<GetObjectResponse> getBlobInputStream(final IngestionBlob ingestionBlob) {
        log.info("Downloading AWS blobName={} of sizeBytes={} from bucket={}, accountId={} and tenantId={}.",
                ingestionBlob.getSourceBlobName(), ingestionBlob.getSizeBytes(), ingestionBlob.getSourceDirectory(),
                ingestionBlob.getSourceAccount(), ingestionBlob.getTenantId());
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        try {
            final S3Client s3Client = awsConfigService.getS3ClientForAccount(
                    ingestionBlob.getSourceAccount(), ingestionBlob.getSourceRegion());
            final GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(ingestionBlob.getSourceDirectory())
                    .key(ingestionBlob.getSourceBlobName())
                    .build();
            final ResponseInputStream<GetObjectResponse> blobInputStream = s3Client.getObject(getObjectRequest);

            stopWatch.stop();
            log.info("Successfully opened download stream for AWS blobName={} from storageAccount={} and tenantId={}. elapsedTimeMs={}",
                    ingestionBlob.getSourceBlobName(), ingestionBlob.getSourceAccount(), ingestionBlob.getTenantId(), stopWatch.getTotalTimeMillis());

            return blobInputStream;
        } catch (final Exception ex) {
            stopWatch.stop();
            log.error("Error occurred while downloading AWS blobName={} of sizeBytes={} from tenantId={} and cloudProvider={}. elapsedTimeMs={}",
                    ingestionBlob.getSourceBlobName(), ingestionBlob.getSizeBytes(), ingestionBlob.getTenantId(),
                    ingestionBlob.getBlobSourceType(), stopWatch.getTotalTimeMillis(), ex);
            throw ex;
        }
    }

}
