package com.illumio.data.components;

import com.launchdarkly.sdk.LDContext;
import com.launchdarkly.sdk.server.integrations.reactor.LDReactorClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import static com.illumio.data.util.Constants.LD_AUTOLABEL_ENABLED_TENANTS_ATTRIBUTE_TENANT;
import static com.illumio.data.util.Constants.LD_FEATURE_FLAG_ML_AUTOLABEL_ENABLED_TENANTS;

@Slf4j
@Service
@RequiredArgsConstructor
public class LDService {

    private final String ldReactorClientUUID;
    private final LDReactorClient ldReactorClient;

    public Mono<Boolean> ingestionIsEnabledForTenantId(final String tenantId) {
        log.info("Reading featureFlag={} from LaunchDarkly for tenantId={}",
                LD_FEATURE_FLAG_ML_AUTOLABEL_ENABLED_TENANTS, tenantId);

        final LDContext ldContext = LDContext.builder(ldReactorClientUUID)
                .set(LD_AUTOLABEL_ENABLED_TENANTS_ATTRIBUTE_TENANT, tenantId)
                .build();

        // if unable to retrieve flag value from LD, default to true as to prevent missing data at ML processing
        return ldReactorClient.boolVariation(LD_FEATURE_FLAG_ML_AUTOLABEL_ENABLED_TENANTS, ldContext, true);
    }

}
