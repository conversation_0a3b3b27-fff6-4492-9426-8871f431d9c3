package com.illumio.data.components.azure;

import com.azure.storage.blob.specialized.BlobInputStream;
import com.illumio.data.components.BlobDownloadService;
import com.illumio.data.configuration.AzureBlobServiceClientService;
import com.illumio.data.pojo.IngestionBlob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

@Slf4j
@Component
@RequiredArgsConstructor
public class AzureBlobDownloadService implements BlobDownloadService {

    private final AzureBlobServiceClientService azureBlobServiceClientService;

    @SneakyThrows
    public BlobInputStream getBlobInputStream(final IngestionBlob ingestionBlob) {
        log.info("Downloading Azure blobName={} from storageAccount={} and tenantId={}.",
                ingestionBlob.getSourceBlobName(), ingestionBlob.getSourceAccount(), ingestionBlob.getTenantId());
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        try {
            final BlobInputStream blobInputStream = azureBlobServiceClientService
                    .getSourceBlobServiceClient(ingestionBlob.getSourceAccount())
                    .getBlobContainerClient(ingestionBlob.getSourceDirectory())
                    .getBlobClient(ingestionBlob.getSourceBlobName())
                    .openInputStream();

            stopWatch.stop();
            log.info("Successfully opened download stream for Azure blobName={} from storageAccount={} and tenantId={}. elapsedTimeMs={}",
                    ingestionBlob.getSourceBlobName(), ingestionBlob.getSourceAccount(),
                    ingestionBlob.getTenantId(), stopWatch.getTotalTimeMillis());

            return blobInputStream;
        } catch (final Exception ex) {
            stopWatch.stop();
            log.error("Error occurred while downloading Azure blobName={} of sizeBytes={} from tenantId={} and cloudProvider={}. elapsedTimeMs={}",
                    ingestionBlob.getSourceBlobName(), ingestionBlob.getSizeBytes(), ingestionBlob.getTenantId(),
                    ingestionBlob.getBlobSourceType(), stopWatch.getTotalTimeMillis(), ex);
            throw ex;
        }
    }

}
