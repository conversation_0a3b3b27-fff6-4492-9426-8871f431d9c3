package com.illumio.data.components.azure;

import com.azure.storage.blob.BlobClient;
import com.illumio.data.configuration.AzureBlobServiceClientService;
import com.illumio.data.configuration.LogFlowIngestionConfig;
import com.illumio.data.pojo.IngestionBlob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.OutputStream;

@Slf4j
@Component
@RequiredArgsConstructor
public class AzureBlobUploadService {

    private final AzureBlobServiceClientService azureBlobServiceClientService;
    private final LogFlowIngestionConfig logFlowIngestionConfig;

    @SneakyThrows
    public void uploadBlobToTarget(final IngestionBlob ingestionBlob, final BufferedInputStream bufferedInputStream) {
        log.info("Uploading blobName={} of sizeBytes={} from tenantId={} to target storage account.",
                ingestionBlob.getTargetBlobName(), ingestionBlob.getSizeBytes(), ingestionBlob.getTenantId());
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        final BlobClient targetBlobClient = azureBlobServiceClientService
                .getTargetBlobServiceClient()
                .getBlobContainerClient(logFlowIngestionConfig.getTargetContainerName())
                .getBlobClient(ingestionBlob.getTargetBlobName());

        try (final OutputStream outputStream = targetBlobClient.getBlockBlobClient().getBlobOutputStream(true)) {
            bufferedInputStream.transferTo(outputStream);
            bufferedInputStream.close();
            outputStream.close(); // Explicitly closing rather than relying on try-with-resources results in quicker resource release

            stopWatch.stop();
            log.info("Successfully uploaded blobName={} of sizeBytes={} from tenantId={} to target storage account in elapsedTimeMs={}.",
                    ingestionBlob.getTargetBlobName(), ingestionBlob.getSizeBytes(), ingestionBlob.getTenantId(),
                    stopWatch.getTotalTimeMillis());
        } catch (final IOException ex) {
            stopWatch.stop();
            log.error("Error occurred while uploading blobName={} of sizeBytes={} from tenantId={} to target storage account. elapsedTimeMs={}",
                    ingestionBlob.getTargetBlobName(), ingestionBlob.getSizeBytes(), ingestionBlob.getTenantId(),
                    stopWatch.getTotalTimeMillis(), ex);
            throw ex;
        }
    }

}
