package com.illumio.data.components.azure;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.Map;

import static com.illumio.data.util.Constants.VALIDATION_CODE_RESPONSE_KEY;

@Slf4j
@Component
@RequiredArgsConstructor
public class EventGridSubscriptionValidationService {

    public Mono<Map<String, String>> validateSubscription(final String validationCode, final String subscriptionTopic) {
        log.info("Validating event grid subscription for subscriptionTopic={}", subscriptionTopic);
        return Mono.just(Collections.singletonMap(VALIDATION_CODE_RESPONSE_KEY, validationCode));
    }

}
