package com.illumio.data.components.azure;

import com.azure.messaging.servicebus.ServiceBusMessage;
import com.azure.messaging.servicebus.ServiceBusSenderAsyncClient;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.configuration.LogFlowIngestionConfig;
import com.illumio.data.pojo.BlobCopyLocation;
import com.illumio.data.pojo.IngestionBlob;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

import java.time.Duration;

@Slf4j
@Component
@RequiredArgsConstructor
public class ServiceBusSenderService {

    private final ServiceBusSenderAsyncClient serviceBusSenderAsyncClient;
    private final LogFlowIngestionConfig logFlowIngestionConfig;
    private final ObjectMapper objectMapper;

    public Mono<Void> sendBlobCopyLocationMessage(final IngestionBlob ingestionBlob) {
        log.info("Sending Service Bus message for blobName={} and tenantId={}.",
                ingestionBlob.getTargetBlobName(), ingestionBlob.getTenantId());
        final StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        final String messageBody = messageFromIngestionBlob(ingestionBlob);
        final ServiceBusMessage serviceBusMessage = new ServiceBusMessage(messageBody);
        return serviceBusSenderAsyncClient.sendMessage(serviceBusMessage)
                .retryWhen(getRetryDefinition(ingestionBlob))
                .publishOn(Schedulers.boundedElastic())
                .doOnSuccess(success -> {
                    stopWatch.stop();
                    log.info("Successfully sent Service Bus message for blobName={} from tenantId={} in elapsedTimeMs={}",
                            ingestionBlob.getTargetBlobName(), ingestionBlob.getTenantId(), stopWatch.getTotalTimeMillis());
                })
                .doOnError(error -> {
                    stopWatch.stop();
                    log.error("Error sending message to Service Bus for blobName={} from tenantId={}. elapsedTimeMs={}",
                            ingestionBlob.getTargetBlobName(), ingestionBlob.getTenantId(), stopWatch.getTotalTimeMillis(), error);
                })
                .then();
    }

    @SneakyThrows
    private String messageFromIngestionBlob(final IngestionBlob ingestionBlob) {
        final BlobCopyLocation blobCopyLocation = BlobCopyLocation.fromIngestionBlob(ingestionBlob);
        return objectMapper.writeValueAsString(blobCopyLocation);
    }

    private Retry getRetryDefinition(final IngestionBlob ingestionBlob) {
        final LogFlowIngestionConfig.ExponentialRetryConfig retryConfig =
                logFlowIngestionConfig.getServiceBusSenderConfig().getExponentialRetryConfig();
        return Retry.backoff(retryConfig.getMaxAttempts(), Duration.ofSeconds(retryConfig.getMinSecondsBetween()))
                .jitter(retryConfig.getJitterFactor())
                .doBeforeRetry(retrySignal ->
                        log.warn("Retrying Service Bus Message send for blobName={} from tenantId={} " +
                                "at attemptNumber={} due to={}",
                        ingestionBlob.getTargetBlobName(),
                        ingestionBlob.getTenantId(),
                        retrySignal.totalRetries(),
                        retrySignal.failure().getMessage()));
    }

}
