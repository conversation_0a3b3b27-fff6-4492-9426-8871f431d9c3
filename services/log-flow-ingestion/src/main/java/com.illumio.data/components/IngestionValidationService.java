package com.illumio.data.components;

import com.illumio.data.pojo.IngestionBlob;
import java.util.regex.Pattern;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import static com.illumio.data.util.Constants.BLOB_COPY_NAME_REGEX_PATTERN;

@Slf4j
@Component
@RequiredArgsConstructor
public class IngestionValidationService {

    private final LDService ldService;

    public Mono<Boolean> shouldIngestBlob(final IngestionBlob ingestionBlob) {
        return Mono.just(shouldIngestBlobBasedOnName(ingestionBlob))
                .flatMap(blobNameIngestionEnabled ->
                        blobNameIngestionEnabled
                                ? shouldIngestBlobBasedOnTenant(ingestionBlob)
                                : Mono.just(false));
    }

    private void logIngestionIgnoreReason(final IngestionBlob ingestionBlob, final String reason) {
        log.info("Ignoring ingestion call for blobName={} and tenantId={} as {}",
                ingestionBlob.getSourceBlobName(),
                ingestionBlob.getTenantId(), reason);
    }

    private Mono<Boolean> shouldIngestBlobBasedOnTenant(final IngestionBlob ingestionBlob) {
        return ldService.ingestionIsEnabledForTenantId(ingestionBlob.getTenantId())
                .doOnNext(tenantIngestionEnabled -> {
                    if (!tenantIngestionEnabled) {
                        logIngestionIgnoreReason(ingestionBlob,
                                "this tenant ID is not enabled for log flow ingestion.");
                    }
                });
    }

    private boolean shouldIngestBlobBasedOnName(final IngestionBlob ingestionBlob) {
        final Pattern blobNamePattern = Pattern.compile(BLOB_COPY_NAME_REGEX_PATTERN);
        final boolean blobNameIngestionEnabled = blobNamePattern.matcher(ingestionBlob.getSourceBlobName()).matches();
        if (!blobNameIngestionEnabled) {
            logIngestionIgnoreReason(ingestionBlob, "the blob name does not match ingestion blob name grammar.");
        }
        return blobNameIngestionEnabled;
    }

}
