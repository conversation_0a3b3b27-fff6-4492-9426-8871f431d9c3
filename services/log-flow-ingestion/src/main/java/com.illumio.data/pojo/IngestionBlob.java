package com.illumio.data.pojo;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.Nullable;

@Slf4j
@Data
@Builder
public class IngestionBlob {

    private String sourceBlobName;
    private String targetBlobName;
    private String sourceDirectory;
    private String sourceAccount;
    private String tenantId;
    private BlobSourceType blobSourceType;
    private long sizeBytes;

    @Nullable private String sourceRegion;

    public enum BlobSourceType {
        AWS,
        AZURE
    }

}
