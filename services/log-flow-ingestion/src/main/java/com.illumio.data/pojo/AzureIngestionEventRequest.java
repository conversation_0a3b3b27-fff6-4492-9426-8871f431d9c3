package com.illumio.data.pojo;

import lombok.Data;

@Data
public class AzureIngestionEventRequest {

    private String topic;
    private String subject;
    private String eventType;
    private String id;
    private DataPayload data;
    private String dataVersion;
    private String metadataVersion;
    private String eventTime;

    @Data
    public static class DataPayload {
        private String api;
        private String clientRequestId;
        private String requestId;
        private String eTag;
        private String contentType;
        private long contentLength;
        private String blobType;
        private String url;
        private String sequencer;
        private StorageDiagnostics storageDiagnostics;

        // Azure webhook verification properties
        private String validationCode;
        private String validationUrl;

        @Data
        public static class StorageDiagnostics {
            private String batchId;
        }
    }
}