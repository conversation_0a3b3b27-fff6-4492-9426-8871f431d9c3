package com.illumio.data.pojo.mappers;

import com.illumio.data.pojo.AwsSnsNotificationRequest;
import com.illumio.data.pojo.IngestionBlob;
import com.illumio.data.util.Util;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@UtilityClass
public class AwsIngestionBlobMapper {

    public static IngestionBlob map(final AwsSnsNotificationRequest.Record awsSnsNotificationRecord,
                             final String topicArn) {
        final String bucket = awsSnsNotificationRecord.getS3().getBucket().getName();
        final String key = awsSnsNotificationRecord.getS3().getObject().getKey();

        log.info("Converting AWS ingestion event request from bucket={}, key={} to ingestion blob.",
                bucket, key);

        final String tenantId = extractTenantIdFromKey(key);
        final IngestionBlob.BlobSourceType blobSourceType = IngestionBlob.BlobSourceType.AWS;
        final String targetBlobName = Util.getTargetBlobName(key, bucket, blobSourceType);
        final String accountId = extractAccountIdFromArn(topicArn);
        final long size = awsSnsNotificationRecord.getS3().getObject().getSize();
        return IngestionBlob.builder()
                .sourceBlobName(key)
                .targetBlobName(targetBlobName)
                .tenantId(tenantId)
                .sourceDirectory(bucket)
                .sourceAccount(accountId)
                .sourceRegion(awsSnsNotificationRecord.getAwsRegion())
                .blobSourceType(IngestionBlob.BlobSourceType.AWS)
                .sizeBytes(size)
                .build();
    }

    private static String extractAccountIdFromArn(String topicArn) {
        final String[] arnParts = topicArn.split(":");
        if (arnParts.length >= 5) {
            return arnParts[4];
        }
        throw new IllegalArgumentException("Unexpected TopicArn format");
    }

    private static String extractTenantIdFromKey(String key) {
        final String[] parts = key.split("/");
        if (parts.length > 1) {
            return parts[0];
        }
        return null;
    }

}
