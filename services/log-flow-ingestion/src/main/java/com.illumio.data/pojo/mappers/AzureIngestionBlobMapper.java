package com.illumio.data.pojo.mappers;

import com.illumio.data.pojo.AzureIngestionEventRequest;
import com.illumio.data.pojo.IngestionBlob;
import com.illumio.data.util.Util;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.net.URI;
import java.nio.file.Path;
import java.nio.file.Paths;

@Slf4j
@UtilityClass
public class AzureIngestionBlobMapper {

    public static IngestionBlob map(final AzureIngestionEventRequest azureIngestionEventRequest) {
        log.info("Converting Azure ingestion event request with topic={}, subject={} to ingestion blob.",
                azureIngestionEventRequest.getTopic(),
                azureIngestionEventRequest.getSubject());

        final String storageAccount = extractStorageAccountFromTopic(azureIngestionEventRequest.getTopic());

        final String subject = azureIngestionEventRequest.getSubject();
        final String[] subjectParts = subject.split("/blobs/");
        if (subjectParts.length != 2) {
            throw new IllegalArgumentException("Unexpected subject format.");
        }

        final String containerName = extractContainerName(subjectParts[0]);
        final String sourceBlobName = subjectParts[1];
        final String tenantId = extractTenantIdFromBlobName(sourceBlobName);
        final IngestionBlob.BlobSourceType blobSourceType = IngestionBlob.BlobSourceType.AZURE;
        final String targetBlobName = Util.getTargetBlobName(sourceBlobName, storageAccount, blobSourceType);

        return IngestionBlob.builder()
                .sourceBlobName(sourceBlobName)
                .targetBlobName(targetBlobName)
                .sourceDirectory(containerName)
                .sourceAccount(storageAccount)
                .tenantId(tenantId)
                .blobSourceType(blobSourceType)
                .sizeBytes(azureIngestionEventRequest.getData().getContentLength())
                .build();
    }

    private static String extractStorageAccountFromTopic(final String topic) {
        final String[] parts = topic.split("/storageAccounts/");
        if (parts.length > 1) {
            return parts[1].split("/")[0];
        }
        throw new IllegalArgumentException("Unexpected topic format.");
    }

    private static String extractContainerName(final String subjectPath) {
        final URI uri = URI.create(subjectPath);
        final Path path = Paths.get(uri.getPath());

        for (int i = 0; i < path.getNameCount(); i++) {
            if (path.getName(i).toString().equals("containers")) {
                return path.getName(i + 1).toString();
            }
        }
        throw new IllegalArgumentException("Container name not found.");
    }

    private static String extractTenantIdFromBlobName(final String blobName) {
        final String[] parts = blobName.split("/");
        if (parts.length >= 2) {
            return parts[0];
        }
        return null;
    }

}
