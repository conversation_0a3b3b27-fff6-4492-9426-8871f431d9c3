package com.illumio.data.pojo;

import lombok.Builder;
import lombok.Data;

import static com.illumio.data.util.Constants.BLOB_COPY_LOCATION_TYPE;

@Data
@Builder
public class BlobCopyLocation {

    private String tenantId;
    private String type;
    private String storageSuffix;

    public static BlobCopyLocation fromIngestionBlob(final IngestionBlob ingestionBlob) {
        return BlobCopyLocation.builder()
                .tenantId(ingestionBlob.getTenantId())
                .type(BLOB_COPY_LOCATION_TYPE)
                .storageSuffix(ingestionBlob.getTargetBlobName())
                .build();
    }

}
