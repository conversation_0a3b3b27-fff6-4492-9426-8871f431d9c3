package com.illumio.data.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
public class AwsSnsNotificationRequest {

    @JsonProperty("Records")
    private List<Record> records;

    @Data
    public static class Record {

        @JsonProperty("eventVersion")
        private String eventVersion;

        @JsonProperty("eventSource")
        private String eventSource;

        @JsonProperty("awsRegion")
        private String awsRegion;

        @JsonProperty("eventTime")
        private String eventTime;

        @JsonProperty("eventName")
        private String eventName;

        @JsonProperty("userIdentity")
        private UserIdentity userIdentity;

        @JsonProperty("requestParameters")
        private RequestParameters requestParameters;

        @JsonProperty("responseElements")
        private ResponseElements responseElements;

        @JsonProperty("s3")
        private S3 s3;

        @Data
        public static class UserIdentity {
            @JsonProperty("principalId")
            private String principalId;
        }

        @Data
        public static class RequestParameters {
            @JsonProperty("sourceIPAddress")
            private String sourceIPAddress;
        }

        @Data
        public static class ResponseElements {
            @JsonProperty("x-amz-request-id")
            private String xAmzRequestId;

            @JsonProperty("x-amz-id-2")
            private String xAmzId2;
        }

        @Data
        public static class S3 {

            @JsonProperty("s3SchemaVersion")
            private String s3SchemaVersion;

            @JsonProperty("configurationId")
            private String configurationId;

            @JsonProperty("bucket")
            private Bucket bucket;

            @JsonProperty("object")
            private ObjectEntity object;

            @Data
            public static class Bucket {
                @JsonProperty("name")
                private String name;

                @JsonProperty("ownerIdentity")
                private OwnerIdentity ownerIdentity;

                @JsonProperty("arn")
                private String arn;

                @Data
                public static class OwnerIdentity {
                    @JsonProperty("principalId")
                    private String principalId;
                }
            }

            @Data
            public static class ObjectEntity {
                @JsonProperty("key")
                private String key;

                @JsonProperty("size")
                private long size;

                @JsonProperty("eTag")
                private String eTag;

                @JsonProperty("sequencer")
                private String sequencer;
            }
        }
    }
}