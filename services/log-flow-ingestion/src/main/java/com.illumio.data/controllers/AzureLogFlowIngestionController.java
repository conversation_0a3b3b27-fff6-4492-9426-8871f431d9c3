package com.illumio.data.controllers;

import com.illumio.data.components.azure.EventGridSubscriptionValidationService;
import com.illumio.data.components.IngestionService;
import com.illumio.data.pojo.IngestionBlob;
import com.illumio.data.pojo.AzureIngestionEventRequest;
import com.illumio.data.pojo.mappers.AzureIngestionBlobMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import static com.illumio.data.util.Constants.AZURE_SUBSCRIPTION_VALIDATION_EVENT_TYPE;
import static com.illumio.data.util.Constants.LOG_FLOW_INGESTION_BASE_API_PATH;
import static com.illumio.data.util.Constants.LOG_FLOW_INGESTION_INGEST_API;

@RestController
@RequiredArgsConstructor
@RequestMapping(LOG_FLOW_INGESTION_BASE_API_PATH)
public class AzureLogFlowIngestionController {

    private final IngestionService ingestionService;
    private final EventGridSubscriptionValidationService validationService;

    @PostMapping(LOG_FLOW_INGESTION_INGEST_API)
    public Mono<ResponseEntity<?>> ingestLogFlowBlob(final @RequestBody AzureIngestionEventRequest[] azureIngestionEventRequests) {
        return Flux.fromArray(azureIngestionEventRequests)
                .flatMap(azureIngestionEventRequest -> {
                    if (AZURE_SUBSCRIPTION_VALIDATION_EVENT_TYPE
                            .equals(azureIngestionEventRequest.getEventType())) {
                        return validationService.validateSubscription(
                                azureIngestionEventRequest.getData().getValidationCode(),
                                azureIngestionEventRequest.getTopic())
                                .flatMap(responseMap -> Mono.just(ResponseEntity.ok(responseMap)));
                    } else {
                        final IngestionBlob ingestionBlob =
                                AzureIngestionBlobMapper.map(azureIngestionEventRequest);
                        ingestionService.beginOrQueueBlobIngestion(ingestionBlob);
                        return Mono.just(ResponseEntity.accepted().build());
                    }
                })
                .collectList()
                .flatMap(responses -> responses.isEmpty() || !responses.get(0).hasBody()
                        ? Mono.just(ResponseEntity.accepted().build())
                        : Mono.just(ResponseEntity.ok(responses.get(0).getBody())));
    }

}