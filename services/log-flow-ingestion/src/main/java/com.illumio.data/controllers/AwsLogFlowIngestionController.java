package com.illumio.data.controllers;

import com.illumio.data.components.IngestionService;
import com.illumio.data.components.aws.AwsSnsSubscriptionService;
import com.illumio.data.pojo.AwsSnsConfirmationRequest;
import com.illumio.data.pojo.AwsSnsNotificationRequest;
import com.illumio.data.pojo.IngestionBlob;
import com.illumio.data.pojo.mappers.AwsIngestionBlobMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;


import static com.illumio.data.util.Constants.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(LOG_FLOW_INGESTION_AWS_API_PATH)
public class AwsLogFlowIngestionController {

    private final IngestionService ingestionService;
    private final AwsSnsSubscriptionService awsSnsSubscriptionService;

    // Endpoint for Subscription Confirmation
    @PostMapping(value = LOG_FLOW_INGESTION_AWS_INGEST_API,
            consumes = "application/json",
            headers = "x-amz-sns-message-type=SubscriptionConfirmation")
    public Mono<Void> handleSubscriptionConfirmation(final @RequestBody AwsSnsConfirmationRequest snsMessageBody) {
        return awsSnsSubscriptionService.confirmSubscription(
                snsMessageBody.getSubscribeURL(), snsMessageBody.getTopicArn());
    }

    // Endpoint for Notifications
    @PostMapping(value = LOG_FLOW_INGESTION_AWS_INGEST_API,
            consumes = "application/json",
            headers = "x-amz-sns-message-type=Notification")
    public Mono<ResponseEntity<Void>> handleNotification(
            final @RequestBody AwsSnsNotificationRequest snsMessageBody,
            final @RequestHeader("x-amz-sns-topic-arn") String topicArn) {
        log.info("Processing Notification for topicArn={} with snsMessageBody={}", topicArn, snsMessageBody);

        if (null == snsMessageBody.getRecords()) {
            log.info("No records found in received SNS notification for topicArn={}. Ignoring notification", topicArn);
            return Mono.just(ResponseEntity.badRequest().build());
        }

        snsMessageBody.getRecords().forEach(record -> {
            final IngestionBlob ingestionBlob =
                    AwsIngestionBlobMapper.map(snsMessageBody.getRecords().get(0), topicArn);
            ingestionService.beginOrQueueBlobIngestion(ingestionBlob);
        });

        return Mono.just(ResponseEntity.accepted().build());
    }
}
