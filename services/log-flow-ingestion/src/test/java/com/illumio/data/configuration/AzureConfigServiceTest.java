package com.illumio.data.configuration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.azure.core.credential.TokenCredential;

import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AzureConfigServiceTest {

    @Mock private LogFlowIngestionConfig logFlowIngestionConfig;

    @Mock private LogFlowIngestionConfig.SAAzureConfig targetConfig;
    @Mock private LogFlowIngestionConfig.AzureAuthConfig targetAuthConfig;

    @Mock private LogFlowIngestionConfig.SAAzureConfig sourceConfig;
    @Mock private LogFlowIngestionConfig.AzureAuthConfig sourceAuthConfig;

    @Mock private TokenCredential targetSACredential;
    @Mock private TokenCredential sourceSACredential;

    @InjectMocks private AzureConfigService azureConfigService;

    @BeforeEach
    void setUp() {
        when(logFlowIngestionConfig.getTargetStorageAccountAzureConfig()).thenReturn(targetConfig);
        when(targetConfig.getAuthConfig()).thenReturn(targetAuthConfig);

        final Map<String, LogFlowIngestionConfig.SAAzureConfig> sourceConfigMap = Map.of("sourceSA1", sourceConfig);
        when(logFlowIngestionConfig.getSourceStorageAccountsAzureConfig()).thenReturn(sourceConfigMap);
        when(sourceConfig.getAuthConfig()).thenReturn(sourceAuthConfig);

        try (var credentialFactory = mockStatic(AzureCredentialFactory.class)) {
            credentialFactory.when(() -> AzureCredentialFactory.createCredential(targetAuthConfig))
                    .thenReturn(targetSACredential);
            credentialFactory.when(() -> AzureCredentialFactory.createCredential(sourceAuthConfig))
                    .thenReturn(sourceSACredential);

            azureConfigService.init();

            credentialFactory.verify(() -> AzureCredentialFactory.createCredential(targetAuthConfig));
            credentialFactory.verify(() -> AzureCredentialFactory.createCredential(sourceAuthConfig));
        }
    }

    @Test
    void testGetCredentialForTargetSA() {
        final TokenCredential credential = azureConfigService.getCredentialForTargetSA();
        assertNotNull(credential);
        assertEquals(targetSACredential, credential);
    }

    @Test
    void testGetCredentialForSourceSA() {
        final TokenCredential credential = azureConfigService.getCredentialForSourceSA("sourceSA1");
        assertNotNull(credential);
        assertEquals(sourceSACredential, credential);
    }

    @Test
    void testGetConfigForTargetSA() {
        final LogFlowIngestionConfig.SAAzureConfig config = azureConfigService.getConfigForTargetSA();
        assertNotNull(config);
        assertEquals(targetConfig, config);
    }

    @Test
    void testGetConfigForSourceSA() {
        final LogFlowIngestionConfig.SAAzureConfig config =
                azureConfigService.getConfigForSourceSA("sourceSA1");
        assertNotNull(config);
        assertEquals(sourceConfig, config);
    }

    @Test
    void testGetSourceSANames() {
        final Set<String> sourceSANames = azureConfigService.getSourceSANames();
        assertNotNull(sourceSANames);
        assertEquals(1, sourceSANames.size());
        assertTrue(sourceSANames.contains("sourceSA1"));
    }

}
