package com.illumio.data.pojo.mappers;

import com.illumio.data.pojo.AwsSnsNotificationRequest;
import com.illumio.data.pojo.IngestionBlob;
import com.illumio.data.util.Util;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class AwsIngestionBlobMapperTest {

    private final String ingestionBlobBucket = "test-S3-bucket";
    private final String ingestionBlobS3Account = "************";
    private final String ingestionBlobS3Region = "test-S3-region";
    private final String ingestionBlobTenantId = "042ee3d2-81a4-432f-9040-698c5abc5e8a";
    private final String ingestionBlobName =
            String.format("%s/2024/07/31/flowindex-**********-**********.db.gz", ingestionBlobTenantId);
    private final long ingestionBlobSize = 1024 * 1024 * 1024;

    private final AwsSnsNotificationRequest.Record snsRecord =
            new AwsSnsNotificationRequest.Record();
    private final String topicArn =
            String.format("arn:aws:sns:us-east-2:%s:trigger-test", ingestionBlobS3Account);

    private final IngestionBlob expectedIngestionBlob = IngestionBlob.builder()
            .sourceBlobName(ingestionBlobName)
            .targetBlobName(Util.getTargetBlobName(ingestionBlobName, ingestionBlobBucket, IngestionBlob.BlobSourceType.AWS))
            .sourceDirectory(ingestionBlobBucket)
            .sourceAccount(ingestionBlobS3Account)
            .sourceRegion(ingestionBlobS3Region)
            .tenantId(ingestionBlobTenantId)
            .blobSourceType(IngestionBlob.BlobSourceType.AWS)
            .sizeBytes(ingestionBlobSize)
            .build();

    @BeforeEach
    public void setUp() {
        final AwsSnsNotificationRequest.Record.S3 s3 = new AwsSnsNotificationRequest.Record.S3();
        final AwsSnsNotificationRequest.Record.S3.ObjectEntity objectEntity = new AwsSnsNotificationRequest.Record.S3.ObjectEntity();
        objectEntity.setKey(ingestionBlobName);
        objectEntity.setSize(ingestionBlobSize);
        s3.setObject(objectEntity);
        final AwsSnsNotificationRequest.Record.S3.Bucket bucket = new AwsSnsNotificationRequest.Record.S3.Bucket();
        bucket.setName(ingestionBlobBucket);
        s3.setBucket(bucket);
        snsRecord.setS3(s3);
        snsRecord.setAwsRegion(ingestionBlobS3Region);
    }

    @Test
    public void mapsCorrectly() {
        final IngestionBlob result = AwsIngestionBlobMapper.map(snsRecord, topicArn);
        assertEquals(expectedIngestionBlob, result);
    }

}
