package com.illumio.data.components.azure;
import com.azure.storage.blob.*;
import com.azure.storage.blob.specialized.BlobInputStream;
import com.illumio.data.configuration.AzureBlobServiceClientService;
import com.illumio.data.pojo.IngestionBlob;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AzureBlobDownloadServiceTest {

    @Mock private AzureBlobServiceClientService azureBlobServiceClientService;

    @Mock private BlobClient blobClient;
    @Mock private BlobContainerClient blobContainerClient;
    @Mock private BlobServiceClient blobServiceClient;

    @InjectMocks
    private AzureBlobDownloadService azureBlobDownloadService;

    private final String ingestionBlobName = "test-blob";
    private final String ingestionBlobContainer = "test-container";
    private final String ingestionBlobSA = "test-SA";
    private final String ingestionBlobTenantId = "test-tenant";
    private final IngestionBlob ingestionBlob = IngestionBlob.builder()
            .sourceBlobName(ingestionBlobName)
            .sourceDirectory(ingestionBlobContainer)
            .sourceAccount(ingestionBlobSA)
            .tenantId(ingestionBlobTenantId)
            .build();

    @BeforeEach
    void setUp() {
        when(azureBlobServiceClientService.getSourceBlobServiceClient(ingestionBlob.getSourceAccount()))
                .thenReturn(blobServiceClient);
        when(blobServiceClient.getBlobContainerClient(ingestionBlob.getSourceDirectory()))
                .thenReturn(blobContainerClient);
        when(blobContainerClient.getBlobClient(ingestionBlob.getSourceBlobName()))
                .thenReturn(blobClient);
    }

    @Test
    void testGetBlobInputStreamSuccess() throws IOException {
        try (final BlobInputStream blobInputStream = mock(BlobInputStream.class)) {
            when(blobClient.openInputStream()).thenReturn(blobInputStream);

            final InputStream result = azureBlobDownloadService.getBlobInputStream(ingestionBlob);
            result.close();

            try (final BlobInputStream ignored = verify(blobClient, times(1)).openInputStream()) {
                assertEquals(blobInputStream, result);
            }
        }
    }

    @Test
    void testGetBlobInputStreamError() {
        when(blobClient.openInputStream()).thenThrow(RuntimeException.class);
        assertThrows(RuntimeException.class, () -> azureBlobDownloadService.getBlobInputStream(ingestionBlob));
    }

}
