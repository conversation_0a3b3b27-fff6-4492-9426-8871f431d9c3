package com.illumio.data.components.aws;

import com.illumio.data.configuration.AwsConfigService;
import com.illumio.data.pojo.IngestionBlob;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AwsBlobDownloadServiceTest {

    @Mock private AwsConfigService awsConfigService;

    @Mock private S3Client s3Client;

    @InjectMocks
    private AwsBlobDownloadService awsBlobDownloadService;

    private final String ingestionBlobName = "test-blob";
    private final String ingestionBlobBucket = "test-bucket";
    private final String ingestionBlobS3Account = "test-S3-account";
    private final String ingestionBlobS3Region = "test-S3-region";
    private final String ingestionBlobTenantId = "test-tenant";
    private final IngestionBlob ingestionBlob = IngestionBlob.builder()
            .sourceBlobName(ingestionBlobName)
            .sourceDirectory(ingestionBlobBucket)
            .sourceAccount(ingestionBlobS3Account)
            .sourceRegion(ingestionBlobS3Region)
            .tenantId(ingestionBlobTenantId)
            .build();
    private final GetObjectRequest getObjectRequest = GetObjectRequest.builder()
            .bucket(ingestionBlobBucket)
            .key(ingestionBlobName)
            .build();

    @BeforeEach
    void setUp() {
        when(awsConfigService.getS3ClientForAccount(eq(ingestionBlobS3Account), eq(ingestionBlobS3Region)))
                .thenReturn(s3Client);
    }

    @Test
    void testGetBlobInputStreamSuccess() throws IOException {
        try (final ResponseInputStream<GetObjectResponse> responseInputStream = mock(ResponseInputStream.class)) {
            when(s3Client.getObject(eq(getObjectRequest))).thenReturn(responseInputStream);

            final ResponseInputStream<GetObjectResponse> result = awsBlobDownloadService.getBlobInputStream(ingestionBlob);
            result.close();

            try (final ResponseInputStream<GetObjectResponse> ignored =
                         verify(s3Client, times(1)).getObject(eq(getObjectRequest))) {
                assertEquals(responseInputStream, result);
            }
        }
    }

    @Test
    void testGetBlobInputStreamError() {
        when(s3Client.getObject(eq(getObjectRequest))).thenThrow(RuntimeException.class);
        assertThrows(RuntimeException.class, () -> awsBlobDownloadService.getBlobInputStream(ingestionBlob));
    }

}
