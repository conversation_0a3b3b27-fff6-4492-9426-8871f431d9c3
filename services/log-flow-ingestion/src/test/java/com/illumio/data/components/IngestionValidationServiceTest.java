package com.illumio.data.components;

import com.illumio.data.pojo.IngestionBlob;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class IngestionValidationServiceTest {

    @Mock private LDService ldService;

    @InjectMocks private IngestionValidationService ingestionValidationService;

    private final String ingestionBlobContainer = "test-container";
    private final String ingestionBlobSA = "test-SA";
    private final String ingestionBlobTenantId = "042ee3d2-81a4-432f-9040-698c5abc5e8a";
    private final String ingestionBlobName =
            ingestionBlobTenantId + "/2024/03/19/flowindex-**********-**********.db.gz";
    private final IngestionBlob validIngestionBlob = IngestionBlob.builder()
            .sourceBlobName(ingestionBlobName)
            .sourceDirectory(ingestionBlobContainer)
            .sourceAccount(ingestionBlobSA)
            .tenantId(ingestionBlobTenantId)
            .build();

    @Test
    public void testShouldIngestBlob_whenTenantNotEnabled_shouldNotIngest() {
        when(ldService.ingestionIsEnabledForTenantId(ingestionBlobTenantId))
                .thenReturn(Mono.just(false));

        final Mono<Boolean> result = ingestionValidationService.shouldIngestBlob(validIngestionBlob);

        StepVerifier.create(result)
                .expectNext(false)
                .verifyComplete();
    }

    @Test
    public void testShouldIngestBlob_whenInvalidBlobName_shouldNotIngest() {
        final String invalidBlobName = ingestionBlobTenantId +
                "/2024/03/19/flowindex-**********-**********-partial.db.gz";
        final IngestionBlob invalidBlob = IngestionBlob.builder()
                .sourceBlobName(invalidBlobName)
                .sourceDirectory(ingestionBlobContainer)
                .sourceAccount(ingestionBlobSA)
                .tenantId(ingestionBlobTenantId)
                .build();

        final Mono<Boolean> result = ingestionValidationService.shouldIngestBlob(invalidBlob);

        StepVerifier.create(result)
                .expectNext(false)
                .verifyComplete();
    }


    @Test
    public void testShouldIngestBlob_whenTenantEnabled_andValidName_shouldIngest() {
        when(ldService.ingestionIsEnabledForTenantId(ingestionBlobTenantId))
                .thenReturn(Mono.just(true));

        final Mono<Boolean> result = ingestionValidationService.shouldIngestBlob(validIngestionBlob);

        StepVerifier.create(result)
                .expectNext(true)
                .verifyComplete();
    }

}
