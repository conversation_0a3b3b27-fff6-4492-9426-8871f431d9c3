package com.illumio.data.pojo;

import com.illumio.data.pojo.mappers.AzureIngestionBlobMapper;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

class IngestionBlobTest {

    private final String tenantId = "tenantId";
    private final String blobName = tenantId + "/date/logFlow.db";
    private final String containerName = "indexes";
    private final String storageAccount = "sa";
    private final String topic =
            "/subscriptions/subID/resourceGroups/rg/providers/Msft.Storage/storageAccounts/" + storageAccount;
    private final String subject = "/blobServices/default/containers/" + containerName + "/blobs/" + blobName;

    @Test
    void testFromIngestionEventRequest_validInput() {
        final AzureIngestionEventRequest azureIngestionEventRequest = ingestionEventRequest(topic, subject);
        final IngestionBlob result = AzureIngestionBlobMapper.map(azureIngestionEventRequest);

        assertNotNull(result);
        assertEquals(blobName, result.getSourceBlobName());
        assertEquals(containerName, result.getSourceDirectory());
        assertEquals(storageAccount, result.getSourceAccount());
        assertEquals(tenantId, result.getTenantId());
    }

    @Test
    void testFromIngestionEventRequest_invalidSubjectFormat() {
        final String invalidSubject = "/blobServices/default/containers/" + containerName;

        final AzureIngestionEventRequest azureIngestionEventRequest = ingestionEventRequest(topic, invalidSubject);

        assertThrows(IllegalArgumentException.class, () ->
                AzureIngestionBlobMapper.map(azureIngestionEventRequest));
    }

    @Test
    void testFromIngestionEventRequest_invalidTopicFormat() {
        final String invalidTopic = "/subscriptions/subID/resourceGroups/rg/providers/Msft.Storage";

        final AzureIngestionEventRequest azureIngestionEventRequest = ingestionEventRequest(invalidTopic, subject);

        assertThrows(IllegalArgumentException.class, () ->
                AzureIngestionBlobMapper.map(azureIngestionEventRequest));
    }

    private AzureIngestionEventRequest ingestionEventRequest(final String topic, final String subject) {
        final AzureIngestionEventRequest azureIngestionEventRequest = new AzureIngestionEventRequest();
        azureIngestionEventRequest.setTopic(topic);
        azureIngestionEventRequest.setSubject(subject);
        final AzureIngestionEventRequest.DataPayload dataPayload = new AzureIngestionEventRequest.DataPayload();
        azureIngestionEventRequest.setData(dataPayload);
        return azureIngestionEventRequest;
    }

}