package com.illumio.data.components;

import com.azure.storage.blob.specialized.BlobInputStream;
import com.illumio.data.components.aws.AwsBlobDownloadService;
import com.illumio.data.components.azure.AzureBlobDownloadService;
import com.illumio.data.components.azure.AzureBlobUploadService;
import com.illumio.data.configuration.LogFlowIngestionConfig;
import com.illumio.data.pojo.IngestionBlob;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;

import java.io.*;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BlobTransferServiceTest {

    @Mock private AwsBlobDownloadService awsBlobDownloadService;
    @Mock private AzureBlobDownloadService azureBlobDownloadService;
    @Mock private AzureBlobUploadService azureBlobUploadService;
    @Mock private LogFlowIngestionConfig logFlowIngestionConfig;

    @InjectMocks
    private BlobTransferService blobTransferService;

    private final String ingestionBlobName = "test-blob";
    private final String ingestionBlobBucket = "test-bucket";
    private final String ingestionBlobS3Account = "test-S3-account";
    private final String ingestionBlobS3Region = "test-S3-region";
    private final String ingestionBlobTenantId = "test-tenant";
    private final IngestionBlob ingestionBlob = IngestionBlob.builder()
            .sourceBlobName(ingestionBlobName)
            .sourceDirectory(ingestionBlobBucket)
            .sourceAccount(ingestionBlobS3Account)
            .sourceRegion(ingestionBlobS3Region)
            .tenantId(ingestionBlobTenantId)
            .build();

    @Test
    public void testTransferBlob_azureSource_success() {
        ingestionBlob.setBlobSourceType(IngestionBlob.BlobSourceType.AZURE);

        when(logFlowIngestionConfig.getTransferBufferSizeBytes()).thenReturn(1024);
        try (final BlobInputStream blobInputStream = mock(BlobInputStream.class)) {
            when(azureBlobDownloadService.getBlobInputStream(eq(ingestionBlob)))
                    .thenReturn(blobInputStream);

            final Mono<Void> result = blobTransferService.transferBlob(ingestionBlob);
            StepVerifier.create(result)
                    .verifyComplete();

            verify(azureBlobUploadService, times(1))
                    .uploadBlobToTarget(eq(ingestionBlob), any(BufferedInputStream.class));
            verifyNoMoreInteractions(azureBlobUploadService);
        }
    }

    @Test
    public void testTransferBlob_awsSource_success() throws IOException {
        ingestionBlob.setBlobSourceType(IngestionBlob.BlobSourceType.AWS);

        when(logFlowIngestionConfig.getTransferBufferSizeBytes()).thenReturn(1024);
        try (final ResponseInputStream<GetObjectResponse> responseInputStream = mock(ResponseInputStream.class)) {
            when(awsBlobDownloadService.getBlobInputStream(eq(ingestionBlob)))
                    .thenReturn(responseInputStream);

            final Mono<Void> result = blobTransferService.transferBlob(ingestionBlob);
            StepVerifier.create(result)
                    .verifyComplete();

            verify(azureBlobUploadService, times(1))
                    .uploadBlobToTarget(eq(ingestionBlob), any(BufferedInputStream.class));
            verifyNoMoreInteractions(azureBlobUploadService);
        }
    }

    @Test
    public void testTransferBlob_doesNotCorruptBlob() throws IOException {
        ingestionBlob.setBlobSourceType(IngestionBlob.BlobSourceType.AWS);
        when(logFlowIngestionConfig.getTransferBufferSizeBytes()).thenReturn(1);

        byte[] inputStreamBytes = "Hello, World! We're testing for corruption during blob transfer :)".getBytes();
        InputStream byteArrayInputStream = new ByteArrayInputStream(inputStreamBytes);

        final ResponseInputStream responseInputStream = mock(ResponseInputStream.class);
        when(responseInputStream.read(any(byte[].class), anyInt(), anyInt())).thenAnswer(invocation -> {
            byte[] buffer = invocation.getArgument(0);
            int offset = invocation.getArgument(1);
            int length = invocation.getArgument(2);
            return byteArrayInputStream.read(buffer, offset, length);
        });
        when(awsBlobDownloadService.getBlobInputStream(eq(ingestionBlob))).thenReturn(responseInputStream);

        final ByteArrayOutputStream capturedOutputStream = new ByteArrayOutputStream();
        doAnswer(invocation -> {
            final BufferedInputStream inputStream = invocation.getArgument(1);
            final byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                capturedOutputStream.write(buffer, 0, bytesRead);
            }
            return null;
        }).when(azureBlobUploadService).uploadBlobToTarget(eq(ingestionBlob), any(BufferedInputStream.class));

        final Mono<Void> result = blobTransferService.transferBlob(ingestionBlob);
        StepVerifier.create(result)
                .verifyComplete();

        final byte[] uploadedBytes = capturedOutputStream.toByteArray();
        assertArrayEquals(inputStreamBytes, uploadedBytes);
    }

    @Test
    void testGetBlobInputStreamError_onDownload() {
        ingestionBlob.setBlobSourceType(IngestionBlob.BlobSourceType.AZURE);

        when(azureBlobDownloadService.getBlobInputStream(eq(ingestionBlob))).thenThrow(RuntimeException.class);

        final Mono<Void> result = blobTransferService.transferBlob(ingestionBlob);
        StepVerifier.create(result)
                .verifyError(RuntimeException.class);
    }

    @Test
    void testGetBlobInputStreamError_onUpload() {
        ingestionBlob.setBlobSourceType(IngestionBlob.BlobSourceType.AZURE);

        when(logFlowIngestionConfig.getTransferBufferSizeBytes()).thenReturn(1024);
        doThrow(RuntimeException.class).when(azureBlobUploadService).
                uploadBlobToTarget(eq(ingestionBlob), any(BufferedInputStream.class));

        final Mono<Void> result = blobTransferService.transferBlob(ingestionBlob);
        StepVerifier.create(result)
                .verifyError(RuntimeException.class);
    }

}
