package com.illumio.data.components.azure;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;

import static com.illumio.data.util.Constants.VALIDATION_CODE_RESPONSE_KEY;
import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class EventGridSubscriptionValidationServiceTest {

    private final EventGridSubscriptionValidationService eventGridSubscriptionValidationService =
            new EventGridSubscriptionValidationService();

    @Test
    public void testValidateSubscription_ShouldReturnValidationCode() {
        final String validationCode = "validationCode";

        final Mono<Map<String, String>> validationResponse = eventGridSubscriptionValidationService
                .validateSubscription(validationCode, "topic");

        StepVerifier.create(validationResponse)
                .assertNext(responseMap -> {
                    assertEquals(1, responseMap.size());
                    assertEquals(validationCode, responseMap.get(VALIDATION_CODE_RESPONSE_KEY));
                })
                .expectComplete()
                .verify();
    }
}
