package com.illumio.data.pojo.mappers;

import com.illumio.data.pojo.AzureIngestionEventRequest;
import com.illumio.data.pojo.IngestionBlob;
import com.illumio.data.util.Util;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class AzureIngestionBlobMapperTest {

    private final String ingestionBlobContainer = "indexes";
    private final String ingestionBlobStorageAccount = "test-storage-account";
    private final String ingestionBlobTenantId = "042ee3d2-81a4-432f-9040-698c5abc5e8a";
    private final String ingestionBlobName =
            String.format("%s/2024/07/31/flowindex-**********-**********.db.gz", ingestionBlobTenantId);
    private final long ingestionBlobSize = 1024 * 1024 * 1024;

    private final AzureIngestionEventRequest ingestionEventRequest = new AzureIngestionEventRequest();

    private final IngestionBlob expectedIngestionBlob = IngestionBlob.builder()
            .sourceBlobName(ingestionBlobName)
            .targetBlobName(Util.getTargetBlobName(ingestionBlobName, ingestionBlobStorageAccount, IngestionBlob.BlobSourceType.AZURE))
            .sourceDirectory(ingestionBlobContainer)
            .sourceAccount(ingestionBlobStorageAccount)
            .sourceRegion(null)
            .tenantId(ingestionBlobTenantId)
            .blobSourceType(IngestionBlob.BlobSourceType.AZURE)
            .sizeBytes(ingestionBlobSize)
            .build();

    @BeforeEach
    public void setUp() {
        ingestionEventRequest.setTopic(String.format(
                "/subscriptions/********-23a0-4ed8-8d4c-b31aab57ffe1/resourceGroups/cloudsecure-data-stage-3-rg/providers/Microsoft.Storage/storageAccounts/%s",
                ingestionBlobStorageAccount));
        ingestionEventRequest.setSubject(String.format(
                "/blobServices/default/containers/%s/blobs/%s", ingestionBlobContainer, ingestionBlobName));
        final AzureIngestionEventRequest.DataPayload dataPayload = new AzureIngestionEventRequest.DataPayload();
        dataPayload.setContentLength(ingestionBlobSize);
        ingestionEventRequest.setData(dataPayload);
    }

    @Test
    public void mapsCorrectly() {
        final IngestionBlob result = AzureIngestionBlobMapper.map(ingestionEventRequest);
        assertEquals(expectedIngestionBlob, result);
    }

}
