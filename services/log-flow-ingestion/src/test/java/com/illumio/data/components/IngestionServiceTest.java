package com.illumio.data.components;

import com.illumio.data.components.azure.ServiceBusSenderService;
import com.illumio.data.pojo.IngestionBlob;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class IngestionServiceTest {

    @Mock private IngestionValidationService ingestionValidationService;
    @Mock private BlobTransferService blobTransferService;
    @Mock private ServiceBusSenderService serviceBusSenderService;

    @InjectMocks private IngestionService ingestionService;

    private final String ingestionBlobContainer = "test-container";
    private final String ingestionBlobSA = "test-SA";
    private final String ingestionBlobTenantId = "042ee3d2-81a4-432f-9040-698c5abc5e8a";
    private final String ingestionBlobName =
            ingestionBlobTenantId + "/2024/03/19/flowindex-**********-**********.db.gz";
    private final IngestionBlob ingestionBlob = IngestionBlob.builder()
            .sourceBlobName(ingestionBlobName)
            .sourceDirectory(ingestionBlobContainer)
            .sourceAccount(ingestionBlobSA)
            .tenantId(ingestionBlobTenantId)
            .blobSourceType(IngestionBlob.BlobSourceType.AZURE)
            .build();

    @BeforeEach
    public void setUp() {
        ingestionService.setSchedulerOverride(Schedulers.immediate());
    }

    @Test
    public void testIngestBlob_whenFailedValidation_shouldNotBeginIngestion() {
        when(ingestionValidationService.shouldIngestBlob(eq(ingestionBlob)))
                .thenReturn(Mono.just(false));

        ingestionService.beginOrQueueBlobIngestion(ingestionBlob);

        verify(ingestionValidationService, times(1))
                .shouldIngestBlob(eq(ingestionBlob));
        verify(blobTransferService, never()).transferBlob(any());
        verify(serviceBusSenderService, never()).sendBlobCopyLocationMessage(any());
    }

    @Test
    public void testIngestBlob_whenPassedValidation_shouldCompleteIngestion() {
        when(ingestionValidationService.shouldIngestBlob(any()))
                .thenReturn(Mono.just(true));
        when(blobTransferService.transferBlob(eq(ingestionBlob)))
                .thenReturn(Mono.empty());
        when(serviceBusSenderService.sendBlobCopyLocationMessage(any()))
                .thenReturn(Mono.empty());

        ingestionService.beginOrQueueBlobIngestion(ingestionBlob);

        verify(ingestionValidationService, times(1))
                .shouldIngestBlob(eq(ingestionBlob));
        verify(blobTransferService, times(1)).transferBlob(eq(ingestionBlob));
        verify(serviceBusSenderService, times(1))
                .sendBlobCopyLocationMessage(eq(ingestionBlob));
    }

    @Test
    public void testBeginBlobIngestion_whenTransferFails_shouldNotSendServiceBusMessage() {
        when(ingestionValidationService.shouldIngestBlob(eq(ingestionBlob)))
                .thenReturn(Mono.just(true));
        final String errorMessage = "Upload failed";
        final Mono<Void> transferMono = Mono.error(new RuntimeException(errorMessage));
        when(blobTransferService.transferBlob(eq(ingestionBlob)))
                .thenReturn(transferMono);

        ingestionService.beginOrQueueBlobIngestion(ingestionBlob);

        verify(ingestionValidationService, times(1))
                .shouldIngestBlob(eq(ingestionBlob));
        verify(serviceBusSenderService, never()).sendBlobCopyLocationMessage(any());
    }

}
