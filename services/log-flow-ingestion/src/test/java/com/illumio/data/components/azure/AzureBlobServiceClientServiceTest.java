package com.illumio.data.components.azure;

import com.azure.storage.blob.BlobServiceClient;
import com.azure.core.credential.TokenCredential;
import com.illumio.data.configuration.AzureBlobServiceClientService;
import com.illumio.data.configuration.AzureConfigService;
import com.illumio.data.configuration.LogFlowIngestionConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AzureBlobServiceClientServiceTest {

    @Mock private AzureConfigService azureConfigService;
    @Mock private LogFlowIngestionConfig logFlowIngestionConfig;
    @Mock private LogFlowIngestionConfig.SAAzureConfig saAzureConfig;
    @Mock private TokenCredential tokenCredential;

    @InjectMocks private AzureBlobServiceClientService azureBlobServiceClientService;

    private final String sourceSAEndpoint = "https://sourceaccount.blob.core.windows.net";
    private final LogFlowIngestionConfig.BlobServiceClientConfig.ExponentialRetryConfig exponentialRetryConfig =
            new LogFlowIngestionConfig.BlobServiceClientConfig.ExponentialRetryConfig();

    public void setUpForSuccessTest() {
        final LogFlowIngestionConfig.HttpClientConfig httpClientConfig =
                mock(LogFlowIngestionConfig.HttpClientConfig.class);
        final LogFlowIngestionConfig.BlobServiceClientConfig blobServiceClientConfig =
                mock(LogFlowIngestionConfig.BlobServiceClientConfig.class);
        final LogFlowIngestionConfig.BlobServiceClientConfig.ExponentialRetryConfig exponentialRetryConfig =
                mock(LogFlowIngestionConfig.BlobServiceClientConfig.ExponentialRetryConfig.class);
        when(logFlowIngestionConfig.getHttpClientConfig()).thenReturn(httpClientConfig);
        when(httpClientConfig.getWiretap()).thenReturn(false);
        when(httpClientConfig.getReadTimeoutMinutes()).thenReturn(1L);
        when(httpClientConfig.getWriteTimeoutMinutes()).thenReturn(1L);
        when(logFlowIngestionConfig.getBlobServiceClientConfig()).thenReturn(blobServiceClientConfig);
        when(blobServiceClientConfig.getExponentialRetryConfig()).thenReturn(exponentialRetryConfig);
        when(exponentialRetryConfig.getMaxAttempts()).thenReturn(1);
        when(exponentialRetryConfig.getRetryDelaySeconds()).thenReturn(1L);
        when(exponentialRetryConfig.getMaxRetryDelaySeconds()).thenReturn(1L);
    }

    @Test
    public void testGetTargetBlobServiceClient_WhenClientIsNull_ShouldInitializeClient() {
        setUpForSuccessTest();
        final String targetSAEndpoint = "https://targetaccount.blob.core.windows.net";
        when(azureConfigService.getConfigForTargetSA()).thenReturn(saAzureConfig);
        when(saAzureConfig.getStorageAccountEndpoint()).thenReturn(targetSAEndpoint);
        when(azureConfigService.getCredentialForTargetSA()).thenReturn(tokenCredential);

        final BlobServiceClient client = azureBlobServiceClientService.getTargetBlobServiceClient();

        assertNotNull(client);
        assertEquals(targetSAEndpoint, client.getAccountUrl());

        // Ensure single initialization
        final BlobServiceClient sameClient = azureBlobServiceClientService.getTargetBlobServiceClient();
        assertEquals(client, sameClient);
        verify(azureConfigService, times(1)).getConfigForTargetSA();
    }

    @Test
    public void testGetSourceBlobServiceClient_WhenClientIsNull_ShouldInitializeClient() {
        setUpForSuccessTest();
        final String storageAccount = "sourceAccount";
        when(azureConfigService.getSourceSANames()).thenReturn(Set.of(storageAccount));
        when(azureConfigService.getConfigForSourceSA(storageAccount)).thenReturn(saAzureConfig);
        when(saAzureConfig.getStorageAccountEndpoint()).thenReturn(sourceSAEndpoint);
        when(azureConfigService.getCredentialForSourceSA(storageAccount)).thenReturn(tokenCredential);

        final BlobServiceClient client = azureBlobServiceClientService.getSourceBlobServiceClient(storageAccount);

        assertNotNull(client);
        assertEquals(sourceSAEndpoint, client.getAccountUrl());

        // Ensure single initialization
        final BlobServiceClient sameClient = azureBlobServiceClientService.getSourceBlobServiceClient(storageAccount);
        assertEquals(client, sameClient);
        verify(azureConfigService, times(1)).getConfigForSourceSA(storageAccount);
    }

    @Test
    public void testGetSourceBlobServiceClient_ReturnsDifferentSourceClients() {
        setUpForSuccessTest();
        final String storageAccount1 = "sourceAccount1";
        final String storageAccount2 = "sourceAccount2";
        final String sourceSAEndpoint2 = sourceSAEndpoint + "2";
        final LogFlowIngestionConfig.SAAzureConfig saAzureConfig2 = mock(LogFlowIngestionConfig.SAAzureConfig.class);
        when(azureConfigService.getSourceSANames()).thenReturn(Set.of(storageAccount1, storageAccount2));
        when(azureConfigService.getConfigForSourceSA(storageAccount1)).thenReturn(saAzureConfig);
        when(azureConfigService.getConfigForSourceSA(storageAccount2)).thenReturn(saAzureConfig2);
        when(saAzureConfig.getStorageAccountEndpoint()).thenReturn(sourceSAEndpoint);
        when(saAzureConfig2.getStorageAccountEndpoint()).thenReturn(sourceSAEndpoint2);
        when(azureConfigService.getCredentialForSourceSA(storageAccount1)).thenReturn(tokenCredential);
        when(azureConfigService.getCredentialForSourceSA(storageAccount2)).thenReturn(tokenCredential);

        final BlobServiceClient client1 = azureBlobServiceClientService.getSourceBlobServiceClient(storageAccount1);
        final BlobServiceClient client2 = azureBlobServiceClientService.getSourceBlobServiceClient(storageAccount2);

        assertNotNull(client1);
        assertNotNull(client2);
        assertEquals(sourceSAEndpoint, client1.getAccountUrl());
        assertEquals(sourceSAEndpoint2, client2.getAccountUrl());
    }

    @Test
    public void testGetSourceBlobServiceClient_WhenStorageAccountNotConfigured_ShouldThrowException() {
        when(azureConfigService.getSourceSANames()).thenReturn(Set.of());

        assertThrows(IllegalArgumentException.class,
                () -> azureBlobServiceClientService.getSourceBlobServiceClient("unconfiguredAccount"));
    }

}