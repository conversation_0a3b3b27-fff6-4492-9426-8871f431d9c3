package com.illumio.data.components.azure;

import com.azure.storage.blob.*;
import com.azure.storage.blob.specialized.BlobOutputStream;
import com.azure.storage.blob.specialized.BlockBlobClient;
import com.illumio.data.configuration.AzureBlobServiceClientService;
import com.illumio.data.configuration.LogFlowIngestionConfig;
import com.illumio.data.pojo.IngestionBlob;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.BufferedInputStream;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AzureBlobUploadServiceTest {

    @Mock private AzureBlobServiceClientService azureBlobServiceClientService;
    @Mock private LogFlowIngestionConfig logFlowIngestionConfig;

    @Mock private BlockBlobClient blockBlobClient;
    @Mock private BlobClient blobClient;
    @Mock private BlobContainerClient blobContainerClient;
    @Mock private BlobServiceClient blobServiceClient;

    @InjectMocks private AzureBlobUploadService azureBlobUploadService;

    private final String sourceIngestionBlobName = "test-blob";
    private final String targetIngestionBlobName = "AZURE/test-blob";
    private final String ingestionBlobSA = "test-SA";
    private final String ingestionBlobTenantId = "test-tenant";
    private final IngestionBlob ingestionBlob = IngestionBlob.builder()
            .sourceBlobName(sourceIngestionBlobName)
            .targetBlobName(targetIngestionBlobName)
            .sourceAccount(ingestionBlobSA)
            .tenantId(ingestionBlobTenantId)
            .build();
    private final String targetContainerName = "target-container";

    @BeforeEach
    void setUp() {
        when(logFlowIngestionConfig.getTargetContainerName())
                .thenReturn(targetContainerName);
        when(azureBlobServiceClientService.getTargetBlobServiceClient())
                .thenReturn(blobServiceClient);
        when(blobServiceClient.getBlobContainerClient(eq(targetContainerName)))
                .thenReturn(blobContainerClient);
        when(blobContainerClient.getBlobClient(eq(targetIngestionBlobName)))
                .thenReturn(blobClient);
        when(blobClient.getBlockBlobClient())
                .thenReturn(blockBlobClient);
    }

    @Test
    void testUploadBlobToTargetSuccess() throws IOException {
        try (final BufferedInputStream bufferedInputStream = mock(BufferedInputStream.class);
             final BlobOutputStream blobOutputStream = mock(BlobOutputStream.class)) {

            when(blockBlobClient.getBlobOutputStream(true)).thenReturn(blobOutputStream);
            when(bufferedInputStream.transferTo(eq(blobOutputStream))).thenReturn(1L);

            azureBlobUploadService.uploadBlobToTarget(ingestionBlob, bufferedInputStream);

            verify(bufferedInputStream, times(1)).transferTo(eq(blobOutputStream));
            verify(bufferedInputStream, times(1)).close();
            verify(blobOutputStream, times(2)).close();
        }
    }

    @Test
    void testUploadBlobToTargetError() throws IOException {
        try (final BufferedInputStream bufferedInputStream = mock(BufferedInputStream.class)) {
            when(blockBlobClient.getBlobOutputStream(true)).thenThrow(RuntimeException.class);
            assertThrows(RuntimeException.class, () -> azureBlobUploadService.uploadBlobToTarget(ingestionBlob, bufferedInputStream));
        }
    }

}
