package com.illumio.data.components;

import com.azure.messaging.servicebus.ServiceBusMessage;
import com.azure.messaging.servicebus.ServiceBusSenderAsyncClient;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.azure.ServiceBusSenderService;
import com.illumio.data.configuration.LogFlowIngestionConfig;
import com.illumio.data.pojo.BlobCopyLocation;
import com.illumio.data.pojo.IngestionBlob;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ServiceBusSenderServiceTest {

    @Mock private ServiceBusSenderAsyncClient serviceBusSenderAsyncClient;
    @Mock private LogFlowIngestionConfig logFlowIngestionConfig;
    @Mock private ObjectMapper objectMapper;

    @InjectMocks private ServiceBusSenderService serviceBusSenderService;

    private final String ingestionBlobName = "test-blob";
    private final String ingestionBlobContainer = "test-container";
    private final String ingestionBlobSA = "test-SA";
    private final String ingestionBlobTenantId = "test-tenant";
    private final IngestionBlob ingestionBlob = IngestionBlob.builder()
            .sourceBlobName(ingestionBlobName)
            .sourceDirectory(ingestionBlobContainer)
            .sourceAccount(ingestionBlobSA)
            .tenantId(ingestionBlobTenantId)
            .build();

    private final BlobCopyLocation blobCopyLocation =
            BlobCopyLocation.fromIngestionBlob(ingestionBlob);

    private final String serviceBusMessageBody =
            "{\"tenantId\":\"tenantId\",\"type\":\"flowdata\",\"storageSuffix\":\"tenantId/flow_log.db\"}";

    @BeforeEach
    void setUp() throws JsonProcessingException {
        final LogFlowIngestionConfig.ServiceBusSenderConfig serviceBusSenderConfig =
                mock(LogFlowIngestionConfig.ServiceBusSenderConfig.class);
        final LogFlowIngestionConfig.ExponentialRetryConfig exponentialRetryConfig =
                mock(LogFlowIngestionConfig.ExponentialRetryConfig.class);
        when(logFlowIngestionConfig.getServiceBusSenderConfig())
                .thenReturn(serviceBusSenderConfig);
        when(serviceBusSenderConfig.getExponentialRetryConfig()).thenReturn(exponentialRetryConfig);
        when(exponentialRetryConfig.getMaxAttempts()).thenReturn(1);
        when(exponentialRetryConfig.getMinSecondsBetween()).thenReturn(1);
        when(exponentialRetryConfig.getJitterFactor()).thenReturn(0.5);

        when(objectMapper.writeValueAsString(blobCopyLocation)).thenReturn(serviceBusMessageBody);
    }

    @Test
    void testSendBlobCopyLocationMessageSuccess() {
        when(serviceBusSenderAsyncClient.sendMessage(any())).thenReturn(Mono.empty());

        final Mono<Void> result = serviceBusSenderService.sendBlobCopyLocationMessage(ingestionBlob);

        StepVerifier.create(result)
                .verifyComplete();

        final ArgumentCaptor<ServiceBusMessage> argumentCaptor =
                ArgumentCaptor.forClass(ServiceBusMessage.class);
        verify(serviceBusSenderAsyncClient).sendMessage(argumentCaptor.capture());

        final ServiceBusMessage capturedMessage = argumentCaptor.getValue();
        assertEquals(serviceBusMessageBody, capturedMessage.getBody().toString());
    }

    @Test
    void testSendBlobCopyLocationMessageError() {
        final String errorMessage = "Service bus error";
        when(serviceBusSenderAsyncClient.sendMessage(any())).thenReturn(Mono.error(new RuntimeException(errorMessage)));

        final Mono<Void> result = serviceBusSenderService.sendBlobCopyLocationMessage(ingestionBlob);

        StepVerifier.create(result)
                .expectErrorMatches(throwable -> throwable.getCause() instanceof RuntimeException &&
                        throwable.getCause().getMessage().equals(errorMessage))
                .verify();

        verify(serviceBusSenderAsyncClient, times(1)).sendMessage(any(ServiceBusMessage.class));
    }

}
