package com.illumio.data.controllers;

import com.illumio.data.components.azure.EventGridSubscriptionValidationService;
import com.illumio.data.components.IngestionService;
import com.illumio.data.pojo.IngestionBlob;
import com.illumio.data.pojo.AzureIngestionEventRequest;
import com.illumio.data.pojo.mappers.AzureIngestionBlobMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.Map;

import static com.illumio.data.util.Constants.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class AzureLogFlowIngestionControllerTest {

    @Mock private IngestionService ingestionService;
    @Mock private EventGridSubscriptionValidationService validationService;

    @InjectMocks private AzureLogFlowIngestionController azureLogFlowIngestionController;

    private WebTestClient webTestClient;

    private final AzureIngestionEventRequest validationRequest = new AzureIngestionEventRequest();
    private final AzureIngestionEventRequest ingestionRequest = new AzureIngestionEventRequest();
    private final AzureIngestionEventRequest.DataPayload validationDataPayload = new AzureIngestionEventRequest.DataPayload();

    @BeforeEach
    public void setUp() {
        webTestClient = WebTestClient.bindToController(azureLogFlowIngestionController).build();

        final String topic = "/subscriptions/subID/resourceGroups/rg/providers/Msft.Storage/storageAccounts/sa";

        validationRequest.setEventType(AZURE_SUBSCRIPTION_VALIDATION_EVENT_TYPE);
        validationRequest.setTopic(topic);
        validationRequest.setData(validationDataPayload);
        validationDataPayload.setValidationUrl("https://validation-url.com");
        validationDataPayload.setValidationCode("valcode");

        ingestionRequest.setEventType("BlobCreatedEvent");
        ingestionRequest.setTopic(topic);
        ingestionRequest.setSubject("/blobServices/default/containers/indexes/blobs/tenantId/logFlow.db");
        ingestionRequest.setData(new AzureIngestionEventRequest.DataPayload());
    }

    @Test
    public void testIngestLogFlowBlob_whenShouldUseValidationService() {
        final Map<String, String> validationResponseMap =
                Collections.singletonMap(VALIDATION_CODE_RESPONSE_KEY, "validationCode");
        when(validationService.validateSubscription(
                eq(validationDataPayload.getValidationCode()),
                eq(validationRequest.getTopic())))
                .thenReturn(Mono.just(validationResponseMap));

        webTestClient.post()
                .uri(LOG_FLOW_INGESTION_BASE_API_PATH + LOG_FLOW_INGESTION_INGEST_API)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(new AzureIngestionEventRequest[]{validationRequest})
                .exchange()
                .expectStatus().isOk()
                .expectBody(Map.class).isEqualTo(validationResponseMap);

        verify(validationService, times(1))
                .validateSubscription(eq(validationDataPayload.getValidationCode()), eq(validationRequest.getTopic()));
        verifyNoInteractions(ingestionService);
    }

    @Test
    public void testIngestLogFlowBlob_whenShouldUseIngestionService() {
        webTestClient.post()
                .uri(LOG_FLOW_INGESTION_BASE_API_PATH + LOG_FLOW_INGESTION_INGEST_API)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(new AzureIngestionEventRequest[]{ingestionRequest})
                .exchange()
                .expectStatus().isAccepted()
                .expectBody().isEmpty();

        verify(ingestionService, times(1))
                .beginOrQueueBlobIngestion(eq(AzureIngestionBlobMapper.map(ingestionRequest)));
        verifyNoInteractions(validationService);
    }

    @Test
    public void testIngestLogFlowBlob_shouldReturnServerError_OnValidationFail() {
        final AzureIngestionEventRequest ingestionRequest = new AzureIngestionEventRequest();
        ingestionRequest.setEventType(AZURE_SUBSCRIPTION_VALIDATION_EVENT_TYPE);
        ingestionRequest.setData(new AzureIngestionEventRequest.DataPayload());

        when(validationService.validateSubscription(any(), any()))
                .thenReturn(Mono.error(new RuntimeException("Validation failed")));

        webTestClient.post()
                .uri(LOG_FLOW_INGESTION_BASE_API_PATH + LOG_FLOW_INGESTION_INGEST_API)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(new AzureIngestionEventRequest[]{ingestionRequest})
                .exchange()
                .expectStatus().is5xxServerError();
    }

    @Test
    public void testIngestLogFlowBlob_shouldReturnServerError_OnIngestionFail() {
        doThrow(new RuntimeException("Ingestion failed"))
                .when(ingestionService).beginOrQueueBlobIngestion(any(IngestionBlob.class));
        webTestClient.post()
                .uri(LOG_FLOW_INGESTION_BASE_API_PATH + LOG_FLOW_INGESTION_INGEST_API)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(new AzureIngestionEventRequest[]{ingestionRequest})
                .exchange()
                .expectStatus().is5xxServerError();
    }
}