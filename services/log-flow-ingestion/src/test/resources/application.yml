logging:
  level:
    ROOT: INFO

spring:
  application:
    name: log-flow-ingestion
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive
server:
  port: 8080

log-flow-ingestion:
  launch-darkly-config:
    sdk-key: DO_NOT_COMMIT

  http-client-config:
    wiretap: false
    read-timeout-minutes: 20
    write-timeout-minutes: 20

  blob-service-client-config:
    exponential-retry-config:
      max-attempts: 5
      max-retry-delay-seconds: 30
      retry-delay-seconds: 10

  transfer-buffer-size-bytes: 1048576 # 1MB
  max-azure-upload-block-size-bytes: 1048576 # 1MB

  source-storage-accounts-azure-config:
    inglogpoc:
      storage-account-endpoint: https://inglogpoc.blob.core.windows.net/
      auth-config:
        azure-client-id: DO_NOT_COMMIT
        azure-client-secret: DO_NOT_COMMIT
        azure-tenant-id: tenant
    SA2:
      storage-account-endpoint: https://sa2.blob.core.windows.net/
      auth-config:
        azure-client-id: DO_NOT_COMMIT
        azure-client-secret: DO_NOT_COMMIT
        azure-tenant-id: tenant

  source-storage-accounts-aws-config:
    "************":
      auth-config:
        awsSecretKey: DO_NOT_COMMIT
        awsAccessKey: DO_NOT_COMMIT

  target-storage-account-azure-config:
    storage-account-endpoint: https://inglogpoctarget.blob.core.windows.net/
    auth-config:
      azure-client-id: DO_NOT_COMMIT
      azure-client-secret: DO_NOT_COMMIT
      azure-tenant-id: tenant

  target-container-name: indexes

  service-bus-sender-config:
    queue-name: csflowevent
    exponential-retry-config:
      max-attempts: 5
      min-seconds-between: 1
      jitter-factor: 0.5
      client-timeout-seconds: 60
    auth-config:
      connection-string: Endpoint=sb://devcsdataeventbus.servicebus.windows.net/;SharedAccessKeyName=DO_NOT_COMMIT;SharedAccessKey=DO_NOT_COMMIT
