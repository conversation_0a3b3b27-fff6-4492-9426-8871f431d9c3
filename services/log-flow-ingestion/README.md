# Log Flow Ingestion Connector

The **Log Flow Ingestion Connector** is a data connector that receives log flow meta-data via API calls. It handles the following tasks:
1. Copies log flow data from a source storage account (AWS or Azure) to a target storage account.
2. Publishes the location of the uploaded data to a Service Bus queue for consumption by a processing service.

---

## Prerequisites

- Credentials for the test AWS/Azure accounts. (Please contact the development team to obtain credentials --> LastPass.)

---

## Running Locally

### 1. Modify `application-local-servicebus.yaml`
Ensure the following configurations are set:

1. **LaunchDarkly SDK Key** (optional, as feature flag values have defaults).
2. **Source Storage Account Config** – Details of the source AWS/Azure storage account.
3. **Target Storage Account Config** – Details of the target storage account.
4. **Service Bus Config** – Configuration of the Service Bus.

### 2. Add Environment Variables for AWS Account

You will need to set the following environment variables:

```bash
set LOGFLOWINGESTION_SOURCEBUCKETSAWSCONFIG_<replace_with_account_id>_AWSACCESSKEY=<aws_access_key>
set LOGFLOWINGESTION_SOURCEBUCKETSAWSCONFIG_<replace_with_account_id>_AWSSECRETKEY=<aws_secret_key>
```


This will allow the application to use AWS credentials for accessing S3 buckets during the data ingestion process.

### 3. Running Locally Using IntelliJ

To run the application in IntelliJ, use the following configuration:

- **Run Configuration**: `log-flow-ingestion/LogFlowIngestionApplication+LocalServiceBus`

This configuration ensures that the application connects to the local service bus and the necessary storage accounts during execution.

### 4. Running on Docker Locally

#### 1. Building the Docker Image

To build the Docker image using Gradle, use the following command:

```bash
gradlew jibDockerBuild
```

 This will build a new Docker image locally based on your project configuration.

### 2. Running the Docker Container

To run the Docker container locally, follow these steps:

1. Set the required environment variables for the AWS account credentials using the `-e` option.
2. Mount the `application.yml` file to the container using the `-v` option to ensure proper configuration.
3. Expose the necessary port (`8080`) to access the service using the `-p` option.

Example command:

```bash
docker run -d \
  -e LOGFLOWINGESTION_SOURCEBUCKETSAWSCONFIG_<replace_with_account_id_AWSACCESSKEY=<your_aws_access_key> \
  -e LOGFLOWINGESTION_SOURCEBUCKETSAWSCONFIG_<replace_with_account_id_AWSSECRETKEY=<your_aws_secret_key> \
  -v C:/Users/<USER>/Project/AWSLogflowIngestion/services/log-flow-ingestion/src/main/resources/application.yml:/var/resources/application.yml \
  -p 8080:8081 illum.azurecr.io/log-flow-ingestion:1.0.0-c44c58b
 ```