plugins {
    id 'org.springframework.boot'
    id 'com.google.cloud.tools.jib'
    id 'com.github.johnrengelman.shadow'
}

apply from: "${project.rootDir}/gradle/scripts/jib-build.gradle"
apply from: "${project.rootDir}/gradle/scripts/opentelemetry.gradle"

dependencies {
    // implementation
    implementation project(":commons:azure-commons")
    implementation project(":commons:utility-commons")
    implementation platform("com.azure:azure-sdk-bom")
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'co.elastic.logging:logback-ecs-encoder'
    implementation 'com.launchdarkly:launchdarkly-java-server-sdk:7.4.1'
    implementation 'com.azure:azure-storage-blob'
    implementation 'com.azure:azure-identity'
    implementation 'com.azure:azure-messaging-servicebus'
    implementation 'com.azure:azure-core-amqp'

    //aws
    implementation 'software.amazon.awssdk:s3'
    implementation 'software.amazon.awssdk:auth'
    implementation 'software.amazon.awssdk:regions'
    implementation 'software.amazon.awssdk:netty-nio-client'
    implementation 'software.amazon.awssdk.crt:aws-crt:0.30.11'

    //jackson
    implementation 'com.fasterxml.jackson.core:jackson-annotations'

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // spring
    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"

    // test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
    testImplementation 'org.mockito:mockito-inline:3.11.2'
}

jib {
    container {
        entrypoint = [
                'java', '-cp', '@/app/jib-classpath-file',
                'com.illumio.data.LogFlowIngestionApplication',
                '--spring.config.location=file:/var/resources/application.yml',
                '--spring.profiles.active=prod'
        ]
        environment = [
                "JAVA_TOOL_OPTIONS": "-javaagent:/otel/opentelemetry-javaagent.jar",
                "OTEL_METRICS_EXPORTER": "prometheus",
                "OTEL_TRACES_EXPORTER": "none",
                "OTEL_LOGS_EXPORTER": "none"
        ]
    }
}
