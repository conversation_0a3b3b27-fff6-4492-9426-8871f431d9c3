# Default values for sink connector.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
logging:
  level:
    root: DEBUG
    
ports:
- name: admin
  port: 8084
- name: rest
  port: 8081

servicePorts:
- name: rest
  podPort: rest
  servicePort: 8081

service:
  type: ClusterIP

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

replicaCount: 1

image:
  repositoryBase: illum.azurecr.io/
  repositoryName: flow-decorator
  tag:      # value given at helm deployment 
  pullPolicy: Always

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 4
  targetCPUUtilizationPercentage: 60
  targetMemoryUtilizationPercentage: 60

podAnnotations: {}

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"



podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000



resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

extraLabels: {}

ansi:
  enabled: ALWAYS
webApplicationType: none
kafkaCommonConfig:
  bootstrapServers: imc-dev-1-eventhub-ns.servicebus.windows.net:9093
  isSasl: true
kafkaReceiverConfig:
  # EventHub configuration (reusing Kafka config keys for compatibility)
  bootstrapServers: imc-dev-1-eventhub-ns.servicebus.windows.net:9093  # EventHub namespace
  topic: aggregated-flow-v1  # EventHub name
  groupId: aggregated-flow-processor  # Consumer group
  autoOffsetReset: latest
  requestTimeoutMs: 180000
  maxPollRecords: 200
  maxPartitionFetchBytes: "1048576"
  # EventHub specific configurations
  eventHubConnectionString: <path:secret/data/wiz-eventhub/imc-dev-1-eventhub-cluster/cluster-info#EVENTHUB_CONNECTION_STRING>  # Will use managed identity for EventHub
  # Storage configuration for checkpoints
  storageEndpoint: "https://dev1flowdecorator.blob.core.windows.net/"  # Storage account endpoint for checkpoints
  storageContainerName: partition-info  # Container for storing checkpoints
  storageConnectionString: <path:secret/data/imc/flow-decorator/imc-dev-1#STORAGE_CONN_STRING>
  isStorageConnectionString: true
  isStorageManagedIdentity: false
kafkaSenderConfig:
  bootstrapServers: imc-dev-1-eventhub-ns.servicebus.windows.net:9093
  topic: decorated-flow-v1
inventoryConfig:
  host: inventory.data.azure.westus3.dev.cloud.ilabs.io
  port: 8080
  useTls: false
  cert: ""
processingConfig:
  maxConcurrentGrpc: 64
resilienceConfig:
  failureRateThreshold: 50
  waitDurationInOpenState: "10s"
  slidingWindowSize: 10
  maxAttempts: 3
  waitDuration: "1s"
  timeoutDuration: "5s"

eventhub:
  password:                 # should give at deployment time

