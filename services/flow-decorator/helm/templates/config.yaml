apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "FlowDecorator.fullname" . }}-env-configmap
  labels:
    {{- include "FlowDecorator.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
        org:
          apache:
            kafka: INFO
    
    spring:
      application:
        name: flow-decorator
      output:
        ansi:
          enabled: ALWAYS


    kafkaReceiverConfig:
      # EventHub configuration (reusing Kafka config keys for compatibility)
      bootstrapServers: "{{.Values.kafkaCommonConfig.bootstrapServers}}"
      isSasl: {{.Values.kafkaCommonConfig.isSasl}}
      topic: "{{.Values.kafkaReceiverConfig.topic}}"
      groupId: "{{.Values.kafkaReceiverConfig.groupId}}"
      autoOffsetReset: "{{.Values.kafkaReceiverConfig.autoOffsetReset}}"
      requestTimeoutMs: "{{.Values.kafkaReceiverConfig.requestTimeoutMs}}"
      maxPollRecords: "{{.Values.kafkaReceiverConfig.maxPollRecords}}"
      maxPartitionFetchBytes: "{{.Values.kafkaReceiverConfig.maxPartitionFetchBytes}}"
      # EventHub specific configurations
      eventHubConnectionString: "{{.Values.kafkaReceiverConfig.eventHubConnectionString}}"
      # Storage configuration for checkpoints
      storageEndpoint: "{{.Values.kafkaReceiverConfig.storageEndpoint}}"
      storageContainerName: "{{.Values.kafkaReceiverConfig.storageContainerName}}"
      isStorageConnectionString: {{.Values.kafkaReceiverConfig.isStorageConnectionString}}
      storageConnectionString: "{{.Values.kafkaReceiverConfig.storageConnectionString}}"
      isStorageManagedIdentity: {{.Values.kafkaReceiverConfig.isStorageManagedIdentity}}
    kafkaSenderConfig:
      bootstrapServers: "{{.Values.kafkaCommonConfig.bootstrapServers}}"
      isSasl: {{.Values.kafkaCommonConfig.isSasl}}
      topic: "{{.Values.kafkaSenderConfig.topic}}"
    inventoryConfig:
      host: "{{.Values.inventoryConfig.host}}"
      port: {{.Values.inventoryConfig.port}}
      useTls: {{.Values.inventoryConfig.useTls}}
      cert: "{{.Values.inventoryConfig.cert}}"
    processingConfig:
      maxConcurrentGrpc: {{.Values.processingConfig.maxConcurrentGrpc}}
    resilienceConfig:
      failureRateThreshold: "{{.Values.resilienceConfig.failureRateThreshold}}"
      waitDurationInOpenState: "{{.Values.resilienceConfig.waitDurationInOpenState}}"
      slidingWindowSize: "{{.Values.resilienceConfig.slidingWindowSize}}"
      maxAttempts: "{{.Values.resilienceConfig.maxAttempts}}"
      waitDuration: "{{.Values.resilienceConfig.waitDuration}}"
      timeoutDuration: "{{.Values.resilienceConfig.timeoutDuration}}"