apiVersion: v1
kind: Secret
metadata:
  name: {{ include "FlowDecorator.fullname" . }}-env-secrets
  labels:
    {{- include "FlowDecorator.labels" . | nindent 4 }}
type: Opaque
stringData:
  KAFKARECEIVERCONFIG_SASLJAASCONFIG: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.eventhub.password }}";
  KAFKASENDERCONFIG_SASLJAASCONFIG: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.eventhub.password }}";
  INVENTORYCONFIG_CERT: "{{ .Values.inventoryConfig.cert }}"

