apiVersion: v1
kind: Service
metadata:
  name: {{ include "FlowDecorator.name" . }}
  labels:
    {{- include "FlowDecorator.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "FlowDecorator.selectorLabels" . | nindent 4 }}
