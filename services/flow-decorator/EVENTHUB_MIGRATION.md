# EventHub Migration Guide

This document describes the migration from Reactor Kafka consumer to Azure EventHub consumer in the Flow Decorator service.

## Overview

The Flow Decorator service has been updated to use Azure EventHub instead of Apache Kafka for consuming flow data. The migration maintains backward compatibility by reusing existing configuration keys and structure.

## Key Changes

### 1. Dependencies
- Removed: `io.projectreactor.kafka:reactor-kafka`
- Added: 
  - `com.azure:azure-messaging-eventhubs`
  - `com.azure:azure-messaging-eventhubs-checkpointstore-blob`
  - `com.azure:azure-storage-blob`

### 2. Configuration
The existing `kafka-receiver-config` configuration keys are reused for EventHub configuration:

```yaml
kafkaReceiverConfig:
  # EventHub configuration (reusing Kafka config keys for compatibility)
  bootstrapServers: eventhubs-connector.servicebus.windows.net:9093  # EventHub namespace
  isSasl: true  # Keep for Kafka sender compatibility
  saslJaasConfig: "Kafka JAAS config for sender"  # Keep for Kafka sender compatibility
  topic: aggregated-flow-v1  # EventHub name
  groupId: flow-decorator-group  # Consumer group
  autoOffsetReset: latest
  requestTimeoutMs: 180000
  maxPollRecords: 500
  maxPartitionFetchBytes: "1048576"
  # EventHub specific configurations
  eventHubConnectionString: "Endpoint=sb://namespace.servicebus.windows.net/;SharedAccessKeyName=key;SharedAccessKey=value;EntityPath=eventhub"  # EventHub connection string
  storageConnectionString: ""  # For checkpoint store (optional, uses managed identity if empty)
  storageContainerName: eventhub-checkpoints
```

**Important**: The `eventHubConnectionString` is specifically for EventHub consumer authentication and should contain a proper EventHub connection string format. The `saslJaasConfig` is kept for Kafka sender compatibility.

### 3. Authentication
Two authentication methods are supported:

#### Connection String Authentication
```yaml
kafkaReceiverConfig:
  eventHubConnectionString: "Endpoint=sb://namespace.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=key;EntityPath=eventhub-name"
```

#### Managed Identity Authentication
```yaml
kafkaReceiverConfig:
  eventHubConnectionString: ""  # Empty to use managed identity
  bootstrapServers: namespace.servicebus.windows.net:9093
  topic: eventhub-name
```

### 4. Checkpoint Store
EventHub requires a checkpoint store for maintaining consumer position. Azure Blob Storage is used:

- **Connection String**: Set `storageConnectionString` for direct access
- **Managed Identity**: Leave `storageConnectionString` empty to use managed identity

## Configuration Examples

### Local Development
```yaml
kafkaReceiverConfig:
  bootstrapServers: localhost:19092
  isSasl: false
  saslJaasConfig: _DO_NOT_COMMIT_  # Keep for Kafka sender compatibility
  topic: aggregated-flow-v1
  groupId: flow-decorator-group
  autoOffsetReset: latest
  requestTimeoutMs: 180000
  maxPollRecords: 20000
  maxPartitionFetchBytes: 5242880
  # EventHub specific configurations
  eventHubConnectionString: _DO_NOT_COMMIT_  # EventHub connection string
  storageConnectionString: _DO_NOT_COMMIT_
  storageContainerName: eventhub-checkpoints
```

### Production (Helm)
```yaml
kafkaReceiverConfig:
  bootstrapServers: eventhubs-connector.servicebus.windows.net:9093
  topic: aggregated-flow-v1
  groupId: flow-decorator-group
  autoOffsetReset: latest
  requestTimeoutMs: 180000
  maxPollRecords: 500
  maxPartitionFetchBytes: "1048576"
  # EventHub specific configurations
  eventHubConnectionString: ""  # Empty to use managed identity
  storageConnectionString: ""  # Uses managed identity
  storageContainerName: eventhub-checkpoints
```

## Migration Steps

1. **Update Dependencies**: The build.gradle has been updated with new Azure dependencies
2. **Update Configuration**: Add EventHub-specific configurations to your application.yml
3. **Deploy**: The service will automatically use EventHub consumer instead of Kafka

## Benefits

- **Azure Native**: Better integration with Azure services
- **Managed Identity**: Secure authentication without connection strings
- **Scalability**: EventHub provides better scalability for high-throughput scenarios
- **Checkpointing**: Automatic checkpoint management for reliable message processing

## Troubleshooting

### Common Issues

1. **Authentication Errors**: Ensure proper connection string or managed identity configuration
2. **Checkpoint Store Errors**: Verify blob storage access and container existence
3. **Connection Timeouts**: Check network connectivity to EventHub namespace

### Logs
Enable debug logging for EventHub:
```yaml
logging:
  level:
    com.azure.messaging.eventhubs: DEBUG
    com.illumio.data: DEBUG
```

## Backward Compatibility

The migration maintains backward compatibility by:
- Reusing existing configuration keys
- Maintaining the same reactive stream interface
- Preserving existing business logic and decorators

No changes are required to the flow processing logic or output configuration. 