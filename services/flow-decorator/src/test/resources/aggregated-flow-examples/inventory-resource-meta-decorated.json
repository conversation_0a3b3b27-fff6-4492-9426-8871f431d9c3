{"StartTime": "2024-06-26T00:08:57.0000000Z", "EndTime": "2024-06-26T00:08:57.0000000Z", "ReceiptTime": "2025-06-26T00:08:58.0000000Z", "Port": 443, "Proto": "tcp", "FlowCount": 1, "PacketsSent": 0, "PacketsReceived": 0, "SrcFlowType": "dc", "IllumioTenantId": "83f1b531-864b-4c02-9124-9de1645017c1", "DeviceAction": "allow", "SentBytes": 7362, "ReceivedBytes": 3087, "LogSeverity": "1", "SourceSystem": "OpsManager", "DeviceName": "DGBWGCN21P52E2", "DeviceOutboundInterface": "ae1.11", "DeviceProduct": "PAN-OS", "DeviceVersion": "10.1.10-h2", "Activity": "TRAFFIC", "SourceZone": "MSFTINET_INSIDE", "DestinationZone": "C2I_OUTSIDE", "DestinationTranslatedAddress": "************", "SrcIP": "*********", "DestIP": "**********", "CSSrcId": "458c1358-e556-51ca-bd5d-14e6a8a1c08b", "CSDestId": "458c1358-e556-51ca-bd5d-14e6a8a1c08b", "SrcSubId": "87332a70-7c1b-4437-aa3b-ec7c00d72de0", "SrcResId": "/subscriptions/87332a70-7c1b-4437-aa3b-ec7c00d72de0/resourceGroups/mc_cloudsecure-data-dev-3-rg_cloudsecure-data-dev-3_westus2/providers/Microsoft.Compute/virtualMachineScaleSets/aks-nds4v21fb09-12946062-vmss/virtualMachines/8", "SrcRegion": "westus2", "SrcCloudProvider": "azure", "SrcTenantId": "83f1b531-864b-4c02-9124-9de1645017c1", "SrcCloudTags": "{\"aks-managed-kubeletIdentityClientID\":\"e3928821-6aac-467b-969e-9b8f92c24a67\",\"email\":\"<EMAIL>\",\"owner\":\"cloudops\",\"aks-managed-enable-imds-restriction\":\"false\",\"aks-managed-resourceNameSuffix\":\"15484129\",\"cluster_name\":\"cloudsecure-data-dev-3\",\"cluster_type\":\"data\",\"environment\":\"dev\",\"managed_by\":\"terraform\",\"aks-managed-consolidated-additional-properties\":\"f4146e79-c76b-11ef-bd73-669c1aea19d2\",\"aks-managed-operationID\":\"56dc4b19-6ba9-4e70-8b7c-9246a1662ef4\",\"aks-managed-ssh-access\":\"LocalUser\",\"aks-managed-creationSource\":\"vmssclient-aks-nds4v21fb09-12946062-vmss\",\"aks-managed-orchestrator\":\"Kubernetes:1.28.15\",\"aks-managed-poolName\":\"nds4v21fb09\",\"project\":\"cloudsecure\"}", "DestSubId": "87332a70-7c1b-4437-aa3b-ec7c00d72de0", "DestResId": "/subscriptions/87332a70-7c1b-4437-aa3b-ec7c00d72de0/resourceGroups/mc_cloudsecure-data-dev-3-rg_cloudsecure-data-dev-3_westus2/providers/Microsoft.Compute/virtualMachineScaleSets/aks-nds4v21fb09-12946062-vmss/virtualMachines/8", "DestRegion": "westus2", "DestCloudProvider": "azure", "DestTenantId": "83f1b531-864b-4c02-9124-9de1645017c1", "DestCloudTags": "{\"aks-managed-kubeletIdentityClientID\":\"e3928821-6aac-467b-969e-9b8f92c24a67\",\"email\":\"<EMAIL>\",\"owner\":\"cloudops\",\"aks-managed-enable-imds-restriction\":\"false\",\"aks-managed-resourceNameSuffix\":\"15484129\",\"cluster_name\":\"cloudsecure-data-dev-3\",\"cluster_type\":\"data\",\"environment\":\"dev\",\"managed_by\":\"terraform\",\"aks-managed-consolidated-additional-properties\":\"f4146e79-c76b-11ef-bd73-669c1aea19d2\",\"aks-managed-operationID\":\"56dc4b19-6ba9-4e70-8b7c-9246a1662ef4\",\"aks-managed-ssh-access\":\"LocalUser\",\"aks-managed-creationSource\":\"vmssclient-aks-nds4v21fb09-12946062-vmss\",\"aks-managed-orchestrator\":\"Kubernetes:1.28.15\",\"aks-managed-poolName\":\"nds4v21fb09\",\"project\":\"cloudsecure\"}"}