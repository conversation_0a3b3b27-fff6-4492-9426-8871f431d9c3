package com.illumio.data.components;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.databind.ObjectMapper;

import com.illumio.data.configuration.ResilienceConfig;
import com.illumio.data.metrics.DecoratorMetrics;
import inventory.ReactorInventoryCacheServiceGrpc;

import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryRegistry;
import lombok.SneakyThrows;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class InventoryResourceIdDecoratorTest {

    private static ObjectMapper objectMapper = new ObjectMapper();
    @Mock
    ReactorInventoryCacheServiceGrpc.ReactorInventoryCacheServiceStub inventory;
    @Mock
    DecoratorMetrics decoratorMetrics;
    @Mock
    CircuitBreakerRegistry circuitBreakerRegistry;
    @Mock
    RetryRegistry retryRegistry;
    @Mock
    CircuitBreaker circuitBreaker;
    @Mock
    Retry retry;
    @Mock
    ResilienceConfig resilienceConfig;

    InventoryResourceIdDecorator inventoryResourceIdDecorator;

    @BeforeEach
    void init() {
        inventoryResourceIdDecorator = new InventoryResourceIdDecorator(inventory, decoratorMetrics, resilienceConfig);
    }

    @Test
    @SneakyThrows
    void testValidJson() {
        // ips only
        var json = "{\"SrcIP\":\"*************\",\"DestIP\":\"*************\",\"IllumioTenantId\":\"83f1b531-864b-4c02-9124-9de1645017c1\"}";
        var jsonNode = objectMapper.readTree(json);
        assertTrue(inventoryResourceIdDecorator.valid(jsonNode));


        json = "{\"DestIP\":\"*************\",\"IllumioTenantId\":\"83f1b531-864b-4c02-9124-9de1645017c1\"}";
        jsonNode = objectMapper.readTree(json);
        assertTrue(inventoryResourceIdDecorator.valid(jsonNode));

        json = "{\"SrcIP\":\"*************\",\"IllumioTenantId\":\"83f1b531-864b-4c02-9124-9de1645017c1\"}";
        jsonNode = objectMapper.readTree(json);
        assertTrue(inventoryResourceIdDecorator.valid(jsonNode));

        // with mac addresses
        json =
                "{\"SrcIP\":\"*************\",\"DestIP\":\"*************\",\"IllumioTenantId\":\"83f1b531-864b-4c02-9124-9de1645017c1\",\"SourceMACAddress\":\"000D3A33F7EA\",\"DestinationMACAddress\":\"000D3A33F7EB\"}";
        jsonNode = objectMapper.readTree(json);
        assertTrue(inventoryResourceIdDecorator.valid(jsonNode));

        // mac addresses only
        json =
                "{\"IllumioTenantId\":\"83f1b531-864b-4c02-9124-9de1645017c1\",\"SourceMACAddress\":\"000D3A33F7EA\",\"DestinationMACAddress\":\"000D3A33F7EB\"}";
        jsonNode = objectMapper.readTree(json);
        assertTrue(inventoryResourceIdDecorator.valid(jsonNode));

        json =
                "{\"IllumioTenantId\":\"83f1b531-864b-4c02-9124-9de1645017c1\",\"SourceMACAddress\":\"000D3A33F7EA\"}";
        jsonNode = objectMapper.readTree(json);
        assertTrue(inventoryResourceIdDecorator.valid(jsonNode));

        json =
                "{\"IllumioTenantId\":\"83f1b531-864b-4c02-9124-9de1645017c1\",\"DestinationMACAddress\":\"000D3A33F7EB\"}";
        jsonNode = objectMapper.readTree(json);
        assertTrue(inventoryResourceIdDecorator.valid(jsonNode));
    }

    @Test
    @SneakyThrows
    void testInvalidJson() {
        var json = "{\"SrcIP\":\"*************\",\"DestIP\":\"*************\"}";
        var jsonNode = objectMapper.readTree(json);
        assertFalse(inventoryResourceIdDecorator.valid(jsonNode));

        json = "{\"IllumioTenantId\":\"83f1b531-864b-4c02-9124-9de1645017c1\"}";
        jsonNode = objectMapper.readTree(json);
        assertFalse(inventoryResourceIdDecorator.valid(jsonNode));
    }
}
