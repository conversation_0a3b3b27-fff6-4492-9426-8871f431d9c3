logging:
  level:
    ROOT: DEBUG
    org:
      apache:
        kafka: INFO
    com.illumio.data.components: DEBUG
    io.github.resilience4j: DEBUG
    io.github.resilience4j.circuitbreaker: DEBUG
spring:
  application:
    name: flow-decorator
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

management:
  endpoints:
    web:
      exposure:
        include: "*"
  metrics:
    export:
      prometheus:
        enabled: true
  server:
    port: 9464

server:
  port : 8089
  kafkaReceiverConfig:
    # EventHub configuration (reusing Kafka config keys for compatibility)
    bootstrapServers: localhost:19092  # EventHub namespace
    isSasl: false
    saslJaasConfig: _DO_NOT_COMMIT_  # Keep for Kafka sender compatibility
    topic: aggregated-flow-v1  # EventHub name
    groupId: flow-decorator-group  # Consumer group
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880
    # EventHub specific configurations
    eventHubConnectionString: _DO_NOT_COMMIT_  # EventHub connection string (different from Kafka JAAS)
    storageConnectionString: _DO_NOT_COMMIT_  # For checkpoint store
    storageContainerName: eventhub-checkpoints
kafkaSenderConfig:
  bootstrapServers: localhost:19092
  isSasl: false
  saslJaasConfig: _DO_NOT_COMMIT_
  topic: decorated-flow-v1
inventoryConfig:
  host: localhost
  port: 7860
  cert: ""
  use-tls: true
resilienceConfig:
  failureRateThreshold: 50
  waitDurationInOpenState: 10s
  slidingWindowSize: 10
  maxAttempts: 3
  waitDuration: 1s
  timeoutDuration: "5s"