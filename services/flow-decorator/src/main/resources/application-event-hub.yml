logging:
  level:
    ROOT: INFO
    org:
      apache:
        kafka: INFO
    com.illumio.data: DEBUG
    com.azure.messaging.eventhubs: INFO

spring:
  application:
    name: flow-decorator
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

management:
  endpoints:
    web:
      exposure:
        include: "*"
  metrics:
    export:
      prometheus:
        enabled: true
  server:
    port: 9464

server:
  port: 8089

# EventHub configuration (reusing Kafka config keys for compatibility)
kafkaReceiverConfig:
  bootstrapServers: eventhubs-connector.servicebus.windows.net:9093  # EventHub namespace
  isSasl: true  # Keep for Kafka sender compatibility
  saslJaasConfig: _DO_NOT_COMMIT_  # Keep for Kafka sender compatibility
  topic: aggregated-flow-v1  # EventHub name
  groupId: flow-decorator-group  # Consumer group
  autoOffsetReset: latest
  requestTimeoutMs: 180000
  maxPollRecords: 500
  maxPartitionFetchBytes: "1048576"
  # EventHub specific configurations
  eventHubConnectionString: _DO_NOT_COMMIT_  # EventHub connection string (different from Kafka JAAS)
  
  # Storage configuration for checkpoints
  storageEndpoint: https://inglogpoc.blob.core.windows.net/  # Storage account endpoint for checkpoints
  storageContainerName: eventhub-checkpoints  # Container for storing checkpoints
  isStorageConnectionString: false  # Use managed identity for storage (not connection string)
  storageConnectionString: ""  # Not used when using managed identity
  isStorageManagedIdentity: true  # Use managed identity for storage authentication

kafkaSenderConfig:
  bootstrapServers: eventhubs-connector.servicebus.windows.net:9093
  isSasl: true
  saslJaasConfig: _DO_NOT_COMMIT_
  topic: decorated-flow-v1

inventoryConfig:
  host: inventory.data.azure.westus3.dev.cloud.ilabs.io
  port: 8080
  cert: ""
  use-tls: false

resilienceConfig:
  failureRateThreshold: 50
  waitDurationInOpenState: 10s
  slidingWindowSize: 10
  maxAttempts: 3
  waitDuration: 1s
  timeoutDuration: "5s" 