package com.illumio.data.configuration;

import static org.apache.kafka.clients.CommonClientConfigs.SECURITY_PROTOCOL_CONFIG;
import static org.apache.kafka.clients.producer.ProducerConfig.*;
import static org.apache.kafka.common.config.SaslConfigs.SASL_JAAS_CONFIG;
import static org.apache.kafka.common.config.SaslConfigs.SASL_MECHANISM;

import lombok.Data;

import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;

import java.util.HashMap;

@Configuration
@ConfigurationProperties(prefix = "kafka-sender-config")
@Data
public class KafkaSenderConfig {
    private String bootstrapServers;
    private Boolean isSasl = false;
    private String saslJaasConfig;
    private String topic;
    private Integer requestTimeoutMs;
    private Integer deliveryTimeoutMs;
    private Integer lingerMs;
    private Integer batchSize;
    private Integer bufferMemory;
    private Integer maxBlockMs;
    private Integer maxInFlight = 1024;

    @Bean
    public KafkaSender<String, String> kafkaSender() {
        var producerProps = new HashMap<String, Object>();
        producerProps.put(BOOTSTRAP_SERVERS_CONFIG, getBootstrapServers());
        producerProps.put(KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        if (null != getIsSasl() && getIsSasl()) {
            producerProps.put(SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            producerProps.put(SASL_MECHANISM, "PLAIN");
            producerProps.put(SASL_JAAS_CONFIG, getSaslJaasConfig());
        }
        if (null != getRequestTimeoutMs()) {
            producerProps.put(REQUEST_TIMEOUT_MS_CONFIG, getRequestTimeoutMs());
        }
        if (null != getDeliveryTimeoutMs()) {
            producerProps.put(DELIVERY_TIMEOUT_MS_CONFIG, getDeliveryTimeoutMs());
        }
        if (null != getLingerMs()) {
            producerProps.put(LINGER_MS_CONFIG, getLingerMs());
        }
        if (null != getBatchSize()) {
            producerProps.put(BATCH_SIZE_CONFIG, getBatchSize());
        }
        if (null != getBufferMemory()) {
            producerProps.put(BUFFER_MEMORY_CONFIG, getBufferMemory());
        }
        if (null != getMaxBlockMs()) {
            producerProps.put(MAX_BLOCK_MS_CONFIG, getMaxBlockMs());
        }

        var senderOptions =
                SenderOptions.<String, String>create(producerProps)
                        // Non-blocking back-pressure
                        .maxInFlight(getMaxInFlight());

        return KafkaSender.create(senderOptions);
    }
}
