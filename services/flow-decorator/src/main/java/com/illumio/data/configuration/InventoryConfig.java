package com.illumio.data.configuration;

import inventory.ReactorInventoryCacheServiceGrpc;

import io.grpc.Grpc;
import io.grpc.InsecureChannelCredentials;
import io.grpc.ManagedChannel;
import io.grpc.ManagedChannelBuilder;
import io.grpc.TlsChannelCredentials;
import io.grpc.netty.shaded.io.netty.handler.ssl.util.InsecureTrustManagerFactory;

import lombok.Data;
import lombok.SneakyThrows;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Configuration
@ConfigurationProperties(prefix = "inventory-config")
@Data
public class InventoryConfig {
    private String host;
    private Integer port;
    private Boolean useTls;
    private String cert;

    @Bean
    @SneakyThrows
    public ReactorInventoryCacheServiceGrpc.ReactorInventoryCacheServiceStub stub() {
        ManagedChannel managedChannel = createManagedChannel();
        return ReactorInventoryCacheServiceGrpc.newReactorStub(managedChannel);
    }

    @SneakyThrows
    private ManagedChannel createManagedChannel() {
        ManagedChannelBuilder<?> builder;

        if (Boolean.FALSE.equals(useTls)) {
            builder = Grpc.newChannelBuilderForAddress(host, port, InsecureChannelCredentials.create());
        } else {
            var credsBuilder = TlsChannelCredentials.newBuilder();
            if (cert != null && !cert.isEmpty()) {
                credsBuilder.trustManager(new ByteArrayInputStream(Base64.getDecoder().decode(cert)));
            } else {
                // Use insecure trust manager if cert is empty
                credsBuilder.trustManager(InsecureTrustManagerFactory.INSTANCE.getTrustManagers());
            }
            builder = Grpc.newChannelBuilderForAddress(host, port, credsBuilder.build());
        }

        // Configure client keepalive to avoid idle disconnects under load
        builder.keepAliveTime(30, TimeUnit.SECONDS)
                .keepAliveTimeout(10, TimeUnit.SECONDS)
                .keepAliveWithoutCalls(true);

        // Enable client retries with a conservative policy for transient errors
        builder.enableRetry()
                .defaultServiceConfig(buildDefaultRetryServiceConfig());

        return builder.build();
    }

    private Map<String, Object> buildDefaultRetryServiceConfig() {
        Map<String, Object> retryPolicy = new HashMap<>();
        retryPolicy.put("maxAttempts", 4.0); // gRPC expects numbers as Double
        retryPolicy.put("initialBackoff", "0.5s");
        retryPolicy.put("maxBackoff", "5s");
        retryPolicy.put("backoffMultiplier", 2.0);
        retryPolicy.put("retryableStatusCodes", List.of("UNAVAILABLE", "DEADLINE_EXCEEDED", "RESOURCE_EXHAUSTED", "CANCELLED"));

        Map<String, Object> methodConfig = new HashMap<>();
        methodConfig.put("name", List.of()); // apply to all services/methods
        methodConfig.put("retryPolicy", retryPolicy);

        Map<String, Object> serviceConfig = new HashMap<>();
        serviceConfig.put("methodConfig", List.of(methodConfig));
        return serviceConfig;
    }
}
