package com.illumio.data.azure;

import com.azure.identity.DefaultAzureCredentialBuilder;
import com.azure.messaging.eventhubs.EventHubClientBuilder;
import com.azure.messaging.eventhubs.EventHubConsumerAsyncClient;
import com.illumio.data.configuration.EventHubReceiverConfig;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Optional;

@Configuration
public class EventHubClientConfig {
    @Bean
    public EventHubConsumerAsyncClient eventHubConsumerAsyncClient(
            EventHubReceiverConfig eventHubReceiverConfig) {

        EventHubClientBuilder eventHubClientBuilder = new EventHubClientBuilder();

        // Check if EventHub connection string is provided
        if (eventHubReceiverConfig.getEventHubConnectionString() != null && 
            !eventHubReceiverConfig.getEventHubConnectionString().isEmpty()) {
            // Use EventHub connection string
            eventHubClientBuilder.connectionString(
                    eventHubReceiverConfig.getEventHubConnectionString(),
                    eventHubReceiverConfig.getTopic());
        } else {
            // Use managed identity authentication
            eventHubClientBuilder.credential(new DefaultAzureCredentialBuilder().build());
            eventHubClientBuilder.eventHubName(eventHubReceiverConfig.getTopic());
            String bootstrapServers = eventHubReceiverConfig.getBootstrapServers();
            if (bootstrapServers.contains(":")) {
                String[] parts = bootstrapServers.split(":");
                bootstrapServers = parts[0];
            }
            eventHubClientBuilder.fullyQualifiedNamespace(bootstrapServers);
        }
        eventHubClientBuilder.consumerGroup(eventHubReceiverConfig.getGroupId());

        EventHubConsumerAsyncClient consumerAsyncClient =
                eventHubClientBuilder.buildAsyncConsumerClient();
        return consumerAsyncClient;
    }
} 