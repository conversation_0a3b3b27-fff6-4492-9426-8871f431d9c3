package com.illumio.data.metrics;

import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.metrics.LongHistogram;
import io.opentelemetry.api.metrics.Meter;
import org.springframework.stereotype.Component;

@Component
public class DecoratorMetrics {
    private final LongHistogram resourceIdLatency;
    private final LongHistogram resourceMetaLatency;

    public DecoratorMetrics(OpenTelemetry openTelemetry) {
        Meter meter = openTelemetry.getMeter("flow_decorator");

        this.resourceIdLatency = meter
                .histogramBuilder("resource_id_decoration_latency")
                .setDescription("Latency of resource ID decoration requests")
                .setUnit("ms")
                .ofLongs()
                .build();

        this.resourceMetaLatency = meter
                .histogramBuilder("resource_meta_decoration_latency")
                .setDescription("Latency of resource metadata decoration requests")
                .setUnit("ms")
                .ofLongs()
                .build();
    }

    public LongHistogram getResourceIdLatency() {
        return resourceIdLatency;
    }

    public LongHistogram getResourceMetaLatency() {
        return resourceMetaLatency;
    }
}
