package com.illumio.data.configuration;

import io.github.resilience4j.retry.RetryConfig;
import io.github.resilience4j.retry.RetryRegistry;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
@ConfigurationProperties(prefix = "resilience-config")
@Data
public class ResilienceConfig {
    private Integer maxAttempts = 3;
    private Duration waitDuration = Duration.ofSeconds(1);
    private Duration timeoutDuration = Duration.ofSeconds(5); //used for individual calls

    @Bean
    public RetryRegistry retryRegistry() {
        RetryConfig config = RetryConfig.custom()
                .maxAttempts(maxAttempts)
                .waitDuration(Duration.ofSeconds(waitDuration.getSeconds()))
                .build();

        return RetryRegistry.of(config);
    }
}