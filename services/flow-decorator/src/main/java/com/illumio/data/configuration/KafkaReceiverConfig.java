package com.illumio.data.configuration;

import static org.apache.kafka.clients.CommonClientConfigs.SECURITY_PROTOCOL_CONFIG;
import static org.apache.kafka.clients.consumer.ConsumerConfig.*;
import static org.apache.kafka.common.config.SaslConfigs.SASL_JAAS_CONFIG;
import static org.apache.kafka.common.config.SaslConfigs.SASL_MECHANISM;

import lombok.Data;

import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;

import java.util.Collections;
import java.util.HashMap;

@Configuration
@ConfigurationProperties(prefix = "kafka-receiver-config")
@Data
public class KafkaReceiverConfig {
    private String bootstrapServers;
    private Boolean isSasl = false;
    private String saslJaasConfig;
    private String topic;
    private String groupId;
    private String autoOffsetReset;
    private Integer requestTimeoutMs;
    private Integer maxPollRecords;
    private Integer maxPartitionFetchBytes;

    @Bean
    public KafkaReceiver<String, String> flowsKafkaReceiver() {
        var consumerProps = new HashMap<String, Object>();
        consumerProps.put(BOOTSTRAP_SERVERS_CONFIG, getBootstrapServers());
        consumerProps.put(GROUP_ID_CONFIG, getGroupId());
        consumerProps.put(KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(REQUEST_TIMEOUT_MS_CONFIG, getRequestTimeoutMs());
        consumerProps.put(AUTO_OFFSET_RESET_CONFIG, getAutoOffsetReset());
        if (null != getIsSasl() && getIsSasl()) {
            consumerProps.put(SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            consumerProps.put(SASL_MECHANISM, "PLAIN");
            consumerProps.put(SASL_JAAS_CONFIG, getSaslJaasConfig());
        }
        consumerProps.put(MAX_POLL_RECORDS_CONFIG, getMaxPollRecords());
        consumerProps.put(
                MAX_PARTITION_FETCH_BYTES_CONFIG, getMaxPartitionFetchBytes());

        var receiverOptions =
                ReceiverOptions.<String, String>create(consumerProps)
                        .subscription(Collections.singleton(getTopic()));

        return KafkaReceiver.create(receiverOptions);
    }
}
