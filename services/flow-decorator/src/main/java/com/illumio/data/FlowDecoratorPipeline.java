package com.illumio.data;

import com.azure.core.amqp.AmqpRetryMode;
import com.azure.core.amqp.AmqpRetryOptions;
import com.azure.identity.DefaultAzureCredentialBuilder;
import com.azure.messaging.eventhubs.EventData;
import com.azure.messaging.eventhubs.EventProcessorClient;
import com.azure.messaging.eventhubs.EventProcessorClientBuilder;
import com.azure.messaging.eventhubs.LoadBalancingStrategy;
import com.azure.messaging.eventhubs.checkpointstore.blob.BlobCheckpointStore;
import com.azure.messaging.eventhubs.models.EventBatchContext;
import com.azure.messaging.eventhubs.models.PartitionContext;
import com.azure.storage.blob.BlobContainerAsyncClient;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.components.InventoryResourceIdDecorator;
import com.illumio.data.components.InventoryResourceMetaDecorator;
import com.illumio.data.configuration.EventHubReceiverConfig;
import com.illumio.data.configuration.KafkaSenderConfig;
import com.illumio.data.configuration.ProcessingConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Optional;

@Slf4j
@Component
public class FlowDecoratorPipeline {
    private final InventoryResourceIdDecorator inventoryResourceIdDecorator;
    private final InventoryResourceMetaDecorator inventoryResourceMetaDecorator;
    private final EventHubReceiverConfig eventHubReceiverConfig;
    private final KafkaSenderConfig kafkaSenderConfig;
    private final ProcessingConfig processingConfig;
    private final KafkaSender<String, String> kafkaSender;
    private final BlobContainerAsyncClient blobContainerAsyncClient;
    private final ObjectMapper objectMapper;
    
    private EventProcessorClient eventProcessorClient;

    @Autowired
    public FlowDecoratorPipeline(
            KafkaSender<String, String> kafkaSender,
            InventoryResourceIdDecorator inventoryResourceIdDecorator,
            InventoryResourceMetaDecorator inventoryResourceMetaDecorator,
            KafkaSenderConfig kafkaSenderConfig,
            EventHubReceiverConfig eventHubReceiverConfig,
            ProcessingConfig processingConfig,
            BlobContainerAsyncClient blobContainerAsyncClient,
            ObjectMapper objectMapper) {
        this.inventoryResourceIdDecorator = inventoryResourceIdDecorator;
        this.inventoryResourceMetaDecorator = inventoryResourceMetaDecorator;
        this.eventHubReceiverConfig = eventHubReceiverConfig;
        this.kafkaSenderConfig = kafkaSenderConfig;
        this.processingConfig = processingConfig;
        this.kafkaSender = kafkaSender;
        this.blobContainerAsyncClient = blobContainerAsyncClient;
        this.objectMapper = objectMapper;
    }

    public FlowDecoratorPipeline(
            KafkaSender<String, String> kafkaSender,
            InventoryResourceIdDecorator inventoryResourceIdDecorator,
            InventoryResourceMetaDecorator inventoryResourceMetaDecorator,
            KafkaSenderConfig kafkaSenderConfig,
            EventHubReceiverConfig eventHubReceiverConfig,
            BlobContainerAsyncClient blobContainerAsyncClient,
            ObjectMapper objectMapper) {
        this(
                kafkaSender,
                inventoryResourceIdDecorator,
                inventoryResourceMetaDecorator,
                kafkaSenderConfig,
                eventHubReceiverConfig,
                new ProcessingConfig(),
                blobContainerAsyncClient,
                objectMapper);
    }

    public void start() {
        log.info("Starting EventProcessorClient for flow decorator");
        this.eventProcessorClient = createEventProcessorClient();
        eventProcessorClient.start();
    }

    public void stop() {
        log.debug("Stopping EventProcessorClient");
        if (eventProcessorClient != null) {
            eventProcessorClient.stop();
        }
    }

    private EventProcessorClient createEventProcessorClient() {
        EventProcessorClientBuilder eventProcessorClientBuilder =
                new EventProcessorClientBuilder()
                        .retryOptions(
                                new AmqpRetryOptions()
                                        .setTryTimeout(
                                                Duration.ofMillis(
                                                        Optional.ofNullable(eventHubReceiverConfig.getRequestTimeoutMs())
                                                                .orElse(30000)))
                                        .setDelay(Duration.ofSeconds(1))
                                        .setMaxRetries(3)
                                        .setMode(AmqpRetryMode.FIXED));

        eventProcessorClientBuilder.prefetchCount(
                Optional.ofNullable(eventHubReceiverConfig.getMaxPollRecords()).orElse(500));
        eventProcessorClientBuilder.loadBalancingStrategy(LoadBalancingStrategy.BALANCED);

        // Check if EventHub connection string is provided
        if (eventHubReceiverConfig.getEventHubConnectionString() != null && 
            !eventHubReceiverConfig.getEventHubConnectionString().isEmpty()) {
            // Use EventHub connection string
            eventProcessorClientBuilder.connectionString(
                    eventHubReceiverConfig.getEventHubConnectionString(),
                    eventHubReceiverConfig.getTopic());
        } else {
            // Use managed identity authentication
            eventProcessorClientBuilder.credential(new DefaultAzureCredentialBuilder().build());
            eventProcessorClientBuilder.eventHubName(eventHubReceiverConfig.getTopic());
            String bootstrapServers = eventHubReceiverConfig.getBootstrapServers();
            if (bootstrapServers.contains(":")) {
                String[] parts = bootstrapServers.split(":");
                bootstrapServers = parts[0];
                log.debug(
                        "Removing port to allow Azure SDK to append AMQP port. New bootstrap servers: {}",
                        bootstrapServers);
            }
            eventProcessorClientBuilder.fullyQualifiedNamespace(bootstrapServers);
        }

        eventProcessorClientBuilder.consumerGroup(eventHubReceiverConfig.getGroupId());
        eventProcessorClientBuilder.checkpointStore(
                new BlobCheckpointStore(blobContainerAsyncClient));

        eventProcessorClientBuilder.processEventBatch(
                this::accept,
                Optional.ofNullable(eventHubReceiverConfig.getMaxPollRecords()).orElse(500),
                Duration.ofMillis(
                        Optional.ofNullable(eventHubReceiverConfig.getRequestTimeoutMs()).orElse(30000)));

        eventProcessorClientBuilder.processError(
                errorContext ->
                        log.error(
                                "Error processing events: {}",
                                errorContext.getThrowable()));

        return eventProcessorClientBuilder.buildEventProcessorClient();
    }

    public void accept(EventBatchContext eventBatchContext) {
        log.debug("Processing batch of {} events", eventBatchContext.getEvents().size());

        Flux<SenderRecord<String, String, String>> senderRecordFlux =
                Flux.fromIterable(eventBatchContext.getEvents())
                        .flatMap(
                                eventData ->
                                        processEventData(
                                                eventBatchContext.getPartitionContext(), eventData),
                                processingConfig.getMaxConcurrentGrpc());

        kafkaSender
                .send(senderRecordFlux)
                .retryWhen(
                        Retry.backoff(
                                3,
                                Duration.ofSeconds(1)))
                .doOnNext(
                        stringSenderResult ->
                                log.debug(
                                        "Produced message for consumed data {} to {}",
                                        stringSenderResult.correlationMetadata(),
                                        stringSenderResult.recordMetadata()))
                .doOnError(throwable -> log.error("Could not send record", throwable))
                .then()
                .doOnSuccess(
                        unused ->
                                log.debug(
                                        "Successfully sent all messages for batch {}",
                                        eventBatchContext.getEvents().size()))
                .block();

        eventBatchContext
                .updateCheckpointAsync()
                .doOnSuccess(
                        unused ->
                                log.debug(
                                        "Committed checkpoint for batch {}",
                                        eventBatchContext.getEvents().size()))
                .subscribeOn(Schedulers.boundedElastic())
                .subscribe();
    }

    Mono<SenderRecord<String, String, String>> processEventData(
            PartitionContext partitionContext, EventData eventData) {
        return Mono.just(eventData)
                .doOnNext(__ -> log.debug("Processing event {}", eventData))
                .flatMap(
                        __ ->
                                Mono.fromCallable(() -> objectMapper.readTree(eventData.getBodyAsString()))
                                        .doOnError(
                                                throwable ->
                                                        log.warn("warning", throwable))
                                        .flatMap(
                                                originalFlow ->
                                                        inventoryResourceIdDecorator
                                                                .decorate(originalFlow)
                                                                .doOnNext(
                                                                        jsonNode ->
                                                                                log.debug("debugging"))
                                                                .doOnError(
                                                                        throwable ->
                                                                                log.error("error", throwable))
                                                                .onErrorReturn(originalFlow)
                                                                .flatMap(
                                                                        idDecoratedFlow ->
                                                                                inventoryResourceMetaDecorator
                                                                                        .decorate(idDecoratedFlow)
                                                                                        .doOnNext(
                                                                                                metaDecoratedFlow ->
                                                                                                        log.debug("debugging"))
                                                                                        .doOnError(
                                                                                                throwable ->
                                                                                                        log.error("error", throwable))
                                                                                        .onErrorReturn(idDecoratedFlow)))
                                        .flatMap(
                                                jsonNode ->
                                                        Mono.fromCallable(() -> objectMapper.writeValueAsString(jsonNode))))
                .doOnError(
                        throwable ->
                                log.warn("warning", throwable))
                .onErrorReturn(eventData.getBodyAsString())
                .flatMap(
                        newValue ->
                                Mono.just(
                                        SenderRecord.create(
                                                new ProducerRecord<>(
                                                        kafkaSenderConfig.getTopic(), eventData.getPartitionKey(), newValue),
                                                eventData.getBodyAsString())));
    }
}
