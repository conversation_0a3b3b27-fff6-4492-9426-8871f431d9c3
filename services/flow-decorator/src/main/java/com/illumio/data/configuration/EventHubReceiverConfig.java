package com.illumio.data.configuration;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import javax.annotation.PostConstruct;

@Configuration
@ConfigurationProperties(prefix = "kafka-receiver-config")
@Data
@Slf4j
public class EventHubReceiverConfig {
    // Reuse existing Kafka config keys for compatibility
    private String bootstrapServers; // Will be used as EventHub namespace
    private Boolean isSasl = false; // Will be used to determine if using connection string
    private String saslJaasConfig; // Will be used as EventHub connection string
    private String topic; // Will be used as EventHub name
    private String groupId; // Will be used as consumer group
    private String autoOffsetReset; // Will be used to determine starting position
    private Integer requestTimeoutMs;
    private Integer maxPollRecords; // Will be used as maxBatchSize
    private Integer maxPartitionFetchBytes; // Will be used as prefetchCount

    // EventHub specific configurations
    private String eventHubConnectionString; // EventHub connection string (different from Kafka JAAS)
    
    // Storage configuration for checkpoints
    private String storageEndpoint; // Storage account endpoint
    private String storageContainerName = "partition-info"; // Default checkpoint container
    private Boolean isStorageConnectionString = true; // Whether to use connection string for storage
    private String storageConnectionString; // Storage connection string (if not using managed identity)
    private Boolean isStorageManagedIdentity = false; // Whether to use managed identity for storage

    @PostConstruct
    public void logConfiguration() {
        log.info("EventHubReceiverConfig loaded:");
        log.info("  storageEndpoint: {}", storageEndpoint);
        log.info("  storageContainerName: {}", storageContainerName);
        log.info("  isStorageConnectionString: {}", isStorageConnectionString);
        log.info("  isStorageManagedIdentity: {}", isStorageManagedIdentity);
        log.info("  eventHubConnectionString: {}", eventHubConnectionString != null ? "***PROVIDED***" : "NULL");
    }
} 