package com.illumio.data.azure;

import com.azure.identity.DefaultAzureCredentialBuilder;
import com.azure.storage.blob.BlobContainerAsyncClient;
import com.azure.storage.blob.BlobContainerClientBuilder;
import com.illumio.data.configuration.EventHubReceiverConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Configuration
public class StorageAccountConfig {

    private static final Logger log = LoggerFactory.getLogger(StorageAccountConfig.class);

    @Bean
    public BlobContainerAsyncClient blobContainerAsyncClient(
            EventHubReceiverConfig eventHubReceiverConfig) {
        BlobContainerClientBuilder blobContainerClientBuilder = new BlobContainerClientBuilder();

        String endpoint = eventHubReceiverConfig.getStorageEndpoint();
        log.warn("OVERRIDING storageEndpoint to: {}", endpoint);
        log.info("StorageAccountConfig - storageContainerName: {}", eventHubReceiverConfig.getStorageContainerName());
        log.info("StorageAccountConfig - isStorageConnectionString: {}", eventHubReceiverConfig.getIsStorageConnectionString());
        log.info("StorageAccountConfig - isStorageManagedIdentity: {}", eventHubReceiverConfig.getIsStorageManagedIdentity());

        blobContainerClientBuilder.endpoint(endpoint);
        log.warn("OVERRIDING storageContainerName to: {}", eventHubReceiverConfig.getStorageContainerName());
        blobContainerClientBuilder.containerName(eventHubReceiverConfig.getStorageContainerName());

        if(eventHubReceiverConfig.getIsStorageConnectionString()){
            blobContainerClientBuilder.connectionString(eventHubReceiverConfig.getStorageConnectionString());
        }else {
            log.info("OVERRIDING storage authentication to Managed Identity");
            blobContainerClientBuilder.credential(new DefaultAzureCredentialBuilder().build());
        }

        BlobContainerAsyncClient blobContainerClient =
                blobContainerClientBuilder.buildAsyncClient();

        return blobContainerClient;
    }
} 