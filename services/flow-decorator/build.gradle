plugins {
    id 'org.springframework.boot'
    id 'com.google.cloud.tools.jib'
    id 'com.github.johnrengelman.shadow'
}

apply from: "${project.rootDir}/gradle/scripts/jib-build.gradle"
apply from: "${project.rootDir}/gradle/scripts/opentelemetry.gradle"

configurations {
    protobuf
}

dependencies {
    implementation project(":commons:azure-commons")
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    // EventHub dependencies for consumer
    implementation 'com.azure:azure-messaging-eventhubs'
    implementation 'com.azure:azure-messaging-eventhubs-checkpointstore-blob'
    implementation 'com.azure:azure-storage-blob'
    // Keep Kafka dependencies for sender and compatibility
    implementation 'org.apache.kafka:kafka-clients'
    implementation 'io.projectreactor.kafka:reactor-kafka'
    // Azure dependencies
    implementation 'com.azure:azure-identity'
    implementation 'co.elastic.logging:logback-ecs-encoder'
    implementation 'io.micrometer:micrometer-core'
    implementation "io.opentelemetry:opentelemetry-api"

    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"

    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
    testImplementation 'org.springframework.kafka:spring-kafka-test'

    implementation 'io.grpc:grpc-netty-shaded'
    implementation 'io.grpc:grpc-protobuf'
    implementation 'io.grpc:grpc-stub'
    implementation 'io.grpc:grpc-api'
    implementation 'com.google.protobuf:protobuf-java'
    protobuf project(":commons:cloudsecure-api")

    implementation project(":commons:cloudsecure-api")

    implementation 'io.github.resilience4j:resilience4j-circuitbreaker'
    implementation 'io.github.resilience4j:resilience4j-reactor'
    implementation 'io.github.resilience4j:resilience4j-retry'
    implementation 'com.salesforce.servicelibs:reactor-grpc-stub'

}

// Add dependency on proto generation
compileJava.dependsOn(":commons:cloudsecure-api:generateProto")

// Add generated proto sources to source sets
sourceSets {
    main {
        java {
            srcDirs += [
                    project(':commons:cloudsecure-api').buildDir.toString() + '/generated/source/proto/main/java',
                    project(':commons:cloudsecure-api').buildDir.toString() + '/generated/source/proto/main/grpc'
            ]
        }
    }
}
tasks.compileJava.dependsOn(":commons:cloudsecure-api:generateProto")

tasks.withType(ProcessResources).configureEach {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

jib {
    container {
        entrypoint = [
                'java', '-cp', '@/app/jib-classpath-file',
                'com.illumio.data.FlowDecoratorApplication',
                '--spring.config.location=file:/var/resources/application.yml',
                '--spring.profiles.active=prod'
        ]
        environment = [
                "JAVA_TOOL_OPTIONS": "-javaagent:/otel/opentelemetry-javaagent.jar",
                "OTEL_METRICS_EXPORTER": "prometheus",
                "OTEL_TRACES_EXPORTER": "none",
                "OTEL_LOGS_EXPORTER": "none"
        ]
    }
}