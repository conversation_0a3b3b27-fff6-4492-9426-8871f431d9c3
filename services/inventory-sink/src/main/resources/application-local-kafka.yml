logging:
  level:
    ROOT: INFO
    org:
      apache:
        kafka: INFO
spring:
  application:
    name: inventory
  output:
    ansi:
      enabled: ALWAYS

inventoryConfig:
  kafkaCommonConfig:
    bootstrapServers: localhost:9092
    isSasl: false
    saslJaasConfig: _DO_NOT_COMMIT_
  kafkaReceiverConfig:
    topic: illumio-decorated-flows
    groupId: inventory-group
    autoOffsetReset: latest
    requestTimeoutMs: 300000
    maxPollRecords: 500
    maxPartitionFetchBytes: 1048576
  kafkaSenderConfig:
    sinkTopic: inventory
    requestTimeoutMs: 300000
    deliveryTimeoutMs: 300000
  kustoConfig:
    clusterUri: https://arch-kusto.eastus.kusto.windows.net
    database: DecoratedFlows
    isManagedIdentity: false
