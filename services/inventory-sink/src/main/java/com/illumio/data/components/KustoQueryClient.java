package com.illumio.data.components;

import com.google.common.base.Stopwatch;
import com.illumio.data.pojo.IncomingPortCountHistory;
import com.illumio.data.configuration.InventoryConfig;
import com.microsoft.azure.kusto.data.Client;
import com.microsoft.azure.kusto.data.KustoOperationResult;
import com.microsoft.azure.kusto.data.KustoResultSetTable;

import lombok.SneakyThrows;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import java.util.HashMap;

@Component
@Slf4j
public class KustoQueryClient {
    private static final Scheduler KUSTO_SCHEDULER =
            Schedulers.newBoundedElastic(10, 10000, "kusto-scheduler", 60, true);
    private final Client kustoClient;
    private final String database;

    public KustoQueryClient(Client kustoClient, InventoryConfig inventoryConfig) {
        this.kustoClient = kustoClient;
        this.database = inventoryConfig.getKustoConfig().getDatabase();
    }

    public Mono<IncomingPortCountHistory> getCurrentHistoryForIP(String destinationIP) {
        return Mono.fromCallable(() -> getCurrentPortCounts(destinationIP))
                .publishOn(KUSTO_SCHEDULER);
    }

    @SneakyThrows
    public IncomingPortCountHistory getCurrentPortCounts(String destinationIP) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        IncomingPortCountHistory result =
                IncomingPortCountHistory.builder().portCountHistory(new HashMap<>()).build();
        KustoOperationResult kustoOperationResult =
                kustoClient.execute(
                        database,
                        "DecoratedFlows | where DestIP == '"
                                + destinationIP
                                + "'| summarize Count = count() by Port");
        KustoResultSetTable kustoResultSetTable = kustoOperationResult.getPrimaryResults();

        while(kustoResultSetTable.next()) {
            int port = kustoResultSetTable.getInt("Port");
            int count = kustoResultSetTable.getInt("Count");
            result.getPortCountHistory().put(port, (long) count);
        }
        log.info(
                "Retrieved data from kusto in {}. IP: {}. History: {}",
                stopwatch.elapsed(),
                destinationIP,
                result);
        return result;
    }
}
