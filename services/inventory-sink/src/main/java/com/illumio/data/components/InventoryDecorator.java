package com.illumio.data.components;

import com.illumio.data.pojo.DecoratedFlow;
import com.illumio.data.pojo.IncomingPortCountHistory;
import com.illumio.data.pojo.IpLabelMapping;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

import java.time.Duration;

@RequiredArgsConstructor
@Slf4j
@Component
public class InventoryDecorator {

    private final PortToLabelRecommender ipPortLabelLookup;
    private final KustoQueryClient kustoQueryClient;

    public Flux<IpLabelMapping> decorate(DecoratedFlow decoratedFlow) {
        return Mono.just(decoratedFlow)
                .flatMap(__ -> kustoQueryClient.getCurrentHistoryForIP(decoratedFlow.getDestIP()))
                .retryWhen(
                        Retry.backoff(10, Duration.ofSeconds(10))
                                .doBeforeRetry(
                                        retrySignal ->
                                                log.warn(
                                                        "Error reading from kusto for flow {} {}",
                                                        decoratedFlow,
                                                        retrySignal.toString(),
                                                        retrySignal.failure())))
                .flatMap(history -> updateHistory(decoratedFlow.getPort(), history))
                .publishOn(Schedulers.parallel())
                .flatMapMany(
                        history ->
                                ipPortLabelLookup.labelRecommendationFlux(decoratedFlow, history))
                .flatMap(
                        labelRecommendation ->
                                Mono.just(
                                        IpLabelMapping.builder()
                                                .IP(decoratedFlow.getDestIP())
                                                .LabelName(labelRecommendation.labelName())
                                                .build()));
    }

    private Mono<IncomingPortCountHistory> updateHistory(
            int port, IncomingPortCountHistory incomingPortCountHistory) {
        incomingPortCountHistory.getPortCountHistory().merge(port, 1L, Long::sum);
        return Mono.just(incomingPortCountHistory);
    }
}
