package com.illumio.data.pojo;

import java.util.List;

public record LabelRecommendation(
        String labelName,
        List<Integer> requiredPorts,
        List<Integer> optionalPorts,
        Integer numOptionalPortsRequired,
        Integer numFlows) {
    @Override
    public String toString() {
        return "LabelRecommendation{" +
                "labelName='" + labelName + '\'' +
                ", requiredPorts=" + (requiredPorts != null ? requiredPorts.size() : null) +
                ", optionalPorts=" + (optionalPorts != null ? optionalPorts.size() : null) +
                ", numOptionalPortsRequired=" + numOptionalPortsRequired +
                ", numFlows=" + numFlows +
                '}';
    }
}
