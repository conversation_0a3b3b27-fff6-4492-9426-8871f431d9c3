package com.illumio.data.configuration;

import com.microsoft.azure.kusto.data.Client;
import com.microsoft.azure.kusto.data.ClientFactory;
import com.microsoft.azure.kusto.data.StringUtils;
import com.microsoft.azure.kusto.data.auth.ConnectionStringBuilder;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;

import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@RequiredArgsConstructor
public class KustoIngestClientConfiguration {
    private final InventoryConfig inventoryConfig;

    @Bean
    @SneakyThrows
    public Client ingestClient() {
        ConnectionStringBuilder connectionStringBuilder;
        if (Optional.ofNullable(inventoryConfig.getKustoConfig())
                .map(InventoryConfig.KustoConfiguration::getIsManagedIdentity)
                .orElse(false)) {
            if (StringUtils.isBlank(
                    inventoryConfig.getKustoConfig().getManagedIdentityClientId())) {
                connectionStringBuilder =
                        ConnectionStringBuilder.createWithAadManagedIdentity(
                                inventoryConfig.getKustoConfig().getClusterUri());
            } else {
                connectionStringBuilder =
                        ConnectionStringBuilder.createWithAadManagedIdentity(
                                inventoryConfig.getKustoConfig().getClusterUri(),
                                inventoryConfig.getKustoConfig().getManagedIdentityClientId());
            }
        } else {
            connectionStringBuilder =
                    ConnectionStringBuilder.createWithAadApplicationCredentials(
                            inventoryConfig.getKustoConfig().getClusterUri(),
                            inventoryConfig.getKustoConfig().getAzureClientId(),
                            inventoryConfig.getKustoConfig().getAzureClientSecret(),
                            inventoryConfig.getKustoConfig().getAzureTenantId());
        }
        return ClientFactory.createClient(connectionStringBuilder);
    }
}
