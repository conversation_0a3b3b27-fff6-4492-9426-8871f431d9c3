package com.illumio.data.configuration;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "inventory-config")
@Data
public class InventoryConfig {
    private final KafkaCommonConfig kafkaCommonConfig;
    private final KafkaReceiverConfig kafkaReceiverConfig;
    private final KafkaSenderConfig kafkaSenderConfig;
    private final KustoConfiguration kustoConfig;

    @Configuration
    @Getter
    @Setter
    public static class KafkaCommonConfig {
        private String bootstrapServers;
        private Boolean isSasl = false;
        private String saslJaasConfig;
    }

    @Configuration
    @Getter
    @Setter
    public static class KafkaReceiverConfig {
        private String topic;
        private String groupId;
        private String autoOffsetReset;
        private Integer requestTimeoutMs;
        private Integer maxPollRecords;
        private Integer maxPartitionFetchBytes;
    }

    @Configuration
    @Getter
    @Setter
    public static class KafkaSenderConfig {
        private String sinkTopic;
        private Integer requestTimeoutMs;
        private Integer deliveryTimeoutMs;
        private Integer lingerMs;
        private Integer batchSize;
        private Integer bufferMemory;
        private Integer maxBlockMs;
    }

    @Configuration
    @Getter
    @Setter
    public static class KustoConfiguration {
        private String azureClientId;
        private String azureClientSecret;
        private String azureTenantId;
        private Boolean isManagedIdentity;
        private String managedIdentityClientId;
        private String clusterUri;
        private String database;
    }
}
