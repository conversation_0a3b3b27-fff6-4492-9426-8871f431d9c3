package com.illumio.data.components;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.illumio.data.pojo.DecoratedFlow;
import com.illumio.data.pojo.IncomingPortCountHistory;
import com.illumio.data.pojo.LabelRecommendation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class PortToLabelRecommender {
    // port to label recommendation
    private final Map<Integer, List<LabelRecommendation>> portToLabel;

    @SneakyThrows
    public PortToLabelRecommender() {
        // read label-port-info JSON file from resources
        Resource resource = new ClassPathResource("recommendations.json");
        InputStreamReader reader =
                new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8);

        // Json to map
        ObjectMapper mapper = new ObjectMapper();
        List<LabelRecommendation> labelRecommendations =
                mapper.readValue(reader, new TypeReference<List<LabelRecommendation>>() {});
        this.portToLabel = new HashMap<>();
        for (LabelRecommendation labelRecommendation : labelRecommendations) {
            for (Integer requiredPort : labelRecommendation.requiredPorts()) {
                portToLabel
                        .computeIfAbsent(requiredPort, integer -> new ArrayList<>())
                        .add(labelRecommendation);
            }
            for (Integer optionalPort : labelRecommendation.optionalPorts()) {
                portToLabel
                        .computeIfAbsent(optionalPort, integer -> new ArrayList<>())
                        .add(labelRecommendation);
            }
        }
        log.info("Created portToLabel with {} values", portToLabel.size());
    }

    public Flux<LabelRecommendation> labelRecommendationFlux(DecoratedFlow decoratedFlow, IncomingPortCountHistory history) {
        List<LabelRecommendation> result = labelFlow(decoratedFlow, history);
        if (null != result && !result.isEmpty()) {
            return Flux.fromIterable(result);
        } else {
            log.info("No new labels for decorated flow {},  history {}", decoratedFlow, history);
            return Flux.empty();
        }
    }

    /**
     * @param decoratedFlow new flow with Destination IP and Destination Port
     * @param history Historical count to this IP and Port.
     */
    public List<LabelRecommendation> labelFlow(
            DecoratedFlow decoratedFlow, IncomingPortCountHistory history) {
        List<LabelRecommendation> result = new ArrayList<>();
        // get the label recommendation for this port
        List<LabelRecommendation> possibleLabelRecommendataions =
                portToLabel.get(decoratedFlow.getPort());
        if (null == possibleLabelRecommendataions) {
            log.info("Label recommendation not found for Port {}", decoratedFlow);
            return null;
        }
        log.info(
                "Possible label recommendations for port {}: {}",
                decoratedFlow.getPort(),
                possibleLabelRecommendataions);
        for (LabelRecommendation labelRecommendation : possibleLabelRecommendataions) {
            boolean isCriteriaMet = checkCriteria(history, labelRecommendation);
            if (isCriteriaMet) {
                result.add(labelRecommendation);
            }
        }

        return result;
    }

    private boolean checkCriteria(
            IncomingPortCountHistory portToCountHistory, LabelRecommendation labelRecommendation) {
        boolean hasRequiredPorts = isRequiredPorts(portToCountHistory, labelRecommendation);
        boolean hasOptionalPorts = isOptionalPorts(portToCountHistory, labelRecommendation);
        boolean hasNumFlows = isNumFlowsMet(portToCountHistory, labelRecommendation);
        boolean result = hasRequiredPorts && hasOptionalPorts && hasNumFlows;
        log.info(
                "Checked criteria for history {}, recommendation label: {}. hasRequiredPorts: {}, hasOptionalPorts: {}, hasNumFlows: {}, result: {}",
                portToCountHistory,
                labelRecommendation,
                hasRequiredPorts,
                hasOptionalPorts,
                hasNumFlows,
                result);
        return result;
    }

    private static boolean isRequiredPorts(
            IncomingPortCountHistory portToCountHistory, LabelRecommendation labelRecommendation) {
        int requiredPortsPresent = 0;
        if (null == labelRecommendation.requiredPorts()) {
            return true;
        } else {
            for (Integer requiredPort : labelRecommendation.requiredPorts()) {
                if (portToCountHistory.getPortCountHistory().containsKey(requiredPort)) {
                    requiredPortsPresent++;
                }
            }
            return requiredPortsPresent >= labelRecommendation.requiredPorts().size();
        }
    }

    private static boolean isOptionalPorts(
            IncomingPortCountHistory portToCountHistory, LabelRecommendation labelRecommendation) {
        int optionalPresentPorts = 0;
        if (null == labelRecommendation.optionalPorts()
                || labelRecommendation.optionalPorts().isEmpty()) {
            return true;
        } else {
            for (Integer optionalPort : labelRecommendation.optionalPorts()) {
                if (portToCountHistory.getPortCountHistory().containsKey(optionalPort)) {
                    optionalPresentPorts++;
                }
            }
            return optionalPresentPorts >= labelRecommendation.numOptionalPortsRequired();
        }
    }

    private static boolean isNumFlowsMet(
            IncomingPortCountHistory portToCountHistory, LabelRecommendation labelRecommendation) {
        boolean hasNumFlows = false;
        long numFlows = 0;
        if (null == labelRecommendation.numFlows()) {
            hasNumFlows = true;
        } else {
            for (Integer requiredPort : labelRecommendation.requiredPorts()) {
                if (portToCountHistory.getPortCountHistory().containsKey(requiredPort)) {
                    numFlows += portToCountHistory.getPortCountHistory().get(requiredPort);
                }
            }
            for (Integer optionalPort : labelRecommendation.optionalPorts()) {
                if (portToCountHistory.getPortCountHistory().containsKey(optionalPort)) {
                    numFlows += portToCountHistory.getPortCountHistory().get(optionalPort);
                }
            }
            hasNumFlows = numFlows >= labelRecommendation.numFlows();
        }
        return hasNumFlows;
    }
}
