package com.illumio.data;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ApplicationListener {
    private final InventoryPipeline inventoryPipeline;

    @EventListener(ApplicationReadyEvent.class)
    public void handleReadyEvent() {
        log.info("Starting inventory pipeline");
        inventoryPipeline.start();
        log.info("Started inventory pipeline");
    }

    @EventListener(ContextClosedEvent.class)
    public void handleClosedEvent() {
        log.info("Stopping inventory pipeline");
        inventoryPipeline.stop();
        log.info("Stopped inventory pipeline");
    }
}
