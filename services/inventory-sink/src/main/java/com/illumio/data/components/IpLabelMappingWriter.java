package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;

import com.illumio.data.pojo.IpLabelMapping;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Component;

import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class IpLabelMappingWriter {
    private final ObjectMapper objectMapper;

    public Mono<String> writeAsString(IpLabelMapping ipLabelMapping) {
        return Mono.fromCallable(
                () -> {
                    String result = objectMapper.writeValueAsString(ipLabelMapping);
                    return result;
                });
    }
}
