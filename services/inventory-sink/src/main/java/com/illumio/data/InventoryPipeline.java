package com.illumio.data;

import com.illumio.data.components.DecoratedFlowReader;
import com.illumio.data.components.InventoryDecorator;
import com.illumio.data.components.IpLabelMappingWriter;
import com.illumio.data.configuration.InventoryConfig;

import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.stereotype.Component;

import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.kafka.sender.SenderResult;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.NoSuchElementException;
import java.util.Optional;

@Component
@Slf4j
public class InventoryPipeline {
    private final KafkaReceiver<String, String> kafkaReceiver;
    private final KafkaSender<String, String> kafkaSender;
    private final String sinkTopic;
    private final DecoratedFlowReader decoratedFlowReader;
    private final InventoryDecorator inventoryLabeler;
    private final IpLabelMappingWriter ipLabelMappingWriter;

    private Disposable disposable;

    public InventoryPipeline(
            KafkaReceiver<String, String> kafkaReceiver,
            KafkaSender<String, String> kafkaSender,
            InventoryConfig inventoryConfig,
            DecoratedFlowReader decoratedFlowReader,
            InventoryDecorator inventoryLabeler,
            IpLabelMappingWriter ipLabelMappingWriter) {
        this.kafkaReceiver = kafkaReceiver;
        this.kafkaSender = kafkaSender;
        this.sinkTopic = getSinkTopic(inventoryConfig);
        this.decoratedFlowReader = decoratedFlowReader;
        this.inventoryLabeler = inventoryLabeler;
        this.ipLabelMappingWriter = ipLabelMappingWriter;
    }

    /**
     * Fail early if sink topic is not defined.
     *
     * @param inventoryConfig
     * @return
     */
    private String getSinkTopic(InventoryConfig inventoryConfig) {
        return Optional.of(inventoryConfig)
                .map(InventoryConfig::getKafkaSenderConfig)
                .map(InventoryConfig.KafkaSenderConfig::getSinkTopic)
                .orElseThrow(
                        () ->
                                new NoSuchElementException(
                                        "No sink topic configured. Please make sure "
                                                + "inventoryConfig.kafkaSenderConfig.sinkTopic is set."));
    }

    public void start() {
        this.disposable = startInternal().subscribe();
    }

    public Flux<SenderResult<String>> startInternal() {
        return kafkaSender
                .send(
                        kafkaReceiver
                                .receiveAutoAck()
                                .publishOn(Schedulers.boundedElastic())
                                .retryWhen(
                                        Retry.backoff(10, Duration.ofSeconds(10L))
                                                .doBeforeRetry(
                                                        retrySignal ->
                                                                log.warn(
                                                                        "Error receiving from kafka {}",
                                                                        retrySignal.toString())))
                                .concatMap(r -> r)
                                .flatMap(this::processConsumerRecord))
                .doOnNext(stringSenderResult -> log.info("Sent record: {}", stringSenderResult))
                .retryWhen(
                        Retry.backoff(10, Duration.ofSeconds(10L))
                                .doBeforeRetry(
                                        retrySignal ->
                                                log.warn(
                                                        "Error sending to kafka {}",
                                                        retrySignal.toString())));
    }

    public Flux<SenderRecord<String, String, String>> processConsumerRecord(
            ConsumerRecord<String, String> consumerRecord) {
        return Mono.just(consumerRecord)
                .publishOn(Schedulers.boundedElastic())
                .doOnNext(
                        __ -> log.info("Received record: {} {}", consumerRecordString(consumerRecord), consumerRecord.value()))
                .flatMap(__ -> decoratedFlowReader.readValue(consumerRecord.value()))
                .doOnError(throwable -> log.warn("Could not parse incoming json {}", consumerRecord.value(), throwable))
                .onErrorComplete()
                .flatMapMany(inventoryLabeler::decorate)
                .doOnNext(
                        ipLabelMapping ->
                                log.info(
                                        "Met criteria for label recommendation {}", ipLabelMapping))
                .flatMap(
                        ipLabelMapping ->
                                ipLabelMappingWriter
                                        .writeAsString(ipLabelMapping)
                                        .flatMap(
                                                ipLabelMappingString ->
                                                        createSenderRecord(
                                                                ipLabelMappingString,
                                                                consumerRecord)))
                .doOnNext(senderRecord -> log.info("Sending record {}", senderRecord));
    }

    private Mono<SenderRecord<String, String, String>> createSenderRecord(
            String value, ConsumerRecord<String, String> consumerRecord) {
        return Mono.just(
                SenderRecord.create(
                        new ProducerRecord<>(sinkTopic, null, value),
                        consumerRecordString(consumerRecord)));
    }

    private String consumerRecordString(ConsumerRecord<String, String> consumerRecord) {
        return consumerRecord.topic()
                + "-"
                + consumerRecord.partition()
                + "@"
                + consumerRecord.offset();
    }

    public void stop() {
        this.disposable.dispose();
    }
}
