apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "InventorySink.fullname" . }}-env-configmap
  labels:
    {{- include "InventorySink.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
        org:
          apache:
            kafka: INFO
    
    spring:
      application:
        name: inventory-sink
      output:
        ansi:
          enabled: ALWAYS
    
    inventoryConfig:
      kafkaCommonConfig:
        bootstrapServers: "{{.Values.inventoryConfig.kafkaCommonConfig.bootstrapServers}}"
        isSasl: true
      kafkaReceiverConfig:
        topic: "{{.Values.inventoryConfig.kafkaReceiverConfig.topic}}"
        groupId: "{{.Values.inventoryConfig.kafkaReceiverConfig.groupId}}"
        autoOffsetReset: "{{.Values.inventoryConfig.kafkaReceiverConfig.autoOffsetReset}}"
        requestTimeoutMs: "{{.Values.inventoryConfig.kafkaReceiverConfig.requestTimeoutMs}}"
        maxPollRecords: "{{.Values.inventoryConfig.kafkaReceiverConfig.maxPollRecords}}"
        maxPartitionFetchBytes: "{{.Values.inventoryConfig.kafkaReceiverConfig.maxPartitionFetchBytes}}"
      kafkaSenderConfig:
        sinkTopic: "{{.Values.inventoryConfig.kafkaSenderConfig.sinkTopic}}"
        requestTimeoutMs: "{{.Values.inventoryConfig.kafkaSenderConfig.requestTimeoutMs}}"
        deliveryTimeoutMs: "{{.Values.inventoryConfig.kafkaSenderConfig.deliveryTimeoutMs}}"
        maxBlockMs: "{{.Values.inventoryConfig.kafkaSenderConfig.maxBlockMs}}"
      kustoConfig:
        clusterUri: "{{.Values.inventoryConfig.kustoConfig.clusterUri}}"
        database: "{{.Values.inventoryConfig.kustoConfig.database}}"
        isManagedIdentity: "{{.Values.inventoryConfig.kustoConfig.isManagedIdentity}}"