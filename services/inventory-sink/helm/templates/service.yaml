apiVersion: v1
kind: Service
metadata:
  name: {{ include "InventorySink.name" . }}
  labels:
    {{- include "InventorySink.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "InventorySink.selectorLabels" . | nindent 4 }}
