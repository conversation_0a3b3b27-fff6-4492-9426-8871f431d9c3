# Default values for sink connector.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
logging:
  level:
    root: DEBUG
    
ports:
- name: admin
  port: 8084
- name: rest
  port: 8081

servicePorts:
- name: rest
  podPort: rest
  servicePort: 8081

service:
  type: ClusterIP

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

replicaCount: 1

image:
  repositoryBase: illum.azurecr.io/
  repositoryName: inventory-sink
  tag:      # value given at helm deployment 
  pullPolicy: Always

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

podAnnotations: {}

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"



podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000



resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

extraLabels: {}

ansi:
  enabled: ALWAYS
webApplicationType: none
inventoryConfig:
  kafkaCommonConfig:
    bootstrapServers: eventhubs-connector.servicebus.windows.net:9093
    isSasl: true
  kafkaReceiverConfig:
    topic: illumio-decorated-flows
    groupId: inventory-sink-group
    autoOffsetReset: latest
    requestTimeoutMs: "300000"
    maxPollRecords: 500
    maxPartitionFetchBytes: "1048576"
  kafkaSenderConfig:
    sinkTopic: illumio-inventory
    requestTimeoutMs: "300000"
    deliveryTimeoutMs: "300000"
    maxBlockMs: "300000"
  kustoConfig:
    clusterUri: https://adx-connector.westus3.kusto.windows.net
    database: illumio-adx-connector
    isManagedIdentity: true
    managedIdentityClientId: # should give at deployment time

eventhub:
  password:                 # should give at deployment time

