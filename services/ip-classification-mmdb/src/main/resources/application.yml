logging:
  level:
    ROOT: INFO
    org:
      apache:
        kafka: INFO
spring:
  application:
    name: ip-classification-mmdb
  output:
    ansi:
      enabled: ALWAYS

ipClassificationMmdb:
  kafkaCommonConfig:
    bootstrapServers: localhost:19092
    isSasl: false
    saslJaasConfig: _DO_NOT_COMMIT_
    metadataMaxAgeMs: 180000
    connectionMaxIdleMs: 180000
  kafkaReceiverConfig:
    topic: decorated-flow-v1
    groupId: ip-classification-mmdb-group
    isGroupInstanceIdEnabled: false # Flag for adding the group.instance.id as pod name in env variable
    autoOffsetReset: latest
    requestTimeoutMs: 60000
    maxPollRecords: 2000
    maxPartitionFetchBytes: 2097152
    maxRetries: 5
    maxBackoff: 3s
    assignmentStrategy: "org.apache.kafka.clients.consumer.CooperativeStickyAssignor" # org.apache.kafka.clients.consumer.RangeAssignor # org.apache.kafka.clients.consumer.RoundRobinAssignor # org.apache.kafka.clients.consumer.CooperativeStickyAssignor
    maxPollIntervalMs: 300000
    sessionTimeoutMs: 300000
    heartbeatIntervalMs: 3000
  kafkaSenderConfig:
    sinkTopic: ip-classification-v1
    maxRequestSize: 1000000
    requestTimeoutMs: 60000
    metadataMaxIdleMs: 180000
    deliveryTimeoutMs: 300000
    maxRetries: 5
    maxBackoff: 3s
    lingerMs: 10
    bufferMemoryMb: 48
    batchSizeKb: 64
    numProducers: 1
  grpcConfig:
    timeout: 1s
    maxRetries: 3
    initialBackoff: 100ms
    maxBackoff: 1s
  backpressureConfig:
    enabled: false
    maxSize: 4000
    maxTime: 5000ms
    lowTide: 600
    highTide: 6000

blueMmdbClient:
  target: "localhost"
  port: 7900

circuitBreaker:
  enabled: false
  slidingWindowSize: 100
  failureRateThreshold: 50.0
  waitDurationInOpenState: 60s
  permittedNumberOfCallsInHalfOpenState: 10
  slidingWindowType: COUNT_BASED
  slowCallDurationThreshold: 5s
  slowCallRateThreshold: 50.0
scheduler:
  maxThreadPoolQueueSize: 100
  keepAliveTimeSeconds: 60
  name: "ipclassification_scheduler"
  maxThreadPoolSize: 20
  grpcMaxThreadPoolQueueSize: 100
  grpcKeepAliveTimeSeconds: 60
  grpcName: "grpc_scheduler_new"
  grpcMaxThreadPoolSize: 20
