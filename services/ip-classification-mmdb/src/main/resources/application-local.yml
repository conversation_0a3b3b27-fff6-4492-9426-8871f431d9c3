# Local development configuration with faster timeouts for quicker failure detection
ipClassificationMmdb:
  kafkaCommonConfig:
    bootstrapServers: localhost:19092
    isSasl: false
    # Faster metadata refresh for quicker broker failure detection
    metadataMaxAgeMs: 30000  # 30 seconds (default: 180000)
    connectionMaxIdleMs: 30000  # 30 seconds (default: 180000)
  kafkaReceiverConfig:
    topic: decorated-flow-v1
    groupId: ip-classification-mmdb-group
    isGroupInstanceIdEnabled: false
    autoOffsetReset: latest
    # Faster request timeout for quicker failure detection
    requestTimeoutMs: 10000  # 10 seconds (default: 60000)
    maxPollRecords: 2000
    maxPartitionFetchBytes: 2097152
    maxRetries: 3  # Reduced retries for faster failure (default: 5)
    maxBackoff: 1s  # Faster backoff (default: 3s)
    assignmentStrategy: "org.apache.kafka.clients.consumer.CooperativeStickyAssignor"
    # Faster consumer timeouts for local development
    maxPollIntervalMs: 60000  # 1 minute (default: 300000 = 5 minutes)
    sessionTimeoutMs: 30000   # 30 seconds (default: 300000 = 5 minutes)
    heartbeatIntervalMs: 3000 # 3 seconds (default: 3000)
    # Faster monitoring for local development
    monitorIntervalInMs: 10000  # 10 seconds (default: 30000)
    noMessagesInMs: 30000       # 30 seconds (default: 360000 = 6 minutes)
  kafkaSenderConfig:
    sinkTopic: ip-classification-v1
    maxRequestSize: 1000000
    # Faster sender timeouts
    requestTimeoutMs: 10000  # 10 seconds (default: 60000)
    metadataMaxIdleMs: 30000 # 30 seconds (default: 180000)
    deliveryTimeoutMs: 60000 # 1 minute (default: 300000 = 5 minutes)
    maxRetries: 3  # Reduced retries (default: 5)
    maxBackoff: 1s # Faster backoff (default: 3s)
    lingerMs: 10
    bufferMemoryMb: 48
    batchSizeKb: 64
    numProducers: 1
  grpcConfig:
    timeout: 1s
    maxRetries: 3
    initialBackoff: 100ms
    maxBackoff: 1s
  backpressureConfig:
    enabled: false
    maxSize: 4000
    maxTime: 5000ms
    lowTide: 600
    highTide: 6000

blueMmdbClient:
  target: "localhost"
  port: 7900

circuitBreaker:
  enabled: false
  slidingWindowSize: 100
  failureRateThreshold: 50.0
  waitDurationInOpenState: 60s
  permittedNumberOfCallsInHalfOpenState: 10
  slidingWindowType: COUNT_BASED
  slowCallDurationThreshold: 5s
  slowCallRateThreshold: 50.0

scheduler:
  maxThreadPoolQueueSize: 100
  keepAliveTimeSeconds: 60
  name: "ipclassification_scheduler"
  maxThreadPoolSize: 20
  grpcMaxThreadPoolQueueSize: 100
  grpcKeepAliveTimeSeconds: 60
  grpcName: "grpc_scheduler_new"
  grpcMaxThreadPoolSize: 20

logging:
  level:
    ROOT: INFO
    org:
      apache:
        kafka: DEBUG  # Enable debug logging for Kafka to see connection issues
    com:
      illumio:
        data: DEBUG  # Enable debug logging for our pipeline
