// Copyright 2023 Illumio, Inc. All Rights Reserved.

syntax = "proto3";

package mmdb;

message BluePipelineIPCRequest {
  string SrcIP = 1;
  string DestIP = 2;
}

message BluePipelineIPCResponse {
  string SrcCity = 1;
  string DestCity = 2;
  string SrcCloudProvider = 3;
  string DestCloudProvider = 4;
  string SrcCountry = 5;
  string DestCountry = 6;
  string SrcDomain = 7;
  string DestDomain = 8;
  string SrcRegion = 9;
  string DestRegion = 10;
  optional int32 SrcThreatLevel = 11;
  optional int32 DestThreatLevel = 12;
  optional bool SrcIsWellknown = 13;
  optional bool DestIsWellknown = 14;
  optional string DestinationExternalLabel = 15;
  optional string DestinationExternalLabelCategory = 16;
  optional string SourceExternalLabel = 17;
  optional string SourceExternalLabelCategory = 18;
}

message BatchBluePipelineIPCRequest {
  repeated BluePipelineIPCRequest reqs = 1;
}

message BatchBluePipelineIPCResponse {
  repeated BluePipelineIPCResponse items = 1;
}

service MmdbService {
  rpc GetBluePipelineIPClassification(BluePipelineIPCRequest) returns (BluePipelineIPCResponse) {}
  rpc GetBatchBluePipelineIPClassification(BatchBluePipelineIPCRequest) returns (BatchBluePipelineIPCResponse) {}
}
