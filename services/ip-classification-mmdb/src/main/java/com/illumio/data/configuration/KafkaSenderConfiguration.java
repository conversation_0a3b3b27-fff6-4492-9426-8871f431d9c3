package com.illumio.data.configuration;

import lombok.RequiredArgsConstructor;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class KafkaSenderConfiguration {
    private final IpClassificationMmdbConfig ipClassificationMmdbConfig;

    @Bean
    public List<KafkaSender<String, String>> kafkaSenders() {
        Map<String, Object> producerProps = createProducerProps();
        List<KafkaSender<String, String>> senders = new ArrayList<>();
        for (int i = 0; i < ipClassificationMmdbConfig.getKafkaSenderConfig().getNumProducers(); i++) {
            SenderOptions<String, String> senderOptions =
                    SenderOptions.<String, String>create(producerProps)
                            .maxInFlight(1024);
            senders.add(KafkaSender.create(senderOptions));
        }
        return senders;
    }

    private Map<String, Object> createProducerProps() {
        Map<String, Object> producerProps = new HashMap<>();
        producerProps.put(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
                ipClassificationMmdbConfig.getKafkaCommonConfig().getBootstrapServers());
        producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.MAX_REQUEST_SIZE_CONFIG,
                ipClassificationMmdbConfig.getKafkaSenderConfig().getMaxRequestSize());
        producerProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG,
                ipClassificationMmdbConfig.getKafkaSenderConfig().getRequestTimeoutMs());
        producerProps.put(ProducerConfig.METADATA_MAX_IDLE_CONFIG,
                ipClassificationMmdbConfig.getKafkaSenderConfig().getMetadataMaxIdleMs());
        producerProps.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG,
                ipClassificationMmdbConfig.getKafkaSenderConfig().getDeliveryTimeoutMs());
        producerProps.put(ProducerConfig.BATCH_SIZE_CONFIG, ipClassificationMmdbConfig.getKafkaSenderConfig().getBatchSizeKb() * 1024);
        producerProps.put(ProducerConfig.LINGER_MS_CONFIG, ipClassificationMmdbConfig.getKafkaSenderConfig().getLingerMs());
        producerProps.put(ProducerConfig.BUFFER_MEMORY_CONFIG, ipClassificationMmdbConfig.getKafkaSenderConfig().getBufferMemoryMb() * 1024 * 1024);
        if (null != ipClassificationMmdbConfig.getKafkaCommonConfig().getIsSasl()
                && ipClassificationMmdbConfig.getKafkaCommonConfig().getIsSasl()) {
            producerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            producerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            producerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    ipClassificationMmdbConfig.getKafkaCommonConfig().getSaslJaasConfig());
        }
        producerProps.put(CommonClientConfigs.METADATA_MAX_AGE_CONFIG,
                ipClassificationMmdbConfig.getKafkaCommonConfig().getMetadataMaxAgeMs());
        producerProps.put(CommonClientConfigs.CONNECTIONS_MAX_IDLE_MS_CONFIG,
                ipClassificationMmdbConfig.getKafkaCommonConfig().getConnectionMaxIdleMs());

        return producerProps;
    }
}
