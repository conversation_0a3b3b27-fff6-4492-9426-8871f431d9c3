package com.illumio.data;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ApplicationListener {
    private final IpClassificationMmdbPipeline flowsPipeline;

    @EventListener(ApplicationReadyEvent.class)
    public void handleReadyEvent() {
        log.info("Starting ip-classification-mmdb pipeline");
        flowsPipeline.start();
        log.info("Started ip-classification-mmdb pipeline");
    }

    @EventListener(ContextClosedEvent.class)
    public void handleClosedEvent() {
        log.info("Stopping ip-classification-mmdb pipeline");
        flowsPipeline.stop();
        log.info("Stopped ip-classification-mmdb pipeline");
    }
}
