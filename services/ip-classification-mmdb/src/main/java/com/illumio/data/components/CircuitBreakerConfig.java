package com.illumio.data.components;

import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig.SlidingWindowType;

import lombok.Data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
@ConfigurationProperties(prefix = "circuit-breaker")
@Data
public class CircuitBreakerConfig {
    private boolean enabled = true;
    private int slidingWindowSize = 100;
    private float failureRateThreshold = 50.0f;
    private Duration waitDurationInOpenState = Duration.ofSeconds(60);
    private int permittedNumberOfCallsInHalfOpenState = 10;
    private SlidingWindowType slidingWindowType = SlidingWindowType.COUNT_BASED;
    private Duration slowCallDurationThreshold = Duration.ofSeconds(5);
    private float slowCallRateThreshold = 50.0f;

    @Bean
    public CircuitBreaker blueMmdbCircuitBreaker() {
        if (!enabled) {
            return CircuitBreaker.ofDefaults("disabled-circuit-breaker");
        }

        io.github.resilience4j.circuitbreaker.CircuitBreakerConfig config =
                io.github.resilience4j.circuitbreaker.CircuitBreakerConfig.custom()
                        .slidingWindowType(slidingWindowType)
                        .slidingWindowSize(slidingWindowSize)
                        .failureRateThreshold(failureRateThreshold)
                        .waitDurationInOpenState(waitDurationInOpenState)
                        .permittedNumberOfCallsInHalfOpenState(permittedNumberOfCallsInHalfOpenState)
                        .slowCallDurationThreshold(slowCallDurationThreshold)
                        .slowCallRateThreshold(slowCallRateThreshold)
                        .build();

        return CircuitBreaker.of("bluemmdb-circuit-breaker", config);
    }
}