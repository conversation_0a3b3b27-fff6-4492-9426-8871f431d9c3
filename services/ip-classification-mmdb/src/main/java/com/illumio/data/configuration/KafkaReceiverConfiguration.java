package com.illumio.data.configuration;

import lombok.RequiredArgsConstructor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;

import java.util.*;

@Configuration
@RequiredArgsConstructor
public class KafkaReceiverConfiguration {
    private final IpClassificationMmdbConfig ipClassificationMmdbConfig;

    @Bean
    public KafkaReceiver<String, String> kafkaReceiver() {
        Map<String, Object> consumerProps = ipClassificationMmdbConfig.createConsumerProps();
        ReceiverOptions<String, String> receiverOptions =
                ReceiverOptions.<String, String>create(consumerProps)
                        .subscription(Collections.singleton(
                                ipClassificationMmdbConfig.getKafkaReceiverConfig().getTopic()));
        return KafkaReceiver.create(receiverOptions);
    }

}
