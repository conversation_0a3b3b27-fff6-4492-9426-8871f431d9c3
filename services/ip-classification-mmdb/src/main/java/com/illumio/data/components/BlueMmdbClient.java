package com.illumio.data.components;

import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.reactor.circuitbreaker.operator.CircuitBreakerOperator;
import io.grpc.*;
import io.grpc.netty.shaded.io.grpc.netty.GrpcSslContexts;
import io.grpc.netty.shaded.io.grpc.netty.NettyChannelBuilder;
import io.grpc.netty.shaded.io.netty.handler.ssl.util.InsecureTrustManagerFactory;
import io.micrometer.core.instrument.*;

import lombok.extern.slf4j.Slf4j;

import mmdb.Mmdb.BluePipelineIPCRequest;
import mmdb.Mmdb.BluePipelineIPCResponse;
import mmdb.ReactorMmdbServiceGrpc;

import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.concurrent.Executor;

import javax.net.ssl.SSLException;

@Slf4j
public class BlueMmdbClient {
    private final ReactorMmdbServiceGrpc.ReactorMmdbServiceStub reactorStub;
    private final CircuitBreaker circuitBreaker;
    private final Duration timeout;
    private final int maxRetries;
    private final Duration initialBackoff;
    private final Duration maxBackoff;
    private final Scheduler ipClassificationScheduler;
    Executor grpcExecutor;
    private final boolean circuitBreakerEnabled;
    private final MeterRegistry meterRegistry;
    private final Timer grpcTimer;
    private final Counter grpcSuccessCounter;
    private final Counter grpcFailureCounter;

    public BlueMmdbClient(
            String target,
            Integer port,
            CircuitBreaker circuitBreaker,
            Duration timeout,
            int maxRetries,
            Duration initialBackoff,
            Duration maxBackoff,
            Scheduler ipClassificationScheduler,
            Executor grpcExecutor,
            boolean circuitBreakerEnabled,
            MeterRegistry meterRegistry) {
        log.info("BlueMmdbClient instantiated for target: {}, port: {}", target, port);
        this.circuitBreaker = circuitBreaker;
        this.timeout = timeout;
        this.maxRetries = maxRetries;
        this.initialBackoff = initialBackoff;
        this.maxBackoff = maxBackoff;
        this.ipClassificationScheduler = ipClassificationScheduler;
        this.grpcExecutor = grpcExecutor;
        this.circuitBreakerEnabled = circuitBreakerEnabled;
        this.meterRegistry = meterRegistry;

        ManagedChannel channel;
        try {
            channel =
                    NettyChannelBuilder.forAddress(target, port)
                            .sslContext(
                                    GrpcSslContexts.forClient()
                                            .trustManager(InsecureTrustManagerFactory.INSTANCE)
                                            .build())
                            .build();
        } catch (SSLException e) {
            log.error("Failed to initialize BlueMmdbClient: {}: {}", target, e.getMessage());
            throw new RuntimeException(e);
        }

        Tags tags =
                Tags.of(
                        Tag.of("grpc.service", "mmdb"),
                        Tag.of("grpc.method", "getBluePipelineIPClassification"));
        this.grpcTimer = Timer.builder("grpc.request.duration").tags(tags).register(meterRegistry);

        this.grpcSuccessCounter =
                Counter.builder("grpc.client.calls")
                        .tag("result", "success")
                        .description("Count of successful gRPC client calls")
                        .register(meterRegistry);

        this.grpcFailureCounter =
                Counter.builder("grpc.client.calls")
                        .tag("result", "failure")
                        .description("Count of failed gRPC client calls")
                        .register(meterRegistry);

        ReactorMmdbServiceGrpc.ReactorMmdbServiceStub baseStub =
                ReactorMmdbServiceGrpc.newReactorStub(channel)
                        .withExecutor(grpcExecutor);
        reactorStub = instrumentStub(baseStub);
    }

    private ReactorMmdbServiceGrpc.ReactorMmdbServiceStub instrumentStub(
            ReactorMmdbServiceGrpc.ReactorMmdbServiceStub stub) {
        return stub.withInterceptors(
                new ClientInterceptor() {
                    @Override
                    public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(
                            MethodDescriptor<ReqT, RespT> method,
                            CallOptions callOptions,
                            Channel next) {
                        return new ForwardingClientCall.SimpleForwardingClientCall<>(
                                next.newCall(method, callOptions)) {
                            @Override
                            public void start(Listener<RespT> responseListener, Metadata headers) {
                                Timer.Sample sample = Timer.start(); // start timing the gRPC call
                                super.start(
                                        new ForwardingClientCallListener
                                                .SimpleForwardingClientCallListener<>(
                                                responseListener) {
                                            @Override
                                            public void onClose(Status status, Metadata trailers) {
                                                if (status.isOk()) {
                                                    grpcSuccessCounter.increment();
                                                } else {
                                                    grpcFailureCounter.increment();
                                                }
                                                sample.stop(grpcTimer); // stop timer on completion
                                                super.onClose(status, trailers);
                                            }
                                        },
                                        headers);
                            }
                        };
                    }
                });
    }

    public Mono<BluePipelineIPCResponse> getBluePipelineIPC(BluePipelineIPCRequest request) {
        Mono<BluePipelineIPCResponse> pipeline =
                Mono.defer(() -> reactorStub.getBluePipelineIPClassification(request))
                        .timeout(timeout)
                        .publishOn(ipClassificationScheduler);

        pipeline =
                pipeline.retryWhen(
                        Retry.backoff(maxRetries, initialBackoff)
                                .maxBackoff(maxBackoff)
                                .filter(
                                        error ->
                                                circuitBreaker
                                                        .tryAcquirePermission()) // Thread-safe
                        );

        if (circuitBreakerEnabled) {
            pipeline = pipeline.transform(CircuitBreakerOperator.of(circuitBreaker));
        }

        return pipeline.doOnSuccess(
                        resp ->
                                log.debug(
                                        "Completed gRPC call for IPs: {}, {}",
                                        request.getSrcIP(),
                                        request.getDestIP()))
                .doOnError(error -> log.debug("Error in gRPC call: {}", error.getMessage()));
    }

    public Mono<Boolean> ping() {
        BluePipelineIPCRequest req =
                BluePipelineIPCRequest.newBuilder()
                        .setSrcIP("*******")
                        .setDestIP("*******")
                        .build();

        return getBluePipelineIPC(req).map(resp -> true).onErrorReturn(false);
    }

    private void dumpBluePipelineIPCResponse(BluePipelineIPCResponse response) {
        log.info("------------------- dump IPC response");
        if (response == null) {
            log.info("IPC response is null");
        } else {
            log.info("SrcCity: {}", response.getSrcCity());
            log.info("DestCity: {}", response.getDestCity());
            log.info("SrcCloudProvider: {}", response.getSrcCloudProvider());
            log.info("DestCloudProvider: {}", response.getDestCloudProvider());
            log.info("SrcCountry: {}", response.getSrcCountry());
            log.info("DestCountry: {}", response.getDestCountry());
            log.info("SrcDomain: {}", response.getSrcDomain());
            log.info("DestDomain: {}", response.getDestDomain());
            log.info("SrcRegion: {}", response.getSrcRegion());
            log.info("DestRegion: {}", response.getDestRegion());
            log.info("SrcThreatLevel: {}", response.getSrcThreatLevel());
            log.info("DestThreatLevel: {}", response.getDestThreatLevel());
            log.info("SrcIsWellknown: {}", response.getSrcIsWellknown());
            log.info("DestIsWellknown: {}", response.getDestIsWellknown());
        }
        log.info("------------------- dump IPC response");
    }
}
