package com.illumio.data.components;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import reactor.core.publisher.Mono;

// TODO: It would be nice if we could do some data validation here
@Component
@RequiredArgsConstructor
@Builder
@Slf4j
public class ResourceIdFlowReader {
    private final ObjectMapper objectMapper;

    public Mono<JsonNode> readTree(String value) {
        return Mono.fromCallable(
                () -> {
                    JsonNode jsonNode = objectMapper.readTree(value);
                    return jsonNode;
                });
    }
}
