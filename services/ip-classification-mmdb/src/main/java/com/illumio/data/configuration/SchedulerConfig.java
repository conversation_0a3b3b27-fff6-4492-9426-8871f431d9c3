package com.illumio.data.configuration;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;
import jakarta.annotation.PostConstruct;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.observability.micrometer.Micrometer;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Configuration
@ConfigurationProperties(prefix = "scheduler")
@Data
public class SchedulerConfig {
    private MeterRegistry meterRegistry;
    private int maxThreadPoolQueueSize = 50;
    private int keepAliveTimeSeconds = 60;
    private int maxThreadPoolSize;
    private String name = "ipclassification_scheduler";
    private int grpcMaxThreadPoolSize;
    private int grpcMaxThreadPoolQueueSize = 50;
    private int grpcKeepAliveTimeSeconds = 60;
    private String grpcName = "grpc_scheduler";

    public SchedulerConfig(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    @PostConstruct
    public void init() {
        if (maxThreadPoolSize <= 0) {
            maxThreadPoolSize = Runtime.getRuntime().availableProcessors();
        }
        if (grpcMaxThreadPoolSize <= 0) {
            grpcMaxThreadPoolSize = Runtime.getRuntime().availableProcessors();
        }
    }

    @Bean
    public Scheduler ipClassificationScheduler() {
        Scheduler rawScheduler = Schedulers.newBoundedElastic(
                maxThreadPoolSize,
                maxThreadPoolQueueSize,
                name,
                keepAliveTimeSeconds
        );

        return Micrometer.timedScheduler(
                rawScheduler,
                meterRegistry,
                "ipclassification_scheduler_micrometer_metrics",
                Tags.of(Tag.of("ipcls", "yes"))
        );
    }

    @Bean
    public Executor grpcExecutor() {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                grpcMaxThreadPoolSize,
                grpcMaxThreadPoolSize,
                grpcKeepAliveTimeSeconds,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(grpcMaxThreadPoolQueueSize),
                new ThreadFactory() {
                    private final AtomicInteger counter = new AtomicInteger();
                    @Override
                    public Thread newThread(Runnable r) {
                        Thread thread = new Thread(r);
                        thread.setName(grpcName + "-" + counter.incrementAndGet());
                        return thread;
                    }
                }
        );

        // Register metrics for the thread pool
        meterRegistry.gauge("grpc_thread_pool_size",
                Tags.of(Tag.of("executor", grpcName)),
                executor,
                ThreadPoolExecutor::getPoolSize
        );

        meterRegistry.gauge("grpc_thread_pool_active_threads",
                Tags.of(Tag.of("executor", grpcName)),
                executor,
                ThreadPoolExecutor::getActiveCount
        );

        meterRegistry.gauge("grpc_thread_pool_queue_size",
                Tags.of(Tag.of("executor", grpcName)),
                executor,
                e -> e.getQueue().size()
        );

        return executor;
    }
}