package com.illumio.data.configuration;

import com.illumio.data.components.BlueMmdbClient;
import com.illumio.data.components.CircuitBreakerConfig;

import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.micrometer.core.instrument.MeterRegistry;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import reactor.core.scheduler.Scheduler;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.concurrent.Executor;

@Configuration
@ConfigurationProperties(prefix = "bluemmdb-client")
@Getter
@Setter
@Slf4j
public class BlueMmdbClientConfig {
    private String target;
    private Integer port;

    @Bean
    public BlueMmdbClient blueMmdbClient(
            CircuitBreaker circuitBreaker,
            IpClassificationMmdbConfig config,
            Scheduler ipClassificationScheduler,
            Executor grpcExecutor,
            CircuitBreakerConfig circuitBreakerConfig,
            MeterRegistry meterRegistry) {
        BlueMmdbClient client =
                new BlueMmdbClient(
                        target,
                        port,
                        circuitBreaker,
                        config.getGrpcConfig().getTimeout(),
                        config.getGrpcConfig().getMaxRetries(),
                        config.getGrpcConfig().getInitialBackoff(),
                        config.getGrpcConfig().getMaxBackoff(),
                        ipClassificationScheduler,
                        grpcExecutor,
                        circuitBreakerConfig.isEnabled(),
                        meterRegistry);

        // We still need to verify connectivity during initialization
        client.ping()
                .retryWhen(
                        Retry.backoff(5, Duration.ofSeconds(1))
                                .maxBackoff(Duration.ofSeconds(10))
                                .doBeforeRetry(
                                        signal ->
                                                log.warn(
                                                        "We will retry... Unable to ping bluemmdb service: {}",
                                                        signal.failure().getMessage())))
                .block(); // Blocking is okay during initialization

        return client;
    }
}
