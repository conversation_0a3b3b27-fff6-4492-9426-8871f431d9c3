package com.illumio.data.test;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.ConsumerGroupDescription;
import org.apache.kafka.clients.admin.MemberDescription;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRebalanceListener;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;

import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;
import reactor.kafka.receiver.ReceiverPartition;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;
import reactor.kafka.sender.SenderRecord;
import org.apache.kafka.clients.producer.ProducerRecord;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * Kafka simulation utility for ip-classification-mmdb service to test:
 * 1. Consumer timeout scenarios
 * 2. Consumer group rebalancing
 * 3. Partition assignment logging
 */
@Slf4j
public class IpClassificationKafkaSimulator {
    
    private final AtomicInteger consumerInstanceCounter = new AtomicInteger(0);
    private volatile Disposable currentSubscription;
    private final String inputTopic;
    private final String outputTopic;
    private final String consumerGroupId;
    private final AdminClient adminClient;
    
    public IpClassificationKafkaSimulator() {
        this.inputTopic = "decorated-flow-v1"; // Default input topic for ip-classification
        this.outputTopic = "ip-classified-flow-v1"; // Default output topic
        this.consumerGroupId = "ip-classification-mmdb-simulator";
        this.adminClient = createAdminClient();
    }

    public IpClassificationKafkaSimulator(String inputTopic, String outputTopic, String consumerGroupId) {
        this.inputTopic = inputTopic;
        this.outputTopic = outputTopic;
        this.consumerGroupId = consumerGroupId;
        this.adminClient = createAdminClient();
    }
    
    /**
     * Simulates consumer timeout by creating a consumer with aggressive timeout settings
     * and introducing processing delays that exceed those timeouts
     */
    public void simulateConsumerTimeout() {
        log.info("=== Starting Consumer Timeout Simulation ===");
        log.info("Input Topic: {}, Output Topic: {}, Consumer Group: {}", inputTopic, outputTopic, consumerGroupId);
        
        KafkaReceiver<String, String> receiver = createTimeoutProneReceiver();
        KafkaSender<String, String> sender = createSender();
        
        currentSubscription = receiver
                .receiveAutoAck()
                .publishOn(Schedulers.boundedElastic())
                .doOnSubscribe(subscription -> {
                    int instanceId = consumerInstanceCounter.incrementAndGet();
                    log.info("🚀 Consumer instance {} started", instanceId);
                })
                .doOnNext(records -> {
                    log.info("📦 Received batch of {} records", records.count());
                    // Simulate processing delay that might cause timeout
                    try {
                        Thread.sleep(2000); // 2 second delay
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                })
                .flatMap(records -> records)
                .doOnNext(record -> {
                    log.info("🔄 Processing record from partition {} offset {} - Key: {}", 
                        record.partition(), record.offset(), record.key());
                    // Simulate slow processing that exceeds max.poll.interval.ms
                    simulateSlowProcessing();
                })
                .map(record -> {
                    // Simulate IP classification processing
                    String enrichedValue = enrichWithIpClassification(record.value());
                    return SenderRecord.create(
                        new ProducerRecord<>(outputTopic, record.key(), enrichedValue),
                        record.key()
                    );
                })
                .as(sender::send)
                .doOnNext(result -> {
                    if (result.exception() != null) {
                        log.error("❌ Send failed: {}", result.exception().getMessage());
                    } else {
                        log.info("✅ Successfully sent record: {}", result.correlationMetadata());
                    }
                })
                .retryWhen(
                    Retry.backoff(5, Duration.ofSeconds(3))
                        .doBeforeRetry(retrySignal -> {
                            log.warn("🔄 Consumer error, retrying... Attempt: {}, Error: {}", 
                                retrySignal.totalRetries() + 1, 
                                retrySignal.failure().getMessage());
                        })
                )
                .doOnError(error -> {
                    log.error("💥 Consumer error: {}", error.getMessage(), error);
                })
                .doOnComplete(() -> {
                    log.warn("⚠️ Consumer completed unexpectedly");
                })
                .doOnCancel(() -> {
                    log.warn("🛑 Consumer cancelled");
                })
                .subscribe(
                    result -> log.debug("Processed result: {}", result.correlationMetadata()),
                    error -> log.error("💥 Final consumer error: {}", error.getMessage(), error),
                    () -> log.info("✅ Consumer stream completed")
                );
    }
    
    /**
     * Simulates consumer group rebalancing by creating multiple consumers
     * and then stopping/starting them to trigger rebalances
     */
    public void simulateConsumerGroupRebalance() {
        log.info("=== Starting Consumer Group Rebalance Simulation ===");
        
        List<Disposable> consumers = new ArrayList<>();
        
        // Start multiple consumers to form a group
        for (int i = 0; i < 3; i++) {
            final int consumerId = i;
            KafkaReceiver<String, String> receiver = createRebalanceAwareReceiver(consumerId);
            
            Disposable consumer = receiver
                    .receiveAutoAck()
                    .publishOn(Schedulers.boundedElastic())
                    .doOnSubscribe(subscription -> {
                        log.info("🚀 Consumer {} joined the group", consumerId);
                    })
                    .flatMap(records -> records)
                    .doOnNext(record -> {
                        log.info("🔄 Consumer {} processing record from partition {} offset {}", 
                            consumerId, record.partition(), record.offset());
                        // Light processing to avoid timeouts during rebalance demo
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    })
                    .subscribe(
                        record -> log.debug("Consumer {} processed record: {}", consumerId, record.key()),
                        error -> log.error("Consumer {} error: {}", consumerId, error.getMessage()),
                        () -> log.info("Consumer {} completed", consumerId)
                    );
            
            consumers.add(consumer);
            
            // Stagger consumer startup
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        // Simulate rebalancing by stopping and starting consumers
        Mono.delay(Duration.ofSeconds(10))
            .doOnNext(tick -> {
                log.info("🔄 Triggering rebalance by stopping consumer 1");
                consumers.get(1).dispose();
            })
            .then(Mono.delay(Duration.ofSeconds(10)))
            .doOnNext(tick -> {
                log.info("🔄 Triggering another rebalance by stopping consumer 2");
                consumers.get(2).dispose();
            })
            .then(Mono.delay(Duration.ofSeconds(10)))
            .doOnNext(tick -> {
                log.info("🔄 Adding new consumer to trigger rebalance");
                KafkaReceiver<String, String> newReceiver = createRebalanceAwareReceiver(99);
                Disposable newConsumer = newReceiver
                        .receiveAutoAck()
                        .publishOn(Schedulers.boundedElastic())
                        .flatMap(records -> records)
                        .subscribe(
                            record -> log.info("New consumer processed: {}", record.key()),
                            error -> log.error("New consumer error: {}", error.getMessage())
                        );
                consumers.add(newConsumer);
            })
            .subscribe();
        
        // Store reference to clean up later
        this.currentSubscription = () -> consumers.forEach(Disposable::dispose);
    }
    
    /**
     * Creates a receiver with partition assignment logging
     */
    private KafkaReceiver<String, String> createRebalanceAwareReceiver(int consumerId) {
        Map<String, Object> consumerProps = createBaseConsumerProps();
        consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, consumerGroupId);
        consumerProps.put(ConsumerConfig.CLIENT_ID_CONFIG, "consumer-" + consumerId);
        
        // More reasonable timeouts for rebalance demo
        consumerProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 30000);      // 30 seconds
        consumerProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 10000);   // 10 seconds
        consumerProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 60000);    // 60 seconds
        
        ReceiverOptions<String, String> receiverOptions = ReceiverOptions
                .<String, String>create(consumerProps)
                .subscription(Collections.singleton(inputTopic))
                .addAssignListener(partitions -> {
                    log.info("🎯 Consumer {} assigned partitions: {}", consumerId, 
                        partitions.stream().map(ReceiverPartition::topicPartition).toList());
                })
                .addRevokeListener(partitions -> {
                    log.info("🔄 Consumer {} revoked partitions: {}", consumerId,
                        partitions.stream().map(ReceiverPartition::topicPartition).toList());
                });
                
        return KafkaReceiver.create(receiverOptions);
    }
    
    /**
     * Creates a receiver with timeout-prone configuration
     */
    private KafkaReceiver<String, String> createTimeoutProneReceiver() {
        Map<String, Object> consumerProps = createBaseConsumerProps();
        consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, consumerGroupId + "-timeout");
        
        // Very aggressive timeout settings to trigger timeouts quickly
        consumerProps.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, 5000);       // 5 seconds
        consumerProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 10000);      // 10 seconds  
        consumerProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 3000);    // 3 seconds
        consumerProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 15000);    // 15 seconds
        consumerProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 5);            // Small batches
        
        ReceiverOptions<String, String> receiverOptions = ReceiverOptions
                .<String, String>create(consumerProps)
                .subscription(Collections.singleton(inputTopic))
                .addAssignListener(partitions -> {
                    log.info("🎯 Timeout-prone consumer assigned partitions: {}", 
                        partitions.stream().map(ReceiverPartition::topicPartition).toList());
                })
                .addRevokeListener(partitions -> {
                    log.info("🔄 Timeout-prone consumer revoked partitions: {}", 
                        partitions.stream().map(ReceiverPartition::topicPartition).toList());
                });
                
        return KafkaReceiver.create(receiverOptions);
    }
    
    /**
     * Creates base consumer properties
     */
    private Map<String, Object> createBaseConsumerProps() {
        Map<String, Object> consumerProps = new HashMap<>();
        consumerProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "localhost:9092");
        consumerProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        return consumerProps;
    }
    
    /**
     * Creates a Kafka sender for output
     */
    private KafkaSender<String, String> createSender() {
        Map<String, Object> producerProps = new HashMap<>();
        producerProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "localhost:9092");
        producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.ACKS_CONFIG, "all");
        producerProps.put(ProducerConfig.RETRIES_CONFIG, 3);
        
        SenderOptions<String, String> senderOptions = SenderOptions.create(producerProps);
        return KafkaSender.create(senderOptions);
    }
    
    /**
     * Simulates slow processing that might exceed max.poll.interval.ms
     */
    private void simulateSlowProcessing() {
        try {
            // Simulate processing that takes longer than max.poll.interval.ms (15 seconds)
            Thread.sleep(18000); // 18 seconds - this should cause timeout
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("⚠️ Processing interrupted");
        }
    }
    
    /**
     * Simulates IP classification enrichment
     */
    private String enrichWithIpClassification(String originalValue) {
        // Simple simulation - in real service this would do MMDB lookup
        return originalValue.replace("}", ", \"ip_classification\": \"simulated_classification\"}");
    }
    
    /**
     * Prints current partition assignments for a consumer group
     */
    public void printPartitionAssignments() {
        log.info("=== Current Partition Assignments ===");
        log.info("Consumer Group: {}", consumerGroupId);
        log.info("Input Topic: {}", inputTopic);
        log.info("Output Topic: {}", outputTopic);
        log.info("Note: Use Kafka admin tools to see detailed partition assignments:");
        log.info("kafka-consumer-groups.sh --bootstrap-server localhost:9092 --group {} --describe", consumerGroupId);
    }
    
    /**
     * Stops the current simulation
     */
    public void stopSimulation() {
        if (currentSubscription != null && !currentSubscription.isDisposed()) {
            log.info("🛑 Stopping simulation...");
            currentSubscription.dispose();
        }
    }

    /**
     * Cleanup resources including AdminClient
     */
    public void cleanup() {
        stopSimulation();
        if (adminClient != null) {
            try {
                adminClient.close(Duration.ofSeconds(5));
                log.info("✅ AdminClient closed successfully");
            } catch (Exception e) {
                log.warn("⚠️ Error closing AdminClient: {}", e.getMessage());
            }
        }
    }
    
    /**
     * Advanced simulation: 4 consumers with timeout and rebalance monitoring
     * 1. Creates 4 consumers for topic decorated-flow-v1 (16 partitions)
     * 2. After 30 seconds, makes one consumer timeout/hang
     * 3. Triggers consumer rebalance (new consumer created)
     * 4. Prints partition assignments every 10 seconds
     * 5. Runs for 1 minute total
     */
    public void simulateAdvancedRebalanceScenario() {
        log.info("=== Advanced Rebalance Scenario Simulation ===");
        log.info("🎯 Scenario: 4 consumers → 1 hangs → rebalance → monitor for 1 minute");
        log.info("📊 Topic: {} (assuming 16 partitions)", inputTopic);
        log.info("👥 Consumer Group: {}", consumerGroupId + "-advanced");
        log.info("");

        List<Disposable> consumers = new ArrayList<>();
        AtomicInteger activeConsumerCount = new AtomicInteger(0);

        // Create 4 initial consumers
        for (int i = 0; i < 4; i++) {
            final int consumerId = i;
            KafkaReceiver<String, String> receiver = createAdvancedReceiver(consumerId);

            Disposable consumer = receiver
                    .receiveAutoAck()
                    .publishOn(Schedulers.boundedElastic())
                    .doOnSubscribe(subscription -> {
                        activeConsumerCount.incrementAndGet();
                        log.info("🚀 Consumer {} started (Active: {})", consumerId, activeConsumerCount.get());
                    })
                    .flatMap(records -> records)
                    .doOnNext(record -> {
                        // Normal fast processing for all consumers
                        try {
                            Thread.sleep(50); // 50ms processing time
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }

                        log.debug("Consumer {} processed record from partition {} offset {}",
                            consumerId, record.partition(), record.offset());
                    })
                    .doOnError(error -> {
                        activeConsumerCount.decrementAndGet();
                        log.warn("💥 Consumer {} error: {} (Active: {})",
                            consumerId, error.getMessage(), activeConsumerCount.get());
                    })
                    .doOnCancel(() -> {
                        activeConsumerCount.decrementAndGet();
                        log.warn("🛑 Consumer {} cancelled (Active: {})", consumerId, activeConsumerCount.get());
                    })
                    .subscribe(
                        record -> { /* processed successfully */ },
                        error -> log.error("Consumer {} final error: {}", consumerId, error.getMessage()),
                        () -> log.info("Consumer {} completed", consumerId)
                    )
                ;

            consumers.add(consumer);

            // Stagger consumer startup by 2 seconds
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        // Schedule consumer 2 to be killed after 30 seconds
        Mono.delay(Duration.ofSeconds(30))
                .doOnNext(tick -> {
                    log.warn("💀 Killing Consumer 2 after 30 seconds to simulate failure...");
                    if (consumers.size() > 2 && !consumers.get(2).isDisposed()) {
                        consumers.get(2).dispose();
                        log.warn("🛑 Consumer 2 has been forcefully stopped - this should trigger rebalance");
                    }
                })
                .subscribe();

        // Schedule partition monitoring every 10 seconds
        Disposable partitionMonitor = Flux.interval(Duration.ofSeconds(10))
                .doOnNext(tick -> {
                    long seconds = (tick + 1) * 10;
                    log.info("⏰ Time: {}s | Active Consumers: {} | Monitoring partition assignments...",
                        seconds, activeConsumerCount.get());
                    printCurrentPartitionState(activeConsumerCount.get());
                })
                .take(60) // Run for 600 seconds (60 intervals of 10 seconds)
                .subscribe();

        // After 30 seconds, create a replacement consumer for the hung one
        Mono.delay(Duration.ofSeconds(90))
                .doOnNext(tick -> {
                    log.info("🆕 Creating replacement consumer (ID: 99) to handle rebalance...");
                    KafkaReceiver<String, String> replacementReceiver = createAdvancedReceiver(99);

                    Disposable replacementConsumer = replacementReceiver
                            .receiveAutoAck()
                            .publishOn(Schedulers.boundedElastic())
                            .doOnSubscribe(subscription -> {
                                activeConsumerCount.incrementAndGet();
                                log.info("🚀 Replacement Consumer 99 started (Active: {})", activeConsumerCount.get());
                            })
                            .flatMap(records -> records)
                            .doOnNext(record -> {
                                // Fast processing for replacement consumer
                                try {
                                    Thread.sleep(50);
                                } catch (InterruptedException e) {
                                    Thread.currentThread().interrupt();
                                }
                                log.info("Replacement Consumer 99 processed record from partition {} offset {}",
                                    record.partition(), record.offset());
                            })
                            .subscribe(
                                record -> { /* processed successfully */ },
                                error -> log.error("Replacement Consumer 99 error: {}", error.getMessage()),
                                () -> log.info("Replacement Consumer 99 completed")
                            );

                    consumers.add(replacementConsumer);
                })
                .subscribe();

        // Store reference for cleanup
        this.currentSubscription = () -> {
            consumers.forEach(Disposable::dispose);
            partitionMonitor.dispose();
        };

        log.info("🎬 Advanced simulation started - will run for 60 seconds");
        log.info("📋 Expected timeline:");
        log.info("   0-30s: 4 consumers processing normally");
        log.info("   30s: Consumer 2 is forcefully killed (simulates failure)");
        log.info("   35s: Replacement consumer 99 joins");
        log.info("   30-35s: Rebalance occurs (3 consumers handle all partitions)");
        log.info("   35-60s: 4 consumers (0,1,3,99) continue processing after rebalance");
    }

    /**
     * Creates a receiver for the advanced simulation
     */
    private KafkaReceiver<String, String> createAdvancedReceiver(int consumerId) {
        Map<String, Object> consumerProps = createBaseConsumerProps();
        consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, consumerGroupId + "-advanced");
        consumerProps.put(ConsumerConfig.CLIENT_ID_CONFIG, "advanced-consumer-" + consumerId);

        // Moderate timeout settings for realistic rebalance behavior
//        consumerProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 10000);      // 10 seconds
//        consumerProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 1000);   // 1 seconds
//        consumerProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 45000);    // 45 seconds
//        consumerProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 50);           // Moderate batch size

        // Very aggressive timeout settings to trigger timeouts quickly
        consumerProps.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, 5000);       // 5 seconds
        consumerProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 10000);      // 10 seconds
        consumerProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 3000);    // 3 seconds
        consumerProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 15000);    // 15 seconds
        consumerProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 5);            // Small batches


        ReceiverOptions<String, String> receiverOptions = ReceiverOptions
                .<String, String>create(consumerProps)
                .subscription(Collections.singleton(inputTopic))
                .addAssignListener(partitions -> {
                    log.info("🎯 Advanced Consumer {} assigned partitions: {}", consumerId,
                        partitions.stream().map(p -> p.topicPartition().partition()).sorted().toList());
                })
                .addRevokeListener(partitions -> {
                    log.info("🔄 Advanced Consumer {} revoked partitions: {}", consumerId,
                        partitions.stream().map(p -> p.topicPartition().partition()).sorted().toList());
                });

        return KafkaReceiver.create(receiverOptions);
    }



    /**
     * Prints current partition state using Kafka Admin API
     */
    private void printCurrentPartitionState(int activeConsumers) {
        log.info("📊 Current State:");
        log.info("   🏷️  Topic: {} (16 partitions)", inputTopic);
        log.info("   👥 Active Consumers: {}", activeConsumers);
        log.info("   📈 Expected partitions per consumer: ~{}", 16 / Math.max(activeConsumers, 1));

        // Get actual partition assignments using Admin API
        try {
            String groupId = consumerGroupId + "-advanced";
            ConsumerGroupDescription groupDescription = adminClient
                    .describeConsumerGroups(Collections.singleton(groupId))
                    .describedGroups()
                    .get(groupId)
                    .get(5, TimeUnit.SECONDS);

            log.info("   🔍 Actual Partition Assignments:");
            log.info("   📋 Consumer Group: {}", groupId);
            log.info("   🏃 Group State: {}", groupDescription.state());

            Collection<MemberDescription> members = groupDescription.members();
            if (members.isEmpty()) {
                log.info("   ⚠️  No active members found in consumer group");
            } else {
                for (MemberDescription member : members) {
                    String clientId = member.clientId();
                    Set<TopicPartition> assignedPartitions = member.assignment().topicPartitions();

                    List<Integer> partitionNumbers = assignedPartitions.stream()
                            .filter(tp -> tp.topic().equals(inputTopic))
                            .map(TopicPartition::partition)
                            .sorted()
                            .toList();

                    log.info("   🎯 Consumer {} assigned partitions: {} (count: {})",
                        clientId, partitionNumbers, partitionNumbers.size());
                }

                // Calculate total assigned partitions
                int totalAssigned = members.stream()
                        .mapToInt(member -> (int) member.assignment().topicPartitions().stream()
                                .filter(tp -> tp.topic().equals(inputTopic))
                                .count())
                        .sum();

                log.info("   📊 Total partitions assigned: {}/16", totalAssigned);

                if (totalAssigned < 16) {
                    log.warn("   ⚠️  {} partitions are unassigned (possible rebalance in progress)", 16 - totalAssigned);
                }
            }

        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            log.warn("   ⚠️  Could not retrieve partition assignments: {}", e.getMessage());
            log.info("   💡 This might be normal during rebalancing or if consumer group doesn't exist yet");
        }

        log.info(""); // Empty line for readability
    }

    /**
     * Creates Kafka Admin Client for monitoring consumer groups
     */
    private AdminClient createAdminClient() {
        Properties adminProps = new Properties();
        adminProps.put("bootstrap.servers", "localhost:9092");
        adminProps.put("request.timeout.ms", 5000);
        adminProps.put("connections.max.idle.ms", 10000);
        return AdminClient.create(adminProps);
    }

    private long startTime = System.currentTimeMillis();

    /**
     * Demonstrates all simulation scenarios
     */
    public void runAllSimulations() {
        log.info("🎬 Starting comprehensive Kafka simulation for ip-classification-mmdb");

        printPartitionAssignments();

        // Run timeout simulation for 30 seconds
        log.info("📍 Phase 1: Consumer Timeout Simulation (30 seconds)");
        simulateConsumerTimeout();

        Mono.delay(Duration.ofSeconds(30))
            .doOnNext(tick -> {
                stopSimulation();
                log.info("📍 Phase 2: Consumer Group Rebalance Simulation (60 seconds)");
                simulateConsumerGroupRebalance();
            })
            .then(Mono.delay(Duration.ofSeconds(60)))
            .doOnNext(tick -> {
                stopSimulation();
                log.info("✅ All simulations completed");
            })
            .subscribe();
    }
}
