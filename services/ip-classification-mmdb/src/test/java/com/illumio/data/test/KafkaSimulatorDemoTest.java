package com.illumio.data.test;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.serialization.StringDeserializer;
import reactor.kafka.receiver.ReceiverOptions;
import reactor.kafka.receiver.KafkaReceiver;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Demo test to show how multiple Kafka consumers are created for the ip-classification-mmdb service.
 * This test doesn't require a running Kafka instance - it just demonstrates the consumer creation logic.
 */
@Slf4j
public class KafkaSimulatorDemoTest {
    
    private final AtomicInteger consumerInstanceCounter = new AtomicInteger(0);
    private final String inputTopic = "decorated-flow-v1";
    private final String consumerGroupId = "ip-classification-mmdb-demo";
    
    @Test
    void demonstrateMultipleConsumerCreation() {
        log.info("=== Demonstrating Multiple Consumer Creation ===");
        log.info("This test shows how multiple consumers are created in the same consumer group");
        log.info("Input Topic: {}, Consumer Group: {}", inputTopic, consumerGroupId);
        log.info("");
        
        List<KafkaReceiver<String, String>> consumers = new ArrayList<>();
        
        // Create multiple consumers (similar to simulateConsumerGroupRebalance method)
        for (int i = 0; i < 3; i++) {
            final int consumerId = i;
            log.info("🔧 Creating consumer {} with configuration:", consumerId);
            
            KafkaReceiver<String, String> receiver = createConsumerReceiver(consumerId);
            consumers.add(receiver);
            
            log.info("   ✅ Consumer {} created successfully", consumerId);
            log.info("   📋 Consumer {} config: group={}, client=consumer-{}", 
                consumerId, consumerGroupId, consumerId);
            log.info("");
        }
        
        log.info("📊 Summary:");
        log.info("   Total consumers created: {}", consumers.size());
        log.info("   All consumers belong to group: {}", consumerGroupId);
        log.info("   All consumers subscribe to topic: {}", inputTopic);
        log.info("");
        
        log.info("🔄 In a real scenario, these consumers would:");
        log.info("   1. Join the consumer group '{}'", consumerGroupId);
        log.info("   2. Participate in partition assignment for topic '{}'", inputTopic);
        log.info("   3. Automatically rebalance when consumers join/leave");
        log.info("   4. Process messages from assigned partitions");
        log.info("");
        
        // Demonstrate dynamic consumer addition
        log.info("🆕 Adding a 4th consumer dynamically (like in rebalance simulation):");
        KafkaReceiver<String, String> newReceiver = createConsumerReceiver(99);
        consumers.add(newReceiver);
        log.info("   ✅ Dynamic consumer 99 created and added to the group");
        log.info("   📊 Total consumers now: {}", consumers.size());
        log.info("");
        
        log.info("✅ Multiple consumer creation demonstration completed!");
        log.info("💡 To see this in action with real Kafka:");
        log.info("   1. Start Kafka on localhost:9092");
        log.info("   2. Create topics: decorated-flow-v1, ip-classified-flow-v1");
        log.info("   3. Remove @Disabled from IpClassificationKafkaSimulatorTest methods");
        log.info("   4. Run the actual simulation tests");
    }
    
    /**
     * Creates a Kafka receiver configured for the consumer group
     * This method shows the actual configuration used for multiple consumers
     */
    private KafkaReceiver<String, String> createConsumerReceiver(int consumerId) {
        Map<String, Object> consumerProps = new HashMap<>();
        
        // Basic Kafka configuration
        consumerProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, "localhost:9092");
        consumerProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        
        // Consumer group configuration - THIS IS KEY FOR MULTIPLE CONSUMERS
        consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, consumerGroupId); // Same group for all consumers
        consumerProps.put(ConsumerConfig.CLIENT_ID_CONFIG, "consumer-" + consumerId); // Unique client ID
        
        // Timeout configurations for rebalancing
        consumerProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 30000);      // 30 seconds
        consumerProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 10000);   // 10 seconds
        consumerProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, 60000);    // 60 seconds
        consumerProps.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 100);          // Batch size
        
        log.info("   🔧 Consumer {} configuration:", consumerId);
        log.info("      - Group ID: {}", consumerProps.get(ConsumerConfig.GROUP_ID_CONFIG));
        log.info("      - Client ID: {}", consumerProps.get(ConsumerConfig.CLIENT_ID_CONFIG));
        log.info("      - Bootstrap Servers: {}", consumerProps.get(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG));
        log.info("      - Session Timeout: {}ms", consumerProps.get(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG));
        
        // Create receiver options with partition assignment listeners
        ReceiverOptions<String, String> receiverOptions = ReceiverOptions
                .<String, String>create(consumerProps)
                .subscription(Collections.singleton(inputTopic))
                .addAssignListener(partitions -> {
                    log.info("🎯 Consumer {} would be assigned partitions: {}", consumerId, 
                        partitions.stream().map(p -> p.topicPartition().toString()).toList());
                })
                .addRevokeListener(partitions -> {
                    log.info("🔄 Consumer {} would revoke partitions: {}", consumerId,
                        partitions.stream().map(p -> p.topicPartition().toString()).toList());
                });
                
        return KafkaReceiver.create(receiverOptions);
    }
    
    @Test
    void demonstrateConsumerGroupConcepts() {
        log.info("=== Consumer Group Concepts Demonstration ===");
        log.info("");
        
        log.info("📚 Key Concepts:");
        log.info("1. Consumer Group: A group of consumers that work together to consume a topic");
        log.info("   - Group ID: '{}'", consumerGroupId);
        log.info("   - All consumers in the group share the same group ID");
        log.info("");
        
        log.info("2. Partition Assignment:");
        log.info("   - Kafka automatically assigns partitions to consumers in the group");
        log.info("   - Each partition is assigned to only one consumer in the group");
        log.info("   - If there are more consumers than partitions, some consumers will be idle");
        log.info("");
        
        log.info("3. Rebalancing:");
        log.info("   - When a consumer joins or leaves, Kafka triggers a rebalance");
        log.info("   - During rebalance, partitions are reassigned among active consumers");
        log.info("   - This ensures fault tolerance and load distribution");
        log.info("");
        
        log.info("4. Consumer Identification:");
        log.info("   - Each consumer has a unique client ID: consumer-0, consumer-1, etc.");
        log.info("   - Client ID helps identify individual consumers in logs and monitoring");
        log.info("");
        
        log.info("5. Timeout Configuration:");
        log.info("   - session.timeout.ms: How long coordinator waits for heartbeat");
        log.info("   - heartbeat.interval.ms: How often consumer sends heartbeats");
        log.info("   - max.poll.interval.ms: Max time between poll() calls");
        log.info("");
        
        log.info("✅ Consumer group concepts demonstration completed!");
    }
    
    @Test
    void demonstrateRebalanceScenarios() {
        log.info("=== Rebalance Scenarios Demonstration ===");
        log.info("");
        
        log.info("🎬 Scenario 1: Starting with 3 consumers");
        log.info("   Topic: {} (assume 3 partitions)", inputTopic);
        log.info("   Consumers: consumer-0, consumer-1, consumer-2");
        log.info("   Assignment: consumer-0→partition-0, consumer-1→partition-1, consumer-2→partition-2");
        log.info("");
        
        log.info("🎬 Scenario 2: Consumer-1 leaves (timeout or shutdown)");
        log.info("   Remaining: consumer-0, consumer-2");
        log.info("   Rebalance triggered!");
        log.info("   New assignment: consumer-0→partition-0,1, consumer-2→partition-2");
        log.info("");
        
        log.info("🎬 Scenario 3: Consumer-2 also leaves");
        log.info("   Remaining: consumer-0");
        log.info("   Rebalance triggered!");
        log.info("   New assignment: consumer-0→partition-0,1,2 (all partitions)");
        log.info("");
        
        log.info("🎬 Scenario 4: New consumer-99 joins");
        log.info("   Active: consumer-0, consumer-99");
        log.info("   Rebalance triggered!");
        log.info("   New assignment: consumer-0→partition-0,1, consumer-99→partition-2");
        log.info("");
        
        log.info("💡 Key Points:");
        log.info("   - Rebalancing ensures even distribution of partitions");
        log.info("   - During rebalance, message processing is paused briefly");
        log.info("   - Consumers coordinate through the Kafka coordinator");
        log.info("   - Partition assignment is automatic and transparent");
        log.info("");
        
        log.info("✅ Rebalance scenarios demonstration completed!");
    }
}
