package com.illumio.data;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.illumio.data.components.BlueMmdbClient;
import com.illumio.data.components.IpClassificationLookup;

import com.illumio.data.util.MetricsUtil;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.metrics.LongCounter;
import mmdb.Mmdb.BluePipelineIPCResponse;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * Unit tests for IpClassificationLookup's addBluePipelineIPCInfo method.
 *
 * <p>These tests verify the behavior of the method when handling optional and non-optional fields.
 *
 * <p>- Optional fields:
 *
 * <ul>
 *   <li>isWellknown: Boolean (True, False, NULL) --> True, Flase, not present
 *   <li>threatLevel: Integer (1, 0, NULL) --> 1, 0, not present
 * </ul>
 *
 * - Non-optional fields (this will be the same for ANY non-optional/String field):
 *
 * <ul>
 *   <li>city: String ("Los Gatos", "", NULL) --> "Los Gatos", not present, not present
 * </ul>
 *
 * <p>The tests ensure:
 *
 * <ul>
 *   <li>When data is present (optional values exist, strings exist and are not empty), the keys are
 *       present in the resulting ObjectNode.
 *   <li>When data is absent (optional values are NULL, strings are empty or NULL), the keys are not
 *       present in the resulting ObjectNode.
 * </ul>
 */
@SpringBootTest(classes = IpClassificationLookupTest.class)
@AutoConfigureMockMvc
@TestPropertySource(locations = "classpath:application.yml")
class IpClassificationLookupTest {
    IpClassificationLookup ipClassificationLookup;
    ObjectMapper objectMapper;
    @Mock BlueMmdbClient blueMmdbClient;
    @Mock LongCounter mismatchFieldsCounter;
    @Mock
    MetricsUtil metricsUtil;

    @BeforeEach
    void setup() {
        objectMapper = new ObjectMapper();
        ipClassificationLookup =
                new IpClassificationLookup(objectMapper, blueMmdbClient, mismatchFieldsCounter, metricsUtil);
    }

    @Test
    void testAddBluePipelineIPCInfoDataWithNullValues() {
        // Init test
        ObjectNode node = objectMapper.createObjectNode();
        BluePipelineIPCResponse resp = BluePipelineIPCResponse.newBuilder().build();

        // UUT - Call the public method
        ipClassificationLookup.addBluePipelineIPCInfo(node, resp);

        // Verify the expected results
        assertFalse(node.has(IpClassificationLookup.SrcIsWellknown));
        assertFalse(node.has(IpClassificationLookup.SrcThreatLevel));
        assertFalse(node.has(IpClassificationLookup.SrcCity));
        assertFalse(node.has(IpClassificationLookup.DestIsWellknown));
        assertFalse(node.has(IpClassificationLookup.DestThreatLevel));
        assertFalse(node.has(IpClassificationLookup.DestCity));
    }

    @Test
    void testAddBluePipelineIPCInfoDataPresent() {
        // Test Data
        boolean isWellknown = true; // Even though response says true...
        Integer threatLevel = 1;
        String city = "Los Gatos";
        String country = "US";
        String region = "California";
        String cloudProvider = "azure";
        String domain = "abc.com";
        String destExternalLabel = "chatgpt";
        String destExternalLabelCategory = "LLM";
        String sourceExternalLabel = "onedrive";
        String sourceExternalLabelCategory = "APP";

        // Init test
        ObjectNode node = objectMapper.createObjectNode();
        node.put(IpClassificationLookup.SrcIP, "********"); // private IP
        node.put(IpClassificationLookup.DestIP, "*******"); // public IP

        BluePipelineIPCResponse resp =
                BluePipelineIPCResponse.newBuilder()
                        .setSrcThreatLevel(threatLevel)
                        .setSrcCity(city)
                        .setDestIsWellknown(isWellknown)
                        .setDestThreatLevel(threatLevel)
                        .setDestCity(city)
                        .setSrcCountry(country)
                        .setDestCountry(country)
                        .setSrcRegion(region)
                        .setDestRegion(region)
                        .setSrcCloudProvider(cloudProvider)
                        .setDestCloudProvider(cloudProvider)
                        .setSrcDomain(domain)
                        .setDestDomain(domain)
                        .setDestinationExternalLabel(destExternalLabel)
                        .setDestinationExternalLabelCategory(destExternalLabelCategory)
                        .setSourceExternalLabel(sourceExternalLabel)
                        .setSourceExternalLabelCategory(sourceExternalLabelCategory)
                        .build();

        // UUT - Call the public method
        ipClassificationLookup.addBluePipelineIPCInfo(node, resp);

        // Verify the expected results
        assertEquals(
                false,
                node.get(IpClassificationLookup.SrcIsWellknown)
                        .asBoolean()); // Should be false because private IP
        assertEquals(
                isWellknown,
                node.get(IpClassificationLookup.DestIsWellknown)
                        .asBoolean()); // Should be true because public IP
        assertEquals(threatLevel, node.get(IpClassificationLookup.SrcThreatLevel).asInt());
        assertEquals(city, node.get(IpClassificationLookup.SrcCity).asText());
        assertEquals(threatLevel, node.get(IpClassificationLookup.DestThreatLevel).asInt());
        assertEquals(city, node.get(IpClassificationLookup.DestCity).asText());
        assertEquals(country, node.get(IpClassificationLookup.SrcCountry).asText());
        assertEquals(country, node.get(IpClassificationLookup.DestCountry).asText());
        assertEquals(region, node.get(IpClassificationLookup.SrcGeoRegion).asText());
        assertEquals(region, node.get(IpClassificationLookup.DestGeoRegion).asText());
        assertEquals(cloudProvider, node.get(IpClassificationLookup.SrcCloudProvider).asText());
        assertEquals(cloudProvider, node.get(IpClassificationLookup.DestCloudProvider).asText());
        assertEquals(domain, node.get(IpClassificationLookup.SrcDomain).asText());
        assertEquals(domain, node.get(IpClassificationLookup.DestDomain).asText());
        assertEquals(destExternalLabel, node.get(IpClassificationLookup.DestinationExternalLabel).asText());
        assertEquals(destExternalLabelCategory, node.get(IpClassificationLookup.DestinationExternalLabelCategory).asText());
        assertEquals(sourceExternalLabel, node.get(IpClassificationLookup.SourceExternalLabel).asText());
        assertEquals(sourceExternalLabelCategory, node.get(IpClassificationLookup.SourceExternalLabelCategory).asText());

        // Verify that ONLY the expected keys are present
        Set<String> expectedKeys = Set.of(
                IpClassificationLookup.SrcIP,
                IpClassificationLookup.DestIP,
                IpClassificationLookup.SrcIsWellknown,
                IpClassificationLookup.DestIsWellknown,
                IpClassificationLookup.SrcThreatLevel,
                IpClassificationLookup.DestThreatLevel,
                IpClassificationLookup.SrcCity,
                IpClassificationLookup.DestCity,
                IpClassificationLookup.SrcCountry,
                IpClassificationLookup.DestCountry,
                IpClassificationLookup.SrcGeoRegion,
                IpClassificationLookup.DestGeoRegion,
                IpClassificationLookup.SrcCloudProvider,
                IpClassificationLookup.DestCloudProvider,
                IpClassificationLookup.SrcDomain,
                IpClassificationLookup.DestDomain,
                IpClassificationLookup.DestinationExternalLabel,
                IpClassificationLookup.DestinationExternalLabelCategory,
                IpClassificationLookup.SourceExternalLabel,
                IpClassificationLookup.SourceExternalLabelCategory
        );

        Set<String> actualKeys = new HashSet<>();
        node.fieldNames().forEachRemaining(actualKeys::add);

        // Find missing keys
        Set<String> missingKeys = new HashSet<>(expectedKeys);
        missingKeys.removeAll(actualKeys);

        // Find unexpected keys
        Set<String> unexpectedKeys = new HashSet<>(actualKeys);
        unexpectedKeys.removeAll(expectedKeys);

        // Assert that there are no missing keys
        assertTrue(missingKeys.isEmpty(), "Missing expected keys in response: " + missingKeys);

        // Assert that there are no unexpected keys
        assertTrue(unexpectedKeys.isEmpty(), "Unexpected keys found in response: " + unexpectedKeys);
    }

    @Test
    void testAddBluePipelineIPCInfoDataPresentButDefault() {
        // Test Data
        boolean isWellknown = false;
        Integer threatLevel = 0;
        String city = "";

        // Init test
        ObjectNode node = objectMapper.createObjectNode();
        BluePipelineIPCResponse resp =
                BluePipelineIPCResponse.newBuilder()
                        .setSrcIsWellknown(isWellknown)
                        .setSrcThreatLevel(threatLevel)
                        .setSrcCity(city)
                        .setDestIsWellknown(isWellknown)
                        .setDestThreatLevel(threatLevel)
                        .setDestCity(city)
                        .build();

        // UUT - Call the public method
        ipClassificationLookup.addBluePipelineIPCInfo(node, resp);

        // Verify the expected results
        assertEquals(isWellknown, node.get(IpClassificationLookup.SrcIsWellknown).asBoolean());
        assertEquals(threatLevel, node.get(IpClassificationLookup.SrcThreatLevel).asInt());
        assertFalse(node.has(IpClassificationLookup.SrcCity));
        assertEquals(isWellknown, node.get(IpClassificationLookup.DestIsWellknown).asBoolean());
        assertEquals(threatLevel, node.get(IpClassificationLookup.DestThreatLevel).asInt());
        assertFalse(node.has(IpClassificationLookup.DestCity));
    }

    @Test
    void testPrivateIpRanges() {
        // Test Data
        boolean isWellknown = true; // Response says true but should be overridden for private IPs
        Integer threatLevel = 1;
        String city = "Los Gatos";

        BluePipelineIPCResponse resp =
                BluePipelineIPCResponse.newBuilder()
                        .setSrcIsWellknown(isWellknown)
                        .setSrcThreatLevel(threatLevel)
                        .setSrcCity(city)
                        .setDestIsWellknown(isWellknown)
                        .setDestThreatLevel(threatLevel)
                        .setDestCity(city)
                        .build();

        // Test each private IP range
        Map<String, String> privateIpTests = new HashMap<>();
        // Class A (10.0.0.0/8)
        privateIpTests.put("********", "Class A start range");
        privateIpTests.put("**************", "Class A end range");
        privateIpTests.put("***********", "Class A middle range");
        privateIpTests.put("*********", "Class A specific case"); // special case

        // Class B (**********/12)
        privateIpTests.put("**********", "Class B start range");
        privateIpTests.put("**************", "Class B end range");
        privateIpTests.put("************", "Class B middle range");

        // Class C (***********/16)
        privateIpTests.put("***********", "Class C start range");
        privateIpTests.put("***************", "Class C end range");
        privateIpTests.put("*************", "Class C middle range");

        // Link Local (***********/16)
        privateIpTests.put("***********", "Link Local start range");
        privateIpTests.put("***************", "Link Local end range");
        privateIpTests.put("*************", "Link Local middle range");

        // Loopback (*********/8)
        privateIpTests.put("127.0.0.1", "Loopback start range");
        privateIpTests.put("***************", "Loopback end range");
        privateIpTests.put("**********", "Loopback middle range");

        for (Map.Entry<String, String> test : privateIpTests.entrySet()) {
            // Init test
            ObjectNode node = objectMapper.createObjectNode();
            node.put(IpClassificationLookup.SrcIP, test.getKey());

            // UUT - Call the public method
            ipClassificationLookup.addBluePipelineIPCInfo(node, resp);

            // Verify the expected results
            assertEquals(
                    false,
                    node.get(IpClassificationLookup.SrcIsWellknown).asBoolean(),
                    "Failed for " + test.getValue() + " IP: " + test.getKey());
        }
    }

    @Test
    void testPublicIpRanges() {
        // Test Data
        boolean isWellknown = true;
        Integer threatLevel = 1;
        String city = "Los Gatos";

        BluePipelineIPCResponse resp =
                BluePipelineIPCResponse.newBuilder()
                        .setSrcIsWellknown(isWellknown)
                        .setSrcThreatLevel(threatLevel)
                        .setSrcCity(city)
                        .setDestIsWellknown(isWellknown)
                        .setDestThreatLevel(threatLevel)
                        .setDestCity(city)
                        .build();

        Map<String, String> publicIpTests = new HashMap<>();
        publicIpTests.put("*******", "Google DNS");
        publicIpTests.put("*******", "Cloudflare DNS");
        publicIpTests.put("**************", "Just below Class B private range");
        publicIpTests.put("**********", "Just above Class B private range");
        publicIpTests.put("***************", "Just below Class C private range");
        publicIpTests.put("***********", "Just above Class C private range");
        publicIpTests.put("*************", "Just below Class A private range");
        publicIpTests.put("********", "Just above Class A private range");

        for (Map.Entry<String, String> test : publicIpTests.entrySet()) {
            // Init test
            ObjectNode node = objectMapper.createObjectNode();
            node.put(IpClassificationLookup.SrcIP, test.getKey());

            // UUT - Call the public method
            ipClassificationLookup.addBluePipelineIPCInfo(node, resp);

            // Verify the expected results
            assertEquals(
                    isWellknown,
                    node.get(IpClassificationLookup.SrcIsWellknown).asBoolean(),
                    "Failed for " + test.getValue() + " IP: " + test.getKey());
        }
    }

    @Test
    void testAddBluePipelineIPCInfoDataConflict() {
        // Test Data
        boolean isWellknown = false;
        Integer threatLevel = 0;
        String city = "sunnyvale";
        String country = "US";
        String region = "California";
        String cloudprovider = "azure";
        String domain = "example.com";
        String externalLabel = "perplexity";

        boolean RespIsWellknown = true;
        Integer RespThreatLevel = 3;
        String RespCity = "tokyo";
        String RespCountry = "JP";
        String RespRegion = "tokyo";
        String RespCloudprovider = "gcp";
        String RespDomain = "wrong.com";
        String RespExternalLabel = "chatgpt";

        // Init test
        ObjectNode node = objectMapper.createObjectNode();
        node.put(IpClassificationLookup.SrcCity,city);
        node.put(IpClassificationLookup.DestCity,city);
        node.put(IpClassificationLookup.SrcCloudProvider,cloudprovider);
        node.put(IpClassificationLookup.DestCloudProvider,cloudprovider);
        node.put(IpClassificationLookup.SrcIsWellknown,isWellknown);
        node.put(IpClassificationLookup.DestIsWellknown,isWellknown);
        node.put(IpClassificationLookup.SrcThreatLevel,threatLevel);
        node.put(IpClassificationLookup.DestThreatLevel,threatLevel);
        node.put(IpClassificationLookup.SrcDomain,domain);
        node.put(IpClassificationLookup.DestDomain,domain);
        node.put(IpClassificationLookup.SrcCountry,country);
        node.put(IpClassificationLookup.DestCountry,country);
        node.put(IpClassificationLookup.SrcGeoRegion,region);
        node.put(IpClassificationLookup.DestGeoRegion,region);
        node.put(IpClassificationLookup.DestinationExternalLabel, externalLabel);


        BluePipelineIPCResponse resp =
                BluePipelineIPCResponse.newBuilder()
                        .setSrcIsWellknown(RespIsWellknown)
                        .setSrcThreatLevel(RespThreatLevel)
                        .setSrcCity(RespCity)
                        .setDestIsWellknown(RespIsWellknown)
                        .setDestThreatLevel(RespThreatLevel)
                        .setDestCity(RespCity)
                        .setSrcCountry(RespCountry)
                        .setDestCountry(RespCountry)
                        .setSrcRegion(RespRegion)
                        .setDestRegion(RespRegion)
                        .setSrcCloudProvider(RespCloudprovider)
                        .setDestCloudProvider(RespCloudprovider)
                        .setSrcDomain(RespDomain)
                        .setDestDomain(RespDomain)
                        .setDestinationExternalLabel(RespExternalLabel)
                        .build();

        // UUT - Call the public method
        ipClassificationLookup.addBluePipelineIPCInfo(node, resp);

        // Verify that no upstream values are overridden
        assertEquals(isWellknown, node.get(IpClassificationLookup.DestIsWellknown).asBoolean());
        assertEquals(threatLevel, node.get(IpClassificationLookup.DestThreatLevel).asInt());
        assertEquals(city, node.get(IpClassificationLookup.DestCity).asText());
        assertEquals(cloudprovider, node.get(IpClassificationLookup.DestCloudProvider).asText());
        assertEquals(domain, node.get(IpClassificationLookup.DestDomain).asText());
        assertEquals(country, node.get(IpClassificationLookup.DestCountry).asText());
        assertEquals(region, node.get(IpClassificationLookup.DestGeoRegion).asText());
        assertEquals(externalLabel, node.get(IpClassificationLookup.DestinationExternalLabel).asText());

        assertEquals(isWellknown, node.get(IpClassificationLookup.SrcIsWellknown).asBoolean());
        assertEquals(threatLevel, node.get(IpClassificationLookup.SrcThreatLevel).asInt());
        assertEquals(city, node.get(IpClassificationLookup.SrcCity).asText());
        assertEquals(cloudprovider, node.get(IpClassificationLookup.SrcCloudProvider).asText());
        assertEquals(domain, node.get(IpClassificationLookup.SrcDomain).asText());
        assertEquals(country, node.get(IpClassificationLookup.SrcCountry).asText());
        assertEquals(region, node.get(IpClassificationLookup.SrcGeoRegion).asText());

        // Verify counter increments for all possible mismatches
        verify(mismatchFieldsCounter, times(1)).add(eq(1L), argThat(attrs ->
                attrs.get(AttributeKey.stringKey("fieldName")).equals("SrcThreatLevel")
        ));
        verify(mismatchFieldsCounter, times(1)).add(eq(1L), argThat(attrs ->
                attrs.get(AttributeKey.stringKey("fieldName")).equals("DestThreatLevel")
        ));
        verify(mismatchFieldsCounter, times(1)).add(eq(1L), argThat(attrs ->
                attrs.get(AttributeKey.stringKey("fieldName")).equals("SrcCity")
        ));
        verify(mismatchFieldsCounter, times(1)).add(eq(1L), argThat(attrs ->
                attrs.get(AttributeKey.stringKey("fieldName")).equals("DestCity")
        ));
        verify(mismatchFieldsCounter, times(1)).add(eq(1L), argThat(attrs ->
                attrs.get(AttributeKey.stringKey("fieldName")).equals("SrcCloudProvider")
        ));
        verify(mismatchFieldsCounter, times(1)).add(eq(1L), argThat(attrs ->
                attrs.get(AttributeKey.stringKey("fieldName")).equals("DestCloudProvider")
        ));
        verify(mismatchFieldsCounter, times(1)).add(eq(1L), argThat(attrs ->
                attrs.get(AttributeKey.stringKey("fieldName")).equals("SrcDomain")
        ));
        verify(mismatchFieldsCounter, times(1)).add(eq(1L), argThat(attrs ->
                attrs.get(AttributeKey.stringKey("fieldName")).equals("DestDomain")
        ));
        verify(mismatchFieldsCounter, times(1)).add(eq(1L), argThat(attrs ->
                attrs.get(AttributeKey.stringKey("fieldName")).equals("SrcCountry")
        ));
        verify(mismatchFieldsCounter, times(1)).add(eq(1L), argThat(attrs ->
                attrs.get(AttributeKey.stringKey("fieldName")).equals("SrcGeoRegion")
        ));
        verify(mismatchFieldsCounter, times(1)).add(eq(1L), argThat(attrs ->
                attrs.get(AttributeKey.stringKey("fieldName")).equals("DestGeoRegion")
        ));
        verify(mismatchFieldsCounter, times(1)).add(eq(1L), argThat(attrs ->
                attrs.get(AttributeKey.stringKey("fieldName")).equals("DestCountry")
        ));
        verify(mismatchFieldsCounter, times(1)).add(eq(1L), argThat(attrs ->
                attrs.get(AttributeKey.stringKey("fieldName")).equals("DestinationExternalLabel")
        ));
    }
}
