logging:
  level:
    ROOT: DEBUG
    org:
      apache:
        kafka: INFO
spring:
  application:
    name: ip-classification-mmdb
  output:
    ansi:
      enabled: ALWAYS

ipClassificationMmdb:
  kafkaCommonConfig:
    bootstrapServers: localhost:19092
    isSasl: false
    saslJaasConfig: _DO_NOT_COMMIT_
    metadataMaxAgeMs: 180000
    connectionMaxIdleMs: 180000
  kafkaReceiverConfig:
    topic: illumio-resource-id-flows
    groupId: ip-classification-mmdb-group
    isGroupInstanceIdEnabled: false # Flag for adding the group.instance.id as pod name in env variable
    autoOffsetReset: latest
    requestTimeoutMs: 60000
    maxPollRecords: 2000
    maxPartitionFetchBytes: 2097152
    maxRetries: 5
    maxBackoff: 3s
    assignmentStrategy: "org.apache.kafka.clients.consumer.CooperativeStickyAssignor" # org.apache.kafka.clients.consumer.RangeAssignor # org.apache.kafka.clients.consumer.RoundRobinAssignor # org.apache.kafka.clients.consumer.CooperativeStickyAssignor
    maxPollIntervalMs: 300000
    sessionTimeoutMs: 300000
    heartbeatIntervalMs: 3000
  kafkaSenderConfig:
    sinkTopic: ip-classification-mmdb
    maxRequestSize: 1000000
    requestTimeoutMs: 60000
    metadataMaxIdleMs: 180000
    deliveryTimeoutMs: 300000
    maxRetries: 5
    maxBackoff: 3s
    lingerMs: 10
    bufferMemoryMb: 48
    batchSizeKb: 64
    numProducers: 1
  grpcConfig:
    timeout: 5s
    maxRetries: 3
    initialBackoff: 100ms
    maxBackoff: 1s

blueMmdbClient:
  target: "localhost"
  port: 7900

circuitBreaker:
  slidingWindowSize: 100
  failureRateThreshold: 50.0
  waitDurationInOpenState: 60s
  permittedNumberOfCallsInHalfOpenState: 10
  slidingWindowType: COUNT_BASED
  slowCallDurationThreshold: 5s
  slowCallRateThreshold: 50.0

