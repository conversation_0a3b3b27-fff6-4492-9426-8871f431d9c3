plugins {
    id 'org.springframework.boot'
    id 'com.google.cloud.tools.jib'
    id 'com.google.protobuf'
}

apply from: "${project.rootDir}/gradle/scripts/jib-build.gradle"
apply from: "${project.rootDir}/gradle/scripts/opentelemetry.gradle"

dependencies {
    // implementation
    implementation project(':commons:azure-commons')
    implementation(project(":commons:utility-commons"))
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation 'org.springframework.kafka:spring-kafka'
    implementation 'io.projectreactor.kafka:reactor-kafka'
    implementation 'co.elastic.logging:logback-ecs-encoder'
    implementation 'com.azure:azure-identity'
    implementation 'io.opentelemetry:opentelemetry-api'
    implementation 'io.micrometer:micrometer-core'
    implementation 'io.projectreactor:reactor-core-micrometer:1.1.8'
    implementation("io.opentelemetry.instrumentation:opentelemetry-micrometer-1.5:2.7.0-alpha")
    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-csv'
    implementation 'io.grpc:grpc-netty-shaded'
    implementation 'io.grpc:grpc-protobuf'
    implementation 'io.grpc:grpc-stub'
    implementation 'io.grpc:grpc-api'
    implementation 'com.google.protobuf:protobuf-java'
    implementation 'javax.annotation:javax.annotation-api'
    implementation 'com.salesforce.servicelibs:reactor-grpc-stub:1.2.4'
    implementation 'com.salesforce.servicelibs:reactor-grpc:1.2.4'

    // resilience
    implementation 'io.github.resilience4j:resilience4j-spring-boot3:2.2.0'
    implementation 'io.github.resilience4j:resilience4j-reactor:2.2.0'

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // spring
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

    // test
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
}

sourceSets {
    main {
        proto {
            srcDir 'src/main/proto'
        }
        java {
            srcDirs = ['src/main/java', 'build/generated/source/proto/main/java', 'build/generated/source/proto/main/grpc', 'build/generated/source/proto/main/reactor']
        }
    }
}

protobuf {
    protoc {
        artifact = "com.google.protobuf:protoc:3.25.5"
    }
    plugins {
        grpc {
            artifact = 'io.grpc:protoc-gen-grpc-java:1.68.1'
        }
        reactor {
            artifact = 'com.salesforce.servicelibs:reactor-grpc:1.2.4'
        }
    }
    generateProtoTasks {
        all().forEach { task ->
            task.plugins {
                grpc {}
                reactor {}
            }
        }
    }
}

compileJava.dependsOn(generateProto)

tasks.withType(ProcessResources).configureEach {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

configurations {
    configureEach {
        exclude group: 'org.slf4j', module: 'slf4j-simple'
    }
}

jib {
    container {
        entrypoint = [
                'java', '-cp', '@/app/jib-classpath-file',
                'com.illumio.data.IpClassificationMmdbApplication',
                '--spring.config.location=file:/var/resources/application.yml',
                '--spring.profiles.active=prod'
        ]
        environment = [
                "JAVA_TOOL_OPTIONS"    : "-javaagent:/otel/opentelemetry-javaagent.jar",
                "OTEL_METRICS_EXPORTER": "prometheus",
                "OTEL_TRACES_EXPORTER" : "none",
                "OTEL_LOGS_EXPORTER"   : "none"
        ]
    }
}
