apiVersion: v1
kind: Service
metadata:
  name: {{ include "IpClassificationMmdb.name" . }}
  labels:
    {{- include "IpClassificationMmdb.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "IpClassificationMmdb.selectorLabels" . | nindent 4 }}
