# Local Development Configuration

## Quick Timeout Detection for Local Development

When developing locally and testing Kafka server failures, use the `application-local.yml` profile for faster timeout detection.

### How to Use

1. **Run with local profile:**
   ```bash
   java -jar target/ip-classification-mmdb.jar --spring.profiles.active=local
   ```

2. **Or set environment variable:**
   ```bash
   export SPRING_PROFILES_ACTIVE=local
   ./gradlew bootRun
   ```

3. **Or in IDE:**
   Add VM option: `-Dspring.profiles.active=local`

### Key Differences from Production

| Configuration | Production | Local Development | Benefit |
|---------------|------------|-------------------|---------|
| `sessionTimeoutMs` | 300000 (5 min) | 30000 (30 sec) | Faster broker failure detection |
| `maxPollIntervalMs` | 300000 (5 min) | 60000 (1 min) | Quicker consumer timeout |
| `requestTimeoutMs` | 60000 (1 min) | 10000 (10 sec) | Faster request failure detection |
| `noMessagesInMs` | 360000 (6 min) | 30000 (30 sec) | Quicker recreation trigger |
| `monitorIntervalInMs` | 30000 (30 sec) | 10000 (10 sec) | More frequent health checks |
| `maxRetries` | 5 | 3 | Faster failure after retries |
| `maxBackoff` | 3s | 1s | Quicker retry attempts |

### Testing Kafka Server Failures

1. **Start the application with local profile**
2. **Stop Kafka server:**
   ```bash
   # If using Docker
   docker stop kafka-container-name
   
   # If using local Kafka
   kafka-server-stop.sh
   ```
3. **Observe logs:** You should see faster timeout detection (within 30-60 seconds instead of 5+ minutes)
4. **Restart Kafka server**
5. **Verify recovery:** The consumer should recreate and resume processing

### Consumer Recreation Improvements

The following improvements have been made to handle consumer recreation issues:

1. **Enhanced Consumer Configuration:**
   - Disabled auto-commit for better control
   - Added isolation level for consistent reads
   - Manual commit intervals for better reliability

2. **Improved Recreation Logic:**
   - Increased wait times during consumer disposal (5 seconds + 3 seconds)
   - Better handling of consumer group membership
   - Prevents `MemberIdRequiredException` during recreation

3. **Debug Logging:**
   - Enabled DEBUG level for Kafka and pipeline components
   - Better visibility into connection and recreation issues

### Troubleshooting

If you still see `MemberIdRequiredException` or recreation issues:

1. **Check consumer group status:**
   ```bash
   kafka-consumer-groups.sh --bootstrap-server localhost:19092 --describe --group ip-classification-mmdb-group
   ```

2. **Reset consumer group if needed:**
   ```bash
   kafka-consumer-groups.sh --bootstrap-server localhost:19092 --delete --group ip-classification-mmdb-group
   ```

3. **Monitor logs for:**
   - Consumer group rebalancing
   - Recreation attempts
   - Network connection issues

### Reverting to Production Settings

Simply remove the `--spring.profiles.active=local` parameter or environment variable to use production timeouts.
