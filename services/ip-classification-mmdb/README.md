# `ip-classification-mmdb`

A sink data connector that decorates the flow and extends it with data from the
`bluemmdb` service

# Running locally

## Application

1. Modify `application.yaml`
    1. `kafkaCommonConfig`
    2. `kafkaReceiverConfig`
    3. `kafkaSenderConfig`
    4. `blueMmdbClientConfig`
2. Use `IntelliJ`'s `Run Configuration`:

- `ip-classification-mmdb/IpClassificationMmdb`

## Kafka

Use Ari's [`local-kafka`](https://stash.ilabs.io/users/ari.sweedler/repos/local-kafka/browse) tool

In 4 different panes:

1. Start Kafka: `kaf kafka`
2. Start `bluemmdb`: (port forward or run locally & configure)
3. Start a consumer on the sinkTopip: `kaf ip-classification-mmdb-consume-sink --jq`
4. Produce to the topic: `kaf ip-classification-mmdb-produce`
