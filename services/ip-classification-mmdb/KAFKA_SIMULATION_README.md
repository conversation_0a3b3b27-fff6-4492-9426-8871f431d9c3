# Kafka Simulation for ip-classification-mmdb Service

This directory contains comprehensive Kafka simulation tools for testing consumer timeout, rebalancing, and partition assignment scenarios in the ip-classification-mmdb service.

## 📁 Files Created

### 1. **IpClassificationKafkaSimulator.java**
The main simulation utility that provides:
- **Consumer Timeout Simulation**: Creates consumers with aggressive timeout settings and simulates slow processing
- **Consumer Group Rebalancing**: Demonstrates partition reassignment when consumers join/leave
- **Partition Assignment Logging**: Real-time monitoring of partition assignments and revocations

### 2. **IpClassificationKafkaSimulatorTest.java**
JUnit test class with multiple test scenarios:
- `testConsumerTimeout()` - Consumer timeout scenarios
- `testConsumerGroupRebalance()` - Consumer group rebalancing
- `testPartitionAssignmentLogging()` - Partition assignment monitoring
- `testAllSimulations()` - Comprehensive test suite
- `testWithCustomConfiguration()` - Custom topics/groups

### 3. **KafkaSimulatorDemoTest.java**
Demo test that shows consumer creation logic without requiring Kafka:
- `demonstrateMultipleConsumerCreation()` - Shows how multiple consumers are created
- `demonstrateConsumerGroupConcepts()` - Explains consumer group concepts
- `demonstrateRebalanceScenarios()` - Walks through rebalance scenarios

### 4. **simulate-kafka-scenarios.sh**
Convenient script for setup and execution:
- Kafka connectivity checks
- Topic creation and management
- Test message producer
- Running specific simulations
- Consumer group monitoring

## 🚀 Quick Start

### Option 1: Demo (No Kafka Required)
```bash
# Run the demo to understand multiple consumer creation
./gradlew :services:ip-classification-mmdb:test --tests KafkaSimulatorDemoTest.demonstrateMultipleConsumerCreation

# See consumer group concepts
./gradlew :services:ip-classification-mmdb:test --tests KafkaSimulatorDemoTest.demonstrateConsumerGroupConcepts

# Understand rebalance scenarios
./gradlew :services:ip-classification-mmdb:test --tests KafkaSimulatorDemoTest.demonstrateRebalanceScenarios
```

### Option 2: Full Simulation (Requires Kafka)
```bash
# 1. Set up Kafka topics
./services/ip-classification-mmdb/simulate-kafka-scenarios.sh setup

# 2. Start test message producer
./services/ip-classification-mmdb/simulate-kafka-scenarios.sh producer

# 3. In the test files, remove @Disabled annotations from the tests you want to run

# 4. Run specific simulations
./services/ip-classification-mmdb/simulate-kafka-scenarios.sh test timeout
./services/ip-classification-mmdb/simulate-kafka-scenarios.sh test rebalance
./services/ip-classification-mmdb/simulate-kafka-scenarios.sh test all

# 5. Monitor consumer groups
./services/ip-classification-mmdb/simulate-kafka-scenarios.sh groups

# 6. Cleanup
./services/ip-classification-mmdb/simulate-kafka-scenarios.sh cleanup
```

## 🔧 Multiple Consumer Creation Logic

The key code for creating multiple consumers in the same consumer group is in the `simulateConsumerGroupRebalance()` method:

```java
// Create multiple consumers to form a group
for (int i = 0; i < 3; i++) {
    final int consumerId = i;
    KafkaReceiver<String, String> receiver = createRebalanceAwareReceiver(consumerId);
    
    Disposable consumer = receiver
            .receiveAutoAck()
            .publishOn(Schedulers.boundedElastic())
            .doOnSubscribe(subscription -> {
                log.info("🚀 Consumer {} joined the group", consumerId);
            })
            .flatMap(records -> records)
            .subscribe(/* ... */);
    
    consumers.add(consumer);
}
```

**Key Configuration for Multiple Consumers:**
```java
// Same group ID for all consumers - THIS IS CRITICAL
consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, consumerGroupId);

// Unique client ID for each consumer
consumerProps.put(ConsumerConfig.CLIENT_ID_CONFIG, "consumer-" + consumerId);
```

## 📊 What You'll See

### Consumer Creation Logs:
```
🔧 Creating consumer 0 with configuration:
   - Group ID: ip-classification-mmdb-demo
   - Client ID: consumer-0
   - Bootstrap Servers: localhost:9092
✅ Consumer 0 created successfully

🔧 Creating consumer 1 with configuration:
   - Group ID: ip-classification-mmdb-demo  
   - Client ID: consumer-1
✅ Consumer 1 created successfully
```

### Rebalance Logs (with real Kafka):
```
🎯 Consumer 0 assigned partitions: [decorated-flow-v1-0]
🎯 Consumer 1 assigned partitions: [decorated-flow-v1-1]
🔄 Consumer 1 revoked partitions: [decorated-flow-v1-1]
🎯 Consumer 0 assigned partitions: [decorated-flow-v1-0, decorated-flow-v1-1]
```

### Timeout Simulation Logs:
```
🚀 Consumer instance 1 started
📦 Received batch of 5 records
🔄 Processing record from partition 0 offset 123
⚠️ Processing interrupted
🔄 Consumer error, retrying... Attempt: 1
```

## 🎯 Key Features

1. **Realistic Simulation**: Mimics actual ip-classification-mmdb service behavior
2. **Multiple Consumer Groups**: Demonstrates how consumers join the same group
3. **Partition Assignment Tracking**: Real-time logging of partition assignments
4. **Timeout Scenarios**: Shows what happens when processing exceeds timeouts
5. **Rebalance Triggers**: Demonstrates consumer join/leave scenarios
6. **Easy Setup**: Script handles all Kafka setup and cleanup

## 📚 Consumer Group Concepts

### Consumer Group Basics:
- **Group ID**: All consumers in the group share the same `group.id`
- **Client ID**: Each consumer has a unique `client.id` for identification
- **Partition Assignment**: Kafka automatically assigns partitions to consumers
- **Rebalancing**: When consumers join/leave, partitions are redistributed

### Rebalance Scenarios:
1. **3 Consumers, 3 Partitions**: Each consumer gets 1 partition
2. **Consumer Leaves**: Remaining consumers split the abandoned partitions
3. **New Consumer Joins**: Partitions are redistributed among all consumers
4. **More Consumers than Partitions**: Some consumers remain idle

## 🛠️ Troubleshooting

### Gradle Deprecation Warning
The "Deprecated Gradle features" warning is harmless and doesn't affect test execution. It's a common warning in Gradle 8.x builds.

### Tests Being Skipped
If tests are skipped, ensure you've removed the `@Disabled` annotations from the test methods you want to run.

### Kafka Connection Issues
Ensure Kafka is running on `localhost:9092` before running the full simulations. Use the setup script to verify connectivity.

## 🎉 Success Criteria

After running the simulations, you should understand:
1. ✅ How multiple consumers are created in the same consumer group
2. ✅ How partition assignment works in Kafka consumer groups
3. ✅ What happens during consumer group rebalancing
4. ✅ How consumer timeouts affect message processing
5. ✅ How to monitor and debug Kafka consumer issues

The simulations provide a comprehensive understanding of Kafka consumer group behavior specific to the ip-classification-mmdb service architecture.
