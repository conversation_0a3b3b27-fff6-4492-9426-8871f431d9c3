# Default values for connector.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
logging:
  level:
    root: DEBUG
    kafka: DEBUG
    
ports:
- name: admin
  port: 8084
- name: rest
  port: 8081

servicePorts:
- name: rest
  podPort: rest
  servicePort: 8081

service:
  type: ClusterIP

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

replicaCount: 1

image:
  repositoryBase: illum.azurecr.io/
  repositoryName: cef-log-connector
  tag:      # value given at helm deployment 
  pullPolicy: Always

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 10
  targetCPUUtilizationPercentage: 50
  targetMemoryUtilizationPercentage: 50
  # Only CPU and Memory scaling - no custom metrics requiring Prometheus Adapter  
    


podAnnotations: {}

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"



podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000



resources:
  # Resource requests and limits are required for HPA to work properly
  limits:
    cpu: 2000m
    memory: 6Gi
  requests:
    cpu: 2000m
    memory: 6Gi

nodeSelector: {}

tolerations: []

affinity: {}

ansi:
  enabled: ALWAYS
webApplicationType: none
cefLogConnector:
  logProcessingMode: CEF_JSON_MODE # CEF_FLOW_MODE is for BLUE pipeline CEF to KUSTO FLOW and CEF_JSON_MODE is for CEF to CEF JSON
  kafkaConsumerConfig:
    bootstrapServers: eventhubs-connector.servicebus.windows.net:9093
    isConnectionString: false
    isManagedIdentity: true
    topic: illumio-cef-log-connector
    groupId: connector-group
    requestTimeoutMs: 300000
    maxPollRecords: 100
    maxPartitionFetchBytes: "1048576"
    autoOffsetReset: latest
    backPressureEvents: 100000
    prefetchCount: 30
    concurrency: 10
    loadBalancingStrategy: BALANCED
  kafkaProducerConfig:
    bootstrapServers: eventhubs-connector.servicebus.windows.net:9093
    isConnectionString: false
    isManagedIdentity: true
    kafkaFlowTopic: illumio-commonsecurity-flow
    kafkaCefJsonTopic: cef-json-v1
    requestTimeoutMs: 300000
    deliveryTimeoutMs: 300000
    lingerMs: 0
    batchSize: "1048576"
    bufferMemory: "********"
    maxBlockMs: "300000"
    senderMaxAttempts: "10"
    senderBackoff: "5s"
    maxInFlight: 1024
  storageAccountConfig:
    endpoint: https://inglogpoc.blob.core.windows.net/
    containerName: john-test
    isConnectionString: false
    isManagedIdentity: true
  tenantConfig:
    tenantId: "83f1b531-864b-4c02-9124-9de1645017c1"
  samplerConfig:
    isEnabled: "false"
    batchSampleSize: "1.0"

eventhub:
  password: # should give at deployment time

extraLabels: {}

jvmOptions: "-Xms4g -Xmx4g -XX:+CrashOnOutOfMemoryError"