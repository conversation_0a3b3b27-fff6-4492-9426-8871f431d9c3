apiVersion: v1
kind: Secret
metadata:
  name: {{ include "CefLogConnector.fullname" . }}-env-secrets
  labels:
    {{- include "CefLogConnector.labels" . | nindent 4 }}
type: Opaque
stringData:
  CEFLOGCONNECTOR_KAFKAPRODUCERCONFIG_SASLJAASCONFIG: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.eventhub.password }}";
  CEFLOGCONNECTOR_KAFKACONSUMERCONFIG_SASLJAASCONFIG: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.eventhub.password }}";