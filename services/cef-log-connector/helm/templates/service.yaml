apiVersion: v1
kind: Service
metadata:
  name: {{ include "CefLogConnector.name" . }}
  labels:
    {{- include "CefLogConnector.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "CefLogConnector.selectorLabels" . | nindent 4 }}
