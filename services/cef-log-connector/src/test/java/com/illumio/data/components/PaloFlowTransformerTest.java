package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.CommonSecurityLog;
import com.illumio.data.pojo.Flow;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class PaloFlowTransformerTest {
    private PaloFlowTransformer paloFlowTransformer;

    @BeforeEach
    public void setUp() {
        ObjectMapper objectMapper = new ObjectMapper();
        paloFlowTransformer = new PaloFlowTransformer(objectMapper);
    }

    @Test
    public void testAsKey() {
        Flow flow = Flow.builder()
                .SourceIP("***********")
                .DestinationIP("***********")
                .Protocol("tcp")
                .DestinationPort(8081)
                .build();
        String actual = paloFlowTransformer.asKey(flow);
        assertEquals("***********,***********,tcp,8081", actual);
    }

    @Test
    public void testTransformInsideToOutside() {
        CommonSecurityLog commonSecurityLog = CommonSecurityLog.builder()
                .DeviceCustomString4("C2I_INSIDE")
                .DeviceCustomString5("C2I_OUTSIDE")
                .build();

        Flow flow = paloFlowTransformer.transform(commonSecurityLog).block(Duration.ofSeconds(30));

        assertEquals("dc", flow.getSrcFlowType());
    }

    @Test
    public void testTransformOutsideToInside() {
        CommonSecurityLog commonSecurityLog = CommonSecurityLog.builder()
                .DeviceCustomString4("C2I_OUTSIDE")
                .DeviceCustomString5("C2I_INSIDE")
                .build();

        Flow flow = paloFlowTransformer.transform(commonSecurityLog).block(Duration.ofSeconds(30));

        assertEquals("dc", flow.getDestFlowType());
    }
}
