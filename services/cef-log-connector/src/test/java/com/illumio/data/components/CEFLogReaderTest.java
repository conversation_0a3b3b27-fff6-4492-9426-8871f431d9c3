package com.illumio.data.components;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.github.jcustenborder.cef.CEFParser;
import com.github.jcustenborder.cef.CEFParserFactory;
import com.illumio.data.configuration.CEFLogConnectorConfig;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import reactor.test.StepVerifier;

import java.time.Year;
import java.time.ZoneOffset;
import java.util.HashMap;

class CEFLogReaderTest {
    CEFParser cefParser;
    CEFLogConnectorConfig cefLogConnectorConfig;
    ObjectMapper objectMapper;
    CEFLogReader cefLogReader;
    int currentYear;

    @BeforeEach
    void init() {
        currentYear = Year.now().getValue();
        cefParser = CEFParserFactory.create();
        cefLogConnectorConfig = new CEFLogConnectorConfig();
        objectMapper = new ObjectMapper();
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        cefLogReader = new CEFLogReader(cefParser);
    }

    @Test
    void testPaloHappyPath() {
        String test =
                "<14>Jun 26 00:00:17 ************ CEF:0|Palo Alto Networks|PAN-OS|10.1.10-h2|end|TRAFFIC|1|rt=Jun 26 2024 00:00:17 GMT deviceExternalId=013101000880 src=************* dst=************* sourceTranslatedAddress=************* destinationTranslatedAddress=************* cs1Label=Rule cs1=allow-CSE_WEB_PROFILE suser= duser= app=incomplete cs3Label=Virtual System cs3=vsys2 cs4Label=Source Zone cs4=C2I_INSIDE cs5Label=Destination Zone cs5=C2I_OUTSIDE deviceInboundInterface=ae2.11 deviceOutboundInterface=ae1.11 cs6Label=LogProfile cs6=GLOBAL_LOG_FORWARDING_PROFILE cn1Label=SessionID cn1=4158557 cnt=1 spt=48918 dpt=443 sourceTranslatedPort=61403 destinationTranslatedPort=443 flexString1Label=Flags flexString1=0x400019 proto=tcp act=allow flexNumber1Label=Total bytes flexNumber1=128 in=128 out=0 cn2Label=Packets cn2=2 PanOSPacketsReceived=0 PanOSPacketsSent=2 start=Jun 26 2024 00:00:17 GMT cn3Label=Elapsed time in seconds cn3=3 cs2Label=URL Category cs2=any externalId=7345991053765967220 reason=aged-out PanOSDGl1=392 PanOSDGl2=178 PanOSDGl3=83 PanOSDGl4=93 PanOSVsysName=C2I dvchost=DEETALAKAP52E1 cat=from-policy PanOSActionFlags=0x8000000000000000 PanOSSrcUUID= PanOSDstUUID= PanOSTunnelID=0 PanOSMonitorTag= PanOSParentSessionID=0 PanOSParentStartTime= PanOSTunnelType=N/A c6a2=************* c6a3=*************";
        StepVerifier.create(cefLogReader.transformToCefMessage(test))
                .expectSubscription()
                .assertNext(
                        message -> {
                            assertEquals("PAN-OS", message.deviceProduct());
                            assertEquals("Palo Alto Networks", message.deviceVendor());
                        })
                .verifyComplete();
    }

    @Test
    void testPaloDateTimeVariation() {
        String test =
                "<14>Dec 4 1:00:17 ************ CEF:0|Palo Alto Networks|PAN-OS|10.1.10-h2|end|TRAFFIC|1|rt=Dec 4 2024 1:00:17 GMT deviceExternalId=013101000880 src=************* dst=************* sourceTranslatedAddress=************* destinationTranslatedAddress=************* cs1Label=Rule cs1=allow-CSE_WEB_PROFILE suser= duser= app=incomplete cs3Label=Virtual System cs3=vsys2 cs4Label=Source Zone cs4=C2I_INSIDE cs5Label=Destination Zone cs5=C2I_OUTSIDE deviceInboundInterface=ae2.11 deviceOutboundInterface=ae1.11 cs6Label=LogProfile cs6=GLOBAL_LOG_FORWARDING_PROFILE cn1Label=SessionID cn1=4158557 cnt=1 spt=48918 dpt=443 sourceTranslatedPort=61403 destinationTranslatedPort=443 flexString1Label=Flags flexString1=0x400019 proto=tcp act=allow flexNumber1Label=Total bytes flexNumber1=128 in=128 out=0 cn2Label=Packets cn2=2 PanOSPacketsReceived=0 PanOSPacketsSent=2 start=Jun 26 2024 00:00:17 GMT cn3Label=Elapsed time in seconds cn3=3 cs2Label=URL Category cs2=any externalId=7345991053765967220 reason=aged-out PanOSDGl1=392 PanOSDGl2=178 PanOSDGl3=83 PanOSDGl4=93 PanOSVsysName=C2I dvchost=DEETALAKAP52E1 cat=from-policy PanOSActionFlags=0x8000000000000000 PanOSSrcUUID= PanOSDstUUID= PanOSTunnelID=0 PanOSMonitorTag= PanOSParentSessionID=0 PanOSParentStartTime= PanOSTunnelType=N/A c6a2=************* c6a3=*************";
        StepVerifier.create(cefLogReader.transformToCefMessage(test))
                .expectSubscription()
                .assertNext(
                        message -> {
                            assertEquals("PAN-OS", message.deviceProduct());
                            assertEquals("Palo Alto Networks", message.deviceVendor());
                        })
                .verifyComplete();
    }

    @Test
    void testCheckPoint_VPN_1() {
        var headers = new HashMap<String, Object>();
        String test =
                "2025-03-31T11:53:10.368290+00:00 ec2-18-214-102-15.compute-1.amazonaws.com CEF: 0|Check Point|VPN-1 & FireWall-1|Check Point|Accept|https|Unknown|act=Accept cn1Label=Elapsed Time in Seconds cn1=15 deviceDirection=0 deviceInboundInterface=eth0 deviceOutboundInterface=eth0 in=52 out=260 rt=1743421964000 spt=8888 dpt=443 start=1743421964000 cs2Label=Rule Name layer_name=Network layer_uuid=38271c2f-ab44-4e25-9aa4-e219cb6e12cf match_id=1 parent_rule=0 rule_action=Accept rule_uid=f9a7f9e6-276b-4f9e-a210-1c583cafddd3 conn_direction=Internal ifname=eth0 logid=6 loguid={0x9f7ed583,0xe153a953,0xf0a5aa92,0xefdd2253} origin=********** originsicname=cn\\=cp_mgmt,o\\=cp-standalone-aws..v386xa sequencenum=3 version=5 __nsons=0 __p_dport=0 __pos=7 bytes=312 c_bytes=1 client_inbound_packets=1 client_outbound_packets=5 context_num=4294967295 dst=********** inzone=Internal lastupdatetime=1743421989 outzone=Local packets=6 product=VPN-1 & FireWall-1 proto=6 segment_time=1743421964 server_inbound_bytes=0 server_inbound_packets=0 server_outbound_bytes=0 server_outbound_packets=0 service_id=https src=*************";
        StepVerifier.create(cefLogReader.transformToCefMessage(test))
                .expectSubscription()
                .assertNext(
                        message -> {
                            assertEquals("VPN-1 & FireWall-1", message.deviceProduct());
                            assertEquals("Check Point", message.deviceVendor());
                        })
                .verifyComplete();
    }

    @Test
    void testCheckPoint_VPN_2() {
        String test =
                "2025-03-30T12:59:58.369381+00:00 ec2-18-214-102-15.compute-1.amazonaws.com CEF: 0|Check Point|VPN-1 & FireWall-1|Check Point|Accept|https|Unknown|act=Accept deviceDirection=0 rt=1743339597000 spt=8888 dpt=443 cs2Label=Rule Name layer_name=Network layer_uuid=38271c2f-ab44-4e25-9aa4-e219cb6e12cf match_id=1 parent_rule=0 rule_action=Accept rule_uid=f9a7f9e6-276b-4f9e-a210-1c583cafddd3 conn_direction=Internal ifname=eth0 logid=0 loguid={0xb6a7df18,0xf808804b,0xc4bc135b,0xb9cac6bf} origin=********** originsicname=cn\\=cp_mgmt,o\\=cp-standalone-aws..v386xa sequencenum=15 version=5 dst=********** inzone=Internal outzone=Local product=VPN-1 & FireWall-1 proto=6 service_id=https src=***************";
        StepVerifier.create(cefLogReader.transformToCefMessage(test))
                .expectSubscription()
                .assertNext(
                        message -> {
                            assertEquals("VPN-1 & FireWall-1", message.deviceProduct());
                            assertEquals("Check Point", message.deviceVendor());
                        })
                .verifyComplete();
    }

    @Test
    void testCheckPoint_WebAPI() {
        String test =
                "2025-03-18T01:25:01.174092+00:00 ec2-18-214-102-15.compute-1.amazonaws.com CEF: 0|Check Point|WEB_API|Check Point|Accept|Log|Unknown|act=Accept deviceDirection=1 rt=1742261099000 src=********** loguid={0x67d8cb6d,0x0,0x1b0a000a,0x1916321f} origin=********** originsicname=cn\\=cp_mgmt,o\\=cp-standalone-aws..v386xa sequencenum=2 version=5 additional_info=login by localhost administrator=WEB_API operation=Log In product=WEB_API sendtotrackerasadvancedauditlog=0 subject=Administrator Login\n";
        StepVerifier.create(cefLogReader.transformToCefMessage(test))
                .expectSubscription()
                .assertNext(
                        message -> {
                            assertEquals("WEB_API", message.deviceProduct());
                            assertEquals("Check Point", message.deviceVendor());
                        })
                .verifyComplete();
    }
}
