package com.illumio.data.components.filter;

import com.illumio.data.CommonSecurityLog;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class TrafficFilterTest {
    @Test
    void testNull() {
        TrafficFilter trafficFilter = new TrafficFilter();
        assertFalse(trafficFilter.test(CommonSecurityLog.builder()
                .Activity(null).build()));
    }

    @Test
    void testThreat() {
        TrafficFilter trafficFilter = new TrafficFilter();
        assertFalse(trafficFilter.test(CommonSecurityLog.builder()
                .Activity("THREAT").build()));
    }

    @Test
    void testTraffic() {
        TrafficFilter trafficFilter = new TrafficFilter();
        assertTrue(trafficFilter.test(CommonSecurityLog.builder()
                .Activity("TRAFFIC").build()));
    }
}
