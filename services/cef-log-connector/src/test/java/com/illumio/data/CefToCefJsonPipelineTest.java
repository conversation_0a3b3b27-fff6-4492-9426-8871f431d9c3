package com.illumio.data;

import com.azure.messaging.eventhubs.EventData;
import com.azure.messaging.eventhubs.models.PartitionContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.jcustenborder.cef.Message;
import org.apache.qpid.proton.amqp.Binary;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.SenderRecord;
import reactor.test.StepVerifier;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

class CefToCefJsonPipelineTest {

    private CefToCefJsonPipeline pipeline;
    
    @Mock
    private ObjectMapper objectMapper;
    
    @Mock
    private PartitionContext partitionContext;
    
    @Mock
    private EventData eventData;
    
    @Mock
    private Message cefMessage;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        pipeline = new CefToCefJsonPipeline(objectMapper);
    }

    @Test
    void testPipeline() throws Exception {
        // Setup
        String kafkaTopic = "test-topic";
        String jsonOutput = "{\"test\":\"value\"}";
        String deviceVendor = "Test Vendor";
        Map<String, String> extensions = new HashMap<>();
        Map<String, Object> properties = new HashMap<>();

        // Mock behavior
        when(cefMessage.deviceVendor()).thenReturn(deviceVendor);
        when(cefMessage.extensions()).thenReturn(extensions);
        when(eventData.getProperties()).thenReturn(properties);
        when(objectMapper.writeValueAsString(any())).thenReturn(jsonOutput);

        // Execute
        Mono<SenderRecord<String, String, String>> result = pipeline.pipeline(
                partitionContext, eventData, Mono.just(cefMessage), kafkaTopic);

        // Verify
        StepVerifier.create(result)
                .assertNext(senderRecord -> {
                    // SenderRecord doesn't have producerRecord() method, we need to verify differently
                    // The actual ProducerRecord is internal to SenderRecord implementation
                    assertNotNull(senderRecord);
                    // We can verify the correlation metadata which should be the eventDataString
                    assertNotNull(senderRecord.correlationMetadata());
                })
                .verifyComplete();
    }

    @Test
    void testDecorateCefMessageWithTenantId() throws Exception {
        // Setup
        String tenantId = "test-tenant-123";
        byte[] tenantIdBytes = tenantId.getBytes(StandardCharsets.UTF_8);
        Binary binary = new Binary(tenantIdBytes);
        Map<String, Object> properties = new HashMap<>();
        properties.put("IllumioTenantId", binary);
        Map<String, String> extensions = new HashMap<>();

        // Mock behavior - need to mock all methods called by the pipeline
        when(cefMessage.extensions()).thenReturn(extensions);
        when(cefMessage.deviceVendor()).thenReturn("Test Vendor"); // Required for header creation
        when(cefMessage.cefVersion()).thenReturn(0);
        when(cefMessage.deviceProduct()).thenReturn("Test Product");
        when(cefMessage.deviceVersion()).thenReturn("1.0");
        when(cefMessage.deviceEventClassId()).thenReturn("100");
        when(cefMessage.name()).thenReturn("Test Event");
        when(cefMessage.severity()).thenReturn("5");
        when(eventData.getProperties()).thenReturn(properties);
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");

        // Execute and verify
        StepVerifier.create(pipeline.pipeline(partitionContext, eventData, Mono.just(cefMessage), "topic"))
                .assertNext(senderRecord -> {
                    assertNotNull(senderRecord);
                    assertNotNull(senderRecord.correlationMetadata());
                })
                .verifyComplete();

        // Verify that extensions() was called to add tenant ID (called multiple times during pipeline)
        verify(cefMessage, atLeastOnce()).extensions();
    }

    @Test
    void testCreateHeaderValue() throws Exception {
        // Setup - the header value is now a constant "cef-firewall-data"
        // Mock behavior - deviceVendor is still called for JSON serialization but not for header
        when(cefMessage.deviceVendor()).thenReturn("Any Vendor");
        when(cefMessage.extensions()).thenReturn(new HashMap<>());
        when(eventData.getProperties()).thenReturn(new HashMap<>());
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");

        // Execute
        Mono<SenderRecord<String, String, String>> result = pipeline.pipeline(
                partitionContext, eventData, Mono.just(cefMessage), "topic");

        // Verify
        StepVerifier.create(result)
                .assertNext(senderRecord -> {
                    // The header value is now constant and doesn't depend on deviceVendor
                    // We verify that the pipeline completed successfully
                    assertNotNull(senderRecord);
                    assertNotNull(senderRecord.correlationMetadata());
                })
                .verifyComplete();

        // Verify that deviceVendor was still called (for JSON serialization)
        verify(cefMessage, atLeastOnce()).deviceVendor();
    }

    @Test
    void testConvertToSerializableMap() throws Exception {
        // Setup
        String deviceVendor = "Test Vendor";
        String deviceProduct = "Test Product";
        String deviceVersion = "1.0";
        String deviceEventClassId = "100";
        String name = "Test Event";
        String severity = "5";

        Map<String, String> extensions = new HashMap<>();
        extensions.put("src", "***********");
        extensions.put("dst", "***********");

        // Mock behavior
        when(cefMessage.cefVersion()).thenReturn(0); // Returns Integer, not String
        when(cefMessage.deviceVendor()).thenReturn(deviceVendor);
        when(cefMessage.deviceProduct()).thenReturn(deviceProduct);
        when(cefMessage.deviceVersion()).thenReturn(deviceVersion);
        when(cefMessage.deviceEventClassId()).thenReturn(deviceEventClassId);
        when(cefMessage.name()).thenReturn(name);
        when(cefMessage.severity()).thenReturn(severity);
        when(cefMessage.extensions()).thenReturn(extensions);
        when(eventData.getProperties()).thenReturn(new HashMap<>());
        when(objectMapper.writeValueAsString(any())).thenReturn("{}");

        // Execute and verify
        StepVerifier.create(pipeline.pipeline(partitionContext, eventData, Mono.just(cefMessage), "topic"))
                .assertNext(senderRecord -> {
                    assertNotNull(senderRecord);
                    assertNotNull(senderRecord.correlationMetadata());
                })
                .verifyComplete();

        // Verify the map structure passed to objectMapper
        verify(objectMapper).writeValueAsString(argThat(map -> {
            Map<String, Object> serializableMap = (Map<String, Object>) map;
            return Integer.valueOf(0).equals(serializableMap.get("version")) &&
                   deviceVendor.equals(serializableMap.get("deviceVendor")) &&
                   deviceProduct.equals(serializableMap.get("deviceProduct")) &&
                   deviceVersion.equals(serializableMap.get("deviceVersion")) &&
                   deviceEventClassId.equals(serializableMap.get("deviceEventClassId")) &&
                   name.equals(serializableMap.get("name")) &&
                   severity.equals(serializableMap.get("severity")) &&
                   serializableMap.containsKey("extensions");
        }));
    }
}