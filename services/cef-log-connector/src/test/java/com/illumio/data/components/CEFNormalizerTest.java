package com.illumio.data.components;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class CEFNormalizerTest {

    @Test
    void handlePrefix() {
        String test =
                "<14>Jun 26 00:00:17 10.164.49.13 CEF:0|Palo Alto Networks|PAN-OS|10.1.10-h2|end|TRAFFIC|1|rt=Jun 26 2024 00:00:17 GMT deviceExternalId=013101000880 src=10.164.164.31";
        String expected =
                "Jun 26 00:00:17 10.164.49.13 CEF:0|Palo Alto Networks|PAN-OS|10.1.10-h2|end|TRAFFIC|1|rt=Jun 26 2024 00:00:17 GMT deviceExternalId=013101000880 src=10.164.164.31";
        String actual = CEFNormalizer.handlePrefix(test);
        assertEquals(expected, actual);
    }

    @Test
    void handleSpacesInCefVersion() {
        String test = "2025-03-31T11:53:10.368290+00:00 ec2-18-214-102-15.compute-1.amazonaws.com CEF: 0|Check Point|VPN-1 & FireWall-1|Check Point|Accept|https|Unknown|act=Accept cn1Label=Elapsed Time in Seconds cn1=15";
        String expected = "2025-03-31T11:53:10.368290+00:00 ec2-18-214-102-15.compute-1.amazonaws.com CEF:0|Check Point|VPN-1 & FireWall-1|Check Point|Accept|https|Unknown|act=Accept cn1Label=Elapsed Time in Seconds cn1=15";
        String actual = CEFNormalizer.handleSpacesInCefVersion(test);
        assertEquals(expected, actual);
    }

    @Test
    void handleCheckpointTimestampFormat() {
        String test = "2025-03-31T11:53:10.368290+00:00 ec2-18-214-102-15.compute-1.amazonaws.com CEF:0|Check Point|VPN-1 & FireWall-1|Check Point|Accept|https|Unknown|act=Accept cn1Label=Elapsed Time in Seconds cn1=15";
        String expected = "Mar 31 2025 11:53:10.368 ec2-18-214-102-15.compute-1.amazonaws.com CEF:0|Check Point|VPN-1 & FireWall-1|Check Point|Accept|https|Unknown|act=Accept cn1Label=Elapsed Time in Seconds cn1=15";
        String actual = CEFNormalizer.handleCheckpointTimestampFormat(test);
        assertEquals(expected, actual);
    }
}
