package com.illumio.data.components.filter;

import com.illumio.data.CommonSecurityLog;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class DCRFilterTest {
    DCRFilter dcrFilter;

    @BeforeEach
    void init() {
        dcrFilter = new DCRFilter();
    }

    @Test
    void testFirstClauseFilters() {
        assertFalse(
                dcrFilter.test(
                        CommonSecurityLog.builder()
                                .DestinationPort(500)
                                .DeviceAction("deny")
                                .build()));
    }

    @Test
    void testSecondClauseFilters() {
        assertFalse(
                dcrFilter.test(
                        CommonSecurityLog.builder()
                                .Activity("TRAFFIC")
                                .DestinationPort(53)
                                .DestinationIP("***********")
                                .build()));
    }

    @Test
    void testNoFiltering() {
        assertTrue(
                dcrFilter.test(
                        CommonSecurityLog.builder()
                                .Activity("THREAT")
                                .DestinationPort(53)
                                .DestinationIP("***********")
                                .build()));

        assertTrue(
                dcrFilter.test(
                        CommonSecurityLog.builder()
                                .Activity(null)
                                .DestinationPort(53)
                                .DestinationIP("***********")
                                .build()));

        assertTrue(
                dcrFilter.test(
                        CommonSecurityLog.builder()
                                .DestinationPort(501)
                                .DeviceAction("deny")
                                .build()));

        assertTrue(
                dcrFilter.test(
                        CommonSecurityLog.builder()
                                .DestinationPort(500)
                                .DeviceAction(null)
                                .build()));
    }
}
