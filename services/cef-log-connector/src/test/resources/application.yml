logging:
  level:
    ROOT: INFO

spring:
  application:
    name: cef-log-connector
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

cef-log-connector:
  kafka-common-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    isSasl: true
    saslJaasConfig: _DO_NOT_COMMIT_

  kafka-consumer-config:
    bootstrapServers: arch-eventhub.servicebus.windows.net
    topic: test-flow-log-event
    groupId: test-consumer-cef-flow
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880
    backPressureEvents: 100000
    prefetchCount: 30
    concurrency: 10

  kafka-producer-config:
    bootstrapServers: arch-eventhub.servicebus.windows.net:9093
    flows-sink-topic: illumio-commonsecurity-flow

  storage-account-config:
    endpoint: https://inglogpoc.blob.core.windows.net/
    container-name: john-test
    is-connection-string: false
    is-managed-identity: true