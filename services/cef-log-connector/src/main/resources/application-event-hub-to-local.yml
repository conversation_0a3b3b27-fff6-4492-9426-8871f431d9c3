logging:
  level:
    ROOT: DEBUG
    org:
      apache:
        kafka: INFO
#    com:
#      azure:
#        messaging:
#          eventhubs: INFO
#    reactor:
#      netty: INFO
spring:
  application:
    name: cef-log-connector
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

cef-log-connector:
  log-processing-mode: CEF_JSON_MODE

  kafka-consumer-config:
    bootstrapServers: arch-eventhub.servicebus.windows.net:9093
    isConnectionString: true
#    isManagedIdentity: true
    topic: cef-logs
    groupId: cef-group
    auto-offset-reset: latest
#    requestTimeoutMs: 300000
    maxPollRecords: 500
#    maxPartitionFetchBytes: "1048576" # 1 MB
    commitFrequency: 10s
    backPressureEvents: 100000
    prefetchCount: 500
    concurrency: 10
    loadBalancingStrategy: GREEDY

  kafka-producer-config:
    bootstrapServers: localhost:9092
    isConnectionString: false
    isManagedIdentity: false
    kafkaFlowtopic: raw-flow-v1
    kafkaCefJsonTopic: cef-json-v1
    requestTimeoutMs: 300000
    deliveryTimeoutMs: 300000
#    Non-Defaults below
#    lingerMs: 1500
    batchSize: 1048576
#    bufferMemory: "********" # 32 MB

  kafka-cef-json-producer-config:
    bootstrapServers: localhost:9092
    isConnectionString: false
    isManagedIdentity: false
    topic: cef-json-v1
    requestTimeoutMs: 300000
    deliveryTimeoutMs: 300000
    #    Non-Defaults below
    #    lingerMs: 1500
    batchSize: 1048576
  #    bufferMemory: "********" # 32 MB

  storage-account-config:
    endpoint: https://inglogpoc.blob.core.windows.net/
    container-name: john-test
    is-connection-string: true
#    is-connection-string: false
    is-managed-identity: false
#    is-managed-identity: true

  tenantConfig:
    tenantId: 83f1b531-864b-4c02-9124-9de1645017c1

  samplerConfig:
    isEnabled: false
    batchSampleSize: "0.5"