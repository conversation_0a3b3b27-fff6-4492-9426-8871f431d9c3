logging:
  level:
    ROOT: INFO

spring:
  application:
    name: cef-log-connector
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

cef-log-connector:
  log-processing-mode: CEF_JSON_MODE

  kafka-common-config:
    bootstrapServers: test-arch-eventhub.servicebus.windows.net:9093
    isSasl: true
    saslJaasConfig: _DO_NOT_COMMIT_

  kafka-consumer-config:
    topic: test-flow-log-event
    groupId: cef-log-connector
    autoOffsetReset: latest
    requestTimeoutMs: 180000
    maxPollRecords: 20000
    maxPartitionFetchBytes: 5242880
    backPressureEvents: 100000
    prefetchCount: 30
    concurrency: 10

  kafka-producer-config:
    kafka-flow-topic: test-cef-flow
    kafka-cef-json-topic: test-cef-json
