logging:
  level:
    ROOT: DEBUG
    org:
      apache:
        kafka: INFO
spring:
  application:
    name: cef-log-connector
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

cef-log-connector:
  log-processing-mode: CEF_FLOW_MODE

  kafka-consumer-config:
    bootstrapServers: localhost:9092
    isConnectionString: false
    isManagedIdentity: false
    topic: cef-logs
    groupId: cef-log-connector
    auto-offset-reset: latest
#    requestTimeoutMs: 300000
#    maxPollRecords: 100
#    maxPartitionFetchBytes: "1048576" # 1 MB

  kafka-producer-config:
    bootstrapServers: localhost:9092
    isConnectionString: false
    isManagedIdentity: false
    kafka-flow-topic: illumio-commonsecurity-flow
    kafka-cef-json-topic: cef-json-v1
    requestTimeoutMs: 300000
    deliveryTimeoutMs: 300000
#    Non-Defaults below
#    lingerMs: 1500
#    batchSize: 300000
#    bufferMemory: "33554432" # 32 MB

