logging:
  level:
    ROOT: TRACE
    org:
      apache:
        kafka: DEBUG
spring:
  application:
    name: cef-log-connector
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

cef-log-connector:
  log-processing-mode: CEF_FLOW_MODE

  kafka-consumer-config:
    bootstrapServers: arch-eventhub.servicebus.windows.net:9093
    isManagedIdentity: true
    topic: cef-logs
    groupId: cef-log-connector
    auto-offset-reset: latest
#    requestTimeoutMs: 300000
#    maxPollRecords: 100
#    maxPartitionFetchBytes: "1048576" # 1 MB
    backPressureEvents: 100000
    prefetchCount: 500
    concurrency: 10
    loadBalancingStrategy: BALANCED

  kafka-producer-config:
    bootstrapServers: arch-eventhub.servicebus.windows.net:9093
#    isConnectionString: true
    isManagedIdentity: true
    kafka-flow-topic: illumio-commonsecurity-flow
    kafka-cef-json-topic: cef-json-v1
    requestTimeoutMs: 300000
    deliveryTimeoutMs: 300000
#    Non-Defaults below
#    lingerMs: 1500
#    batchSize: 300000
#    bufferMemory: "********" # 32 MB

  storage-account-config:
    endpoint: https://inglogpoc.blob.core.windows.net/
    container-name: john-test
    is-connection-string: true
    #    is-connection-string: false
    is-managed-identity: false
#    is-managed-identity: true