package com.illumio.data;

import com.azure.core.amqp.AmqpRetryMode;
import com.azure.core.amqp.AmqpRetryOptions;
import com.azure.identity.DefaultAzureCredentialBuilder;
import com.azure.messaging.eventhubs.EventData;
import com.azure.messaging.eventhubs.EventProcessorClient;
import com.azure.messaging.eventhubs.EventProcessorClientBuilder;
import com.azure.messaging.eventhubs.checkpointstore.blob.BlobCheckpointStore;
import com.azure.messaging.eventhubs.models.EventBatchContext;
import com.azure.messaging.eventhubs.models.PartitionContext;
import com.azure.storage.blob.BlobContainerAsyncClient;
import com.illumio.data.components.CEFLogReader;
import com.illumio.data.components.UtilityFunctions;
import com.illumio.data.components.strategy.LogProcessingStrategy;
import com.illumio.data.configuration.CEFLogConnectorConfig;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
@RequiredArgsConstructor
public class CEFLogPipeline {
    private final CEFLogReader cefLogReader;
    private final CEFLogConnectorConfig cefLogConnectorConfig;
    private final BlobContainerAsyncClient blobContainerAsyncClient;
    private final LogProcessingStrategy logProcessingStrategy;


    private EventProcessorClient eventProcessorClient;

    public void start() {
        EventProcessorClient client = buildEventProcessorClient();
        this.eventProcessorClient = client;
        client.start();
    }

    public void stop() {
        log.debug("Stopping EventProcessorClient");
        if (this.eventProcessorClient != null) {
            this.eventProcessorClient.stop();
        }
    }

    private EventProcessorClient buildEventProcessorClient() {
        EventProcessorClientBuilder builder = new EventProcessorClientBuilder()
                .retryOptions(buildRetryOptions())
                .prefetchCount(cefLogConnectorConfig.getKafkaConsumerConfig().getPrefetchCount())
                .loadBalancingStrategy(cefLogConnectorConfig.getKafkaConsumerConfig().getLoadBalancingStrategy())
                .consumerGroup(cefLogConnectorConfig.getKafkaConsumerConfig().getGroupId())
                .checkpointStore(new BlobCheckpointStore(blobContainerAsyncClient))
                .processEventBatch(
                        this::processBatch,
                        cefLogConnectorConfig.getKafkaConsumerConfig().getMaxPollRecords(),
                        Duration.ofMillis(cefLogConnectorConfig.getKafkaConsumerConfig().getRequestTimeoutMs())
                )
                .processError(errorContext ->
                        log.error("Error processing record {}",
                                UtilityFunctions.partitionContextString(errorContext.getPartitionContext()),
                                errorContext.getThrowable())
                );

        configureConnection(builder);

        return builder.buildEventProcessorClient();
    }

    private AmqpRetryOptions buildRetryOptions() {
        return new AmqpRetryOptions()
                .setTryTimeout(cefLogConnectorConfig.getRetryConfig().getTryTimeoutSeconds())
                .setDelay(cefLogConnectorConfig.getRetryConfig().getDelaySeconds())
                .setMaxRetries(cefLogConnectorConfig.getRetryConfig().getMaxRetries())
                .setMode(AmqpRetryMode.EXPONENTIAL);
    }

    private void configureConnection(EventProcessorClientBuilder builder) {
        if (isConnectionStringMode()) {
            builder.connectionString(
                    cefLogConnectorConfig.getKafkaConsumerConfig().getSaslJaasConfig(),
                    cefLogConnectorConfig.getKafkaConsumerConfig().getTopic()
            );
        } else {
            builder.credential(new DefaultAzureCredentialBuilder().build())
                    .eventHubName(cefLogConnectorConfig.getKafkaConsumerConfig().getTopic())
                    .fullyQualifiedNamespace(getNamespace());
        }
    }

    private boolean isConnectionStringMode() {
        return Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaConsumerConfig)
                .map(CEFLogConnectorConfig.KafkaConsumerConfig::getIsConnectionString)
                .orElse(Boolean.FALSE);
    }

    private String getNamespace() {
        String bootstrapServers = cefLogConnectorConfig.getKafkaConsumerConfig().getBootstrapServers();
        if (bootstrapServers.contains(":")) {
            String namespace = bootstrapServers.split(":")[0];
            log.debug("Extracted namespace: {}", namespace);
            return namespace;
        }
        return bootstrapServers;
    }

    private void processBatch(EventBatchContext eventBatchContext) {
        if (eventBatchContext.getEvents().isEmpty()) {
            return;
        }

        List<EventData> events = getSampledEvents(eventBatchContext.getEvents());

        events.forEach(eventData ->
                processEvent(eventBatchContext.getPartitionContext(), eventData)
        );

        updateCheckpoint(eventBatchContext);
    }

    private List<EventData> getSampledEvents(List<EventData> events) {
        if (Boolean.FALSE.equals(cefLogConnectorConfig.getSamplerConfig().getIsEnabled())) {
            return events;
        }

        int sampleSize = calculateSampleSize(events.size());
        log.debug("Sampling {} events from batch of {}", sampleSize, events.size());
        return events.subList(0, Math.min(sampleSize, events.size()));
    }

    private int calculateSampleSize(int batchSize) {
        try {
            double sampleRate = cefLogConnectorConfig.getSamplerConfig().getBatchSampleSize();
            return (int) Math.ceil(sampleRate * batchSize);
        } catch (Exception e) {
            log.error("Error calculating sample size, using full batch", e);
            return batchSize;
        }
    }

    private void processEvent(PartitionContext partitionContext, EventData eventData) {
        String eventBody = eventData.getBodyAsString();

        log.debug("Processing event: {} {}",
                UtilityFunctions.eventDataString(partitionContext, eventData),
                eventBody);

        cefLogReader.transformToCefMessage(eventBody)
                .publishOn(Schedulers.parallel())
                .flatMap(cefMessage -> logProcessingStrategy.process(partitionContext, eventData, cefMessage))
                .retryWhen(
                Retry.backoff(
                        cefLogConnectorConfig
                                .getKafkaProducerConfig()
                                .getSenderMaxAttempts(),
                        cefLogConnectorConfig
                                .getKafkaProducerConfig()
                                .getSenderBackoff()))
                .doOnError(error ->
                        log.error("Failed to process event after retries", error)
                ).then()
                .doOnSuccess(unused -> log.debug("Batch Event processed successfully"))
                .block();
    }

    private void updateCheckpoint(EventBatchContext eventBatchContext) {
        eventBatchContext.updateCheckpointAsync()
                .doOnSuccess(unused ->
                        log.debug("Checkpoint updated for batch {}",
                                UtilityFunctions.eventBatchContextString(eventBatchContext))
                )
                .doOnError(error ->
                        log.error("Failed to update checkpoint", error)
                )
                .subscribeOn(Schedulers.boundedElastic())
                .subscribe();
    }
}