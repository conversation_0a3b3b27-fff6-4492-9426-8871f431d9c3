package com.illumio.data.configuration;


import com.illumio.data.managedidentity.kafka.KafkaOAuth2AuthenticateCallbackHandler;

import lombok.RequiredArgsConstructor;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Configuration
@RequiredArgsConstructor
public class KafkaConsumerConfig {
    private final CEFLogConnectorConfig cefLogConnectorConfig;

    @Bean
    public KafkaReceiver<String, String> kafkaReceiverOptions() {
        Map<String, Object> consumerProps = new HashMap<>();
        consumerProps.put(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                cefLogConnectorConfig.getKafkaConsumerConfig().getBootstrapServers());
        consumerProps.put(
                ConsumerConfig.GROUP_ID_CONFIG,
                cefLogConnectorConfig.getKafkaConsumerConfig().getGroupId());
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);

        if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaConsumerConfig)
                .map(CEFLogConnectorConfig.KafkaConsumerConfig::getIsManagedIdentity)
                .orElse(Boolean.FALSE)) {
            consumerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            consumerProps.put(SaslConfigs.SASL_MECHANISM, "OAUTHBEARER");
            consumerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    "org.apache.kafka.common.security.oauthbearer.OAuthBearerLoginModule required;");
            consumerProps.put(
                    SaslConfigs.SASL_LOGIN_CALLBACK_HANDLER_CLASS,
                    KafkaOAuth2AuthenticateCallbackHandler.class.getName());
        } else if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaConsumerConfig)
                .map(CEFLogConnectorConfig.KafkaConsumerConfig::getIsConnectionString)
                .orElse(Boolean.FALSE)) {
            consumerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            consumerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            consumerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    cefLogConnectorConfig.getKafkaConsumerConfig().getSaslJaasConfig());
        }

        if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaConsumerConfig)
                .map(CEFLogConnectorConfig.KafkaConsumerConfig::getAutoOffsetReset)
                .isPresent()) {
            consumerProps.put(
                    ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,
                    cefLogConnectorConfig.getKafkaConsumerConfig().getAutoOffsetReset());
        }

        if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaConsumerConfig)
                .map(CEFLogConnectorConfig.KafkaConsumerConfig::getRequestTimeoutMs)
                .isPresent()) {
            consumerProps.put(
                    CommonClientConfigs.REQUEST_TIMEOUT_MS_CONFIG,
                    cefLogConnectorConfig.getKafkaConsumerConfig().getRequestTimeoutMs());
        }

        if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaConsumerConfig)
                .map(CEFLogConnectorConfig.KafkaConsumerConfig::getMaxPollRecords)
                .isPresent()) {
            consumerProps.put(
                    ConsumerConfig.MAX_POLL_RECORDS_CONFIG,
                    cefLogConnectorConfig.getKafkaConsumerConfig().getMaxPollRecords());
        }

        if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaConsumerConfig)
                .map(CEFLogConnectorConfig.KafkaConsumerConfig::getMaxPartitionFetchBytes)
                .isPresent()) {
            consumerProps.put(
                    ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG,
                    cefLogConnectorConfig.getKafkaConsumerConfig().getMaxPartitionFetchBytes());
        }

        ReceiverOptions<String, String> receiverOptions =
                ReceiverOptions.<String, String>create(consumerProps)
                        .subscription(
                                Collections.singleton(
                                        cefLogConnectorConfig.getKafkaConsumerConfig().getTopic()));

        return KafkaReceiver.create(receiverOptions);
    }
}
