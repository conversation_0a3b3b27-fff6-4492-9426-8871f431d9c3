package com.illumio.data.components.filter;

import com.illumio.data.CommonSecurityLog;
import org.springframework.stereotype.Component;

import java.util.Set;
import java.util.function.Predicate;

/**
 * Implements the DCR filter which this pipeline needs to handle. Below is the KQL version: where
 * not((tostring(DestinationPort) == '500' and DeviceAction =~ 'deny') or (Activity =~ 'TRAFFIC' and
 * tostring(DestinationPort) == '53' and DestinationIP in ('***********', '***********',
 * '2001:4898::1050:1050', '2001:4898::1050:5050')))
 */
@Component
public class DCRFilter implements Predicate<CommonSecurityLog> {

    private static final Set<String> IP_BLACKLIST =
            Set.of("***********", "***********", "2001:4898::1050:1050", "2001:4898::1050:5050");

    @Override
    public boolean test(CommonSecurityLog commonSecurityLog) {
        return !(firstClause(commonSecurityLog) || secondClause(commonSecurityLog));
    }

    public boolean firstClause(CommonSecurityLog commonSecurityLog) {
        return commonSecurityLog.getDestinationPort() == 500
                && null != commonSecurityLog.getDeviceAction()
                && commonSecurityLog.getDeviceAction().equalsIgnoreCase("deny");
    }

    public boolean secondClause(CommonSecurityLog commonSecurityLog) {
        return null != commonSecurityLog.getActivity()
                && commonSecurityLog.getActivity().equalsIgnoreCase("TRAFFIC")
                && commonSecurityLog.getDestinationPort() == 53
                && IP_BLACKLIST.contains(commonSecurityLog.getDestinationIP());
    }
}
