package com.illumio.data.components.strategy;

import com.azure.messaging.eventhubs.EventData;
import com.azure.messaging.eventhubs.models.PartitionContext;
import com.github.jcustenborder.cef.Message;
import com.illumio.data.CefToFlowPipeline;
import com.illumio.data.configuration.CEFLogConnectorConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;



@Slf4j
@Component
public class CefFlowStrategy extends AbstractLogProcessingStrategy {

    private final CefToFlowPipeline cefToFlowPipeline;

    public CefFlowStrategy(CefToFlowPipeline cefToFlowPipeline,
                           KafkaSender<String, String> kafkaSender,
                           CEFLogConnectorConfig config) {
        super(kafkaSender, config);
        this.cefToFlowPipeline = cefToFlowPipeline;
    }

    @Override
    protected String getTopic() {
        return config.getKafkaProducerConfig().getKafkaFlowTopic();
    }

    @Override
    protected Mono<SenderRecord<String, String, String>> processWithPipeline(
            PartitionContext partitionContext, EventData eventData, Mono<Message> cefMessage) {
        return cefToFlowPipeline.pipeline(partitionContext, eventData, cefMessage,getTopic());
    }

}
