package com.illumio.data.azure;

import com.azure.identity.DefaultAzureCredentialBuilder;
import com.azure.messaging.eventhubs.EventHubClientBuilder;
import com.azure.messaging.eventhubs.EventHubConsumerAsyncClient;
import com.illumio.data.configuration.CEFLogConnectorConfig;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Optional;

@Configuration
public class EventHubClientConfig {
    @Bean
    public EventHubConsumerAsyncClient eventHubConsumerAsyncClient(
            CEFLogConnectorConfig cefLogConnectorConfig) {
        EventHubClientBuilder eventHubClientBuilder = new EventHubClientBuilder();

        if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaConsumerConfig)
                .map(CEFLogConnectorConfig.KafkaConsumerConfig::getIsConnectionString)
                .orElse(Boolean.FALSE)) {
            eventHubClientBuilder.connectionString(
                    cefLogConnectorConfig.getKafkaConsumerConfig().getSaslJaasConfig(),
                    cefLogConnectorConfig.getKafkaConsumerConfig().getTopic());
        } else {
            eventHubClientBuilder.credential(new DefaultAzureCredentialBuilder().build());
            eventHubClientBuilder.eventHubName(
                    cefLogConnectorConfig.getKafkaConsumerConfig().getTopic());
            eventHubClientBuilder.fullyQualifiedNamespace(
                    cefLogConnectorConfig.getKafkaConsumerConfig().getBootstrapServers());
        }
        eventHubClientBuilder.consumerGroup(
                cefLogConnectorConfig.getKafkaConsumerConfig().getGroupId());

        EventHubConsumerAsyncClient consumerAsyncClient =
                eventHubClientBuilder.buildAsyncConsumerClient();
        return consumerAsyncClient;
    }
}
