package com.illumio.data.components.firewalls;

import com.github.jcustenborder.cef.Message;
import com.illumio.data.CommonSecurityLog;
import com.illumio.data.configuration.CEFLogConnectorConfig;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import reactor.core.publisher.Mono;

import java.util.Map;

@Slf4j
@Component
public class PaloCefTransformer extends CefTransformer {

    public PaloCefTransformer(CEFLogConnectorConfig cefLogConnectorConfig) {
        super(cefLogConnectorConfig);
    }

    public Mono<CommonSecurityLog> transform(
            Message cefMessage, Map<String, Object> properties) {
        CommonSecurityLog.CommonSecurityLogBuilder commonSecurityLogBuilder =
                CommonSecurityLog.builder();
        // standard fields
        commonSecurityLogBuilder.DeviceAction(extension(cefMessage, "act"));
        commonSecurityLogBuilder.ApplicationProtocol(extension(cefMessage, "app"));
        commonSecurityLogBuilder.EventCount(integerExtension(cefMessage, "cnt"));
        commonSecurityLogBuilder.DeviceVendor(cefMessage.deviceVendor());
        commonSecurityLogBuilder.DeviceProduct(cefMessage.deviceProduct());
        commonSecurityLogBuilder.DeviceVersion(cefMessage.deviceVersion());
        commonSecurityLogBuilder.DestinationTranslatedAddress(
                extension(cefMessage, "destinationTranslatedAddress"));
        commonSecurityLogBuilder.DestinationTranslatedPort(
                integerExtension(cefMessage, "destinationTranslatedPort"));
        commonSecurityLogBuilder.DeviceEventClassID(cefMessage.deviceEventClassId());
        commonSecurityLogBuilder.DeviceExternalId(extension(cefMessage, "deviceExternalId"));
        commonSecurityLogBuilder.DeviceInboundInterface(
                extension(cefMessage, "deviceInboundInterface"));
        commonSecurityLogBuilder.DeviceOutboundInterface(
                extension(cefMessage, "deviceOutboundInterface"));
        commonSecurityLogBuilder.DestinationPort(integerExtension(cefMessage, "dpt"));
        commonSecurityLogBuilder.DestinationIP(extension(cefMessage, "dst"));
        commonSecurityLogBuilder.DeviceName(extension(cefMessage, "dvchost"));
        commonSecurityLogBuilder.Computer(cefMessage.host());
        commonSecurityLogBuilder.ReceivedBytes(longExtension(cefMessage, "in"));
        commonSecurityLogBuilder.Activity(cefMessage.name());
        commonSecurityLogBuilder.SentBytes(longExtension(cefMessage, "out"));
        commonSecurityLogBuilder.Protocol(extension(cefMessage, "proto"));
        String receiptTime = extension(cefMessage, "rt");
        String parsedReceiptTime = parseReceivedTime(receiptTime);
        commonSecurityLogBuilder.StartTime(parsedReceiptTime);
        commonSecurityLogBuilder.EndTime(parsedReceiptTime);
        commonSecurityLogBuilder.ReceiptTime(parsedReceiptTime);
        commonSecurityLogBuilder.LogSeverity(cefMessage.severity());
        commonSecurityLogBuilder.SourceTranslatedAddress(
                extension(cefMessage, "sourceTranslatedAddress"));
        commonSecurityLogBuilder.SourceTranslatedPort(
                integerExtension(cefMessage, "sourceTranslatedPort"));
        commonSecurityLogBuilder.SourcePort(integerExtension(cefMessage, "spt"));
        commonSecurityLogBuilder.SourceIP(extension(cefMessage, "src"));
        String timeGenerated = convertToIso8601(cefMessage.timestamp());
        commonSecurityLogBuilder.TimeGenerated(timeGenerated);
        // custom IP v6
        commonSecurityLogBuilder.DeviceCustomIPv6Address2(extension(cefMessage, "c6a2"));
        commonSecurityLogBuilder.DeviceCustomIPv6Address3(extension(cefMessage, "c6a3"));
        // custom number fields
        commonSecurityLogBuilder.DeviceCustomNumber1(integerExtension(cefMessage, "cn1"));
        commonSecurityLogBuilder.DeviceCustomNumber1Label(extension(cefMessage, "cn1Label"));
        commonSecurityLogBuilder.DeviceCustomNumber2(integerExtension(cefMessage, "cn2"));
        commonSecurityLogBuilder.DeviceCustomNumber2Label(extension(cefMessage, "cn2Label"));
        commonSecurityLogBuilder.DeviceCustomNumber3(integerExtension(cefMessage, "cn3"));
        commonSecurityLogBuilder.DeviceCustomNumber3Label(extension(cefMessage, "cn3Label"));
        // custom string fields
        commonSecurityLogBuilder.DeviceCustomString1(extension(cefMessage, "cs1"));
        commonSecurityLogBuilder.DeviceCustomString1Label(extension(cefMessage, "cs1Label"));
        commonSecurityLogBuilder.DeviceCustomString2(extension(cefMessage, "cs2"));
        commonSecurityLogBuilder.DeviceCustomString2Label(extension(cefMessage, "cs2Label"));
        commonSecurityLogBuilder.DeviceCustomString3(extension(cefMessage, "cs3"));
        commonSecurityLogBuilder.DeviceCustomString3Label(extension(cefMessage, "cs3Label"));
        commonSecurityLogBuilder.DeviceCustomString4(extension(cefMessage, "cs4"));
        commonSecurityLogBuilder.DeviceCustomString4Label(extension(cefMessage, "cs4Label"));
        commonSecurityLogBuilder.DeviceCustomString5(extension(cefMessage, "cs5"));
        commonSecurityLogBuilder.DeviceCustomString5Label(extension(cefMessage, "cs5Label"));
        commonSecurityLogBuilder.DeviceCustomString6(extension(cefMessage, "cs6"));
        commonSecurityLogBuilder.DeviceCustomString6Label(extension(cefMessage, "cs6Label"));
        commonSecurityLogBuilder.FlexString1(extension(cefMessage, "flexString1"));
        commonSecurityLogBuilder.FlexString1Label(extension(cefMessage, "flexString1Label"));
        // other enrichment fields
        commonSecurityLogBuilder.SimplifiedDeviceAction(extension(cefMessage, "act"));
        commonSecurityLogBuilder.SourceSystem("OpsManager");
        commonSecurityLogBuilder.TenantId(getTenantId(properties));
        commonSecurityLogBuilder.DeviceVendor(cefMessage.deviceVendor());
        var commonSecurityLog = commonSecurityLogBuilder.build();
        return Mono.just(commonSecurityLog);
    }
}
