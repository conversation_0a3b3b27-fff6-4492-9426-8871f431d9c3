package com.illumio.data;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.util.List;

@SpringBootApplication
@Slf4j
public class CEFLogConnectorApp {
    public static void main(String[] args) {
        long maxMemory = Runtime.getRuntime().maxMemory();
        long totalMemory = Runtime.getRuntime().totalMemory();
        long freeMemory = Runtime.getRuntime().freeMemory();

        log.debug("Max Memory: {}MB", maxMemory / 1024 / 1024);
        log.debug("Total Memory: {}MB", totalMemory / 1024 / 1024);
        log.debug("Free Memory: {}MB", freeMemory / 1024 / 1024);

        RuntimeMXBean runtimeMxBean = ManagementFactory.getRuntimeMXBean();
        List<String> vmOptions = runtimeMxBean.getInputArguments();
        log.debug("VM Options: {}", vmOptions);

        SpringApplication.run(CEFLogConnectorApp.class, args);
    }
}
