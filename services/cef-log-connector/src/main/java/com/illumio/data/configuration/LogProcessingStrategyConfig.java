package com.illumio.data.configuration;

import com.illumio.data.components.strategy.CefFlowStrategy;
import com.illumio.data.components.strategy.CefJsonStrategy;
import com.illumio.data.components.strategy.LogProcessingStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class LogProcessingStrategyConfig {

    private final CEFLogConnectorConfig config;
    private final CefFlowStrategy cefFlowStrategy;
    private final CefJsonStrategy cefJsonStrategy;

    @Bean
    public LogProcessingStrategy logProcessingStrategy() {
        return config.getLogProcessingMode() == LogProcessingMode.CEF_FLOW_MODE
                ? cefFlowStrategy
                : cefJsonStrategy;
    }
}
