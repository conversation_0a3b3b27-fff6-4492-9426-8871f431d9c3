package com.illumio.data.components;

import com.azure.messaging.eventhubs.EventData;
import com.azure.messaging.eventhubs.models.EventBatchContext;
import com.azure.messaging.eventhubs.models.EventContext;
import com.azure.messaging.eventhubs.models.PartitionContext;
import com.illumio.data.CommonSecurityLog;

import lombok.experimental.UtilityClass;

import java.util.function.Predicate;

@UtilityClass
public class UtilityFunctions {

    public boolean filterAndLog(
            CommonSecurityLog currentCSL, Predicate<CommonSecurityLog> tester, Runnable c) {
        boolean result = tester.test(currentCSL);
        if (!result) {
            c.run();
        }
        return result;
    }

    public String eventDataString(PartitionContext partitionContext, EventData eventData) {
        return partitionContext.getEventHubName()
                + "-"
                + partitionContext.getPartitionId()
                + "@"
                + eventData.getSequenceNumber();
    }

    public String eventContextString(EventContext eventContext) {
        return eventContext.getPartitionContext().getEventHubName()
                + "-"
                + eventContext.getPartitionContext().getPartitionId()
                + "@"
                + eventContext.getEventData().getSequenceNumber();
    }

    public String partitionContextString(PartitionContext partitionContext) {
        return partitionContext.getFullyQualifiedNamespace()
                + "-"
                + partitionContext.getEventHubName()
                + "-"
                + partitionContext.getConsumerGroup()
                + "-"
                + partitionContext.getPartitionId();
    }

    public String eventBatchContextString(EventBatchContext eventBatchContext) {
        if (eventBatchContext.getEvents().isEmpty()) {
            return partitionContextString(eventBatchContext.getPartitionContext());
        } else if (eventBatchContext.getEvents().size() == 1) {
            return eventDataString(
                    eventBatchContext.getPartitionContext(), eventBatchContext.getEvents().get(0));
        } else {
            return "size "
                    + eventBatchContext.getEvents().size()
                    + " "
                    + eventDataString(
                    eventBatchContext.getPartitionContext(),
                    eventBatchContext.getEvents().get(0))
                    + " to "
                    + eventDataString(
                    eventBatchContext.getPartitionContext(),
                    eventBatchContext
                            .getEvents()
                            .get(eventBatchContext.getEvents().size() - 1));
        }
    }
}
