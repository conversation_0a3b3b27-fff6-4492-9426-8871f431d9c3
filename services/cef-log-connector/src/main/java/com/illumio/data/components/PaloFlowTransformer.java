package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.CommonSecurityLog;
import com.illumio.data.pojo.Flow;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import reactor.core.publisher.Mono;

@Component
@Slf4j
@RequiredArgsConstructor
public class PaloFlowTransformer {
    private static final String DC_FLOW_TYPE = "dc";
    private static final String INSIDE = "inside";
    private static final String OUTSIDE = "outside";

    private final ObjectMapper objectMapper;

    public Mono<Flow> transform(CommonSecurityLog commonSecurityLog) {
        return Mono.fromCallable(
                () -> {
                    Flow.FlowBuilder flowBuilder =
                            Flow.builder()
                                    .SourceHostName(commonSecurityLog.getSourceHostName())
                                    .SourceIP(commonSecurityLog.getSourceIP())
                                    .SourceMACAddress(commonSecurityLog.getSourceMACAddress())
                                    .SourceNTDomain(commonSecurityLog.getSourceNTDomain())
                                    .SourceProcessId(commonSecurityLog.getSourceProcessId())
                                    .SourceProcessName(commonSecurityLog.getSourceProcessName())
                                    .SourceUserID(commonSecurityLog.getSourceUserID())
                                    .SourceUserName(commonSecurityLog.getSourceUserName())
                                    .SourceUserPrivileges(
                                            commonSecurityLog.getSourceUserPrivileges())
                                    .DeviceAction(commonSecurityLog.getDeviceAction())
                                    .DeviceAddress(commonSecurityLog.getDeviceAddress())
                                    .DestinationDnsDomain(
                                            commonSecurityLog.getDestinationDnsDomain())
                                    .DestinationHostName(commonSecurityLog.getDestinationHostName())
                                    .DestinationIP(commonSecurityLog.getDestinationIP())
                                    .DestinationMACAddress(
                                            commonSecurityLog.getDestinationMACAddress())
                                    .DestinationNTDomain(commonSecurityLog.getDestinationNTDomain())
                                    .DestinationPort(commonSecurityLog.getDestinationPort())
                                    .DestinationProcessId(
                                            commonSecurityLog.getDestinationProcessId())
                                    .DestinationProcessName(
                                            commonSecurityLog.getDestinationProcessName())
                                    .DestinationServiceName(
                                            commonSecurityLog.getDestinationServiceName())
                                    .DestinationTranslatedAddress(
                                            commonSecurityLog.getDestinationTranslatedAddress())
                                    .DestinationUserID(commonSecurityLog.getDestinationUserID())
                                    .DestinationUserName(commonSecurityLog.getDestinationUserName())
                                    .Protocol(commonSecurityLog.getProtocol())
                                    .LogSeverity(commonSecurityLog.getLogSeverity())
                                    .MaliciousIP(commonSecurityLog.getMaliciousIP())
                                    .MaliciousIPCountry(commonSecurityLog.getMaliciousIPCountry())
                                    .MaliciousIPLatitude(commonSecurityLog.getMaliciousIPLatitude())
                                    .MaliciousIPLongitude(
                                            commonSecurityLog.getMaliciousIPLongitude())
                                    .SentBytes(commonSecurityLog.getSentBytes())
                                    .ReceivedBytes(commonSecurityLog.getReceivedBytes())
                                    .TenantId(commonSecurityLog.getTenantId())
                                    .ThreatConfidence(commonSecurityLog.getThreatConfidence())
                                    .ThreatDescription(commonSecurityLog.getThreatDescription())
                                    .ThreatSeverity(commonSecurityLog.getThreatSeverity())
                                    .StartTime(commonSecurityLog.getStartTime())
                                    .EndTime(commonSecurityLog.getEndTime())
                                    .SourceDnsDomain(commonSecurityLog.getSourceDnsDomain())
                                    .SourceServiceName(commonSecurityLog.getSourceServiceName())
                                    .SourceSystem(commonSecurityLog.getSourceSystem())
                                    .DeviceMacAddress(commonSecurityLog.getDeviceMacAddress())
                                    .DeviceName(commonSecurityLog.getDeviceName())
                                    .DeviceOutboundInterface(
                                            commonSecurityLog.getDeviceOutboundInterface())
                                    .DeviceProduct(commonSecurityLog.getDeviceProduct())
                                    .DeviceTranslatedAddress(
                                            commonSecurityLog.getDeviceTranslatedAddress())
                                    .DeviceVersion(commonSecurityLog.getDeviceVersion())
                                    .DeviceTimeZone(commonSecurityLog.getDeviceTimeZone())
                                    .DeviceExternalId(commonSecurityLog.getDeviceExternalId())
                                    .DeviceCustomNumber3(commonSecurityLog.getDeviceCustomNumber3())
                                    .ReceiptTime(commonSecurityLog.getTimeGenerated())
                                    .TimeGenerated(commonSecurityLog.getTimeGenerated())
                                    .Activity(commonSecurityLog.getActivity())
                                    .AdditionalExtensions(
                                            commonSecurityLog.getAdditionalExtensions())
                                    .SourceZone(commonSecurityLog.getDeviceCustomString4())
                                    .DestinationZone(commonSecurityLog.getDeviceCustomString5())
                                    .RequestURL(commonSecurityLog.getRequestURL())
                                    .Computer(commonSecurityLog.getComputer());
                    calculateFlowTypes(commonSecurityLog, flowBuilder);
                    return flowBuilder.build();
                });
    }

    @SneakyThrows
    public String asValue(Flow flow) {
        return objectMapper.writeValueAsString(flow);
    }

    public String asKey(Flow flow) {
        StringBuilder key = new StringBuilder();
        key.append(flow.getSourceIP())
                .append(",")
                .append(flow.getDestinationIP())
                .append(",")
                .append(flow.getProtocol())
                .append(",")
                .append(flow.getDestinationPort())
        ;

        return key.toString();
    }

    private void calculateFlowTypes(CommonSecurityLog commonSecurityLog, Flow.FlowBuilder flowBuilder) {
        if (null == commonSecurityLog.getDeviceCustomString4() || null == commonSecurityLog.getDeviceCustomString5()) {
            return;
        }
        final String srcZone = commonSecurityLog.getDeviceCustomString4().toLowerCase();
        final String dstZone = commonSecurityLog.getDeviceCustomString5().toLowerCase();
        if (isInsideToOutside(srcZone, dstZone)) {
            flowBuilder.SrcFlowType(DC_FLOW_TYPE);
        } else if (isOutsideToInside(srcZone, dstZone)) {
            flowBuilder.DestFlowType(DC_FLOW_TYPE);
        }
    }

    public boolean isInsideToOutside(String srcZone, String dstZone) {
        return (srcZone.contains(INSIDE) && dstZone.contains(OUTSIDE));
    }

    public boolean isOutsideToInside(String srcZone, String dstZone) {
        return (srcZone.contains(OUTSIDE) && dstZone.contains(INSIDE));
    }
}
