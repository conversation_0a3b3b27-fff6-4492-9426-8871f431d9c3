package com.illumio.data.configuration;

import com.azure.messaging.eventhubs.LoadBalancingStrategy;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
@ConfigurationProperties(prefix = "cef-log-connector")
@Getter
@Setter
public class CEFLogConnectorConfig {
    private final KafkaConsumerConfig kafkaConsumerConfig = new KafkaConsumerConfig();
    private final KafkaProducerConfig kafkaProducerConfig = new KafkaProducerConfig();
    private final StorageAccountConfig storageAccountConfig = new StorageAccountConfig();
    private LogProcessingMode logProcessingMode = LogProcessingMode.CEF_FLOW_MODE;
    private final RetryConfig retryConfig = new RetryConfig();
    private final TenantConfig tenantConfig = new TenantConfig();
    private final SamplerConfig samplerConfig = new SamplerConfig();

    @Getter
    @Setter
    public static class KafkaConsumerConfig {
        private String bootstrapServers;
        private Boolean isConnectionString = false;
        private Boolean isManagedIdentity = false;
        private String saslJaasConfig;
        private String topic;
        private String groupId;
        private String autoOffsetReset;
        private Integer requestTimeoutMs = 30000;
        private Integer maxPollRecords = 500;
        private Integer maxPartitionFetchBytes;
        private Integer receiverMaxAttempts = 10;
        private Duration receiverBackoff = Duration.ofSeconds(5);
        private Integer backPressureEvents;
        private Integer prefetchCount;
        private LoadBalancingStrategy loadBalancingStrategy = LoadBalancingStrategy.BALANCED;
    }

    @Getter
    @Setter
    public static class KafkaProducerConfig {
        private String bootstrapServers;
        private Boolean isConnectionString = false;
        private Boolean isManagedIdentity = false;
        private String saslJaasConfig;
        private String kafkaFlowTopic;
        private String kafkaCefJsonTopic;
        private Integer requestTimeoutMs;
        private Integer deliveryTimeoutMs;
        private Integer lingerMs;
        private Integer batchSize;
        private Integer bufferMemory;
        private Integer maxBlockMs;
        private Integer senderMaxAttempts = 10;
        private Duration senderBackoff = Duration.ofSeconds(5);
        private Integer maxInFlight = 1024;
    }

    @Getter
    @Setter
    public static class StorageAccountConfig {
        private String endpoint;
        private String containerName;
        private String connectionString;
        private Boolean isConnectionString = false;
        private Boolean isManagedIdentity = false;
        private Integer maxRetries = 5;
        private Duration timeoutSeconds = Duration.ofSeconds(10);
        private Duration retryDuration = Duration.ofSeconds(10);
    }

    @Getter
    @Setter
    public static class RetryConfig {
        private Duration tryTimeoutSeconds = Duration.ofSeconds(10);
        private Duration delaySeconds = Duration.ofSeconds(10);
        private Integer maxRetries = 50;
    }

    @Getter
    @Setter
    public static class TenantConfig {
        private String tenantId = "********-********-********-********";
    }

    @Getter
    @Setter
    public static class SamplerConfig {
        private Boolean isEnabled = false;
        private Double batchSampleSize = 1.0;
    }
}


