package com.illumio.data;

import com.azure.messaging.eventhubs.EventData;
import com.azure.messaging.eventhubs.models.PartitionContext;
import com.github.jcustenborder.cef.Message;
import com.illumio.data.components.PaloFlowTransformer;
import com.illumio.data.components.UtilityFunctions;
import com.illumio.data.components.filter.*;
import com.illumio.data.components.firewalls.PaloCefTransformer;
import com.illumio.data.configuration.CEFLogConnectorConfig;
import com.illumio.data.pojo.Flow;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.kafka.sender.SenderRecord;

@Component
@Slf4j
@RequiredArgsConstructor
public class CefToFlowPipeline {
    private final CEFLogConnectorConfig cefLogConnectorConfig;
    private final PaloCefTransformer paloCefTransformer;
    private final TrafficFilter trafficFilter;
    private final ZoneFilter zoneFilter;
    private final ProtocolFilter protocolFilter;
    private final IPFilter ipFilter;
    private final DCRFilter dcrFilter;
    private final PaloFlowTransformer paloFlowTransformer;

    public Mono<SenderRecord<String, String, String>> pipeline(
            PartitionContext partitionContext, EventData eventData, Mono<Message> cefMessagMono, String kafkaTopic) {
        return cefMessagMono
                .flatMap(
                        message -> paloCefTransformer.transform(message, eventData.getProperties()))
                .doOnNext(
                        commonSecurityLog ->
                                log.debug(
                                        "Converted Palo CEF from record {} to CommonSecurityLog: {}",
                                        UtilityFunctions.eventDataString(
                                                partitionContext, eventData),
                                        commonSecurityLog))
                .filter(
                        commonSecurityLog ->
                                UtilityFunctions.filterAndLog(
                                        commonSecurityLog,
                                        trafficFilter,
                                        () ->
                                                log.debug(
                                                        "Traffic filter filtered out a record from the batch. {}",
                                                        UtilityFunctions.eventDataString(
                                                                partitionContext, eventData))))
                .filter(
                        commonSecurityLog ->
                                UtilityFunctions.filterAndLog(
                                        commonSecurityLog,
                                        zoneFilter,
                                        () ->
                                                log.debug(
                                                        "Zone filter filtered out a record from the batch. {}",
                                                        UtilityFunctions.eventDataString(
                                                                partitionContext, eventData))))
                .filter(
                        commonSecurityLog ->
                                UtilityFunctions.filterAndLog(
                                        commonSecurityLog,
                                        protocolFilter,
                                        () ->
                                                log.debug(
                                                        "Protocol filter filtered out a record from the batch: {}",
                                                        UtilityFunctions.eventDataString(
                                                                partitionContext, eventData))))
                .filter(
                        commonSecurityLog ->
                                UtilityFunctions.filterAndLog(
                                        commonSecurityLog,
                                        ipFilter,
                                        () ->
                                                log.debug(
                                                        "IP filter filtered out a record from the batch: {}",
                                                        UtilityFunctions.eventDataString(
                                                                partitionContext, eventData))))
                .filter(
                        commonSecurityLog ->
                                UtilityFunctions.filterAndLog(
                                        commonSecurityLog,
                                        dcrFilter,
                                        () ->
                                                log.debug(
                                                        "DCR filter filtered out a record from the batch: {}",
                                                        UtilityFunctions.eventDataString(
                                                                partitionContext, eventData))))
                .flatMap(paloFlowTransformer::transform)
                .flatMap(flow -> createSenderRecord(flow, partitionContext, eventData, kafkaTopic))
                .doOnNext(senderRecord -> log.debug("Created Palo record: {}", senderRecord));
    }

    private Mono<SenderRecord<String, String, String>> createSenderRecord(
            Flow flow, PartitionContext partitionContext, EventData eventData, String kafkaTopic) {
        return Mono.just(
                SenderRecord.create(
                        new ProducerRecord<>(
                                kafkaTopic,
                                paloFlowTransformer.asKey(flow),
                                paloFlowTransformer.asValue(flow)),
                        UtilityFunctions.eventDataString(partitionContext, eventData)));
    }
}
