package com.illumio.data.components.strategy;

import com.azure.messaging.eventhubs.EventData;
import com.azure.messaging.eventhubs.models.PartitionContext;
import com.github.jcustenborder.cef.Message;
import com.illumio.data.configuration.CEFLogConnectorConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import reactor.core.publisher.Mono;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.util.retry.Retry;



@Slf4j
@RequiredArgsConstructor
public abstract class AbstractLogProcessingStrategy implements LogProcessingStrategy {
    protected final KafkaSender<String, String> kafkaSender;
    protected final CEFLogConnectorConfig config;

    protected abstract String getTopic();

    protected abstract Mono<SenderRecord<String, String, String>> processWithPipeline(
            PartitionContext partitionContext, EventData eventData, Mono<Message> cefMessage);


    @Override
    public Mono<Void> process(PartitionContext partitionContext, EventData eventData, Message cefMessage) {
        return processWithPipeline(partitionContext, eventData, Mono.just(cefMessage))
                .flatMap(this::sendToKafka)
                .then();
    }


    private Mono<Void> sendToKafka(SenderRecord<String, String, String> record) {
        return kafkaSender.send(Mono.just(record))
                .retryWhen(Retry.backoff(config.getRetryConfig().getMaxRetries(), config.getRetryConfig().getDelaySeconds()))
                .doOnNext(result ->
                        log.debug("Sent to {} topic: {}", getTopic(), result.correlationMetadata())
                )
                .onErrorResume(error -> {
                    log.error("Failed to send to {} topic", getTopic(), error);
                    return Mono.empty();
                })
                .then();
    }
}
