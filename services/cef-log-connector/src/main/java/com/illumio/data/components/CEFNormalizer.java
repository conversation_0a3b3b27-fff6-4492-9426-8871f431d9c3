package com.illumio.data.components;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@UtilityClass
public class CEFNormalizer {
    // Handle spaces in cef version
    private final Pattern CHECKPOINT_CEF_VERSION_PATTERN = Pattern.compile("CEF:\\s+(?<cs0>\\d+)");

    /**
     * CEF Prefix pattern taken from {@link
     * com.github.jcustenborder.cef.CEFParser#parse(String)}}
     */
    private final Pattern CEF_PREFIX =
            Pattern.compile("^((?<timestamp>.+)\\s+(?<host>\\S+)\\s+)(?<cs0>CEF:\\d+)");

    // Used for Normalizing CEF Timestamp for Check Point firewalls
    private final DateTimeFormatter CHECKPOINT_TIMESTAMP_FORMAT =
            DateTimeFormatter.ISO_OFFSET_DATE_TIME;
    private final DateTimeFormatter CEF_TIMESTAMP_FORMAT =
            DateTimeFormatter.ofPattern("MMM dd yyyy HH:mm:ss.SSS");

    // handle prefix "<14>"
    public String handlePrefix(String cefEvent) {
        if (cefEvent.startsWith("<")) {
            return cefEvent.substring(cefEvent.indexOf('>') + 1);
        }
        return cefEvent;
    }

    // handle "CEF: 0"
    public String handleSpacesInCefVersion(String cefEvent) {
        Matcher matcher = CHECKPOINT_CEF_VERSION_PATTERN.matcher(cefEvent);
        if (matcher.find()) {
            cefEvent = matcher.replaceFirst("CEF:${cs0}");
        }
        return cefEvent;
    }

    // handle timestamp in CheckPoint format
    public String handleCheckpointTimestampFormat(String cefEvent) {
        try {
            Matcher cefPrefixMatcher = CEF_PREFIX.matcher(cefEvent);
            if (!cefPrefixMatcher.find()) {
                log.debug("Cannot find CEF Prefix in handleCheckpointTimestampFormat {}", cefEvent);
                return cefEvent;
            }
            final String timestampText = cefPrefixMatcher.group("timestamp");
            LocalDateTime timestampLDT =
                    LocalDateTime.parse(timestampText, CHECKPOINT_TIMESTAMP_FORMAT);
            String cefTimestamp = timestampLDT.atZone(ZoneOffset.UTC).format(CEF_TIMESTAMP_FORMAT);
            return cefPrefixMatcher.replaceFirst(cefTimestamp + " ${host} ${cs0}");
        } catch (Exception e) {
            log.debug("Could not handleCheckpointTimestampFormat {}", cefEvent, e);
            return cefEvent;
        }
    }
}
