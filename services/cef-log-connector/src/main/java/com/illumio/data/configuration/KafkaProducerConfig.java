package com.illumio.data.configuration;


import com.illumio.data.managedidentity.kafka.KafkaOAuth2AuthenticateCallbackHandler;

import lombok.RequiredArgsConstructor;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Configuration
@RequiredArgsConstructor
public class KafkaProducerConfig {
    private final CEFLogConnectorConfig cefLogConnectorConfig;

    @Bean
    public SenderOptions<String, String> kafkaSenderOptions() {
        Map<String, Object> producerProps = producerOptions();

        return SenderOptions.<String, String>create(producerProps)
                // Non-blocking back-pressure
                .maxInFlight(cefLogConnectorConfig.getKafkaProducerConfig().getMaxInFlight());
    }

    @Bean
    public KafkaSender<String, String> reactiveKafkaSender(
            SenderOptions<String, String> senderOptions) {
        return KafkaSender.create(senderOptions);
    }


    private Map<String, Object> producerOptions() {
        Map<String, Object> producerProps = new HashMap<>();
        producerProps.put(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG,
                cefLogConnectorConfig.getKafkaProducerConfig().getBootstrapServers());
        producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

        if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaProducerConfig)
                .map(CEFLogConnectorConfig.KafkaProducerConfig::getIsManagedIdentity)
                .orElse(Boolean.FALSE)) {
            producerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            producerProps.put(SaslConfigs.SASL_MECHANISM, "OAUTHBEARER");
            producerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    "org.apache.kafka.common.security.oauthbearer.OAuthBearerLoginModule required;");
            producerProps.put(
                    SaslConfigs.SASL_LOGIN_CALLBACK_HANDLER_CLASS,
                    KafkaOAuth2AuthenticateCallbackHandler.class.getName());
        } else if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaProducerConfig)
                .map(CEFLogConnectorConfig.KafkaProducerConfig::getIsConnectionString)
                .orElse(Boolean.FALSE)) {
            producerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            producerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            producerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    cefLogConnectorConfig.getKafkaProducerConfig().getSaslJaasConfig());
        }

        if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaProducerConfig)
                .map(CEFLogConnectorConfig.KafkaProducerConfig::getRequestTimeoutMs)
                .isPresent()) {
            producerProps.put(
                    CommonClientConfigs.REQUEST_TIMEOUT_MS_CONFIG,
                    cefLogConnectorConfig.getKafkaProducerConfig().getRequestTimeoutMs());
        }

        if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaProducerConfig)
                .map(CEFLogConnectorConfig.KafkaProducerConfig::getDeliveryTimeoutMs)
                .isPresent()) {
            producerProps.put(
                    ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG,
                    cefLogConnectorConfig.getKafkaProducerConfig().getDeliveryTimeoutMs());
        }

        if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaProducerConfig)
                .map(CEFLogConnectorConfig.KafkaProducerConfig::getLingerMs)
                .isPresent()) {
            producerProps.put(
                    ProducerConfig.LINGER_MS_CONFIG,
                    cefLogConnectorConfig.getKafkaProducerConfig().getLingerMs());
        }

        if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaProducerConfig)
                .map(CEFLogConnectorConfig.KafkaProducerConfig::getBatchSize)
                .isPresent()) {
            producerProps.put(
                    ProducerConfig.BATCH_SIZE_CONFIG,
                    cefLogConnectorConfig.getKafkaProducerConfig().getBatchSize());
        }

        if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaProducerConfig)
                .map(CEFLogConnectorConfig.KafkaProducerConfig::getBufferMemory)
                .isPresent()) {
            producerProps.put(
                    ProducerConfig.BUFFER_MEMORY_CONFIG,
                    cefLogConnectorConfig.getKafkaProducerConfig().getBufferMemory());
        }
        if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getKafkaProducerConfig)
                .map(CEFLogConnectorConfig.KafkaProducerConfig::getMaxBlockMs)
                .isPresent()) {
            producerProps.put(
                    ProducerConfig.MAX_BLOCK_MS_CONFIG,
                    cefLogConnectorConfig.getKafkaProducerConfig().getMaxBlockMs());
        }
        return producerProps;
    }
}