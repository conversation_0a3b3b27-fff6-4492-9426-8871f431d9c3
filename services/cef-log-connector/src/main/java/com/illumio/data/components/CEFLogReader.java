package com.illumio.data.components;

import com.github.jcustenborder.cef.CEFParser;
import com.github.jcustenborder.cef.Message;

import com.illumio.data.CommonSecurityLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import reactor.core.publisher.Mono;

import java.util.Map;

@Component
@RequiredArgsConstructor
@Slf4j
public class CEFLogReader {

    private final CEFParser cefParser;

    public Mono<Message> transformToCefMessage(String cefMessage) {
        return Mono.fromCallable(
                () -> {
                    try {
                        var parsable = CEFNormalizer.handlePrefix(cefMessage);
                        parsable = CEFNormalizer.handleSpacesInCefVersion(parsable);
                        parsable = CEFNormalizer.handleCheckpointTimestampFormat(parsable);
                        var message = cefParser.parse(parsable);
                        return message;
                    } catch (Exception e) {
                        log.error("Error parsing CEF log or converting to object", e);
                        return null;
                    }
                });
    }
}
