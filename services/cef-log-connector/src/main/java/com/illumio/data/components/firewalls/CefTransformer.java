package com.illumio.data.components.firewalls;

import com.github.jcustenborder.cef.Message;
import com.illumio.data.configuration.CEFLogConnectorConfig;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.qpid.proton.amqp.Binary;

import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.UUID;

import java.util.UUID;

@Slf4j
@AllArgsConstructor
public abstract class CefTransformer {
    private static final String ILLUMIO_TENANT_ID = "IllumioTenantId";

    private static final DateTimeFormatter DATE_TIME_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSS'Z'");

    private static final DateTimeFormatter RECEIPT_TIME_FORMAT =
            DateTimeFormatter.ofPattern("MMM d yyyy H:m:s zzz", Locale.ENGLISH);

    private CEFLogConnectorConfig cefLogConnectorConfig;

    String getTenantId(Map<String, Object> properties) {
        if (properties.containsKey(ILLUMIO_TENANT_ID)) {
            Binary binary = (Binary) properties.get(ILLUMIO_TENANT_ID);
            return convertBinaryToString(binary);
        } else {
            return cefLogConnectorConfig.getTenantConfig().getTenantId();
        }
    }

    String convertBinaryToString(Binary binary) {
        if (binary == null) {
            return null;
        }
        byte[] bytes = binary.getArray();

        return new String(
                bytes, binary.getArrayOffset(), binary.getLength(), StandardCharsets.UTF_8);
    }

    String extension(Message cefMessage, String extension) {
        return cefMessage.extensions().getOrDefault(extension, null);
    }

    Integer integerExtension(Message cefMessage, String extension) {
        String eventCountString = extension(cefMessage, extension);
        if (null != eventCountString) {
            try {
                return Integer.parseInt(eventCountString);
            } catch (NumberFormatException e) {
                log.debug(
                        "Couldn't parse integer for extension {}: {}", extension, eventCountString);
            }
        }
        return null;
    }

    Long longExtension(Message cefMessage, String extension) {
        String stringExtension = extension(cefMessage, extension);
        if (null != stringExtension) {
            try {
                return Long.parseLong(stringExtension);
            } catch (NumberFormatException e) {
                log.debug("Couldn't parse long for extension {}: {}", extension, stringExtension);
            }
        }
        return null;
    }

    String convertToIso8601(Date date) {
        if (null == date) {
            return null;
        }
        try {
            Instant instant = date.toInstant();
            String iso8601 = instant.atZone(ZoneOffset.UTC).format(DATE_TIME_FORMATTER);

            return iso8601;
        } catch (Exception e) {
            log.debug("Couldn't parse date for CEF: {}", date, e);
            return date.toString();
        }
    }

    String parseReceivedTime(String rt) {
        if (null == rt) {
            return null;
        }
        try {
            LocalDateTime dateTime = LocalDateTime.parse(rt, RECEIPT_TIME_FORMAT);

            String formatted = dateTime.atZone(ZoneOffset.UTC).format(DATE_TIME_FORMATTER);
            return formatted;
        } catch (Exception e) {
            log.debug("Couldn't parse rt for CEF: {}", rt, e);
        }

        try {
            long epochMillis = Long.parseLong(rt);
            Date rtDate = new Date(epochMillis);
            Instant rtInstant = rtDate.toInstant();
            String formatted = rtInstant.atZone(ZoneOffset.UTC).format(DATE_TIME_FORMATTER);
            return formatted;
        } catch (Exception e) {
            log.debug("Couldn't parse rt as epoch millis for CEF: {}", rt, e);
        }
        return rt;
    }
}
