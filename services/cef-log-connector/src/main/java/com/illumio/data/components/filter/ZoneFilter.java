package com.illumio.data.components.filter;

import com.illumio.data.CommonSecurityLog;

import org.springframework.stereotype.Component;

import java.util.function.Predicate;

/**
 * Filters out CommonSecurityLogs with the equivalent KQL: | extend SrcZone = DeviceCustomString4,
 * DstZone = DeviceCustomString5 | where (Src<PERSON>one has 'inside' and DstZone has 'outside') or
 * (SrcZone has 'outside' and <PERSON>stZone has 'inside') or (SrcZone has 'vpn' and <PERSON>stZone has 'corp')
 * or (SrcZone has 'corp' and DstZone has 'vpn')
 *
 */
@Component
public class ZoneFilter implements Predicate<CommonSecurityLog> {

    private static final String INSIDE = "inside";
    private static final String OUTSIDE = "outside";

    private static final String VPN = "vpn";
    private static final String CORP = "corp";

    @Override
    public boolean test(CommonSecurityLog commonSecurityLog) {
        final String srcZone = commonSecurityLog.getDeviceCustomString4().toLowerCase();
        final String dstZone = commonSecurityLog.getDeviceCustomString5().toLowerCase();
        return isInsideToOutside(srcZone, dstZone)
                || isOutsideToInside(srcZone, dstZone)
                || isVpnToCorp(srcZone, dstZone)
                || isCorpToVpn(srcZone, dstZone);
    }

    public boolean isInsideToOutside(String srcZone, String dstZone) {
        return (srcZone.contains(INSIDE) && dstZone.contains(OUTSIDE));
    }

    public boolean isOutsideToInside(String srcZone, String dstZone) {
        return (srcZone.contains(OUTSIDE) && dstZone.contains(INSIDE));
    }

    public boolean isVpnToCorp(String srcZone, String dstZone) {
        return srcZone.contains(VPN) && dstZone.contains(CORP);
    }

    public boolean isCorpToVpn(String srcZone, String dstZone) {
        return srcZone.contains(CORP) && dstZone.contains(VPN);
    }
}
