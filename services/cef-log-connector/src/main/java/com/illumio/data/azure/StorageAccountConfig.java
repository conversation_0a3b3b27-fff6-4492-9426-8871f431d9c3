package com.illumio.data.azure;

import com.azure.identity.DefaultAzureCredentialBuilder;
import com.azure.storage.blob.BlobContainerAsyncClient;
import com.azure.storage.blob.BlobContainerClientBuilder;
import com.illumio.data.configuration.CEFLogConnectorConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Optional;

@Configuration
public class StorageAccountConfig {

    @Bean
    public BlobContainerAsyncClient blobContainerAsyncClient(
            CEFLogConnectorConfig cefLogConnectorConfig) {
        BlobContainerClientBuilder blobContainerClientBuilder = new BlobContainerClientBuilder();

        blobContainerClientBuilder.endpoint(
                cefLogConnectorConfig.getStorageAccountConfig().getEndpoint());
        blobContainerClientBuilder.containerName(
                cefLogConnectorConfig.getStorageAccountConfig().getContainerName());

        if (Optional.of(cefLogConnectorConfig)
                .map(CEFLogConnectorConfig::getStorageAccountConfig)
                .map(CEFLogConnectorConfig.StorageAccountConfig::getIsConnectionString)
                .orElse(Boolean.FALSE)) {
            blobContainerClientBuilder.connectionString(
                    cefLogConnectorConfig.getStorageAccountConfig().getConnectionString());
        } else {
            blobContainerClientBuilder.credential(new DefaultAzureCredentialBuilder().build());
        }

        BlobContainerAsyncClient blobContainerClient =
                blobContainerClientBuilder.buildAsyncClient();

        return blobContainerClient;
    }
}
