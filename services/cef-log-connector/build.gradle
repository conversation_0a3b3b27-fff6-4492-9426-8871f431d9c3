plugins {
    id 'org.springframework.boot'
    id 'com.google.cloud.tools.jib'
    id 'com.github.johnrengelman.shadow'
}

apply from: "${project.rootDir}/gradle/scripts/jib-build.gradle"
apply from: "${project.rootDir}/gradle/scripts/opentelemetry.gradle"

repositories {
    maven {
        url  = uri("https://packages.confluent.io/maven/")
    }
}

dependencies {
    // implementation
    implementation project(":commons:azure-commons")
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.kafka:spring-kafka'
    implementation "io.projectreactor.kafka:reactor-kafka"
    implementation 'co.elastic.logging:logback-ecs-encoder'
    implementation 'com.github.jcustenborder:cef-parser:*******'
    // https://mvnrepository.com/artifact/com.azure/azure-core-metrics-opentelemetry
    implementation 'com.azure:azure-core-metrics-opentelemetry:1.0.0-beta.25'
    // TODO remove. Syslog format is different than incoming message format (so far)
//    implementation 'com.github.palindromicity:simple-syslog:1.0.0'
    implementation 'com.azure:azure-identity'
    implementation 'com.azure:azure-messaging-eventhubs'
    implementation 'com.azure:azure-messaging-eventhubs-checkpointstore-blob'

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // spring
    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"

    // test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'io.projectreactor:reactor-test'
    testImplementation 'org.springframework.kafka:spring-kafka-test'
}


jib {
    container {
        entrypoint = [
                'java', '-cp', '@/app/jib-classpath-file',
                'com.illumio.data.CEFLogConnectorApp',
                '--spring.config.location=file:/var/resources/application.yml',
                '--spring.profiles.active=prod'
        ]
        environment = [
                "JAVA_TOOL_OPTIONS": "-javaagent:/otel/opentelemetry-javaagent.jar",
                "OTEL_METRICS_EXPORTER": "prometheus",
                "OTEL_TRACES_EXPORTER": "none",
                "OTEL_LOGS_EXPORTER": "none"
        ]
    }
}
