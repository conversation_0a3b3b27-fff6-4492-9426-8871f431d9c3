# Load - Logs Ingestion API

# Running locally
1. Modify application.yaml
   1. Data Collection Config
   2. Csv Input
2. Create src/main/resources/secrets.yml and fill in secrets:
```yaml
load-logs-ingestion-api:
  azure-client-id: _DO_NOT_COMMIT_
  azure-client-secret: _DO_NOT_COMMIT_
  azure-tenant-id: _DO_NOT_COMMIT_
```
3. Use IntelliJ Run Configuration:
- load-logs-ingestion-api/LoadLogsIngestionApiApplication - CommonSecurityLog 