apiVersion: v1
kind: Service
metadata:
  name: {{ include "LoadLogsIngestionApi.name" . }}
  labels:
    {{- include "LoadLogsIngestionApi.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "LoadLogsIngestionApi.selectorLabels" . | nindent 4 }}
