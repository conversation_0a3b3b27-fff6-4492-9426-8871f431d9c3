apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "LoadLogsIngestionApi.fullname" . }}-env-configmap
  labels:
    {{- include "LoadLogsIngestionApi.labels" . | nindent 4 }}
data:
  application.yml: |
    load-logs-ingestion-api:
      data-collection-config:
        data-collection-endpoint: "{{.Values.loadLogsIngestionApi.data_collection_config.data_collection_endpoint}}"
        data-collection-rule-id: "{{.Values.loadLogsIngestionApi.data_collection_config.data_collection_rule_id}}"
        data-collection-rule-stream-name: "{{.Values.loadLogsIngestionApi.data_collection_config.data_collection_rule_stream_name}}"
      csv-input:
        test-file: /csv-input/test.csv