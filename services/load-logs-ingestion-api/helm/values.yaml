# Default values for connector.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
logging:
  level:
    root: INFO
    
ports:
- name: admin
  port: 8084
- name: rest
  port: 8081

servicePorts:
- name: rest
  podPort: rest
  servicePort: 8081

service:
  type: ClusterIP

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

replicaCount: 50

image:
  repositoryBase: illum.azurecr.io/
  repositoryName: load-logs-ingestion-api
  tag:      # value given at helm deployment 
  pullPolicy: Always

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

podAnnotations: {}

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"



podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000



resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

ansi:
  enabled: ALWAYS
webApplicationType: none
loadLogsIngestionApi:
  data_collection_config:
    data_collection_endpoint: https://law-sink-dce-m9hu.westus3-1.ingest.monitor.azure.com
    data_collection_rule_id: dcr-1386e62044914b69a12092e2ed228042
    data_collection_rule_stream_name: law-sink_CL
  azure_config:
    azure_client_id:        # should give at deployment time
    azure_client_secret:    # should give at deployment time
    azure_tenant_id:        # should give at deployment time

