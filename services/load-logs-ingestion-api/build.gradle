plugins {
    id 'org.springframework.boot'
    id 'com.google.cloud.tools.jib'
}

dependencies {
    implementation project(":commons:azure-commons")
    implementation project(":commons:utility-commons")
    implementation 'org.springframework.boot:spring-boot-starter'
    implementation("com.azure:azure-monitor-ingestion")
    implementation("com.azure:azure-identity")
    implementation('com.fasterxml.jackson.dataformat:jackson-dataformat-csv')
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
}

apply from: "${project.rootDir}/gradle/scripts/jib-build.gradle"