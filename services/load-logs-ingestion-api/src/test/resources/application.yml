spring:
  application:
    name: load-logs-ingestion-api
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

load-logs-ingestion-api:
  azure-config:
    azure-client-id: _DO_NOT_COMMIT_
    azure-client-secret: _DO_NOT_COMMIT_
    azure-tenant-id: tenant
  data-collection-config:
    dataCollectionEndpoint: https://commonsecuritylog-dce-znph.eastus-1.ingest.monitor.azure.com
    dataCollectionRuleId: dcr-7a46b9d4a6d7456bafbf9972cf24369d
    dataCollectionRuleStreamName: Custom-CommonSecurityLog_CL
  csv-input:
    test-file-class-path: /csv-files/panw.csv

# Enables file logging and disables console logging
#logging:
#  level:
#    ROOT: TRACE
#  file.name: ../../../../../logs/debug.log
#  pattern:
#    console:

# Below did not work. Attempted to set max netty connections to 10
#reactor:
#  netty:
#    pool:
#      maxConnections: 10