package com.illumio.data.logsingestionapi;

import com.azure.identity.ClientSecretCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class AzureCredentialsConfig {
    private final LogsIngestionApiConfiguration logsIngestionApiConfiguration;
    @Bean
    public ClientSecretCredential clientSecretCredential() {
        return new ClientSecretCredentialBuilder()
                .clientId(logsIngestionApiConfiguration.getAzureConfig()
                        .getAzureClientId())
                .clientSecret(logsIngestionApiConfiguration.getAzureConfig()
                        .getAzureClientSecret())
                .tenantId(logsIngestionApiConfiguration.getAzureConfig()
                        .getAzureTenantId())
                .build();
    }
}
