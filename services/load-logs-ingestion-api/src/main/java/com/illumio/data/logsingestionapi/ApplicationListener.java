package com.illumio.data.logsingestionapi;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ApplicationListener {
    private final LoadRunner loadRunner;

    @EventListener(ApplicationReadyEvent.class)
    public void handleReadyEvent() {
        log.info("Starting load test");
        loadRunner.run();
    }

    @EventListener(ContextClosedEvent.class)
    public void handleClosedEvent() {
        log.info("Stopping load test");
        loadRunner.stop();
    }
}
