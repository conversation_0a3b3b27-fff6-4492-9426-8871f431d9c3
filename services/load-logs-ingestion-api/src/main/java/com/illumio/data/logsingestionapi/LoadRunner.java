package com.illumio.data.logsingestionapi;

import com.azure.identity.ClientSecretCredential;
import com.azure.monitor.ingestion.LogsIngestionAsyncClient;
import com.azure.monitor.ingestion.LogsIngestionClientBuilder;
import com.azure.monitor.ingestion.models.LogsUploadOptions;
import com.fasterxml.jackson.databind.MappingIterator;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvParser;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import com.illumio.data.CommonSecurityLog;
import com.illumio.data.util.Util;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Component
@RequiredArgsConstructor
public class LoadRunner {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final CsvMapper CSV_MAPPER = new CsvMapper()
            .enable(CsvParser.Feature.IGNORE_TRAILING_UNMAPPABLE);
    private static final LogsUploadOptions logsUploadOptions = new LogsUploadOptions()
            .setLogsUploadErrorConsumer(uploadLogsError -> {
                log.warn("Error message {}", uploadLogsError.getResponseException().getMessage());
                log.warn("Total logs failed to upload = {}", uploadLogsError.getFailedLogs().size());
                // throw the exception here to abort uploading remaining logs
                // throw uploadLogsError.getResponseException();
            });

    private final LogsIngestionApiConfiguration logsIngestionApiConfiguration;
    private final ClientSecretCredential clientSecretCredential;
    private Disposable disposable;

    public void run() {
        runAsync();
    }

    @SneakyThrows
    private void runAsync() {
        // TODO configuration to read from json and csv
        List<Object> logs = getCommonSecurityLogFromJson();
//        List<Object> logs = getCommonSecurityLogFromCsv();
        // TODO configuration to run once, or cycle infinitely through payloads
        cycle(logs);
    }

    public void stop() {
        this.disposable.dispose();
    }

    @SneakyThrows
    private List<Object> getCommonSecurityLogFromJson() {
        CommonSecurityLog commonSecurityLog = OBJECT_MAPPER.readValue(
                LoadRunner.class.getClassLoader().getResourceAsStream("CommonSecurityLog.json"),
                CommonSecurityLog.class);
        List<Object> logs = new ArrayList<>();
        for (int i = 0; i < 200; i++) {
            logs.add(commonSecurityLog);
        }
        return logs;
    }

    @SneakyThrows
    private List<Object> getCommonSecurityLogFromCsv() {
        final File testCsvFile = Util.getFileFromClassPathOrAbsolutePath(
                logsIngestionApiConfiguration.getCsvInput().getTestFileClassPath(),
                logsIngestionApiConfiguration.getCsvInput().getTestFileAbsolutePath()
        );
        if (null == testCsvFile) {
            log.error("Couldn't find CSV file for resource logs");
            return Collections.emptyList();
        }

        List<CommonSecurityLog> commonSecurityLogs;
        CsvSchema csvSchema = CSV_MAPPER.schemaFor(CommonSecurityLog.class)
                .withHeader()
                .withColumnReordering(true);
        try (MappingIterator<CommonSecurityLog> iterator = CSV_MAPPER
                .disable(CsvParser.Feature.FAIL_ON_MISSING_HEADER_COLUMNS)
                .readerFor(CommonSecurityLog.class)
                .with(csvSchema)
                .readValues(testCsvFile)) {

            // read all
            commonSecurityLogs = iterator.readAll();
        } catch (IOException e) {
            log.error("Couldn't read CSV file for resource logs", e);
            throw e;
        }

        return new ArrayList<>(commonSecurityLogs);
    }

    private void cycle(List<Object> logs) {
        final String dataCollectionEndpoint = logsIngestionApiConfiguration.getDataCollectionConfig()
                .getDataCollectionEndpoint();
        final String dataCollectionRuleId = logsIngestionApiConfiguration.getDataCollectionConfig()
                .getDataCollectionRuleId();
        final String dataCollectionRuleStreamName = logsIngestionApiConfiguration.getDataCollectionConfig()
                .getDataCollectionRuleStreamName();
        final LogsIngestionAsyncClient asyncClient = new LogsIngestionClientBuilder()
                .endpoint(dataCollectionEndpoint)
                .credential(clientSecretCredential)
                .buildAsyncClient();

        this.disposable = Mono.just(logs)
                // comment out repeat() to run once
                .repeat()
                .flatMap(objects ->
                        asyncClient.upload(dataCollectionRuleId, dataCollectionRuleStreamName, logs, logsUploadOptions)
                                .publishOn(Schedulers.boundedElastic())
                                .doOnEach(voidSignal -> log.info("Uploaded logs")))
                .subscribe();
    }
}
