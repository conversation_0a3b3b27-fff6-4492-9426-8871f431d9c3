package com.illumio.data.logsingestionapi;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "load-logs-ingestion-api")
@Data
public class LogsIngestionApiConfiguration {
    private DataCollectionConfig dataCollectionConfig = new DataCollectionConfig();
    private AzureConfig azureConfig = new AzureConfig();
    private CsvInput csvInput = new CsvInput();

    @Getter
    @Setter
    public static class DataCollectionConfig {
        private String dataCollectionEndpoint;
        private String dataCollectionRuleId;
        private String dataCollectionRuleStreamName;
    }

    @Getter
    @Setter
    public static class CsvInput {
        private String testFileClassPath;
        private String testFileAbsolutePath;
    }

    @Getter
    @Setter
    public static class AzureConfig {
        private String azureClientId;
        private String azureClientSecret;
        private String azureTenantId;
    }
}
