{"Activity": "activity", "AdditionalExtensions": "extension", "ApplicationProtocol": "application", "CollectorHostName": "collector", "CommunicationDirection": "communicationDirection", "Computer": "computer", "DestinationDnsDomain": "www.illumio.com", "DestinationHostName": "host.illumio.com", "DestinationIP": "********", "DestinationMACAddress": " 00-B0-D0-63-C2-26", "DestinationNTDomain": "dummydata", "DestinationPort": "8080", "DestinationProcessId": "65000", "DestinationProcessName": "dummydata", "DestinationServiceName": "dummydata", "DestinationTranslatedAddress": "dummydata", "DestinationTranslatedPort": "8080", "DestinationUserID": "dummydata", "DestinationUserName": "dummydata", "DestinationUserPrivileges": "dummydata", "DeviceAction": "dummydata", "DeviceAddress": "dummydata", "DeviceCustomDate1": "dummydata", "DeviceCustomDate1Label": "dummydata", "DeviceCustomDate2": "dummydata", "DeviceCustomDate2Label": "dummydata", "DeviceCustomFloatingPoint1": 1.0001, "DeviceCustomFloatingPoint1Label": "dummydata", "DeviceCustomFloatingPoint2": 1.0001, "DeviceCustomFloatingPoint2Label": "dummydata", "DeviceCustomFloatingPoint3": 1.0001, "DeviceCustomFloatingPoint3Label": "dummydata", "DeviceCustomFloatingPoint4": 1.0001, "DeviceCustomFloatingPoint4Label": "dummydata", "DeviceCustomIPv6Address1": "dummydata", "DeviceCustomIPv6Address1Label": "dummydata", "DeviceCustomIPv6Address2": "dummydata", "DeviceCustomIPv6Address2Label": "dummydata", "DeviceCustomIPv6Address3": "dummydata", "DeviceCustomIPv6Address3Label": "dummydata", "DeviceCustomIPv6Address4": "dummydata", "DeviceCustomIPv6Address4Label": "dummydata", "DeviceCustomNumber1": 100, "DeviceCustomNumber1Label": "dummydata", "DeviceCustomNumber2": 100, "DeviceCustomNumber2Label": "dummydata", "DeviceCustomNumber3": 100, "DeviceCustomNumber3Label": "dummydata", "DeviceCustomString1": "dummydata", "DeviceCustomString1Label": "dummydata", "DeviceCustomString2": "dummydata", "DeviceCustomString2Label": "dummydata", "DeviceCustomString3": "dummydata", "DeviceCustomString3Label": "dummydata", "DeviceCustomString4": "dummydata", "DeviceCustomString4Label": "dummydata", "DeviceCustomString5": "dummydata", "DeviceCustomString5Label": "dummydata", "DeviceCustomString6": "dummydata", "DeviceCustomString6Label": "dummydata", "DeviceDnsDomain": "dummydata", "DeviceEventCategory": "dummydata", "DeviceEventClassID": "dummydata", "DeviceExternalID": "dummydata", "DeviceFacility": "dummydata", "DeviceInboundInterface": "dummydata", "DeviceMacAddress": "dummydata", "DeviceName": "dummydata", "DeviceNtDomain": "dummydata", "DeviceOutboundInterface": "dummydata", "DevicePayloadId": "dummydata", "DeviceProduct": "dummydata", "DeviceTimeZone": "dummydata", "DeviceTranslatedAddress": "dummydata", "DeviceVendor": "dummydata", "DeviceVersion": "dummydata", "EndTime": "dummydata", "EventCount": 100, "EventOutcome": "dummydata", "EventType": 0, "ExternalID": 100, "ExtID": "dummydata", "FieldDeviceCustomNumber1": 100, "FieldDeviceCustomNumber2": 100, "FieldDeviceCustomNumber3": 100, "FileCreateTime": "dummydata", "FileHash": "dummydata", "FileID": "dummydata", "FileModificationTime": "dummydata", "FileName": "dummydata", "FilePath": "dummydata", "FilePermission": "dummydata", "FileSize": 100, "FileType": "dummydata", "FlexDate1": "dummydata", "FlexDate1Label": "dummydata", "FlexNumber1": 100, "FlexNumber1Label": "dummydata", "FlexNumber2": 100, "FlexNumber2Label": "dummydata", "FlexString1": "dummydata", "FlexString1Label": "dummydata", "FlexString2": "dummydata", "FlexString2Label": "dummydata", "IndicatorThreatType": "dummydata", "LogSeverity": "dummydata", "MaliciousIP": "dummydata", "MaliciousIPCountry": "dummydata", "MaliciousIPLatitude": 1.0001, "MaliciousIPLongitude": 1.0001, "Message": "dummydata", "OldFileCreateTime": "dummydata", "OldFileHash": "dummydata", "OldFileID": "dummydata", "OldFileModificationTime": "dummydata", "OldFileName": "dummydata", "OldFilePath": "dummydata", "OldFilePermission": "dummydata", "OldFileSize": 100, "OldFileType": "dummydata", "OriginalLogSeverity": "dummydata", "ProcessID": 100, "ProcessName": "dummydata", "Protocol": "dummydata", "Reason": "dummydata", "ReceiptTime": "2024/04/30 10:42:36", "ReceivedBytes": 100, "RemoteIP": "dummydata", "RemotePort": "dummydata", "ReportReferenceLink": "dummydata", "RequestClientApplication": "dummydata", "RequestContext": "dummydata", "RequestCookies": "dummydata", "RequestMethod": "dummydata", "RequestURL": "dummydata", "SentBytes": 100, "SimplifiedDeviceAction": "dummydata", "SourceDnsDomain": "dummydata", "SourceHostName": "dummydata", "SourceIP": "dummydata", "SourceMACAddress": "dummydata", "SourceNTDomain": "dummydata", "SourcePort": 100, "SourceProcessId": 100, "SourceProcessName": "dummydata", "SourceServiceName": "dummydata", "SourceSystem": "dummydata", "SourceTranslatedAddress": "dummydata", "SourceTranslatedPort": 100, "SourceUserID": "dummydata", "SourceUserName": "dummydata", "SourceUserPrivileges": "dummydata", "StartTime": "dummydata", "ThreatConfidence": "dummydata", "ThreatDescription": "dummydata", "ThreatSeverity": 100}