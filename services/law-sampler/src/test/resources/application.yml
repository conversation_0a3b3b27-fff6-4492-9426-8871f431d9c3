logging:
  level:
    ROOT: INFO

spring:
  application:
    name: law-sampler
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

law-sampler:
  kafka-common-config:
    bootstrapServers: localhost:9092
    isSasl: false

  kafka-sender-config:
    common-security-log-sink-topic: illumio-common-security-log

  law-query-config:
    workspaceId: 364ddedb-e0d6-408f-a7d4-f7e6a6cfbb1a
    query: CommonSecurityLog_CL
    queryTimeInterval: 24h
    sampleInitialDelay: 5s
    sampleFrequency: 5m

  azure-config:
    azure-client-id: _DO_NOT_COMMIT_
    azure-client-secret: _DO_NOT_COMMIT_
    azure-tenant-id: tenant