package com.illumio.data;

import static org.mockito.Mockito.when;

import com.fasterxml.jackson.databind.json.JsonMapper;
import com.illumio.data.components.CommonSecurityLogWriter;
import com.illumio.data.components.LawClient;
import com.illumio.data.configuration.LawSamplerConfiguration;
import com.illumio.data.pojo.CommonSecurityLogLAW;
import com.illumio.data.pojo.CommonSecurityLogRecordsLAW;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Answers;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import reactor.core.publisher.Flux;
import reactor.kafka.sender.KafkaSender;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class LawSamplerPipelineTest {
    @Mock(answer = Answers.RETURNS_DEEP_STUBS) LawSamplerConfiguration lawSamplerConfiguration;
    @Mock KafkaSender<String, String> kafkaSender;
    @Mock LawClient lawClient;
    CommonSecurityLogWriter commonSecurityLogWriter;
    LawSamplerPipeline lawSamplerPipeline;

    @BeforeEach
    void setup() {
        when(lawSamplerConfiguration.getKafkaSenderConfig().getCommonSecurityLogSinkTopic())
                .thenReturn("sink");
        when(lawSamplerConfiguration.getLAWQueryConfig().getSampleInitialDelay())
                .thenReturn(Duration.ofSeconds(0));
        when(lawSamplerConfiguration.getLAWQueryConfig().getSampleFrequency())
                .thenReturn(Duration.ofMinutes(1));

        commonSecurityLogWriter = new CommonSecurityLogWriter(new JsonMapper());
        lawSamplerPipeline =
                new LawSamplerPipeline(
                        lawSamplerConfiguration, kafkaSender, lawClient, commonSecurityLogWriter);

    }

    @Test
    void testHappyPathWithSimulatedTime() {
        List<CommonSecurityLogRecordsLAW> commonSecurityLogRecordsLAWList = List.of(
                CommonSecurityLogRecordsLAW.builder()
                        .records(List.of(CommonSecurityLogLAW.builder()
                                .SourceIP("********")
                                .DestinationIP("********")
                                .build()))
                        .build());
        when(lawClient.fetchLatestLogsAsFlux()).thenReturn(Flux.fromIterable(commonSecurityLogRecordsLAWList));
        StepVerifier.withVirtualTime(() -> lawSamplerPipeline.fetchLogsPeriodically()
                        .take(2))
                .thenAwait(Duration.ofMinutes(2))
                .expectNextMatches(commonSecurityLogRecordsLAW -> {
                    return commonSecurityLogRecordsLAW.getRecords().size() == 1 &&
                            commonSecurityLogRecordsLAW.getRecords().get(0).getSourceIP().equals("********") &&
                            commonSecurityLogRecordsLAW.getRecords().get(0).getDestinationIP().equals("********");
                })
                .expectNextMatches(commonSecurityLogRecordsLAW -> {
                    return commonSecurityLogRecordsLAW.getRecords().size() == 1 &&
                            commonSecurityLogRecordsLAW.getRecords().get(0).getSourceIP().equals("********") &&
                            commonSecurityLogRecordsLAW.getRecords().get(0).getDestinationIP().equals("********");
                })
                .expectComplete()
                .verify(Duration.ofSeconds(1));
    }
}
