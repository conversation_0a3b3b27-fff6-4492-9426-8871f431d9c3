logging:
  level:
    ROOT: INFO

spring:
  application:
    name: law-sampler
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none

law-sampler:
  kafka-common-config:
    bootstrapServers: localhost:9092
    isSasl: false

  kafka-sender-config:
    common-security-log-sink-topic: illumio-common-security-log
    maxRetryAttempts: 5
    retryBackoffDuration: 3s

  law-query-config:
    workspaceId: 364ddedb-e0d6-408f-a7d4-f7e6a6cfbb1a
    query: CommonSecurityLog_CL | extend SrcZone = DeviceCustomString4, DstZone = DeviceCustomString5 | take 25000
    queryTimeInterval: 24h
    sampleInitialDelay: 0s
    sampleFrequency: 1m
    maxRetryAttempts: 3
    retryBackoffDuration: 1s
    httpLogDetailLevel: basic