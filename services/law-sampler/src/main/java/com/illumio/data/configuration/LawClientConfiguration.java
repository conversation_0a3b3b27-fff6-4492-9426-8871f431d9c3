package com.illumio.data.configuration;

import com.azure.core.credential.TokenCredential;
import com.azure.core.http.policy.HttpLogDetailLevel;
import com.azure.core.http.policy.HttpLogOptions;
import com.azure.monitor.query.LogsQueryAsyncClient;
import com.azure.monitor.query.LogsQueryClientBuilder;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@RequiredArgsConstructor
public class LawClientConfiguration {
    private final TokenCredential tokenCredential;
    private final LawSamplerConfiguration lawSamplerConfiguration;

    @Bean
    public LogsQueryAsyncClient logsQueryAsyncClient() {
        LogsQueryClientBuilder logsQueryClientBuilder = new LogsQueryClientBuilder();
        logsQueryClientBuilder.credential(tokenCredential);
        if (null != lawSamplerConfiguration.getLAWQueryConfig().getHttpLogDetailLevel()) {
            logsQueryClientBuilder.httpLogOptions(
                    new HttpLogOptions()
                            .setLogLevel(
                                    HttpLogDetailLevel.valueOf(
                                            lawSamplerConfiguration
                                                    .getLAWQueryConfig()
                                                    .getHttpLogDetailLevel()
                                                    .toUpperCase())));
        }

        return logsQueryClientBuilder.buildAsyncClient();
    }
}
