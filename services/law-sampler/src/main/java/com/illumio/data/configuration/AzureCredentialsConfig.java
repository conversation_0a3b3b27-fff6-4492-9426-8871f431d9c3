package com.illumio.data.configuration;

import com.azure.core.credential.TokenCredential;
import com.azure.identity.*;

import lombok.RequiredArgsConstructor;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Optional;

@Configuration
@RequiredArgsConstructor
public class AzureCredentialsConfig {
    private final LawSamplerConfiguration lawSamplerConfiguration;
    @Bean
    public TokenCredential tokenCredential() {
        if (Optional.ofNullable(lawSamplerConfiguration.getAzureConfig())
                .map(LawSamplerConfiguration.AzureConfig::getIsWorkloadIdentity)
                .orElse(false)) {
            TokenCredential tokenCredential = getWorkloadIdentity();
            return tokenCredential;
        } else if (Optional.ofNullable(lawSamplerConfiguration.getAzureConfig())
                .map(LawSamplerConfiguration.AzureConfig::getIsManagedIdentity)
                .orElse(false)) {
            TokenCredential tokenCredential = new ManagedIdentityCredentialBuilder()
                    .clientId(lawSamplerConfiguration.getAzureConfig().getManagedIdentityClientId())
                    .build();
            return tokenCredential;
        } else {
            ClientSecretCredential clientSecretCredential = new ClientSecretCredentialBuilder()
                    .clientId(lawSamplerConfiguration.getAzureConfig().getAzureClientId())
                    .clientSecret(lawSamplerConfiguration.getAzureConfig().getAzureClientSecret())
                    .tenantId(lawSamplerConfiguration.getAzureConfig().getAzureTenantId())
                    .build();
            return clientSecretCredential;
        }
    }

    private TokenCredential getWorkloadIdentity() {
        LawSamplerConfiguration.AzureConfig.WorkloadIdentity workloadIdentity =
                lawSamplerConfiguration.getAzureConfig().getWorkloadIdentity();

        TokenCredential tokenCredential =
                new WorkloadIdentityCredentialBuilder()
                        .clientId(workloadIdentity.getClientId())
                        .tenantId(workloadIdentity.getTenantId())
                        .tokenFilePath(workloadIdentity.getTokenFilePath())
                        .build();
        return tokenCredential;
    }
}
