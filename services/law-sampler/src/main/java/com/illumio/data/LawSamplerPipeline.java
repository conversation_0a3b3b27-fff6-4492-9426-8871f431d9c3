package com.illumio.data;

import com.illumio.data.components.CommonSecurityLogWriter;
import com.illumio.data.components.LawClient;
import com.illumio.data.configuration.LawSamplerConfiguration;
import com.illumio.data.pojo.CommonSecurityLogRecordsLAW;

import lombok.extern.slf4j.Slf4j;

import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.stereotype.Component;

import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderRecord;
import reactor.kafka.sender.SenderResult;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.time.Instant;
import java.util.NoSuchElementException;
import java.util.Optional;

@Slf4j
@Component
public class LawSamplerPipeline {

    private final LawSamplerConfiguration lawSamplerConfiguration;
    private final KafkaSender<String, String> kafkaSender;
    private final LawClient lawClient;
    private final CommonSecurityLogWriter commonSecurityLogWriter;

    private final String sinkTopic;
    private final Duration sampleInitialDelay;
    private final Duration sampleFrequency;

    private Disposable disposable;

    public LawSamplerPipeline(LawSamplerConfiguration lawSamplerConfiguration,
                              KafkaSender<String, String> kafkaSender,
                              LawClient lawClient,
                              CommonSecurityLogWriter commonSecurityLogWriter) {
        this.lawSamplerConfiguration = lawSamplerConfiguration;
        this.kafkaSender = kafkaSender;
        this.lawClient = lawClient;
        this.commonSecurityLogWriter = commonSecurityLogWriter;
        this.sinkTopic = getSinkTopic();
        this.sampleInitialDelay = lawSamplerConfiguration.getLAWQueryConfig().getSampleInitialDelay();
        this.sampleFrequency = lawSamplerConfiguration.getLAWQueryConfig().getSampleFrequency();
    }

    public void start() {
        log.info("Starting sampler with logs query config: {}", lawSamplerConfiguration.getLAWQueryConfig());
        this.disposable = startInternal()
                .subscribe();
    }

    /**
     * Fail early if sink topics are not defined.
     * @return sink topic name
     */
    private String getSinkTopic() {
        return Optional.of(lawSamplerConfiguration)
                .map(LawSamplerConfiguration::getKafkaSenderConfig)
                .map(LawSamplerConfiguration.KafkaSenderConfig::getCommonSecurityLogSinkTopic)
                .orElseThrow(() -> new NoSuchElementException(
                        "No sink topic configured. Please make sure " +
                                "law-sampler.kafka-sender-config.common-security-log-sink-topic is set."));
    }

    public Flux<SenderResult<String>> startInternal() {
        return kafkaSender
                .send(fetchLogsPeriodically()
                        .flatMap(this::createSenderRecord))
                .doOnNext(
                        stringSenderResult ->
                                log.info(
                                        "Produced message {}", stringSenderResult.recordMetadata()))
                .retryWhen(
                        Retry.backoff(
                                lawSamplerConfiguration.getKafkaSenderConfig().getMaxRetryAttempts(),
                                lawSamplerConfiguration
                                        .getKafkaSenderConfig()
                                        .getRetryBackoffDuration()));
    }

    public Flux<CommonSecurityLogRecordsLAW> fetchLogsPeriodically() {
        return Flux.interval(sampleInitialDelay, sampleFrequency)
                .publishOn(Schedulers.boundedElastic())
                .doOnNext(aLong -> log.info("Triggered event {} at {}", aLong, Instant.now()))
                .flatMap(__ -> lawClient.fetchLatestLogsAsFlux());
    }

    private Mono<SenderRecord<String, String, String>> createSenderRecord(
            CommonSecurityLogRecordsLAW commonSecurityLogRecords) {
        return Mono.just(commonSecurityLogRecords)
                .flatMap(commonSecurityLogWriter::writeAsString)
                .map(value -> new ProducerRecord<String, String>(sinkTopic, value))
                .map(producerRecord -> SenderRecord.create(producerRecord, null));
    }

    public void stop() {
        this.disposable.dispose();
    }
}
