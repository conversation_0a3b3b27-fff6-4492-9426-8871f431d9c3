package com.illumio.data.configuration;

import lombok.RequiredArgsConstructor;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import reactor.kafka.sender.KafkaSender;
import reactor.kafka.sender.SenderOptions;

import java.util.HashMap;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class KafkaSenderConfiguration {
    private final LawSamplerConfiguration lawSamplerConfiguration;

    @Bean
    public KafkaSender<String, String> kafkaSender() {
        Map<String, Object> producerProps = new HashMap<>();
        producerProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, lawSamplerConfiguration.getKafkaCommonConfig().getBootstrapServers());
        producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        if (null != lawSamplerConfiguration.getKafkaCommonConfig().getIsSasl()
                && lawSamplerConfiguration.getKafkaCommonConfig().getIsSasl()) {
            producerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            producerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            producerProps.put(SaslConfigs.SASL_JAAS_CONFIG, lawSamplerConfiguration.getKafkaCommonConfig().getSaslJaasConfig());
        }

        SenderOptions<String, String> senderOptions =
                SenderOptions.<String, String>create(producerProps)
                        // Non-blocking back-pressure
                        .maxInFlight(1024);

        return KafkaSender.create(senderOptions);
    }
}
