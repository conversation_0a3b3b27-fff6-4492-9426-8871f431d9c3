package com.illumio.data.pojo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.jackson.Jacksonized;

import java.time.OffsetDateTime;

/**
 * The difference between this class and {@link com.illumio.data.CommonSecurityLog}
 * is that TimeGenerated is of type {@link OffsetDateTime} instead of String.
 * LogsQueryClient is strict about deserializing this field into OffsetDateTime.
 */
@Data
@Jacksonized
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class CommonSecurityLogLAW {
    @JsonProperty("Activity")
    private String Activity;
    @JsonProperty("AdditionalExtensions")
    private String AdditionalExtensions;
    @JsonProperty("ApplicationProtocol")
    private String ApplicationProtocol;
    @JsonProperty("_BilledSize")
    private Double _BilledSize;
    @JsonProperty("CollectorHostName")
    private String CollectorHostName;
    @JsonProperty("CommunicationDirection")
    private String CommunicationDirection;
    @JsonProperty("Computer")
    private String Computer;
    @JsonProperty("DestinationDnsDomain")
    private String DestinationDnsDomain;
    @JsonProperty("DestinationHostName")
    private String DestinationHostName;
    @JsonProperty("DestinationIP")
    private String DestinationIP;
    @JsonProperty("DestinationMACAddress")
    private String DestinationMACAddress;
    @JsonProperty("DestinationNTDomain")
    private String DestinationNTDomain;
    @JsonProperty("DestinationPort")
    private Integer DestinationPort;
    @JsonProperty("DestinationProcessId")
    private Integer DestinationProcessId;
    @JsonProperty("DestinationProcessName")
    private String DestinationProcessName;
    @JsonProperty("DestinationServiceName")
    private String DestinationServiceName;
    @JsonProperty("DestinationTranslatedAddress")
    private String DestinationTranslatedAddress;
    @JsonProperty("DestinationTranslatedPort")
    private Integer DestinationTranslatedPort;
    @JsonProperty("DestinationUserID")
    private String DestinationUserID;
    @JsonProperty("DestinationUserName")
    private String DestinationUserName;
    @JsonProperty("DestinationUserPrivileges")
    private String DestinationUserPrivileges;
    @JsonProperty("DeviceAction")
    private String DeviceAction;
    @JsonProperty("DeviceAddress")
    private String DeviceAddress;
    @JsonProperty("DeviceCustomDate1")
    private String DeviceCustomDate1;
    @JsonProperty("DeviceCustomDate1Label")
    private String DeviceCustomDate1Label;
    @JsonProperty("DeviceCustomDate2")
    private String DeviceCustomDate2;
    @JsonProperty("DeviceCustomDate2Label")
    private String DeviceCustomDate2Label;
    @JsonProperty("DeviceCustomFloatingPoint1")
    private Double DeviceCustomFloatingPoint1;
    @JsonProperty("DeviceCustomFloatingPoint1Label")
    private String DeviceCustomFloatingPoint1Label;
    @JsonProperty("DeviceCustomFloatingPoint2")
    private Double DeviceCustomFloatingPoint2;
    @JsonProperty("DeviceCustomFloatingPoint2Label")
    private String DeviceCustomFloatingPoint2Label;
    @JsonProperty("DeviceCustomFloatingPoint3")
    private Double DeviceCustomFloatingPoint3;
    @JsonProperty("DeviceCustomFloatingPoint3Label")
    private String DeviceCustomFloatingPoint3Label;
    @JsonProperty("DeviceCustomFloatingPoint4")
    private Double DeviceCustomFloatingPoint4;
    @JsonProperty("DeviceCustomFloatingPoint4Label")
    private String DeviceCustomFloatingPoint4Label;
    @JsonProperty("DeviceCustomIPv6Address1")
    private String DeviceCustomIPv6Address1;
    @JsonProperty("DeviceCustomIPv6Address1Label")
    private String DeviceCustomIPv6Address1Label;
    @JsonProperty("DeviceCustomIPv6Address2")
    private String DeviceCustomIPv6Address2;
    @JsonProperty("DeviceCustomIPv6Address2Label")
    private String DeviceCustomIPv6Address2Label;
    @JsonProperty("DeviceCustomIPv6Address3")
    private String DeviceCustomIPv6Address3;
    @JsonProperty("DeviceCustomIPv6Address3Label")
    private String DeviceCustomIPv6Address3Label;
    @JsonProperty("DeviceCustomIPv6Address4")
    private String DeviceCustomIPv6Address4;
    @JsonProperty("DeviceCustomIPv6Address4Label")
    private String DeviceCustomIPv6Address4Label;
    @JsonProperty("DeviceCustomNumber1")
    private Integer DeviceCustomNumber1;
    @JsonProperty("DeviceCustomNumber1Label")
    private String DeviceCustomNumber1Label;
    @JsonProperty("DeviceCustomNumber2")
    private Integer DeviceCustomNumber2;
    @JsonProperty("DeviceCustomNumber2Label")
    private String DeviceCustomNumber2Label;
    @JsonProperty("DeviceCustomNumber3")
    private Integer DeviceCustomNumber3;
    @JsonProperty("DeviceCustomNumber3Label")
    private String DeviceCustomNumber3Label;
    @JsonProperty("DeviceCustomString1")
    private String DeviceCustomString1;
    @JsonProperty("DeviceCustomString1Label")
    private String DeviceCustomString1Label;
    @JsonProperty("DeviceCustomString2")
    private String DeviceCustomString2;
    @JsonProperty("DeviceCustomString2Label")
    private String DeviceCustomString2Label;
    @JsonProperty("DeviceCustomString3")
    private String DeviceCustomString3;
    @JsonProperty("DeviceCustomString3Label")
    private String DeviceCustomString3Label;
    @JsonProperty("DeviceCustomString4")
    private String DeviceCustomString4;
    @JsonProperty("DeviceCustomString4Label")
    private String DeviceCustomString4Label;
    @JsonProperty("DeviceCustomString5")
    private String DeviceCustomString5;
    @JsonProperty("DeviceCustomString5Label")
    private String DeviceCustomString5Label;
    @JsonProperty("DeviceCustomString6")
    private String DeviceCustomString6;
    @JsonProperty("DeviceCustomString6Label")
    private String DeviceCustomString6Label;
    @JsonProperty("DeviceDnsDomain")
    private String DeviceDnsDomain;
    @JsonProperty("DeviceEventCategory")
    private String DeviceEventCategory;
    @JsonProperty("DeviceEventClassID")
    private String DeviceEventClassID;
    @JsonProperty("DeviceExternalID")
    private String DeviceExternalID;
    @JsonProperty("DeviceFacility")
    private String DeviceFacility;
    @JsonProperty("DeviceInboundInterface")
    private String DeviceInboundInterface;
    @JsonProperty("DeviceMacAddress")
    private String DeviceMacAddress;
    @JsonProperty("DeviceName")
    private String DeviceName;
    @JsonProperty("DeviceNtDomain")
    private String DeviceNtDomain;
    @JsonProperty("DeviceOutboundInterface")
    private String DeviceOutboundInterface;
    @JsonProperty("DevicePayloadId")
    private String DevicePayloadId;
    @JsonProperty("DeviceProduct")
    private String DeviceProduct;
    @JsonProperty("DeviceTimeZone")
    private String DeviceTimeZone;
    @JsonProperty("DeviceTranslatedAddress")
    private String DeviceTranslatedAddress;
    @JsonProperty("DeviceVendor")
    private String DeviceVendor;
    @JsonProperty("DeviceVersion")
    private String DeviceVersion;
    @JsonProperty("EndTime")
    private OffsetDateTime EndTime;
    @JsonProperty("EventCount")
    private Integer EventCount;
    @JsonProperty("EventOutcome")
    private String EventOutcome;
    @JsonProperty("EventType")
    private Integer EventType;
    @JsonProperty("ExternalID")
    private Integer ExternalID;
    @JsonProperty("ExtID")
    private String ExtID;
    @JsonProperty("FieldDeviceCustomNumber1")
    private Long FieldDeviceCustomNumber1;
    @JsonProperty("FieldDeviceCustomNumber2")
    private Long FieldDeviceCustomNumber2;
    @JsonProperty("FieldDeviceCustomNumber3")
    private Long FieldDeviceCustomNumber3;
    @JsonProperty("FileCreateTime")
    private String FileCreateTime;
    @JsonProperty("FileHash")
    private String FileHash;
    @JsonProperty("FileID")
    private String FileID;
    @JsonProperty("FileModificationTime")
    private String FileModificationTime;
    @JsonProperty("FileName")
    private String FileName;
    @JsonProperty("FilePath")
    private String FilePath;
    @JsonProperty("FilePermission")
    private String FilePermission;
    @JsonProperty("FileSize")
    private Integer FileSize;
    @JsonProperty("FileType")
    private String FileType;
    @JsonProperty("FlexDate1")
    private String FlexDate1;
    @JsonProperty("FlexDate1Label")
    private String FlexDate1Label;
    @JsonProperty("FlexNumber1")
    private Integer FlexNumber1;
    @JsonProperty("FlexNumber1Label")
    private String FlexNumber1Label;
    @JsonProperty("FlexNumber2")
    private Integer FlexNumber2;
    @JsonProperty("FlexNumber2Label")
    private String FlexNumber2Label;
    @JsonProperty("FlexString1")
    private String FlexString1;
    @JsonProperty("FlexString1Label")
    private String FlexString1Label;
    @JsonProperty("FlexString2")
    private String FlexString2;
    @JsonProperty("FlexString2Label")
    private String FlexString2Label;
    @JsonProperty("IndicatorThreatType")
    private String IndicatorThreatType;
    @JsonProperty("_IsBillable")
    private String _IsBillable;
    @JsonProperty("LogSeverity")
    private String LogSeverity;
    @JsonProperty("MaliciousIP")
    private String MaliciousIP;
    @JsonProperty("MaliciousIPCountry")
    private String MaliciousIPCountry;
    @JsonProperty("MaliciousIPLatitude")
    private Double MaliciousIPLatitude;
    @JsonProperty("MaliciousIPLongitude")
    private Double MaliciousIPLongitude;
    @JsonProperty("Message")
    private String Message;
    @JsonProperty("OldFileCreateTime")
    private String OldFileCreateTime;
    @JsonProperty("OldFileHash")
    private String OldFileHash;
    @JsonProperty("OldFileID")
    private String OldFileID;
    @JsonProperty("OldFileModificationTime")
    private String OldFileModificationTime;
    @JsonProperty("OldFileName")
    private String OldFileName;
    @JsonProperty("OldFilePath")
    private String OldFilePath;
    @JsonProperty("OldFilePermission")
    private String OldFilePermission;
    @JsonProperty("OldFileSize")
    private Integer OldFileSize;
    @JsonProperty("OldFileType")
    private String OldFileType;
    @JsonProperty("OriginalLogSeverity")
    private String OriginalLogSeverity;
    @JsonProperty("ProcessID")
    private Integer ProcessID;
    @JsonProperty("ProcessName")
    private String ProcessName;
    @JsonProperty("Protocol")
    private String Protocol;
    @JsonProperty("Reason")
    private String Reason;
    @JsonProperty("ReceiptTime")
    private String ReceiptTime;
    @JsonProperty("ReceivedBytes")
    private Long ReceivedBytes;
    @JsonProperty("RemoteIP")
    private String RemoteIP;
    @JsonProperty("RemotePort")
    private String RemotePort;
    @JsonProperty("ReportReferenceLink")
    private String ReportReferenceLink;
    @JsonProperty("RequestClientApplication")
    private String RequestClientApplication;
    @JsonProperty("RequestContext")
    private String RequestContext;
    @JsonProperty("RequestCookies")
    private String RequestCookies;
    @JsonProperty("RequestMethod")
    private String RequestMethod;
    @JsonProperty("RequestURL")
    private String RequestURL;
    @JsonProperty("_ResourceId")
    private String _ResourceId;
    @JsonProperty("SentBytes")
    private Long SentBytes;
    @JsonProperty("SimplifiedDeviceAction")
    private String SimplifiedDeviceAction;
    @JsonProperty("SourceDnsDomain")
    private String SourceDnsDomain;
    @JsonProperty("SourceHostName")
    private String SourceHostName;
    @JsonProperty("SourceIP")
    private String SourceIP;
    @JsonProperty("SourceMACAddress")
    private String SourceMACAddress;
    @JsonProperty("SourceNTDomain")
    private String SourceNTDomain;
    @JsonProperty("SourcePort")
    private Integer SourcePort;
    @JsonProperty("SourceProcessId")
    private Integer SourceProcessId;
    @JsonProperty("SourceProcessName")
    private String SourceProcessName;
    @JsonProperty("SourceServiceName")
    private String SourceServiceName;
    @JsonProperty("SourceSystem")
    private String SourceSystem;
    @JsonProperty("SourceTranslatedAddress")
    private String SourceTranslatedAddress;
    @JsonProperty("SourceTranslatedPort")
    private Integer SourceTranslatedPort;
    @JsonProperty("SourceUserID")
    private String SourceUserID;
    @JsonProperty("SourceUserName")
    private String SourceUserName;
    @JsonProperty("SourceUserPrivileges")
    private String SourceUserPrivileges;
    @JsonProperty("StartTime")
    private OffsetDateTime StartTime;
    @JsonProperty("_SubscriptionId")
    private String _SubscriptionId;
    @JsonProperty("TenantId")
    private String TenantId;
    @JsonProperty("ThreatConfidence")
    private String ThreatConfidence;
    @JsonProperty("ThreatDescription")
    private String ThreatDescription;
    @JsonProperty("ThreatSeverity")
    private Integer ThreatSeverity;
    @JsonProperty("TimeGenerated")
    private OffsetDateTime TimeGenerated;
    @JsonProperty("Type")
    private String Type;
}
