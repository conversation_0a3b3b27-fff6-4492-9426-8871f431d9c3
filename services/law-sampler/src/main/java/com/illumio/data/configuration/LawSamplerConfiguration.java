package com.illumio.data.configuration;

import lombok.*;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;

@Configuration
@ConfigurationProperties(prefix = "law-sampler")
@Data
public class LawSamplerConfiguration {
    private final KafkaCommonConfig kafkaCommonConfig;
    private final KafkaSenderConfig kafkaSenderConfig;
    private final AzureConfig azureConfig;
    private final LAWQueryConfig LAWQueryConfig;

    @Configuration
    @Getter
    @Setter
    public static class KafkaCommonConfig {
        private String bootstrapServers;
        private Boolean isSasl = false;
        private String saslJaasConfig;
    }

    @Configuration
    @Getter
    @Setter
    public static class KafkaSenderConfig {
        private String commonSecurityLogSinkTopic;
        private Integer maxRetryAttempts = 5;
        private Duration retryBackoffDuration = Duration.ofSeconds(3);
    }

    @Configuration
    @Getter
    @Setter
    public static class AzureConfig {
        private String azureClientId;
        private String azureClientSecret;
        private String azureTenantId;
        private Boolean isManagedIdentity;
        private String managedIdentityClientId;
        private Boolean isWorkloadIdentity;
        private WorkloadIdentity workloadIdentity;

        @Configuration
        @Getter
        @Setter
        public static class WorkloadIdentity {
            private String clientId;
            private String tenantId;
            private String tokenFilePath;
        }
    }

    @Configuration
    @Getter
    @Setter
    @ToString
    public static class LAWQueryConfig {
        private Duration sampleInitialDelay;
        private Duration sampleFrequency;
        private String workspaceId;
        private String query;
        private Duration queryTimeInterval;
        private Duration queryServerTimeout = Duration.ofMinutes(10);
        private Integer maxRetryAttempts = 5;
        private Duration retryBackoffDuration = Duration.ofSeconds(3);
        private String httpLogDetailLevel = "basic";
    }
}
