package com.illumio.data.components;

import com.azure.monitor.query.LogsQueryAsyncClient;
import com.azure.monitor.query.models.LogsQueryOptions;
import com.azure.monitor.query.models.QueryTimeInterval;
import com.illumio.data.configuration.LawSamplerConfiguration;
import com.illumio.data.pojo.CommonSecurityLogLAW;
import com.illumio.data.pojo.CommonSecurityLogRecordsLAW;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.util.Collections;

@Component
@RequiredArgsConstructor
@Slf4j
public class LawClient {
    private final LawSamplerConfiguration lawSamplerConfiguration;
    private final LogsQueryAsyncClient logsQueryAsyncClient;

    public Flux<CommonSecurityLogRecordsLAW> fetchLatestLogsAsFlux() {
        LogsQueryOptions logsQueryOptions = new LogsQueryOptions()
                .setServerTimeout(lawSamplerConfiguration.getLAWQueryConfig().getQueryServerTimeout());
        return logsQueryAsyncClient
                .queryWorkspace(
                        lawSamplerConfiguration.getLAWQueryConfig().getWorkspaceId(),
                        lawSamplerConfiguration.getLAWQueryConfig().getQuery(),
                        new QueryTimeInterval(
                                lawSamplerConfiguration
                                        .getLAWQueryConfig()
                                        .getQueryTimeInterval()),
                        CommonSecurityLogLAW.class,
                        logsQueryOptions)
                .retryWhen(
                        Retry.backoff(
                                lawSamplerConfiguration.getLAWQueryConfig().getMaxRetryAttempts(),
                                lawSamplerConfiguration
                                        .getLAWQueryConfig()
                                        .getRetryBackoffDuration())
                                .doBeforeRetry(retrySignal -> log.info("Retry {}", retrySignal)))
                .onErrorResume(throwable -> Mono.empty())
                .doOnNext(
                        commonSecurityLogs ->
                                log.info("Query response size: {}", commonSecurityLogs.size()))
                .flatMapMany(Flux::fromIterable)
                .flatMap(
                        commonSecurityLogLAW ->
                                Mono.just(
                                        CommonSecurityLogRecordsLAW.builder()
                                                .records(
                                                        Collections.singletonList(
                                                                commonSecurityLogLAW))
                                                .build()));
    }
}
