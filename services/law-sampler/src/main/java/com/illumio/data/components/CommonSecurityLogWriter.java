package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.pojo.CommonSecurityLogRecordsLAW;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
public class CommonSecurityLogWriter {
    private final ObjectMapper objectMapper;

    public Mono<String> writeAsString(CommonSecurityLogRecordsLAW commonSecurityLogRecordsLAW) {
        return Mono.fromCallable(() -> {
            String result = objectMapper.writeValueAsString(commonSecurityLogRecordsLAW);
            return result;
        });
    }
}

