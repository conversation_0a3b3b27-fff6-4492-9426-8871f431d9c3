apiVersion: v1
kind: Service
metadata:
  name: {{ include "LawSampler.name" . }}
  labels:
    {{- include "LawSampler.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    {{- range .Values.servicePorts }}
    - name: {{ .name }}
      targetPort: {{ .podPort }}
      port: {{ .servicePort }}
    {{- end }}
  selector:
    {{- include "LawSampler.selectorLabels" . | nindent 4 }}
