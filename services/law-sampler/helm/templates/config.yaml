apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "LawSampler.fullname" . }}-env-configmap
  labels:
    {{- include "LawSampler.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
    
    spring:
      application:
        name: law-sampler
      output:
        ansi:
          enabled: ALWAYS
    
    law-sampler:
      kafka-common-config:
        bootstrapServers: "{{.Values.lawSampler.kafkaCommonConfig.bootstrapServers}}"
        isSasl: true
        saslJaasConfig: org.apache.kafka.common.security.plain.PlainLoginModule required username="$ConnectionString" password="{{ .Values.eventhub.password }}";

      kafka-sender-config:
        common-security-log-sink-topic: "{{.Values.lawSampler.kafkaSenderConfig.commonSecurityLogSinkTopic}}"
        maxRetryAttempts: "{{.Values.lawSampler.kafkaSenderConfig.maxRetryAttempts}}"
        retryBackoffDuration: "{{.Values.lawSampler.kafkaSenderConfig.maxRetryAttempts}}"
    
      azure-config:
        is-workload-identity: "{{.Values.lawSampler.azureConfig.isWorkloadIdentity}}"
        workload-identity:
          client-id: "{{.Values.lawSampler.azureConfig.workloadIdentity.clientId}}"
    
      lawQueryConfig:
        workspaceId: "{{.Values.lawSampler.lawQueryConfig.workspaceId}}"
        query: "{{.Values.lawSampler.lawQueryConfig.query}}"
        queryTimeInterval: "{{.Values.lawSampler.lawQueryConfig.queryTimeInterval}}"
        sampleInitialDelay: "{{.Values.lawSampler.lawQueryConfig.sampleInitialDelay}}"
        sampleFrequency: "{{.Values.lawSampler.lawQueryConfig.sampleFrequency}}"
        maxRetryAttempts: "{{.Values.lawSampler.lawQueryConfig.maxRetryAttempts}}"
        retryBackoffDuration: "{{.Values.lawSampler.lawQueryConfig.retryBackoffDuration}}"
        httpLogDetailLevel: "{{.Values.lawSampler.lawQueryConfig.httpLogDetailLevel}}"
