package com.illumio.data.configuration;

import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@Configuration
@RequiredArgsConstructor
public class KafkaReceiverConfiguration {
    private final InsightsOnboardingConfig insightsOnboardingConfig;

    @Bean
    public ReceiverOptions<String, String> receiverOptions() {
        Map<String, Object> consumerProps = new HashMap<>();
        consumerProps.put(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG,
                insightsOnboardingConfig.getKafkaReceiverConfig().getBootstrapServers());
        consumerProps.put(
                ConsumerConfig.GROUP_ID_CONFIG,
                insightsOnboardingConfig.getKafkaReceiverConfig().getGroupId());
        consumerProps.put(
                ConsumerConfig.AUTO_OFFSET_RESET_CONFIG,
                insightsOnboardingConfig.getKafkaReceiverConfig().getAutoOffsetReset());
        consumerProps.put(
                ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG,
                StringDeserializer.class);
        consumerProps.put(
                ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG,
                StringDeserializer.class);
        consumerProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG,
                insightsOnboardingConfig.getKafkaReceiverConfig().getHeartbeatIntervalMs());
        consumerProps.put(
                ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG,
                insightsOnboardingConfig.getKafkaReceiverConfig().getRequestTimeoutMs());
        consumerProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG,
                insightsOnboardingConfig.getKafkaReceiverConfig().getSessionTimeoutMs());
        consumerProps.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG,
                insightsOnboardingConfig.getKafkaReceiverConfig().getMaxPollIntervalMs());
        consumerProps.put(
                ConsumerConfig.MAX_POLL_RECORDS_CONFIG,
                insightsOnboardingConfig.getKafkaReceiverConfig().getMaxPollRecords());
        consumerProps.put(
                ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG,
                insightsOnboardingConfig.getKafkaReceiverConfig().getMaxPartitionFetchBytes());
        consumerProps.put(ConsumerConfig.PARTITION_ASSIGNMENT_STRATEGY_CONFIG,
                insightsOnboardingConfig.getKafkaReceiverConfig().getPartitionAssignmentStrategy());

        if (null != insightsOnboardingConfig.getKafkaReceiverConfig().getIsSasl()
                && insightsOnboardingConfig.getKafkaReceiverConfig().getIsSasl()) {
            consumerProps.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_SSL");
            consumerProps.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
            consumerProps.put(
                    SaslConfigs.SASL_JAAS_CONFIG,
                    insightsOnboardingConfig.getKafkaReceiverConfig().getSaslJaasConfig());
        }
        consumerProps.put(CommonClientConfigs.METADATA_MAX_AGE_CONFIG,
                insightsOnboardingConfig.getKafkaReceiverConfig().getMetadataMaxAgeMs());
        consumerProps.put(CommonClientConfigs.CONNECTIONS_MAX_IDLE_MS_CONFIG,
                insightsOnboardingConfig.getKafkaReceiverConfig().getConnectionMaxIdleMs());

        return ReceiverOptions.<String, String>create(consumerProps)
                        .subscription(
                                Collections.singleton(
                                        insightsOnboardingConfig.getKafkaReceiverConfig().getTopic()));
    }

    @Bean
    public KafkaReceiver<String, String> kafkaReceiver(ReceiverOptions<String, String> receiverOptions){
        return KafkaReceiver.create(receiverOptions);
    }
}
