package com.illumio.data.util;

import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import io.opentelemetry.api.metrics.LongCounter;
import io.opentelemetry.api.metrics.Meter;
import org.springframework.stereotype.Component;

@Component
public class MetricsUtil {
    private final LongCounter tenantIdsAccepted;
    private final LongCounter jobTriggerCount;

    public static final String METRIC_TENANT_IDS_ACCEPTED = "tenant_ids_accepted";
    public static final String METRIC_JOB_TRIGGER_COUNT = "job_trigger_count";

    public MetricsUtil(Meter meter) {
        this.tenantIdsAccepted = meter
                .counterBuilder(METRIC_TENANT_IDS_ACCEPTED)
                .setDescription("No of tenant ids accepted by the service for insights onboarding")
                .build();
        this.jobTriggerCount = meter
                .counterBuilder(METRIC_JOB_TRIGGER_COUNT)
                .setDescription("No of jobs attempted to trigger/triggered by the service for insights onboarding")
                .build();
    }

    public void incrementTenantIdsAccepted(long count) {
        tenantIdsAccepted.add(count);
    }

    public void incrementJobTriggerSuccessCount(String cronJobNamespace, String cronJobName) {
        Attributes successAttrs = Attributes.of(
                AttributeKey.stringKey("cronjob_namespace"), cronJobNamespace,
                AttributeKey.stringKey("cronjob_name"), cronJobName,
                AttributeKey.stringKey("result"), "success"
        );
        jobTriggerCount.add(1L, successAttrs);
    }

    public void incrementJobTriggerFailureCount(String cronJobNamespace, String cronJobName) {
        Attributes failureAttrs = Attributes.of(
                AttributeKey.stringKey("cronjob_namespace"), cronJobNamespace,
                AttributeKey.stringKey("cronjob_name"), cronJobName,
                AttributeKey.stringKey("result"), "failure"
        );
        jobTriggerCount.add(1L, failureAttrs);
    }
}
