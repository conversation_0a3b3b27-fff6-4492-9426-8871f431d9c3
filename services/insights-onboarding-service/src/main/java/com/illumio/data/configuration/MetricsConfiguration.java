package com.illumio.data.configuration;

import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.metrics.Meter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MetricsConfiguration {

    public static final String INSTRUMENTATION_NAME = "insights-onboarding-service";

    /*
     * The application uses otel java agent jar, hence,
     * we can access the OpenTelemetry instance via GlobalOpenTelemetry
     */
    @Bean
    public OpenTelemetry openTelemetry() {
        return GlobalOpenTelemetry.get();
    }

    @Bean
    public Meter meter(OpenTelemetry openTelemetry) {
        return openTelemetry.getMeter(INSTRUMENTATION_NAME);
    }
}
