package com.illumio.data.service;

import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.apis.BatchV1Api;
import io.kubernetes.client.openapi.models.V1CronJob;
import io.kubernetes.client.openapi.models.V1Job;
import io.kubernetes.client.openapi.models.V1JobSpec;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import lombok.RequiredArgsConstructor;

import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class KubernetesCronJobService {
    private final BatchV1Api batchV1Api;

    public V1CronJob getCronJob(String namespace, String cronJobName) throws ApiException {
        return batchV1Api.readNamespacedCronJob(cronJobName, namespace).execute();
    }

    public V1Job createJob(String namespace, String jobName, V1JobSpec jobSpec, V1ObjectMeta objectMeta) throws ApiException {
        V1Job job = new V1Job()
                .metadata(objectMeta
                        .name(jobName)
                        .namespace(namespace))
                .spec(jobSpec);
        return batchV1Api.createNamespacedJob(namespace, job).execute();
    }
}
