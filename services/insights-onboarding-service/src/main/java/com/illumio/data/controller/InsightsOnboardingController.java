package com.illumio.data.controller;

import com.illumio.data.configuration.InsightsOnboardingConfig;
import com.illumio.data.model.InsightsOnboardingTenantRequest;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/api/v1/insights/onboard")
@Deprecated
public class InsightsOnboardingController {

    private final String cronJobName;
    private final String cronJobNamespace;

    public InsightsOnboardingController(InsightsOnboardingConfig config) {
        this.cronJobName = getCronJobName(config);
        this.cronJobNamespace = getCronJobNamespace(config);
    }

    @PostMapping("/tenant")
    public Mono<ResponseEntity<Void>> onboardTenant(@RequestBody InsightsOnboardingTenantRequest requestPayload) {
        if (cronJobName.isEmpty() || cronJobNamespace.isEmpty()) {
            log.warn("Received request for Insights Onboarding API that is not supported.");
            return Mono.just(ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED).build());
        }
        String tenantId = requestPayload.getTenantId();
        if (tenantId == null || tenantId.isBlank()) {
            log.error("Missing tenant id in the Insights onboarding request");
            return Mono.just(ResponseEntity.badRequest().build());
        }

        log.info("Received onboarding request for tenant: {}", tenantId);

        log.info("Cron Job Namespace: {}, Cron Job Name: {}", cronJobNamespace, cronJobName);
        return Mono.just(ResponseEntity.accepted().build());

        // Won't do in favor of EventHub - Trigger cron job, instead of logging the id
    }

    private String getCronJobName(final InsightsOnboardingConfig config) {
        return Optional.ofNullable(config.getJobTriggerConfig())
                .map(InsightsOnboardingConfig.JobTriggerConfig::getCronJobName)
                .filter(s -> !s.isBlank())
                .orElse("");
    }

    private String getCronJobNamespace(final InsightsOnboardingConfig config) {
        return Optional.ofNullable(config.getJobTriggerConfig())
                .map(InsightsOnboardingConfig.JobTriggerConfig::getCronJobNamespace)
                .filter(s -> !s.isBlank())
                .orElse("");
    }
}
