package com.illumio.data.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class InsightsOnboardingTenantEvent {
    // Better to use Instant for timestamp; At this moment, we are using this only for logging, as such, using String would suffice
    @JsonProperty("ts")
    private String timestamp;
    @JsonProperty("event_type")
    private String eventType;
    @JsonProperty("tenant_id")
    private String tenantId;
    @JsonProperty("region")
    private String region;
}
