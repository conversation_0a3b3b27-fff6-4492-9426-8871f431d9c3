package com.illumio.data.configuration;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.time.Duration;

@Primary
@ConfigurationProperties(prefix = "insights-onboarding-config")
@Configuration
@Data
public class InsightsOnboardingConfig {
    private final JobTriggerConfig jobTriggerConfig;
    private final KafkaReceiverConfig kafkaReceiverConfig;

    @Configuration
    @Getter
    @Setter
    public static class JobTriggerConfig {
        private String cronJobName;
        private String cronJobNamespace;
        private Duration interval = Duration.ofMinutes(1L);
        private int retryAttempts = 1;
        private Duration retryBackoff = Duration.ofSeconds(5L);
        private Boolean isDisabled = false;
    }

    @Configuration
    @Getter
    @Setter
    public static class KafkaReceiverConfig {
        private String bootstrapServers;
        private Boolean isSasl;
        private String saslJaasConfig;
        private String groupId;
        private String topic;
        private String autoOffsetReset;
        private Integer requestTimeoutMs;
        private Integer maxPollRecords;
        private Integer maxPollIntervalMs;
        private Integer maxPartitionFetchBytes;
        private String partitionAssignmentStrategy;
        private Integer heartbeatIntervalMs;
        private Integer sessionTimeoutMs;
        private Integer metadataMaxAgeMs;
        private Integer connectionMaxIdleMs;
    }
}
