package com.illumio.data.configuration;

import io.kubernetes.client.openapi.ApiClient;
import io.kubernetes.client.openapi.apis.BatchV1Api;
import io.kubernetes.client.util.Config;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

@Configuration
@Slf4j
public class KubernetesConfig {
    @Bean
    public ApiClient kubernetesApiClient() {
        try {
            return Config.defaultClient();
        } catch (IOException e) {
            log.error("Error setting up Kubernetes API client", e);
            throw new RuntimeException(e);
        }
    }

    @Bean
    public BatchV1Api batchV1Api(ApiClient kubernetesApiClient) {
        return new BatchV1Api(kubernetesApiClient);
    }
}
