package com.illumio.data.components;

import com.illumio.data.service.KubernetesCronJobService;
import io.kubernetes.client.openapi.ApiException;
import io.kubernetes.client.openapi.models.V1Container;
import io.kubernetes.client.openapi.models.V1CronJob;
import io.kubernetes.client.openapi.models.V1EnvVar;
import io.kubernetes.client.openapi.models.V1JobSpec;
import io.kubernetes.client.openapi.models.V1ObjectMeta;
import io.kubernetes.client.openapi.models.V1PodSpec;
import io.kubernetes.client.openapi.models.V1PodTemplateSpec;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
@Slf4j
@RequiredArgsConstructor
public class K8sCronJobTrigger {
    private final KubernetesCronJobService kubernetesCronJobService;

    public void triggerJob(final String cronJobNamespace, final String cronJobName, final List<String> tenantIds) {
        V1CronJob cronJob;
        try {
            log.info("Fetching the cron job details. Namespace: {}, CronJobName: {}", cronJobNamespace, cronJobName);
            cronJob = kubernetesCronJobService.getCronJob(cronJobNamespace, cronJobName);
            log.info("Fetched the cron job details.");
        } catch (ApiException e) {
            log.error("Unable to fetch cron job details. Status Code: {}, Error: {}", e.getCode(), e.getResponseBody());
            throw new RuntimeException("Failed to fetch the cron job details.", e);
        }

        try {
            // Cron Job spec shouldn't be null
            V1JobSpec jobSpec = Objects.requireNonNull(cronJob.getSpec())
                    .getJobTemplate()
                    .getSpec();
            // Job spec shouldn't be null
            V1PodTemplateSpec templateSpec = Objects.requireNonNull(jobSpec).getTemplate();
            V1ObjectMeta jobMetadata = Objects.requireNonNull(templateSpec.getMetadata());
            V1PodSpec podSpec = templateSpec.getSpec();
            List<V1Container> containers = Objects.requireNonNull(podSpec).getContainers();
            for (V1Container container : containers) {
                List<V1EnvVar> envVars = container.getEnv();
                Optional<V1EnvVar> tenantIdEnv = envVars
                        .stream()
                        .filter(e -> "TENANT_IDS".equals(e.getName()))
                        .findFirst();
                if (tenantIdEnv.isPresent()) {
                    log.info("Updating the value of env variable TENANT_IDS to {}", tenantIds);
                    tenantIdEnv.get().setValue(String.join(",", tenantIds));
                } else {
                    log.info("Adding env variable TENANT_IDS as {}", tenantIds);
                    envVars.add(new V1EnvVar()
                            .name("TENANT_IDS")
                            .value(String.join(",", tenantIds)));
                }
            }
            if (!containers.isEmpty()) {
                String jobName = cronJobName + "-manual-" + System.currentTimeMillis();
                try {
                    log.info("Initiating job trigger for tenants {} via job {} in namespace {}", tenantIds, jobName, cronJobNamespace);
                    kubernetesCronJobService.createJob(cronJobNamespace, jobName, jobSpec, jobMetadata);
                    log.info("Successfully triggered the job {} in namespace {} with tenants {}", jobName, cronJobNamespace, tenantIds);
                } catch (ApiException e) {
                    log.error("Exception occurred during job trigger. Status Code: {}, Error: {}", e.getCode(), e.getResponseBody());
                    throw new RuntimeException("Failed to execute the job", e);
                }
            } else {
                log.error("No jobs found to be triggered for {}", cronJobName);
                throw new RuntimeException("Failed to execute the job as no cron job details were found");
            }
        } catch (NullPointerException e) {
            throw new RuntimeException("Failed to execute the job as cron job template didn't meet the required parameters", e);
        }
    }
}

