package com.illumio.data.components;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.illumio.data.model.InsightsOnboardingTenantEvent;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@RequiredArgsConstructor
@Builder
public class OnboardingEventReader {
    private final ObjectMapper objectMapper;

    public Mono<InsightsOnboardingTenantEvent> readTree(String value) {
        return Mono.fromCallable(() -> objectMapper.readValue(value, InsightsOnboardingTenantEvent.class));
    }
}
