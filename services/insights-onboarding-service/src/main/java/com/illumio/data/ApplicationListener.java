package com.illumio.data;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class ApplicationListener {
    private final InsightsOnboardingPipeline insightsOnboardingPipeline;

    @EventListener(ApplicationReadyEvent.class)
    public void handleReadyEvent() {
        log.info("Starting tenant onboarding pipeline for insights aggregation");
        insightsOnboardingPipeline.start();
        log.info("Started tenant onboarding pipeline for insights aggregation");
    }

    @EventListener(ContextClosedEvent.class)
    public void handleClosedEvent() {
        log.info("Stopping tenant onboarding pipeline for insights aggregation");
        insightsOnboardingPipeline.stop();
        log.info("Stopped tenant onboarding pipeline for insights aggregation");
    }
}
