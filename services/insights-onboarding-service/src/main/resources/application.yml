logging:
  level:
    ROOT: INFO
    org:
      apache:
        kafka: INFO

server:
  port:
    8081

spring:
  application:
    name: insights-onboarding-service
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: reactive

insights-onboarding-config:
  job-trigger-config:
    cron-job-name: "dev-insightsprochrly-api-cronjob"
    cron-job-namespace: "dev-insightsprochrly"
    interval: PT1M
    retry-attempts: 1
    retry-backoff: PT5S
    is-disabled: false
  kafka-receiver-config:
    bootstrap-servers: eventhubs-connector.servicebus.windows.net:9093
    is-sasl: true
    sasl-jaas-config: _DO_NOT_COMMIT_
    group-id: insights-tenant-onboarding
    topic: tenant-notifications-v1
    auto-offset-reset: latest
    request-timeout-ms: 60000
    max-poll-records: 500
    max-poll-interval-ms: 300000
    max-partition-fetch-bytes: 1048576
    partition-assignment-strategy: org.apache.kafka.clients.consumer.CooperativeStickyAssignor
    heartbeat-interval-ms: 3000
    session-timeout-ms: 60000
    metadata-max-age-ms: 180000
    connection-max-idle-ms: 180000