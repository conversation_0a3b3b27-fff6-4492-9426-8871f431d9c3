plugins {
    id 'org.springframework.boot'
    id 'com.google.cloud.tools.jib'
    id 'com.github.johnrengelman.shadow'
}

apply from: "${project.rootDir}/gradle/scripts/jib-build.gradle"
apply from: "${project.rootDir}/gradle/scripts/opentelemetry.gradle"

repositories {
    maven {
        url  = uri("https://packages.confluent.io/maven/")
    }
}

dependencies {

    implementation 'org.springframework.boot:spring-boot-starter-webflux'

    implementation("io.kubernetes:client-java:23.0.0")

    implementation 'org.springframework.kafka:spring-kafka'
    implementation "io.projectreactor.kafka:reactor-kafka"

    implementation("io.opentelemetry:opentelemetry-api")

    // lombok
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    testCompileOnly 'org.projectlombok:lombok'
    testAnnotationProcessor 'org.projectlombok:lombok'

    // spring
    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"

    // test
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.boot:spring-boot-starter-web'
    testImplementation platform('org.junit:junit-bom:5.10.0')
    testImplementation 'org.junit.jupiter:junit-jupiter'
    testImplementation 'io.projectreactor:reactor-test'
}


jib {
    container {
        entrypoint = [
                'java', '-cp', '@/app/jib-classpath-file',
                'com.illumio.data.InsightsOnboardingApp',
                '--spring.config.location=file:/var/resources/application.yml',
                '--spring.profiles.active=prod'
        ]
        environment = [
                "JAVA_TOOL_OPTIONS": "-javaagent:/otel/opentelemetry-javaagent.jar",
                "OTEL_METRICS_EXPORTER": "prometheus",
                "OTEL_TRACES_EXPORTER": "none",
                "OTEL_LOGS_EXPORTER": "none"
        ]
    }
}