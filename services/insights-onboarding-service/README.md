Insights Onboarding Service: To trigger aggregations for newly onboarded tenant.

## Overview

1. Use EventHub to receive events to trigger Spark job for newly onboarded tenant

2. Batch incoming tenant ids for a minute (configurable)

3. Trigger aggregation hourly Spark job passing the tenant ids as a comma-separated String in the env variable TENANT_IDS

## Custom Metrics

- No of tenant ids accepted by the service for insights onboarding . This doesn't mean that the tenant ids were successfully processed by Spark jobs. -> Counter tenant_ids_accepted_count
- No of jobs attempted to trigger/triggered by the service for insights onboarding -> Counter job_trigger_count_total{result="success|failure"}