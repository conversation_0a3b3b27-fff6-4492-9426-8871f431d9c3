# Default values for insights onboarding service.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# This will set the replicaset count more information can be found here: https://kubernetes.io/docs/concepts/workloads/controllers/replicaset/
replicaCount: 1

# This sets the container image more information can be found here: https://kubernetes.io/docs/concepts/containers/images/
image:
  repositoryBase: "illum.azurecr.io/"
  repositoryName: "insights-onboarding-service"
  pullPolicy: Always
  tag: # value given at helm deployment

# This is for the secretes for pulling an image from a private repository more information can be found here: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
imagePullSecrets: []
# This is to override the chart name.
nameOverride: ""
fullnameOverride: ""

# To refer to the namespace while binding role to the service account
namespace: "default"

# This section builds out the service account more information can be found here: https://kubernetes.io/docs/concepts/security/service-accounts/
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

# This is for setting Kubernetes Annotations to a Pod.
# For more information checkout: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
podAnnotations: {}
# This is for setting Kubernetes Labels to a Pod.
# For more information checkout: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
podLabels: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

ports:
  - name: rest
    port: 8081

servicePorts:
  - name: rest
    podPort: rest
    servicePort: 8081

service:
  type: ClusterIP

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

# This section is for setting up autoscaling more information can be found here: https://kubernetes.io/docs/concepts/workloads/autoscaling/
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 1
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

podMetricsAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9464"
  prometheus.io/scheme: http
  prometheus.io/scrape: "true"

# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

extraLabels: {}

logging:
  level:
    ROOT: INFO
    org:
      apache:
        kafka: INFO

insightsOnboardingConfig:
  jobTriggerConfig:
    cronJobName: "dev-insightsprochrly-api-cronjob"
    cronJobNamespace: "dev-insightsprochrly"
    interval: PT1M
    retryAttempts: 1
    retryBackoff: PT5S
    isDisabled: false
  kafkaReceiverConfig:
    bootstrapServers: _DO_NOT_COMMIT_
    isSasl: true
    saslJaasConfig: _DO_NOT_COMMIT_
    groupId: "insights-tenant-onboarding"
    topic: "tenant-notifications-v1"
    autoOffsetReset: latest
    requestTimeoutMs: 60000
    maxPollIntervalMs: 300000
    maxPollRecords: 500
    maxPartitionFetchBytes: 1048576
    partitionAssignmentStrategy: "org.apache.kafka.clients.consumer.CooperativeStickyAssignor"
    heartbeatIntervalMs: 3000
    sessionTimeoutMs: 60000
    metadataMaxAgeMs: 180000
    connectionMaxIdleMs: 180000

eventhub:
  password:                 # should give at deployment time
