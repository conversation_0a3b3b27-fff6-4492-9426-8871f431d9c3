apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ .Values.insightsOnboardingConfig.jobTriggerConfig.cronJobNamespace }}-cronjob-trigger-binding
  namespace: {{ .Values.insightsOnboardingConfig.jobTriggerConfig.cronJobNamespace }}
  labels:
    {{- include "ios.labels" . | nindent 4 }}
subjects:
  - kind: ServiceAccount
    name: {{ include "ios.serviceAccountName" . }}
    namespace: {{ .Values.namespace }}
roleRef:
  kind: Role
  name: {{ .Values.insightsOnboardingConfig.jobTriggerConfig.cronJobNamespace }}-cronjob-trigger-role
  apiGroup: rbac.authorization.k8s.io