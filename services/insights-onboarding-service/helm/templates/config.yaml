apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "ios.fullname" . }}-env-configmap
  labels:
    {{- include "ios.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{ .Values.logging.level.ROOT }}
        org:
          apache:
            kafka: {{ .Values.logging.level.org.apache.kafka }}
    server:
      port:
        8081
    spring:
      application:
        name: insights-onboarding-service
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: reactive
    
    insights-onboarding-config:
      job-trigger-config:
        cron-job-name: {{ .Values.insightsOnboardingConfig.jobTriggerConfig.cronJobName }}
        cron-job-namespace: {{ .Values.insightsOnboardingConfig.jobTriggerConfig.cronJobNamespace }}
        interval: {{ .Values.insightsOnboardingConfig.jobTriggerConfig.interval }}
        retry-attempts: {{ .Values.insightsOnboardingConfig.jobTriggerConfig.retryAttempts }}
        retry-backoff: {{ .Values.insightsOnboardingConfig.jobTriggerConfig.retryBackoff }}
        is-disabled: {{ .Values.insightsOnboardingConfig.jobTriggerConfig.isDisabled }}
      kafka-receiver-config:
        bootstrap-servers: {{ .Values.insightsOnboardingConfig.kafkaReceiverConfig.bootstrapServers }}
        is-sasl: {{ .Values.insightsOnboardingConfig.kafkaReceiverConfig.isSasl }}
        sasl-jaas-config: ${INSIGHTSONBOARDINGCONFIG_KAFKARECEIVERCONFIG_SASLJAASCONFIG}
        group-id: {{ .Values.insightsOnboardingConfig.kafkaReceiverConfig.groupId }}
        topic: {{ .Values.insightsOnboardingConfig.kafkaReceiverConfig.topic }}
        auto-offset-reset: {{ .Values.insightsOnboardingConfig.kafkaReceiverConfig.autoOffsetReset }}
        request-timeout-ms: {{ .Values.insightsOnboardingConfig.kafkaReceiverConfig.requestTimeoutMs }}
        max-poll-interval-ms: {{ .Values.insightsOnboardingConfig.kafkaReceiverConfig.maxPollIntervalMs }}
        max-poll-records: {{ .Values.insightsOnboardingConfig.kafkaReceiverConfig.maxPollRecords }}
        max-partition-fetch-bytes: {{ .Values.insightsOnboardingConfig.kafkaReceiverConfig.maxPartitionFetchBytes }}
        partition-assignment-strategy: {{ .Values.insightsOnboardingConfig.kafkaReceiverConfig.partitionAssignmentStrategy }}
        heartbeat-interval-ms: {{ .Values.insightsOnboardingConfig.kafkaReceiverConfig.heartbeatIntervalMs }}
        session-timeout-ms: {{ .Values.insightsOnboardingConfig.kafkaReceiverConfig.sessionTimeoutMs }}
        metadata-max-age-ms: {{ .Values.insightsOnboardingConfig.kafkaReceiverConfig.metadataMaxAgeMs }}
        connection-max-idle-ms: {{ .Values.insightsOnboardingConfig.kafkaReceiverConfig.connectionMaxIdleMs }}
