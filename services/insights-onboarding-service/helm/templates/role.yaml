apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ .Values.insightsOnboardingConfig.jobTriggerConfig.cronJobNamespace }}-cronjob-trigger-role
  namespace: {{ .Values.insightsOnboardingConfig.jobTriggerConfig.cronJobNamespace }}
  labels:
    {{- include "ios.labels" . | nindent 4 }}
rules:
  - apiGroups: ["batch"]
    resources: ["cronjobs"]
    verbs: ["get"]
    resourceNames: [{{ .Values.insightsOnboardingConfig.jobTriggerConfig.cronJobName }}]
  - apiGroups: ["batch"]
    resources: ["jobs"]
    verbs: ["create"]