logging:
  level:
    ROOT: INFO
spring:
  application:
    name: database-manager
  output:
    ansi:
      enabled: ALWAYS
  main:
    web-application-type: none
server:
  port: 8081

database-manager:
  managed-postgres-instances:
    integrationstest:
      admin-db-url: ************************************************************************
      admin-username: postgres
      admin-password: DO_NOT_COMMIT
      managed-databases:
        integrations:
          baseline-on-migrate: false
        integrations-inventory:
          baseline-on-migrate: false