CREATE TABLE IF NOT EXISTS vulnerability (
    cve_id character varying NOT NULL,
    cve_description text,
    cvss_severity character varying,
    score numeric,
    has_exploit boolean,
    has_cisa_kev_exploit boolean,
    CONSTRAINT vulnerability_pkey PRIMARY KEY (cve_id)
);

CREATE TABLE IF NOT EXISTS workload_vulnerability (
    id SERIAL,
    workload_id character varying,
    vulnerability_id character varying,
    tenant_id character varying,
    integration character varying,
    status character varying,
    first_detected_at character varying,
    last_detected_at character varying,
    impact_score numeric,
    exploitability_score numeric,
    severity character varying,
    vulnerable_component_name character varying,
    subscription_id character varying,
    CONSTRAINT workload_vulnerability_pkey PRIMARY KEY (id),
    CONSTRAINT workload_vulnerability_composite_key UNIQUE (workload_id, vulnerability_id),
    CONSTRAINT workload_vulnerability_vulnerability_id_fkey FOREIGN KEY (vulnerability_id)
        REFERENCES vulnerability(cve_id)
);
