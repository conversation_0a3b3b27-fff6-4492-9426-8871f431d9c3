CREATE TABLE IF NOT EXISTS devices (
    id INT,
    tenant_id CHARACTER VARYING,
    integration CHARACTER VARYING,
    site CHARACTER VARYING,
    category CHARACTER VARYING,
    boundaries CHARACTER VARYING,
    display_title CHARACTER VARYING,
    name <PERSON><PERSON><PERSON><PERSON><PERSON> VARYING,
    names <PERSON><PERSON><PERSON><PERSON><PERSON> VARYING,
    manufacturer <PERSON><PERSON><PERSON><PERSON><PERSON> VARYING,
    type <PERSON><PERSON>ACTER VARYING,
    model CHARACTER VARYING,
    first_seen CHARACTER VARYING,
    last_seen CHARACTER VARYING,
    mac_address CHARACTER VARYING,
    ip_v4 CHARACTER VARYING,
    ip_v6 CHARACTER VARYING ARRAY,
    access_switch CHARACTER VARYING,
    tags CHARACTER VARYING ARRAY,
    purdue_level FLOAT,
    risk_score INT,
    operating_system CHARACTER VARYING,
    operating_system_version CHARACTER VARYING,
    protections TEXT,
    sensor TEXT,
    tier CHARACTER VARYING,
    type_enum CHARACTER VARYING,
    user_ids INT[],
    visibility CHARACTER VARYING,
    data_sources TEXT,
    PRIMARY KEY (tenant_id, id),

    CONSTRAINT fk_devices_site
        FOREIGN KEY (tenant_id, site)
        REFERENCES sites (tenant_id, name),

    CONSTRAINT fk_devices_boundaries
        FOREIGN KEY (tenant_id, boundaries)
        REFERENCES boundaries (tenant_id, name)
);