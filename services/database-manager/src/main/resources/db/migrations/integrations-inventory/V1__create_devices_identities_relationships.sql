CREATE TABLE IF NOT EXISTS device (
    id UUID PRIMARY KEY NOT NULL,
    tenant_id UUID NOT NULL,
    type TEXT NOT NULL,
    entity_id TEXT NOT NULL,
    resource_id UUID NOT NULL,
    data JSONB NOT NULL,
    hash BYTEA NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_seen_at TIMESTAMP WITH TIME ZONE NOT NUlL,
    updated_at TIMESTAMP WITH TIME ZONE
    );

CREATE TABLE IF NOT EXISTS "identity" (
    id UUID PRIMARY KEY NOT NULL,
    tenant_id UUID NOT NULL,
    type TEXT NOT NULL,
    entity_id TEXT NOT NULL,
    resource_id UUID NOT NULL,
    data JSONB NOT NULL,
    hash BYTEA NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    last_seen_at TIMESTAMP WITH TIME ZONE NOT NUlL,
    updated_at TIMESTAMP WITH TIME ZONE
    );

CREATE TABLE IF NOT EXISTS device_to_device (
    src_device_id UUID NOT NULL,
    dest_device_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    PRIMARY KEY (src_device_id, dest_device_id)
    );

CREATE TABLE IF NOT EXISTS identity_to_device (
    identity_id UUID NOT NULL,
    device_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    PRIMARY KEY (identity_id, device_id)
    );