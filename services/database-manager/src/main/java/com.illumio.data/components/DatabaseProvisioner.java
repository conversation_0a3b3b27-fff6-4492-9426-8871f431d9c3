package com.illumio.data.components;

import com.illumio.data.configuration.DatabaseManagerConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.FlywayException;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.ExitCodeGenerator;
import org.springframework.stereotype.Component;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class DatabaseProvisioner implements CommandLineRunner, ExitCodeGenerator {

    private final DatabaseManagerConfig databaseManagerConfig;

    private boolean provisionFailed = false;

    @Override
    public void run(String... args) {
        for (final Map.Entry<String, DatabaseManagerConfig.PostgresInstanceConfig> instanceNameAndConfig
                : databaseManagerConfig.getManagedPostgresInstances().entrySet()) {
            final DatabaseManagerConfig.PostgresInstanceConfig instanceConfig = instanceNameAndConfig.getValue();
            for (final Map.Entry<String, DatabaseManagerConfig.ManagedDbConfig> managedDb : instanceConfig.getManagedDatabases().entrySet()) {
                createDatabaseIfNotExists(instanceNameAndConfig.getKey(), instanceConfig.getAdminDbUrl(), managedDb.getKey(),
                        instanceConfig.getAdminUsername(), instanceConfig.getAdminPassword());
                migrateDatabaseWithFlyway(instanceNameAndConfig.getKey(), instanceConfig.getAdminDbUrl(), managedDb.getKey(),
                        instanceConfig.getAdminUsername(), instanceConfig.getAdminPassword(), managedDb.getValue().getBaselineOnMigrate());
            }
        }
    }

    private void createDatabaseIfNotExists(final String instanceName,
                                            final String instanceUrl,
                                            final String databaseName,
                                            final String adminUsername,
                                            final String adminPassword) {
        try {
            try (final Connection connection = DriverManager.getConnection(
                    instanceUrl,
                    adminUsername,
                    adminPassword)) {

                var rs = connection.createStatement().executeQuery(
                        "SELECT 1 FROM pg_database WHERE datname = '%s'".formatted(databaseName));

                if (!rs.next()) {
                    log.info("Creating database={} on instance={}", databaseName, instanceName);
                    connection.createStatement().executeUpdate("CREATE DATABASE \"%s\"".formatted(databaseName));
                    log.info("Created database={} on instance={}", databaseName, instanceName);
                } else {
                    log.info("Skipping database creation for database={} as it already exists on instance={}",
                            databaseName, instanceName);
                }
            }
        } catch (final SQLException e) {
            log.error("Encountered error while creating database={} within instance={}",
                    databaseName, instanceName, e);
            provisionFailed = true;
        }
    }

    private void migrateDatabaseWithFlyway(final String instanceName,
                                            final String instanceUrl,
                                            final String databaseName,
                                            final String adminUsername,
                                            final String adminPassword,
                                            final boolean baselineOnMigrate) {
        try {
            final String migrationPath = "db/migrations/" + databaseName;
            final String migrationFullLocation = "classpath:" + migrationPath;

            final ClassLoader cl = Thread.currentThread().getContextClassLoader();
            if (cl.getResource(migrationPath) == null) {
                log.warn("Skipping Flyway migration: No migration folder found at '{}'", migrationFullLocation);
                return;
            }

            log.info("Running Flyway migrations for database={} on instance={} with migrations at={}",
                    databaseName, instanceName, migrationPath);

            final String dbUrl = instanceUrl.replaceFirst("/[^/]+$", "/" + databaseName);
            final Flyway flyway = Flyway.configure()
                    .dataSource(dbUrl, adminUsername, adminPassword)
                    .locations(migrationFullLocation)
                    .baselineOnMigrate(baselineOnMigrate)
                    .load();
            flyway.migrate();

            log.info("Completed Flyway migrations for database={} on instance={} with migrations at={}",
                    databaseName, instanceName, migrationPath);
        } catch (final FlywayException e) {
            log.error("Encountered error while performing database migration for database={} within instance={}",
                    databaseName, instanceName, e);
            provisionFailed = true;
        }
    }

    @Override
    public int getExitCode() {
        return provisionFailed ? 1 : 0;
    }

}
