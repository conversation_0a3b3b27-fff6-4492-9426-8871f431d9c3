package com.illumio.data.configuration;

import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
@ConfigurationProperties(prefix = "database-manager")
@Getter
@Setter
public class DatabaseManagerConfig {

    private Map<String, PostgresInstanceConfig> managedPostgresInstances;

    @Getter
    @Setter
    public static class PostgresInstanceConfig {
        private String adminDbUrl;
        private String adminUsername;
        private String adminPassword;
        private HashMap<String, ManagedDbConfig> managedDatabases;
    }

    @Getter
    @Setter
    public static class ManagedDbConfig {
        private Boolean baselineOnMigrate = false;
    }

}
