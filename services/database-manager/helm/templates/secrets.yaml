apiVersion: v1
kind: Secret
metadata:
  name: {{ include "DatabaseManager.fullname" . }}-env-secrets
  labels:
    {{- include "DatabaseManager.labels" . | nindent 4 }}
type: Opaque
stringData:
  {{- range $instanceName, $instanceConfig := .Values.databaseManager.managedPostgresInstances }}
  DATABASEMANAGER_MANAGEDPOSTGRESINSTANCES_{{ $instanceName }}_ADMINPASSWORD: "{{ $instanceConfig.adminPassword }}"
  {{- end }}