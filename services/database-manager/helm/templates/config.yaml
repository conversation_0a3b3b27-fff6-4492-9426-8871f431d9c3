apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "DatabaseManager.fullname" . }}-env-configmap
  labels:
    {{- include "DatabaseManager.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
    spring:
      application:
        name: "database-manager"
      output:
        ansi:
          enabled: ALWAYS
      main:
        web-application-type: none
    server:
      port: {{.Values.server.port}}

    database-manager:
      managed-postgres-instances:
        {{- range $instanceName, $instanceConfig := .Values.databaseManager.managedPostgresInstances }}
        {{ $instanceName }}:
          admin-db-url: {{ $instanceConfig.adminDbUrl }}
          admin-username: {{ $instanceConfig.adminUsername }}
          managed-databases:
            {{- range $databaseName, $databaseConfig := $instanceConfig.managedDatabases }}
            {{ $databaseName }}:
              baseline-on-migrate: {{ $databaseConfig.baselineOnMigrate }}
            {{- end }}
        {{- end }}