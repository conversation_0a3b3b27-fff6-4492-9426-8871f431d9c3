# Database Manager

The **Database Manager** is intended to be run as a Kubernetes job that allows for the creation of new databases within Postgres instances 
and versioned migrations within Postgres databases.

Versioned migrations are tracked by Flyway. Flyway will create a table called `flyway_schema_history` within
each database that is managed by the Database Manager and contains database migrations.

Database migrations are versioned, and are stored within `classpath:db/migration/<postgres instance name>`.
Each migration should follow format `V<migration number>__<migration name>.sql`. e.g. `V1__create_tenants.sql`

## Managing a Postgres Instance

### 1. Add an entry corresponding to the Postgres instance to manage to the `database-manager.managed-postgres-instances` map in config.
```
database-manager:
  managed-postgres-instances:
    <instance name - application level identifier>:
      admin-db-url: ************************************************************************
      admin-username: postgres
      admin-password: DO_NOT_COMMIT
      managed-databases:
        <sample name of database #1 to manage>:
            baseline-on-migrate: true # allow Flyway migration even if database is non-empty
        <sample name of database #2 to manage>:
            baseline-on-migrate: false # disallow Flyway migration if database is non-empty
```

Not all databases within the instance have to be added to the `managed-databases`. If a database is added to this list of names
that does not exist, that database will automatically be created by the application. The application has no support for deleting databases.

Also note that Flyway by default will not apply migrations on databases that are non-empty and do not have a schema history maintained
by Flyway (i.e. flyway_schema_history table). If you wish to override this behavior, set baseline-on-migrate to true for the database that
this setting should be overridden for.

### 2. Add any migrations to be performed on database instances to `classpath:db/migrations/<instance name - e.g. application level identifier>`
It is not required that a database has migrations. If there are no migrations for a Postgres instance, you must not include that instance within the `migrations` folder.
Under `classpath:db/migrations/<instance name - e.g. application level identifier>`, you should add each migration.
Migrations should not be deleted, and should be treated as a changelog. Flyway will ensure that each migration is executed exactly once.

Sample migrations for a sample database instance called `sample` are included within `classpath:db/migrations/sample`.

## Running Locally

### 1. Modify `application.yml`
Ensure the following configurations are set:

1. **Managed Postgres Instances** - Postgres Instances which are to be managed by Database Manager.

### 2. Run Locally Using IntelliJ

To run the application from within IntelliJ, use the following run configuration:

- **Run Configuration**: `database-manager/DatabaseManagerApplication`