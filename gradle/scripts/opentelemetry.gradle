// See: https://github.com/open-telemetry/opentelemetry-java-instrumentation/discussions/2039
configurations {
    /*
    We create a separate gradle configuration to grab a published Otel instrumentation agent.
    We don't need the agent during development of this extension module.
    This agent is used only during integration test.
    */
    otel
}

dependencies {
    //Otel Java instrumentation that we use and extend during integration tests
    otel("io.opentelemetry.javaagent:opentelemetry-javaagent:2.5.0")
}

// Copy the OpenTelemetry agent to the Jib folder so it will be included in the
// docker image. It should also be available for local development (referenced
// from a run configuration).
task copyOpentelemetryAgentJar(type: Copy) {
    from configurations.otel
    into "src/main/jib/otel"
    rename { fileName -> "opentelemetry-javaagent.jar" }
}
compileJava.dependsOn copyOpentelemetryAgentJar
clean.doFirst {
    delete "src/main/jib/otel"
}
