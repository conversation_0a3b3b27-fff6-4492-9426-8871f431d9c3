def getGitHash = { ->
    def stdout = new ByteArrayOutputStream()
    exec {
        commandLine 'git', 'rev-parse', '--short', 'HEAD'
        standardOutput = stdout
    }
    return stdout.toString().trim()
}

def gitVersion = "${getGitHash()}"

jib {
    to {
        image = "illum.azurecr.io/${project.name}:${project.version}-${gitVersion}"
    }
}

task printImageName {
    dependsOn 'jibDockerBuild'
    doLast {
        def imageName = "illum.azurecr.io/${project.name}:${version}-${gitVersion}"
        file('build/imageName.txt').text = imageName
        println "Image Name: ${imageName} (written to build/imageName.txt)"
    }
}

build.dependsOn printImageName