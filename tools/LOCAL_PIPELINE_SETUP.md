Tools Needed:

    brew install kafka

    brew install zookeeper

<PERSON><PERSON><PERSON> needs to be brought up to use local kafka. 

    Run restart-local-kafka.sh

Since we’re connecting to local kafka, the local input and output kafka topics need to be created for each service:

    kafka-topics --bootstrap-server localhost:9092  --create --partitions 4 --topic "decorated-flow-v1"

Since flow-decorator will be connecting to inventory sync, we need port forwarding to connect from local to the sunnyvale inventory redis instance

Login to teleport 
    
    tsh kube login sunnyvale-cloudsecure-data-1
Port forward
    
    kubectl port-forward svc/inventory -n dev-inventory 7860:8080

Bringing up services:

CEF Connector	

    Easiest to use 'CEFLogConnectorApp+EventHubToLocalKafka' config
    Add azure subscription connectionString to saslJaasConfig in kafka-consumer-config of local yaml file
    Add storage account connectionString to storage-account-config of local yaml file

Flow Aggregator

    use FlowAggApplication+LocalKafka

Flow Decorator

    use FlowDecoratorApplication+LocalKafka

Produce message to raw-flow-topic

    Add connectionString for EH where you want to produce messages in sasl.password in tools/kcat/arch-eventhub/eventhub.properties
    run script tools/kcat/arch-eventhub/cef/produce-cef-log-sample.sh
