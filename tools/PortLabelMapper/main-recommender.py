import openpyxl
import json


def excel_to_json(input_path, output_path):
    wb = openpyxl.load_workbook(input_path)
    ws = wb.active

    port_dict = {}

    def expand_ports(port_str):
        ports = []
        for part in port_str.replace(' ', ',').split(','):
            part = part.strip()
            if part:
                if '-' in part:
                    start, end = part.split('-')
                    if start.isdigit() and end.isdigit():
                        ports.extend(range(int(float(start)), int(float(end)) + 1))
                elif part.isdigit():
                    ports.append(int(float(part)))
        return ports

    def create_new_recommendation():
        return {
            'labelName': None,
            'requiredPorts': [],
            'optionalPorts': [],
            'numOptionalPortsRequired': 1,
            'numFlows': 10
        }

    def add_name_to_port(port, name):
        name_lower = name.lower()

        if port in port_dict:
            existing_names = port_dict[port].split(',')
            existing_names_lower = [n.lower() for n in existing_names]

            if name_lower not in existing_names_lower:
                port_dict[port] = ','.join(existing_names + [name])
        else:
            port_dict[port] = name

    def add_required_port(port):
        port_dict[port][required_ports].append(port)

    recommendation_list = []
    for row in ws.iter_rows(min_row=2, values_only=True):
        # print("row: " + str(row) + 'size:' + str(len(row)))
        name, provider, required_ports, optional_ports, num_optional_ports_required, num_flows, processes, processes_required, role, app, env, loc, core_service_uuid, confidence = row

        required_ports = str(required_ports) if required_ports else ''
        optional_ports = str(optional_ports) if optional_ports else ''

        recommendation = create_new_recommendation()
        if required_ports:
            for port in expand_ports(required_ports):
                recommendation['requiredPorts'].append(port)

        if optional_ports:
            for port in expand_ports(optional_ports):
                recommendation['optionalPorts'].append(port)
        recommendation['labelName'] = name
        if num_flows:
            recommendation['numFlows'] = num_flows
        if num_optional_ports_required:
            recommendation['numOptionalPortsRequired'] = num_optional_ports_required

        recommendation_list.append(recommendation)

    # recommend_dict = {}
    # for recommendation in recommendation_list:
    #     for req_port in recommendation['requiredPorts']:
    #         if req_port in recommend_dict:
    #             recommend_dict[req_port].append(recommendation)
    #         else:
    #             recommend_dict[req_port] = [recommendation]
    #     for optional_port in recommendation['optionalPorts']:
    #         if optional_port in recommend_dict:
    #             recommend_dict[optional_port].append(recommendation)
    #         else:
    #             recommend_dict[optional_port] = [recommendation]

    with open(output_path, 'w') as json_file:
        json.dump(recommendation_list, json_file, indent=4)

    print(f"JSON file created at: {output_path}")

input_path = "/Users/<USER>/Downloads/Label-Reccomendation-Original.xlsx"  #update your input path to excel sheet
output_path = "/Users/<USER>/git/stash/data/connector/tools/PortLabelMapper/recommendations.json" #update your output path here
excel_to_json(input_path, output_path)
