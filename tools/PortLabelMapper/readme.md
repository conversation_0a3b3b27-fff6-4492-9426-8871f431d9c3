# PortLabelMapper

This Python script converts Excel files to JSON format, mapping ports to their respective labels by combining information from both `required_ports` and `optional_ports`.

## Features
- Combines port information from `required_ports` and `optional_ports`.
- If a port has multiple labels, they will be shown as a comma-separated string.
- Expands port ranges and adds the label to each port.
- Avoids adding duplicate labels to the string list, regardless of case.

## Requirements
- Python 3.x
- `openpyxl` library: Install using `pip install openpyxl`

## Expected Input Format
The input Excel file should contain the following fields:
- `name`
- `required_ports`
- `optional_ports`

## Output
The script will generate a JSON file mapping ports to their respective labels.

    "<port>": "<label>" 

