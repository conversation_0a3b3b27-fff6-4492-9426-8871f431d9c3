"""
Python script to convert excel to json
- code combine ports information provided in both required_ports, optional_ports
- if port has multiple labels attached it will be shown as string with comma seperated labels
- if port is a range it will expand and add the label to each ports
- avoid adding duplicates to string list irrespective of case.

#Developer:
<PERSON><PERSON>
"""
import openpyxl
import json

def excel_to_json(input_path, output_path):
    wb = openpyxl.load_workbook(input_path)
    ws = wb.active

    port_dict = {}

    def expand_ports(port_str):
        ports = []
        for part in port_str.replace(' ', ',').split(','):
            part = part.strip()
            if part:
                if '-' in part:
                    start, end = part.split('-')
                    if start.isdigit() and end.isdigit():
                        ports.extend(range(int(float(start)), int(float(end)) + 1))
                elif part.isdigit():
                    ports.append(int(float(part)))
        return ports

    def add_name_to_port(port, name):
        name_lower = name.lower()

        if port in port_dict:
            existing_names = port_dict[port].split(',')
            existing_names_lower = [n.lower() for n in existing_names]

            if name_lower not in existing_names_lower:
                port_dict[port] = ','.join(existing_names + [name])
        else:
            port_dict[port] = name

    for row in ws.iter_rows(min_row=2, values_only=True):
        print("row: " + str(row))
        name, required_ports, optional_ports = row
        required_ports = str(required_ports) if required_ports else ''
        optional_ports = str(optional_ports) if optional_ports else ''

        if required_ports:
            for port in expand_ports(required_ports):
                add_name_to_port(port, name)

        if optional_ports:
            for port in expand_ports(optional_ports):
                add_name_to_port(port, name)

    with open(output_path, 'w') as json_file:
        json.dump(port_dict, json_file, indent=4)

    print(f"JSON file created at: {output_path}")

input_path = "/Users/<USER>/Downloads/Label-Reccomendation.xlsx"  #update your input path to excel sheet
output_path = "/Users/<USER>/git/stash/data/connector/tools/PortLabelMapper/mapping.json" #update your output path here
excel_to_json(input_path, output_path)
