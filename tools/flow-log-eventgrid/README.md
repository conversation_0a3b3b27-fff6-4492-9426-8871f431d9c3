# Deployment of Flow Log Event Grid Infrastructure:

Note that parameters that are filled out in commands below are solely filled for example purposes and should be populated as desired at run time.
Some values to be supplied are generated from prior command's resource generation. Please refer to output of priorly generated resources if a value to be supplied is unclear.

# PowerShell Method:

## 1. We create a Managed Identity on Illumio' subscription that is to be adopted by Event Grid on Microsoft's subscription. We also assign Event Hub data sender role to this identity on Event Grid's Event Hub destination.

```
./illumio-eventgrid-identity-init.ps1 \
-subscriptionId "<Illumio subscription ID>" \
-managedIdentityName "testeventgridpwshstg-eventgrid-identity" \
-managedIdentityResourceGroup "test-eventgrid-pwsh" \
-managedIdentityLocation "eastus" \
-eventGridEventHubDestnResourceId "/subscriptions/fd72c36d-cdc1-4fe9-9c31-13f3e670b601/resourceGroups/arch-log-ing-rg/providers/Microsoft.EventHub/namespaces/test-arch-eventhub/eventhubs/test-eventgrid-automation"
```

## 2. Microsoft creates Event Grid topics and subscriptions on source data Storage Accounts on Microsoft's subscription.
```
./illumio-eventgrid-init.ps1 \
-subscriptionId "<Microsoft subscription ID>" \
-csTenantId "testtenant" \
-sourceStorageAccountNames @("testeventgridpwshstg”) \
-eventGridManagedIdentityResourceId "<Resource ID output from prior command>" \
-eventGridEventHubDestnResourceId "/subscriptions/fd72c36d-cdc1-4fe9-9c31-13f3e670b601/resourceGroups/arch-log-ing-rg/providers/Microsoft.EventHub/namespaces/test-arch-eventhub/eventhubs/test-eventgrid-automation"
```

# BICEP Method:

## 1. Microsoft runs a deployment on their subscription that creates a managed identity to be used by its event grid topic/subscription

```	
az deployment group create \
--resource-group test-eventgrid-bicep \
--template-file flow-log-eventgrid-grid-managed-identity.bicep \
--parameters eventGridManagedIdentityName="FlowLogEventGrid-identity" \
eventGridManagedIdentityLocation="eastus"
```

## 2. We deploy an Azure Function on Illumio subscription that checks our dead-letter storage account daily and sends email alerts if any dead-lettered messages are found

```
cd connector/tools/flow-log-eventgrid/flow-log-eventgrid-failure-notifier
```
```
gradle azureFunctionsDeploy -PresourceGroup=<resource-group> -Pregion=<region>”
```

## 3. We run a deployment on Illumio subscription that creates a dead-letter storage account and communication services that will allow for daily email notifications if any failures exist in dead-letter storage

```
az deployment group create \
  --resource-group test-eventgrid-bicep \
  --template-file flow-log-eventgrid-deadletter-handle.bicep \
  --parameters deadLetterStorageAccountName="testeventgridbicepstgdl" \
               deadLetterStorageContainerName="dead-messages" \
               failureNotifierFunctionAppName="flow-log-eventgrid-failure-notifier" \
               failureNotifierFunctionASPName="asp-flow-log-eventgrid-failure-notifier"
```

## 4. We create an Azure subdomain for email notifications to be sent from. Unfortunately, this cannot be automated due to MS limitations.
1.	Go to flow-log-eventgrid-failure-notifier-communication in Azure portal -> Email tab -> Try Email -> Send email from -> Set up a free Azure subdomain
2.	Activate a free Azure subdomain on the same subscription and resource group that the Azure Function was created on, choosing the email service which was already pre-created “flow-log-eventgrid-failure-notifierCommService-email” -> Click Create + Activate
3.	Once the free subdomain is created, select the newly created subdomain from the “Send email from” field, and copy the senderAddress shown in the code snippet preview as below (will be using in next steps)

## 5.	We update environment variables of our Azure function to include necessary run properties
1.	Retrieve client ID of Azure function’s UAMI using:
```
      az identity show \
        --resource-group test-eventgrid-bicep \
        --name flow-log-eventgrid-failure-notifier-identity \
        --query "clientId" \
        --output tsv
```

2.	Update env vars using:
```
az functionapp config appsettings set \
  --name flow-log-eventgrid-failure-notifier \
  --resource-group test-eventgrid-bicep \
  --settings AZURE_CLIENT_ID="<Client ID outputted from previous command>" \
              DEAD_LETTER_STORAGE_ACCOUNT_NAME="testeventgridbicepstgdl" \
              DEAD_LETTER_CONTAINER_NAME="dead-messages" \
              AZURE_COMMUNICATION_SERVICE_CONNECTION_STRING="<primary connection string for failureNotifierCommunicationService>" \
              SENDER_EMAIL="<sender address generated from step 4>" \
              RECIPIENT_EMAILS="<EMAIL>"
```

## 6.	We run a deployment on Illumio subscription that grants permissions to Azure function’s managed identity to read from dead-letter storage account and send emails with communication services

```
az deployment group create \
  --resource-group test-eventgrid-bicep \
  --template-file flow-log-eventgrid-deadletter-function-role-assignments.bicep \
  --parameters failureNotifierFunctionManagedIdentityPrincipalId="142430ce-2041-4d75-a8bb-5c80ea985487" \
               deadLetterStorageAccountName="testeventgridbicepstgdl" \
               failureNotifierCommunicationServiceName="flow-log-eventgrid-failure-notifier-communication"
```

## 7. We run a deployment on Illumio subscription that grants permissions to Microsoft’s managed identity to produce to our EventHub and dead-letter storage account

```
az deployment group create \
  --resource-group test-eventgrid-bicep \
  --template-file flow-log-eventgrid-grid-role-assignments.bicep \
  --parameters eventGridManagedIdentityPrincipalId="a18fbe78-78f8-418e-9be5-************" \
               eventHubNamespaceName="test-eventgrid-bicep" \
               eventHubName="test-eventgrid-automation-hub" \
               deadLetterStorageAccountName="testeventgridbicepstgdl"
```

## 8. Microsoft runs a deployment on their subscription to set up EventGrid topics and subscriptions to relay flow-log creation events to our EventHub (and any dead-letter events to our dead-letter Storage Account)

```
az deployment group create \
  --resource-group test-eventgrid-bicep \
  --template-file flow-log-eventgrid-grid.bicep \
  --parameters sourceStorageAccountName="testeventgridbicepstg" \
               sourceStorageResourceGroupName="test-eventgrid-bicep" \
               eventGridManagedIdentityResourceId="/subscriptions/********-23a0-4ed8-8d4c-b31aab57ffe1/resourceGroups/test-eventgrid-bicep/providers/Microsoft.ManagedIdentity/userAssignedIdentities/FlowLogEventGrid-identity" \
               eventHubResourceId="/subscriptions/********-23a0-4ed8-8d4c-b31aab57ffe1/resourceGroups/test-eventgrid-bicep/providers/Microsoft.EventHub/namespaces/test-eventgrid-bicep/eventhubs/test-eventgrid-automation-hub" \
               deadLetterStorageAccountResourceId="/subscriptions/********-23a0-4ed8-8d4c-b31aab57ffe1/resourceGroups/test-eventgrid-bicep/providers/Microsoft.Storage/storageAccounts/testeventgridbicepstgdl" \
               deadLetterContainerName="dead-messages" \
               eventGridTopicName="test-eventgrid-topic" \
               eventGridTopicLocation="eastus" \
               eventGridSubscriptionName="test-eventgrid-sub"

```

