param($subscriptionId = "", $managedIdentityName = "", $managedIdentityResourceGroup = "", $managedIdentityLocation = "",
$eventGridEventHubDestnResourceId = "")

$ErrorActionPreference = "Stop"

New-Variable -Name EventHubDataSenderRole -Value "Azure Event Hubs Data Sender" -Option Constant

function Create-EventGrid-ManagedIdentity {
    Write-Host "Attempting to create User-Assigned Managed Identity $managedIdentityName`n"

    try {
        $managedIdentity = New-AzUserAssignedIdentity -Name $managedIdentityName `
            -ResourceGroupName $managedIdentityResourceGroup `
            -Location $managedIdentityLocation

        Write-Host "Created User-Assigned Managed Identity $managedIdentityName`n"
        Write-Host "EventGrid Managed Identity Resource ID: $($managedIdentity.id)`n"

        return $managedIdentity
    }
    catch {
        Write-Host "Error: $_"
    }
}

function Assign-EventGrid-ManagedIdentity-EventHub-Data-Sender-Role {
    param (
        $managedIdentityPrincipalId
    )

    Write-Host "Attempting to assign role $EventHubDataSenderRole to Managed Identity $EventHubDataSenderRole in EventHub scope $eventGridEventHubDestnResourceId`n"

    try {
        New-AzRoleAssignment -ObjectId $managedIdentityPrincipalId -RoleDefinitionName $EventHubDataSenderRole -Scope $eventGridEventHubDestnResourceId -ErrorAction:Stop | Out-null

        Write-Host "Assigned role $EventHubDataSenderRole to Managed Identity $EventHubDataSenderRole in EventHub scope $eventGridEventHubDestnResourceId`n"
    }
    catch {
        Write-Host "Error: $_"
    }
}

# main
$ctx = Get-AzContext
if (!$ctx) {
    Connect-AzAccount -UseDeviceAuthentication
}

try {
    $orgSubscId = $ctx.Subscription.Id
    $orgTenantId = $ctx.Tenant.Id

    if ($subscriptionId -eq "") {
        throw "Subscription ID cannot be empty"
    }
    if ($managedIdentityName -eq "") {
        throw "Managed Identity Name cannot be empty"
    }
    if ($managedIdentityResourceGroup -eq "") {
        throw "Managed Identity Resource Group cannot be empty"
    }
    if (!$managedIdentityLocation) {
        throw "Managed Identity Location cannot be empty"
    }
    if ($eventGridEventHubDestnResourceId -eq "") {
        throw "EventGrid's EventHub destination cannot be empty"
    }

    Write-Host "Select subscription $subscriptionId`n"
    Select-AzSubscription -SubscriptionId $subscriptionId -TenantId $orgTenantId | Out-Null
}
catch {
    Write-Host "Error: $_"
    exit 1
}

try {
    $managedIdentity = Create-EventGrid-ManagedIdentity

    Write-Host "Pausing for 15 seconds before role assignment. Managed Identity may not be recognized too soon...`n"
    Start-Sleep -Seconds 15

    Assign-EventGrid-ManagedIdentity-EventHub-Data-Sender-Role -managedIdentityPrincipalId $managedIdentity.principalId
}
catch {
    Write-Host "Error: $_"
}
finally {
    if ($orgSubscId) {
        Write-Host "Resetting subscription selection... Selecting subscription $orgSubscId, tenant $orgTenantId`n"
        Select-AzSubscription -SubscriptionId $orgSubscId -TenantId $orgTenantId | Out-null
    }
}