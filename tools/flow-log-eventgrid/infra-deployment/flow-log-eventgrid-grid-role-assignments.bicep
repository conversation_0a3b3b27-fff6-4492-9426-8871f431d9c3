// To be executed on Illumio subscription in order to grant Managed Identity associated with Microsoft EventGrid access to write to EventHub and Storage Account for dead-lettered messages
// Must be executed within resource group containing dead letter Storage Account and EventHub

param eventGridManagedIdentityPrincipalId string
param eventHubNamespaceName string
param eventHubName string
param deadLetterStorageAccountName string

resource eventHub 'Microsoft.EventHub/namespaces/eventhubs@2024-05-01-preview' existing = {
  name: '${eventHubNamespaceName}/${eventHubName}'
}

var azureEventsHubDataSenderRoleDefinitionId = '2b629674-e913-4c01-ae53-ef4638d8f975'
resource azureEventsHubDataSenderRoleAssignment 'Microsoft.Authorization/roleAssignments@2022-04-01' = {
  name: guid(eventHub.id, eventGridManagedIdentityPrincipalId, azureEventsHubDataSenderRoleDefinitionId)
  scope: eventHub
  properties: {
    roleDefinitionId: resourceId('Microsoft.Authorization/roleDefinitions', azureEventsHubDataSenderRoleDefinitionId)
    principalId: eventGridManagedIdentityPrincipalId
    principalType: 'ServicePrincipal'
  }
}

resource deadLetterStorageAccount 'Microsoft.Storage/storageAccounts@2023-05-01' existing = {
  name: deadLetterStorageAccountName
}

var storageBlobDataContributorRoleDefinitionId = 'ba92f5b4-2d11-453d-a403-e96b0029c9fe'
resource storageBlobDataContributorRoleAssignment 'Microsoft.Authorization/roleAssignments@2022-04-01' = {
  name: guid(deadLetterStorageAccount.id, eventGridManagedIdentityPrincipalId, storageBlobDataContributorRoleDefinitionId)
  scope: deadLetterStorageAccount
  properties: {
    roleDefinitionId: resourceId('Microsoft.Authorization/roleDefinitions', storageBlobDataContributorRoleDefinitionId)
    principalId: eventGridManagedIdentityPrincipalId
    principalType: 'ServicePrincipal'
  }
}
