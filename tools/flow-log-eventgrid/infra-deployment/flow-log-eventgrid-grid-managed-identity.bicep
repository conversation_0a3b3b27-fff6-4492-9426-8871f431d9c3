// To be executed on Microsoft subscription in order to create a User Assigned Managed Identity that is to be provided to Illumio to allow for production of flow logs to EventHub and dead-lettered messages to Storage Account
// Must be executed within resource group where Managed Identity is to be created

@description('The name for the user-assigned managed identity that allows for flow log creation events to be sent to Illumio EventHub flow-log-events')
param eventGridManagedIdentityName string

@description('The location for the user-assigned managed identity that allows for flow log creation events to be sent to Illumio EventHub flow-log-events')
param eventGridManagedIdentityLocation string

resource eventGridManagedIdentity 'Microsoft.ManagedIdentity/userAssignedIdentities@2023-01-31' = {
  name: eventGridManagedIdentityName
  location: eventGridManagedIdentityLocation
  tags: {
    purpose: 'Identity that allows for sending flow log creation events to Illumio EventHub flow-log-events'
  }
}

output eventGridManagedIdentityResourceId string = eventGridManagedIdentity.id
output eventGridManagedIdentityPrincipalId string = eventGridManagedIdentity.properties.principalId
