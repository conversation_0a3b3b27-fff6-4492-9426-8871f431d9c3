// To be executed on Illumio subscription in order to create:
//  a) A dead-letter storage account + container where failed EventGrid messages are stored 
//  b) A Communication Service + Email Communication Service that allows for sending emails about EventGrid failed messages
//  c) A User Assigned Managed Identity for the Azure Function that checks the dead-letter storage account on a daily basis and potentially sends email notifications about failures
// Prerequisite: Azure Function that checks for and sends failure notification emails if needed is already created.

@description('Name of the Dead-Letter Storage Accounts where failed EventGrid messages are to be stored')
param deadLetterStorageAccountName string

@description('Name of the Dead-Letter Storage Container where failed EventGrid messages are to be stored')
param deadLetterStorageContainerName string

@description('Name of the Azure Function that checks for and sends failure notification emails if needed (Failure Notifier Function)')
param failureNotifierFunctionAppName string

@description('Name of the ASP of the Azure Function that checks for and sends failure notification emails if needed (Failure Notifier Function)')
param failureNotifierFunctionASPName string

resource deadLetterStorageAccount 'Microsoft.Storage/storageAccounts@2023-05-01' = {
  name: deadLetterStorageAccountName
  location: resourceGroup().location
  sku: {
    name: 'Standard_LRS'
  }
  kind: 'StorageV2'
  properties: {
    accessTier: 'Hot'
    supportsHttpsTrafficOnly: true
  }
}

resource deadLetterBlobService 'Microsoft.Storage/storageAccounts/blobServices@2023-05-01' = {
  parent: deadLetterStorageAccount
  name: 'default'
}

resource deadLetterContainer 'Microsoft.Storage/storageAccounts/blobServices/containers@2023-05-01' = {
  parent: deadLetterBlobService
  name: deadLetterStorageContainerName
  properties: {
    publicAccess: 'None'
  }
}

var communicationServiceName = '${failureNotifierFunctionAppName}-communication'
resource communicationService 'Microsoft.Communication/communicationServices@2023-06-01-preview' = {
  name: communicationServiceName
  location: 'global'
  properties: {
    dataLocation: 'United States'
    linkedDomains: []
  }
}

resource emailService 'Microsoft.Communication/emailServices@2023-06-01-preview' = {
  name: '${communicationServiceName}-email'
  location: 'global'
  properties: {
    dataLocation: 'United States'
  }
}

resource appServicePlan 'Microsoft.Web/serverfarms@2023-12-01' existing = {
  name: failureNotifierFunctionASPName
}

resource failureNotifierFunctionAppIdentity 'Microsoft.ManagedIdentity/userAssignedIdentities@2023-01-31' = {
  name: '${failureNotifierFunctionAppName}-identity'
  location: resourceGroup().location
  tags: {
    purpose: 'Identity that allows for reading from Microsoft flow-logs EventGrid Dead-Letter SA and sending emails using Azure Communication Services'
  }
}

resource failureNotifierFunctionApp 'Microsoft.Web/sites@2022-03-01' = {
  name: failureNotifierFunctionAppName
  location: resourceGroup().location
  kind: 'functionapp'
  identity: {
    type: 'UserAssigned'
    userAssignedIdentities: {
      '${failureNotifierFunctionAppIdentity.id}': {}
    }  
  }
  properties: {
    serverFarmId: appServicePlan.id
  }
}

output failureNotifierFunctionManagedIdentityPrincipalId string = failureNotifierFunctionAppIdentity.properties.principalId
output failureNotifierCommunicationServiceName string = communicationServiceName
output deadLetterStorageAccountResourceId string = deadLetterStorageAccount.id
