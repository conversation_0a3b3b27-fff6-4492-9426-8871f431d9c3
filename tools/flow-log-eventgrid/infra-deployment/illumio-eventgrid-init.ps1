param($subscriptionId = "", $csTenantId = "",
    [String[]]$sourceStorageAccountNames,
    $eventGridManagedIdentityResourceId, $eventGridEventHubDestnResourceId = "")

$ErrorActionPreference = "Stop"

function Create-EventGrid-for-Storage-Accounts {
    try {
        # Getting the storage accounts for the given source storage account names
        $storageAccounts = get-StorageAccounts -storageAccountNames $sourceStorageAccountNames -ctx $ctx
        if ($sourceStorageAccountNames -and !$storageAccounts) {
            throw "Storage Accounts $sourceStorageAccountNames are invalid. Please provide valid storage account names `n"
        }

        # If valid storage accounts are found, create EventGrid topic and subscriptions on them
        if ($storageAccounts) {
            foreach ($sa in $storageAccounts) {
                try {
                    Write-Host "Creating EventGrid topic and subscription for storage account $($sa.StorageAccountName)`n"

                    # Create Event Grid Topic
                    $eventGridBaseName = "$($sa.StorageAccountName)-illumio"
                    $eventGridTopicName = "$($eventGridBaseName)-topic"
                    New-AzEventGridSystemTopic -Name $eventGridTopicName `
                        -ResourceGroupName $sa.ResourceGroupName `
                        -Location $sa.Location `
                        -TopicType "microsoft.storage.storageaccounts" `
                        -Source $sa.Id `
                        -UserAssignedIdentity @($eventGridManagedIdentityResourceId)

                    # Create Delivery Attributes (Headers) and Reference Destination for Event Grid Subscription
                    $deliveryAttributePartitionKey = New-Object Microsoft.Azure.PowerShell.Cmdlets.EventGrid.Models.DynamicDeliveryAttributeMapping
                    $deliveryAttributePartitionKey.Name = "PartitionKey"
                    $deliveryAttributePartitionKey.Type = "Dynamic"
                    $deliveryAttributePartitionKey.SourceField = "data.url"
                    $deliveryAttributeMapping = @(
                        (Create-EventGrid-Static-Delivery-Attribute -name "CloudProvider" -value "AZURE"),
                        (Create-EventGrid-Static-Delivery-Attribute -name "CommunicationModel" -value "PUSH"),
                        (Create-EventGrid-Static-Delivery-Attribute -name "CSTenantId" -value $csTenantId),
                        $deliveryAttributePartitionKey
                    )

                    # Reference to Illumio EventHub
                    $destination = New-AzEventGridEventHubEventSubscriptionDestinationObject -DeliveryAttributeMapping $deliveryAttributeMapping -ResourceId $eventGridEventHubDestnResourceId

                    # Create Event Grid Subscription
                    New-AzEventGridSystemTopicEventSubscription -EventSubscriptionName "$($eventGridBaseName)-sub" `
                        -ResourceGroupName $sa.ResourceGroupName `
                        -SystemTopicName $eventGridTopicName `
                        -DeliveryWithResourceIdentityDestination $destination `
                        -DeliveryWithResourceIdentityType "UserAssigned" `
                        -DeliveryWithResourceIdentityUserAssignedIdentity $eventGridManagedIdentityResourceId `
                        -FilterIncludedEventType @("Microsoft.Storage.BlobCreated") `
                        -FilterIsSubjectCaseSensitive:$false `
                        -RetryPolicyMaxDeliveryAttempt 30 `
                        -RetryPolicyEventTimeToLiveInMinute 1440

                    Write-Host "Created EventGrid topic and subscription for storage account $($sa.StorageAccountName)`n"
                }
                catch {
                    Write-Error "Error creating EventGrid for Storage Account $($sa.StorageAccountName). $_`n"
                }
            }
        }
    }
    catch {
        Write-Error "Error: $_"
    }
}

function Get-StorageAccounts {
    param (
        $storageAccountNames
    )

    if (!$storageAccountNames) {
        return
    }

    Write-Host "Attempting to find storage accounts $storageAccountNames on subscription`n"

    # remove duplicates
    $storageAccountNames = $storageAccountNames | Select-Object -Unique
    $storageAccounts = [System.Collections.ArrayList]@()
    try {
        $foundStorageAccounts = Get-AzStorageAccount
        foreach ($storageAccountName in $storageAccountNames) {
            foreach ($foundSA in $foundStorageAccounts) {
                if ($foundSA.StorageAccountName -eq $storageAccountName) {
                    [Void]$storageAccounts.Add($foundSA)
                }
            }
        }
    }
    catch {
        Write-Host "Error: $_"
    }

    return $storageAccounts
}

function Create-EventGrid-Static-Delivery-Attribute {
    param (
        $name,
        $value
    )

    $deliveryAttribute = New-Object Microsoft.Azure.PowerShell.Cmdlets.EventGrid.Models.StaticDeliveryAttributeMapping
    $deliveryAttribute.Name = $name
    $deliveryAttribute.Type = "Static"
    $deliveryAttribute.Value = $value
    $deliveryAttribute.IsSecret = $false

    return $deliveryAttribute
}

# main
$ctx = Get-AzContext
if (!$ctx) {
    Connect-AzAccount -UseDeviceAuthentication
}

try {
    $orgSubscId = $ctx.Subscription.Id
    $orgTenantId = $ctx.Tenant.Id

    if ($subscriptionId -eq "") {
        throw "Subscription Id cannot be empty"
    }
    if ($csTenantId -eq "") {
        throw "Cloudsecure Tenant Id cannot be empty"
    }
    if (!$sourceStorageAccountNames) {
        throw "Must provide at least one source storage account"
    }
    if ($eventGridManagedIdentityResourceId -eq "") {
        throw "EventGrid Managed Identity resource ID cannot be empty"
    }
    if ($eventGridEventHubDestnResourceId -eq "") {
        throw "EventGrid's EventHub destination cannot be empty"
    }

    Write-Host "Select subscription $subscriptionId`n"
    Select-AzSubscription -SubscriptionId $subscriptionId -TenantId $orgTenantId | Out-Null
}
catch {
    Write-Host "Error: $_"
    exit 1
}

try {
    Create-EventGrid-for-Storage-Accounts
}
catch {
    Write-Host "Error: $_"
}
finally {
    if ($orgSubscId) {
        Write-Host "Resetting subscription selection... Selecting subscription $orgSubscId, tenant $orgTenantId`n"
        Select-AzSubscription -SubscriptionId $orgSubscId -TenantId $orgTenantId | Out-null
    }
}