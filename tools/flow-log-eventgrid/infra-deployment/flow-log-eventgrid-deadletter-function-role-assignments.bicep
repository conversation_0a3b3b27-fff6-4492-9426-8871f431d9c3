// To be executed on Illumio subscription in order to assign necessary roles to the UAMI assigned to the Azure Function that checks for and sends failure notification emails if needed:
//  a) Storage Blob Data Reader role on dead-letter storage account
// Prerequisite: flow-log-eventgrid-deadletter-handle.bicep has been executed successfully

param failureNotifierFunctionManagedIdentityPrincipalId string
param deadLetterStorageAccountName string

resource deadLetterStorageAccount 'Microsoft.Storage/storageAccounts@2023-05-01' existing = {
  name: deadLetterStorageAccountName
}

var storageBlobDataReaderRoleDefinitionId = '2a2b9908-6ea1-4ae2-8e65-a410df84e7d1'
resource storageBlobDataContributorRoleAssignment 'Microsoft.Authorization/roleAssignments@2022-04-01' = {
  name: guid(deadLetterStorageAccount.id, failureNotifierFunctionManagedIdentityPrincipalId, storageBlobDataReaderRoleDefinitionId)
  scope: deadLetterStorageAccount
  properties: {
    roleDefinitionId: resourceId('Microsoft.Authorization/roleDefinitions', storageBlobDataReaderRoleDefinitionId)
    principalId: failureNotifierFunctionManagedIdentityPrincipalId
    principalType: 'ServicePrincipal'
  }
}
