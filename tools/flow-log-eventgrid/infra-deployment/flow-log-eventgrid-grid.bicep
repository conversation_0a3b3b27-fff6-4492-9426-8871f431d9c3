// To be executed on Microsoft subscription in order to create a User Assigned Managed Identity that is to be provided to Illumio to allow for production of flow logs to EventHub and dead-lettered messages to Storage Account
// Prerequisite: UAMI for EventGrid has been created and such UAMI has been granted necessary roles to produce to Illumio EventHub and dead-letter Storage Account
// Must be executed within resource group where the EventGrid is to be created

@description('Name of the source storage account that will trigger the Event Grid on BlobCreated events.')
param sourceStorageAccountName string

@description('Name of the resource group for the source storage account that will trigger the Event Grid on BlobCreated events.')
param sourceStorageResourceGroupName string

@description('The resource ID of the User Assigned Managed Identity to be used for Event Grid message delivery to Event Hub. Please verify with <PERSON>lumio that this UAMI has been granted the "Azure Event Hubs Data Sender" role on the Event Hub or deployment will fail.')
param eventGridManagedIdentityResourceId string

@description('The resource ID of the Event Hub destination for the Event Grid subscription.')
param eventHubResourceId string

@description('Resource ID of the destination storage account for dead-lettering Event Grid messages failed to deliver. This storage account is owned by Illumio.')
param deadLetterStorageAccountResourceId string

@description('The container in the dead-letter storage account for storing dead-lettered Event Grid messages.')
param deadLetterContainerName string

@description('The name of the Event Grid topic to be created.')
param eventGridTopicName string

@description('Geographical region to deploy Event Grid topic. Ideally the same region as the source storage account.')
param eventGridTopicLocation string

@description('The name of the Event Grid subscription.')
param eventGridSubscriptionName string

resource sourceStorageAccount 'Microsoft.Storage/storageAccounts@2023-05-01' existing = {
  name: sourceStorageAccountName
  scope: resourceGroup(sourceStorageResourceGroupName)
}

resource eventGridTopic 'Microsoft.EventGrid/systemTopics@2024-06-01-preview' = {
  name: eventGridTopicName
  location: eventGridTopicLocation
  identity: {
    type: 'UserAssigned'
    userAssignedIdentities: {
      '${eventGridManagedIdentityResourceId}': {}
    }
  }
  properties: {
    source: sourceStorageAccount.id
    topicType: 'Microsoft.Storage.StorageAccounts'
  }
}

resource eventGridSubscription 'Microsoft.EventGrid/systemTopics/eventSubscriptions@2024-06-01-preview' = {
  name: eventGridSubscriptionName
  parent: eventGridTopic
  properties: {
    deliveryWithResourceIdentity: {
      destination: {
        endpointType: 'EventHub'
        properties: {
          resourceId: eventHubResourceId
          deliveryAttributeMappings: [
            {
              name: 'CloudProvider'
              type: 'Static'
              properties: {
                value: 'AZURE'
                isSecret: false
              }
            }
            {
              name: 'CommunicationModel'
              type: 'Static'
              properties: {
                value: 'PUSH'
                isSecret: false
              }
            } 
            {
              name: 'PartitionKey'
              type: 'Dynamic'
              properties: {
                sourceField: 'data.url'
              }
            } 
          ]
        }
      }
      identity: {
        type: 'UserAssigned'
        userAssignedIdentity: eventGridManagedIdentityResourceId
      }
    }
    filter: {
      includedEventTypes: [
        'Microsoft.Storage.BlobCreated'
      ]
      isSubjectCaseSensitive: false
    }
    retryPolicy: {
      maxDeliveryAttempts: 30
      eventTimeToLiveInMinutes: 1440 // 24 hours
    }
    deadLetterWithResourceIdentity: {
      deadLetterDestination: {
        endpointType: 'StorageBlob'
        properties: {
          resourceId: deadLetterStorageAccountResourceId
          blobContainerName: deadLetterContainerName
        }
      }
      identity: {
        type: 'UserAssigned'
        userAssignedIdentity: eventGridManagedIdentityResourceId
      }
    }
  }
}
