plugins {
  id "com.microsoft.azure.azurefunctions" version "1.11.0"
}
apply plugin: "com.microsoft.azure.azurefunctions"
apply plugin: 'java'

repositories {
	// Define repositories for dependencies
	mavenCentral()
}

group 'com.illumio'
version '1.0'

dependencies {
    implementation 'com.microsoft.azure.functions:azure-functions-java-library:2.0.1'
    implementation 'com.azure:azure-identity:1.14.0'
    implementation 'com.azure:azure-storage-blob:12.24.0'
    implementation 'com.azure:azure-communication-email:1.1.0-beta.1'
}

sourceCompatibility = '17'
targetCompatibility = '17'
compileJava.options.encoding = 'UTF-8'

jar {
    archiveBaseName = 'flow-log-eventgrid-failure-notifier'
    version = '1.0'
}

azurefunctions {
    resourceGroup = project.findProperty('resourceGroup') ?: 'test-eventgrid-bicep'
    appName = 'flow-log-eventgrid-failure-notifier'
    pricingTier = 'Consumption'
    region = project.findProperty('region') ?: 'eastus'
    runtime {
      os = 'windows'
      javaVersion = '17'
    }
    localDebug = "transport=dt_socket,server=y,suspend=n,address=5005"
}