package com.illumio;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import com.azure.communication.email.EmailClient;
import com.azure.communication.email.EmailClientBuilder;
import com.azure.communication.email.models.EmailAddress;
import com.azure.communication.email.models.EmailMessage;
import com.azure.communication.email.models.EmailSendResult;
import com.azure.core.util.polling.LongRunningOperationStatus;
import com.azure.core.util.polling.PollResponse;
import com.azure.core.util.polling.SyncPoller;
import com.azure.identity.DefaultAzureCredential;
import com.azure.identity.DefaultAzureCredentialBuilder;
import com.azure.storage.blob.BlobContainerClient;
import com.azure.storage.blob.BlobServiceClient;
import com.azure.storage.blob.BlobServiceClientBuilder;
import com.azure.storage.blob.models.BlobItem;
import com.microsoft.azure.functions.annotation.FunctionName;
import com.microsoft.azure.functions.annotation.TimerTrigger;
import com.microsoft.azure.functions.ExecutionContext;

public class DailyFailureNotifier {

    private static final String DEAD_LETTER_STORAGE_ACCOUNT_NAME = System.getenv("DEAD_LETTER_STORAGE_ACCOUNT_NAME");
    private static final String DEAD_LETTER_CONTAINER_NAME = System.getenv("DEAD_LETTER_CONTAINER_NAME");

    private static final String ACS_CONNECTION_STRING = System.getenv("AZURE_COMMUNICATION_SERVICE_CONNECTION_STRING");
    private static final String SENDER_EMAIL = System.getenv("SENDER_EMAIL");
    private static final String RECIPIENT_EMAILS = System.getenv("RECIPIENT_EMAILS");

    private static final DefaultAzureCredential IDENTITY_CREDENTIAL = new DefaultAzureCredentialBuilder().build();

    @FunctionName("DailyFailureNotifier")
    public void run(
        @TimerTrigger(name = "timerInfo", schedule = "0 0 11 * * *") String timerInfo,
        final ExecutionContext context
    ) {
        context.getLogger().info("Checking dead-letter container for failed EventGrid messages...");

        int failedEventGridMessageCount = getFailedEventGridMessageCount(context);
        if (failedEventGridMessageCount > 0) {
            context.getLogger().info(String.format("Dead-letter container contains %d failed EventGrid messages. Sending email to alert about failed messages...", failedEventGridMessageCount));
            sendAlertEmail(context, failedEventGridMessageCount);
        } else {
            context.getLogger().info("Dead-letter container is empty. No alert email will be sent.");
        } 
    }

    private int getFailedEventGridMessageCount(ExecutionContext context) {
        int failedEventGridMessageCount = 0;
        try {
            BlobServiceClient blobServiceClient = new BlobServiceClientBuilder()
            .endpoint(String.format("https://%s.blob.core.windows.net", DEAD_LETTER_STORAGE_ACCOUNT_NAME))
            .credential(IDENTITY_CREDENTIAL)
            .buildClient();

            BlobContainerClient containerClient = blobServiceClient.getBlobContainerClient(DEAD_LETTER_CONTAINER_NAME);

            for (BlobItem blobItem : containerClient.listBlobs()) {
                failedEventGridMessageCount += 1;
            }
        } catch (Exception ex) {
            context.getLogger().severe(String.format("Failed to read from container %s in storage account %s. Error message: %s", DEAD_LETTER_CONTAINER_NAME, DEAD_LETTER_STORAGE_ACCOUNT_NAME, ex.getMessage()));
            throw new RuntimeException(ex);
        }

        return failedEventGridMessageCount;
    }

    private void sendAlertEmail(ExecutionContext context, int failedEventGridMessageCount) {
        try {
            EmailClient emailClient = new EmailClientBuilder()
                    .connectionString(ACS_CONNECTION_STRING)
                    .buildClient();
            EmailMessage emailMessage = formAlertEmail(failedEventGridMessageCount);

            SyncPoller<EmailSendResult, EmailSendResult> poller = emailClient.beginSend(emailMessage, null);
            PollResponse<EmailSendResult> result = poller.waitForCompletion();
            if (result.getStatus() == LongRunningOperationStatus.SUCCESSFULLY_COMPLETED) {
                context.getLogger().info("Alert email sent successfully.");
            } else {
                context.getLogger().severe("Failed to send alert email.");
            }
        } catch (Exception ex) {
            context.getLogger().severe(String.format("Failed to send alert email using ACS. Error message: %s", ex.getMessage()));
            throw new RuntimeException(ex);
        }
    }

    private EmailMessage formAlertEmail(int failedEventGridMessageCount) {
        List<EmailAddress> recipients = Arrays.stream(RECIPIENT_EMAILS.split(","))
            .map(EmailAddress::new).collect(Collectors.toList());
        String bodyMessage = String.format("""
            The dead-letter container %s in storage account %s contains information about %d EventGrid messages 
            which failed to be delivered to the flow-log-events Event Hub for flow logs.\nPlease check the dead-letter container, and once
            an action has been decided for each message, delete it from the container to prevent further alerts.""", 
            DEAD_LETTER_CONTAINER_NAME, DEAD_LETTER_STORAGE_ACCOUNT_NAME, failedEventGridMessageCount);

        return new EmailMessage()
            .setSenderAddress(SENDER_EMAIL)
            .setToRecipients(recipients)
            .setSubject("Failed Microsoft Flow Logs EventGrid Messages Notification")
            .setBodyPlainText(bodyMessage)
            .setBodyHtml(String.format("""
                <html>
                    <body>
                        <h1>%s</h1>
                    </body>
                </html>""", bodyMessage));
    }
}
