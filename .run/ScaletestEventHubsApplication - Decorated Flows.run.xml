<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="ScaletestEventHubsApplication - Decorated Flows" type="Application" factoryName="Application" folderName="scaletest-eventhubs" singleton="false">
    <option name="MAIN_CLASS_NAME" value="com.illumio.data.ScaletestEventHubsApplication" />
    <module name="connector.services.scaletest-eventhubs.main" />
    <option name="PROGRAM_PARAMETERS" value="--spring.config.location=classpath:/application-decorated-flows.yml --spring.config.additional-location=classpath:/secrets.yml" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.illumio.data.components.json.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>