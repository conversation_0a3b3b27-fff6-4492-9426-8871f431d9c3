<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="LoadLogsIngestionApiApplication - CommonSecurityLog" type="Application" factoryName="Application" folderName="load-logs-ingestion-api" singleton="false">
    <option name="MAIN_CLASS_NAME" value="com.illumio.data.LoadLogsIngestionApiApplication" />
    <module name="connector.services.load-logs-ingestion-api.main" />
    <option name="PROGRAM_PARAMETERS" value="--spring.config.additional-location=classpath:/secrets.yml" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.illumio.data.logsingestionapi.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>