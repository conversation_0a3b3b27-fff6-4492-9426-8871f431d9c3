<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="CommonSecurityLogApplication" type="Application" factoryName="Application" folderName="common-security-log">
    <option name="MAIN_CLASS_NAME" value="com.illumio.data.CommonSecurityLogApplication" />
    <module name="connector.services.common-security-log.main" />
    <option name="PROGRAM_PARAMETERS" value="--spring.config.additional-location=classpath:/secrets.yml" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.illumio.data.insights.configuration.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>