<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="LawSinkApplication" type="Application" factoryName="Application" folderName="law-sink">
    <option name="MAIN_CLASS_NAME" value="com.illumio.data.LawSinkApplication" />
    <module name="connector.services.law-sink.main" />
    <option name="PROGRAM_PARAMETERS" value="--spring.config.additional-location=classpath:/secrets.yml" />
    <extension name="coverage">
      <pattern>
        <option name="PATTERN" value="com.illumio.data.insights.configuration.*" />
        <option name="ENABLED" value="true" />
      </pattern>
    </extension>
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>