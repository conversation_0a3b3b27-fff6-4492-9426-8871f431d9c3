function validate::service_helm() {
  local svc="${1:?}"
  otto::is_true "${OTTO_NO_VALIDATE_HELM_SERVICE:-}" && VALID_HELM_SERVICE+=( "${svc}" )
  arr::contains "${svc}" "${VALID_HELM_SERVICE[@]}" && return 0

  # Ensure the dir is there
  local helm_dir="$(_svc::helm::dir)"
  if ! [ -d "${helm_dir}" ]; then
    log::err "Helm dir does not exist | helm_dir='${helm_dir}'"
    return 1
  fi

  # Run lint to verify the values
  if ! helm lint "${helm_dir}" &>/dev/null; then
    _log::_err_helm_lint
    return 1
  fi

  # Cache and continue
  VALID_HELM_SERVICE+=("${svc}")
  return 0
}
