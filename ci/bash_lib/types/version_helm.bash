function validate::version_helm() {
  local vers_helm="${1:?}"
  otto::is_true "${OTTO_NO_VALIDATE_VERSION_HELM:-}" && VALID_VERSION_HELM+=( "${vers_helm}" )
  arr::contains "${vers_helm}" "${VALID_VERSION_HELM[@]}" && return 0

  local acceptable_tags=("")
  if [ "${OTTO_PUBLISH_NONPROD:-}" == "true" ]; then
    if [ "${OTTO_VALIDATE_VERSION_HELM_FOR_DEPLOY:-}" != "true" ]; then
      acceptable_tags+=("nonprod")
    fi
  fi

  local version hash t tags tag_string maj min pat
  IFS='-' read -r version hash tag_string <<< "${vers_helm}"
  IFS='-' read -r -a tags <<< "${tag_string}"
  IFS='.' read -r maj min pat <<< "${version}"
  arg::_int "maj"
  arg::_int "min"
  arg::_int "pat"
  validate::shorthash "${hash}"
  for t in "${tags[@]}"; do
    arg::_string "t" "${acceptable_tags[@]}"
  done
}

function version_helm::normalize() {
  local vers_helm="${1:?}"
  arg::_required::type "version_helm" "vers_helm"

  local version hash maj min pat
  IFS='-' read -r version hash <<< "${vers_helm}"
  IFS='.' read -r maj min pat <<< "${version}"

  echo "${maj}.${min}.${pat}.${hash}"
}

function version_helm::bump() {
  local vers_helm_old="${1:?}"
  local bump_type="${2:?}"
  arg::_required::type "version_helm" "vers_helm_old"
  arg::_required::enum "version_bump" "bump_type"

  local vers_old vers_new vers_new_helm
  vers_old="$(version_helm::normalize "${vers_helm_old}")"
  vers_new="$(version::bump "${vers_old}" "${bump_type}")"

  version::to::helm "${vers_new}"
}
