function _svc::dispatch() {
  local method="${1:?}"
  local svc="${2:?}"
  shift

  # Load the service's functions dynamically
  local svc_dir lib
  svc_dir="$(_svc::dir)"
  lib="${svc_dir}/bash_lib.config.bash"
  [ -f "${lib}" ] && source "${lib}"

  # Determine what to dispatch to or die
  local fxn="service::${svc}::${method}"
  reflection::exists "${fxn}" || fxn="service::default::${method}"
  reflection::exists "${fxn}" || _fatal::service::dispatch

  # Dispatch
  "${fxn}" "$@"
}

function service::default::deployable() {
  true
}

function _fatal::service::dispatch() {
  log::ERR "
Cannot dispatch to method. default_function and service_specific_function do not exist

method='${method}'
svc='${svc}'
default_function='service::default::${method}'
service_specific_function='service::${svc}::${method}'
"
}
