function validate::version() {
  local vers="${1:?}"
  otto::is_true "${OTTO_NO_VALIDATE_VERSION:-}" && VALID_VERSION+=( "${vers}" )
  arr::contains "${vers}" "${VALID_VERSION[@]}" && return 0

  local maj min pat hash
  IFS='.' read -r maj min pat hash <<< "${vers}"
  arg::_int "maj"
  arg::_int "min"
  arg::_int "pat"
  validate::shorthash "${hash}"
}

function version::to::helm() {
  local vers="${1:?}"
  arg::_required::type "version" "vers"

  local maj min pat hash
  IFS='.' read -r maj min pat hash <<< "${vers}"

  echo "${maj}.${min}.${pat}-${hash}"
}

function version::bump() {
  # Parse args
  local vers_prev="${1:?}"
  local bump_type="${2:?}"
  shift 2

  # Validate args
  arg::_required::type "version" "vers_prev"
  arg::_required::enum "version_bump" "bump_type"

  # Do work
  local maj min pat hash
  IFS='.' read -r maj min pat hash <<< "${vers_prev}"

  # Update all the necessary components
  hash="$(git::shorthash)"
  case "${bump_type}" in
    major) ((++maj)); min=0; pat=0;;
    minor) ((++min)); pat=0;;
    patch) ((++pat));;
    *) log::err "Unknown version type | bump_type='${bump_type}'"; return 1 ;;
  esac

  echo "${maj}.${min}.${pat}.${hash}"
}
