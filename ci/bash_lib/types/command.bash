function validate::command() {
  local cmd="${1:?}"
  otto::is_true "${OTTO_NO_VALIDATE_COMMAND:-}" && VALID_COMMAND+=( "${cmd}" )
  arr::contains "${cmd}" "${VALID_COMMAND[@]}" && return 0

  local -r cmd_file="$(find "${OTTO_BASHLIB}/commands" -name "${cmd}.bash")"
  if [ ! -f "$cmd_file" ]; then
    log::err "Unknown command | command='${cmd}'"
    return 1
  fi

  if ! otto::is_jenk && grep -q "jenkins" <<< "${cmd_file}"; then
    log::err "Only <PERSON> is allowed to run this command | command='${cmd}'"
    return 1
  fi

  local commands
  mapfile -t commands < <(reflection::list "command")
  if ! arr::contains "${cmd}" "${commands[@]}"; then
    log::err "Validation returned failure | func='${BASH_SOURCE[0]}' cmd='${cmd}'"
    return 1
  fi
}

function _fatal::unknown_cmd() {
  log::err "Command does not exist | cmd='${cmd:?}'"
  exit 1
}
