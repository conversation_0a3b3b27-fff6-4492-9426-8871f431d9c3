function validate::argocd_config_yaml() {
  local cfg="${1:?}"
  otto::is_true "${OTTO_NO_VALIDATE_ARGOCD_CONFIG_YAML:-}" && VALID_ARGOCD_CONFIG_YAML+=( "${cfg}" )
  arr::contains "${cfg}" "${VALID_ARGOCD_CONFIG_YAML[@]}" && return 0

  _acy::validate::yaml "${cfg}"          || _fatal::_acy::invalid "contains invalid yaml"
  _acy::validate::required_keys "${cfg}" || _fatal::_acy::invalid "is missing required keys"
  _acy::validate::helm_values "${cfg}"   || _fatal::_acy::invalid::helm_values

  # Debug... dump this config file to logs JUST the first time we validate it
  log::DEBUG "Dumping the config file | cfg='${cfg}' contents='$(cat "${cfg}")'"
}

function argocd_config_yaml::write() {
  local key="${1:?}"
  local value="${2:?}"
  local config_file="${3:?}"
  local yq_expr="$(cat << EOF
.${key} = "${value}"
EOF
)"
  run_cmd yq -i "${yq_expr}" "${config_file}"
}

function argocd_config_yaml::helm_values::write() {
  local key="${1:?}"
  local value="${2:?}"
  local config_file="${3:?}"

  # Read the helm values out
  local helm_values_old helm_values_new
  helm_values_old="$(run_cmd yq '.helm_values' "${config_file}" | str::escape_quote)"

  # Compute the new helm values
  local yq_expr
  yq_expr="$(cat << EOF
.${key} = "${value}"
EOF
)"
  helm_values_new="$(run_cmd yq "${yq_expr}" <<< "${helm_values_old}")"

  # Put the helm values back in
  yq_expr="$(cat << EOF
.helm_values = "${helm_values_new}"
EOF
)"
  run_cmd yq -i "${yq_expr}" "${config_file}"
}

function _acy::validate::yaml() {
  # Exit early & silently upon success
  yq "${cfg}" &>/dev/null && return 0

  # Failure path: print out the error
  run_cmd_cap yq "${cfg}"
}

function _acy::validate::required_keys() {
  local cfg="${1:?}"
  local required_keys=(
    "app_name"
    "cluster"
    "namespace"
    "repo_url"
    "chart"
    "chart_version"
    "owner"
    "helm_values"
  )
  local key val
  for key in "${required_keys[@]}"; do
    local val="$(yq ".${key}" "${cfg}")"
    if [ -z "${val}" ] || [ "${val}" == "null" ]; then
      log::ERR "Required key is empty or missing | key='${key}' cfg='${cfg}'"
      return 1
    fi
  done
}

function _acy::validate::helm_values() {
  local cfg="${1:?}"
  local helm_values="$(yq '.helm_values' "${cfg}" 2>/dev/null)"
  if [ -z "${helm_values}" ] || [ "${helm_values}" == "null" ]; then
    log::err "The helm_values key is missing or empty | cfg='${cfg}'"
    return 1
  fi

  # Exit early & silently upon success
  yq <<< "${helm_values}" &>/dev/null && return 0

  # Failure path: print out the error
  yq <<< "${helm_values}"
}

function _fatal::_acy::invalid() {
  log::ERR "The config file ${1:?}. You must fix this manually

filepath: '${cfg}'

==== contents: ====
$(cat "${cfg}")
"
  exit 1
}

function _fatal::_acy::invalid::helm_values() {
  log::ERR "
The config file contains invalid yaml in the helm_values key. You must fix this manually

filepath: '${cfg}'
helm_values:
$(run_cmd yq '.helm_values' "${cfg}" | str::indent 2)
"
  exit 1
}
