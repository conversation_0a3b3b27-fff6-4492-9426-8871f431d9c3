function servicedir() {
  echo "services"
}

# We have various services in the connector repo and not all of them have test
# coverage enforcement enabled. This function returns a list of services on
# which you want to enforce test coverage. Enforcement is enabled using jacoco.
# Jenkins build will fail if test coverage does not meet specified criteria in
# jacoco.
function get_services_for_coverage_enforcement() {
  # Add your service name to this array once you have enabled test coverage
  # enforcement in your service.
  local services=(
    "insights-query-service"
    )
  echo "${services[@]}"
}
