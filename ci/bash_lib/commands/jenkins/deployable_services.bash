function command::deployable_services::help() {
  cat << EOF
Return a list of deployable services to stdout
EOF
}

function command::deployable_services() {
  # Extract the deploy commit message & handle the complex return code
  local msg
  msg="$(deploy_commit::extract git::commit_msg::since_last_commit)" && :
  rc=$?
  case "$rc" in
    0) : ;;
    40) log::debug "Not a deploy commit" ;;
    41) log::warn "Malformed deploy commit message"; exit 1 ;;
    128) _fatal::git;;
    *) log::err "Failed to extract commit message for unknown reason | rc='$rc'"; exit 1 ;;
  esac

  # Iterate over the services and find any deployable services
  local services svc
  mapfile -t services < <(yq -r '.updates[].service' <<< "${msg}")
  for svc in "${services[@]}"; do
    arg::_required::type "service" "svc"
    if ! _svc::dispatch "deployable" "${svc}"; then
      log::warn "Service is marked as not deployable | svc='${svc}'"
      continue
    fi
    echo "${svc}"
  done
}
