function command::deploy() {
  # the harness pushd's us into ${OTTO_REPO_ROOT} - this command is the ONLY
  # time we don't want that
  otto::popd

  # We should only run this if we are prod jenkins w/ a deploy commit
  otto::is_jenk              || _fatal::_not_jenk
  command::is_prod           || _fatal::_not_prod
  command::is_deploy_commit  || _fatal::_not_deploy_commit
  deploy::is_in_argo_git_dir || _fatal::_not_in_argo_git_dir

  # Get the deployable services & deploy_commit_yaml
  local deployable_services deploy_commit_yaml
  mapfile -t deployable_services < <(command::deployable_services)
  deploy_commit_yaml="$(deploy_commit::extract git::commit_msg::since_last_commit)"

  deploy::_body
}

function deploy::_body() {
  # For each config file, change the chart version (which changes the image)
  local svc vers_helm
  for svc in "${deployable_services[@]}"; do
    vers_helm="$(yq -r ".updates[] | select(.service == \"${svc}\") | .value" <<< "${deploy_commit_yaml}")"
    OTTO_VALIDATE_VERSION_HELM_FOR_DEPLOY=true arg::_required::type "version_helm" "vers_helm"

    # Find the config files to update
    local config_files cfg
    mapfile -t config_files < <(_svc::dispatch "argocd_configyaml_filepaths" "${svc}")
    _log::debug_dump_repo

    # Implement the updates
    for cfg in "${config_files[@]}"; do
      arg::_required::type "argocd_config_yaml" "cfg"
      log::info "Deploying service | svc='${svc}' vers_helm='${vers_helm}' cfg='${cfg}'"
      argocd_config_yaml::write "chart_version" "${vers_helm}" "${cfg}"
    done
  done
}

function deploy::is_in_argo_git_dir() {
  local otto_git_remote
  if ! otto_git_remote="$(otto::git remote -v | awk 'NR==1{print $2}')"; then
    log::err "Failed to get git remote"
    exit 1
  fi

  # We should already be cd'd into the git remote
  local argocd_git_remote
  if ! argocd_git_remote="$(git remote -v | awk 'NR==1{print $2}')"; then
    log::err "Failed to get git remote"
    exit 1
  fi

  [ "${otto_git_remote}" == "${argocd_git_remote}" ] && return 1
  log::info "Deploying app from otto to argo | otto='${otto_git_remote}' argocd='${argocd_git_remote}'"
}

function _log::debug_dump_repo() {
  if (( "${#config_files[@]}" != 0 )); then
    log::INFO "We will deploy service to the following config_files | config_files:
$(printf "* %s\n" "${config_files[@]}")
"
    return
  fi

  log::WARN "No config files found for service | vers_helm='${vers_helm}'

PWD='$(pwd)'
Directories in repo:
$(find . -type d | grep -v '\.git')

config.yaml files:
$(find . -type f -name "config.yaml")
"
}

function _fatal::_not_jenk() {
  log::err "Refusing to deploy from non-jenkins environment"
  exit 1
}

function _fatal::_not_prod() {
  log::err "Refusing to deploy from non-prod environment"
  exit 1
}

function _fatal::_not_deploy_commit() {
  log::err "Refusing to deploy from non-deploy commit"
  exit 1
}

function _fatal::_not_in_argo_git_dir() {
  log::err "Refusing to deploy from non-argocd git directory"
  exit 1
}
