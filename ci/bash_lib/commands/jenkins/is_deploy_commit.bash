function command::is_deploy_commit::help() {
  cat << EOF
exit with a return code of 0 if the most recent commit is a deploy commit, 1 otherwise
EOF
}

# If we can extract the commit message (rc=0), then it's a deploy_commit.
function command::is_deploy_commit() {
  local msg
  msg="$(deploy_commit::extract git::commit_msg::since_last_commit)" && :
  rc=$?
  log::debug "Got rc | rc=$rc"
  case "$rc" in
    0) log::DEBUG "This is a deploy commit! | msg='$msg'" ;;
    40) log::debug "Not a deploy commit" ;;
    41) log::warn "Malformed deploy commit message"; exit 1 ;;
    128) _fatal::git;;
    *) log::err "Failed to extract commit message for unknown reason | rc='$rc'"; exit 1 ;;
  esac
  return $rc
}
