function command::is_prod() {
  if ! otto::is_jenk; then
    log::warn "Not a jenkins run ==> not prod"
    return 1
  fi

  if [ "${OTTO_TEST_PIPELINE:-}" == "true" ]; then
    log::warn "OTTO_TEST_PIPELINE is true! We report is_prod as true"
    return 0
  fi

  if [ "${BRANCH_NAME}" != "main" ]; then
    log::warn "BRANCH_NAME is not main - not prod"
    return 1
  fi

  log::info "This is a prod build | OTTO_INVOKER='${OTTO_INVOKER}' BRANCH_NAME='${BRANCH_NAME}'"
  return 0
}
