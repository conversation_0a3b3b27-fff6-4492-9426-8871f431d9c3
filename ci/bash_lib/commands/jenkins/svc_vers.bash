function command::svc_vers::help() {
  cat << EOF
Print out the version of a service.
EOF
}

function command::svc_vers() {
  local svc
  while (( $# > 0 )); do
    case "${1}" in
      --svc) svc="${2:?}"; shift 2 ;;
      *) log::err "Unknown arg | func='${BASH_SOURCE[0]}' arg='${1}'"; exit 1 ;;
    esac
  done

  # Validate args
  arg::_required::type "service" "svc"

  # Compute values
  local vers_helm
  if ! vers_helm="$(service::version::get "${svc}")"; then
    log::err "Failed to get service version | svc='${svc}'"
    exit 1
  fi

  # Return success
  echo "${vers_helm}"
}
