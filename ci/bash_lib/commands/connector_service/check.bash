function command::check() {
  local svc
  while (( $# > 0 )); do
    case "${1}" in
      --svc) svc="${2:?}"; shift 2 ;;
      *) log::err "Unknown arg | func='${BASH_SOURCE[0]}' arg='${1}'"; exit 1 ;;
    esac
  done

  # Validate argument
  arg::_required::type "service" "svc"

  # Build Gradle checkstyle command
  local gradlew_cmd=(
    "${OTTO_REPO_ROOT}/gradlew"
    ":services:${svc}:check"
  )

  # Run the command
  run_cmd "${gradlew_cmd[@]}"
  log::info "Successfully ran Checkstyle for service '${svc}'"
}
