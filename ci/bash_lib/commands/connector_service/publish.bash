function command::publish() {
  local acr
    # Parse just the --acr argument
    local i=1
    while (( i <= $# )); do
      arg="${!i}"
      if [[ "${arg}" == "--acr" ]]; then
        next=$((i+1))
        acr="${!next}"
        break
      fi
      ((i++))
    done

    if [[ -z "${acr}" ]]; then
      log::err "Missing required --acr argument"
      exit 1
    fi

    if [[ "${acr}" == *"azurecr.io"* ]]; then
      log::info "Publishing to ACR: ${acr}"
      publish::docker "$@"
      publish::helm "$@"
    elif [[ "${acr}" == *"amazonaws.com"* ]]; then
      log::info "Publishing to ECR: ${acr}"
      publish::dockerToECR "$@"
      publish::helmToECR "$@"
    else
      log::err "Unknown registry type for ACR value: ${acr}"
      exit 1
    fi
}

function publish::helm() {
  # Parse args
  local acr svc
  while (( $# > 0 )); do
    case "${1}" in
      --platform) shift 2 ;; # silently ignore :)
      --acr) acr="${2:?}"; shift 2 ;;
      --svc) svc="${2:?}"; shift 2 ;;
      *) log::err "Unknown arg | func='${BASH_SOURCE[0]}' arg='${1}'"; exit 1 ;;
    esac
  done

  # Validate args
  arg::_required::type "service_helm" "svc"
  arg::_required "acr"
  arg::_env "ACR_TOKEN_USERNAME" "ACR_TOKEN_PASSWORD"

  # Compute values
  local vers_helm
  if ! vers_helm="$(service::version::get "${svc}")"; then
    log::err "Failed to get service version | svc='${svc}'"
    exit 1
  fi

  local helm_tgz
  if ! helm_tgz="${helm_tgz:-$(service::helm::tgz "${svc}" "${vers_helm}")}"; then
    log::err "Failed to get helm dest"
    exit 1
  fi

  ### Do work

  # Log in to the ACR
  local helm_login_args=(
    "${acr:?}"
    --username "${ACR_TOKEN_USERNAME:?}"
    --password "${ACR_TOKEN_PASSWORD:?}"
  )
  run_cmd_cap helm registry login "${helm_login_args[@]}"

  # Package up the helm chart (build)
  local -r helm_dest="$(dirname "${helm_tgz}")"
  local helm_package_args=(
    "$(_svc::helm::dir)"
    --version "${vers_helm}"
    --app-version "${vers_helm}"
    --destination "${helm_dest}"
  )
  run_cmd_cap helm package "${helm_package_args[@]}"
  if ! [ -f "${helm_tgz}" ]; then
    log::err "Failed to package helm chart | helm_tgz='${helm_tgz}'"
    exit 1
  fi

  # Find where to push the helm chart to
  local acr_helm_proj
  acr_helm_proj="$(_svc::dispatch "acr_helm_proj" "${svc}")"
  local remote_path="${acr}/helm/${acr_helm_proj}/"
  remote_path="$(tr -s '/' <<< "${remote_path}")"

  # Push the helm chart
  local helm_push_args=(
    "${helm_tgz}"
    "oci://${remote_path}"
  )
  run_cmd helm push "${helm_push_args[@]}"
}

function publish::docker() {
  local svc acr platform
  while (( $# > 0 )); do
    case "${1:?}" in
      --svc) svc="${2:?}"; shift 2 ;;
      --acr) acr="${2:?}"; shift 2 ;;
      --platform) platform="${2:?}"; shift 2 ;;
      *) log::err "Unknown arg | func='${BASH_SOURCE[0]}' arg='${1}'"; exit 1 ;;
    esac
  done

  arg::_required::type "service" "svc"
  arg::_required "acr"
  # arg::_required "platform"
  arg::_env "ACR_TOKEN_USERNAME" "ACR_TOKEN_PASSWORD"

  # Log in to the ACR
  local docker_login_args=(
    --username "${ACR_TOKEN_USERNAME:?}"
    --password "${ACR_TOKEN_PASSWORD:?}"
    "${acr:?}"
  )
  run_cmd docker login "${docker_login_args[@]}"

  # Push the image
  local image_name
  image_name="$(service::image_name "${svc}" --acr "${acr:?}")"
  run_cmd docker push "${image_name}"
}

function publish::helmToECR() {
  # ---------- parse flags ----------
  local repo_url svc
  while (( $# > 0 )); do
    case "$1" in
      --platform) shift 2 ;;           # ignore
      --acr)      repo_url="$2"; shift 2 ;;  # Artifactory URL
      --svc)      svc="$2"; shift 2 ;;
      *) log::err "Unknown arg '$1'"; exit 1 ;;
    esac
  done

  # ---------- validate ----------
  arg::_required::type "service_helm" "svc"
  arg::_required "repo_url"                # <-- was acr
  arg::_env "ART_USER" "ART_PASSWORD"

  # ---------- derive version ----------
  local vers_helm
  if ! vers_helm="$(service::version::get "$svc")"; then
    log::err "Failed to get version for '$svc'"
    exit 1
  fi

  # ---------- package the chart ----------
  local chart_dir="$(_svc::helm::dir)"
  local helm_tgz
  helm_tgz="$(service::helm::tgz "$svc" "$vers_helm")"

  # actually create the tgz
  helm package "$chart_dir" \
        --version "$vers_helm" \
        --app-version "$vers_helm" \
        --destination "$(dirname "$helm_tgz")"

  if [[ ! -f "$helm_tgz" ]]; then
    log::err "Helm package not found at '$helm_tgz'"
    exit 1
  fi
  repo_url="https://artifactory.ilabs.io/artifactory/repo-helm-local/gateway-connector/"
  # ---------- push to Artifactory ----------
  log::info "Uploading $helm_tgz → $repo_url"
  curl -u "${ART_USER}:${ART_PASSWORD}" -T "$helm_tgz" "${repo_url}$(basename "$helm_tgz")" \
    || { log::err "Upload failed"; exit 1; }

  log::info "Helm chart uploaded successfully"
}


function publish::dockerToECR() {
  log::info "✅ Loaded publish_to_ecr.bash"
  local svc acr platform
  while (( $# > 0 )); do
    case "${1:?}" in
      --svc) svc="${2:?}"; shift 2 ;;
      --acr) acr="${2:?}"; shift 2 ;;
      --platform) platform="${2:?}"; shift 2 ;;
      *) log::err "Unknown arg | func='${BASH_SOURCE[0]}' arg='${1}'"; exit 1 ;;
    esac
  done

  arg::_required::type "service" "svc"
  arg::_required "acr"
  # arg::_required "platform"
  arg::_env "AWS_ECR_ACCESS_KEY_ID" "AWS_ECR_SECRET_ACCESS_KEY"

  if ! command -v aws &> /dev/null; then
      echo "Installing AWS CLI in workspace"
      log::info "Installing AWS CLI in workspace"
      # Ensure unzip is available
        if ! command -v unzip &> /dev/null; then
          log::info "Installing unzip"
          if command -v apt-get &> /dev/null; then
            sudo apt-get update && sudo apt-get install -y unzip
          elif command -v yum &> /dev/null; then
            yum install -y unzip
          else
            log::err "Package manager not found. Please install 'unzip' manually."
            exit 1
          fi
        fi
      rm -rf "$WORKSPACE/aws-cli" "$WORKSPACE/aws-cli-bin"
      mkdir -p "$WORKSPACE/aws-cli" && cd "$WORKSPACE/aws-cli"
      curl -s "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
      unzip -q awscliv2.zip
      ./aws/install -i "$WORKSPACE/aws-cli-bin" -b "$WORKSPACE/aws-cli-bin/bin"
      export PATH="$WORKSPACE/aws-cli-bin/bin:$PATH"
    else
      log::info "Using existing AWS CLI: $(which aws)"
  fi
  # Log in to the ECR
  export AWS_ACCESS_KEY_ID="${AWS_ECR_ACCESS_KEY_ID}"
  export AWS_SECRET_ACCESS_KEY="${AWS_ECR_SECRET_ACCESS_KEY}"
  export AWS_DEFAULT_REGION="us-west-2"

  local repo_name="${svc}"
  if ! aws ecr describe-repositories --repository-names "${repo_name}" --region "${AWS_DEFAULT_REGION}" &>/dev/null; then
      log::info "Creating ECR repo '${repo_name}'"
      aws ecr create-repository --repository-name "${repo_name}" --region "${AWS_DEFAULT_REGION}" \
        || { log::err "❌ Failed to create ECR repo '${repo_name}'"; exit 1; }
    else
      log::info "ECR repo '${repo_name}' already exists"
  fi



  local login_password="$(aws ecr get-login-password --region "us-west-2")"
  echo "${login_password}" | docker login --username AWS --password-stdin "${acr}"

  # Push the image
  local image_name
  image_name="$(service::image_name "${svc}" --acr "${acr:?}")"
  run_cmd docker push "${image_name}"
}

function _log::_err_helm_lint() {
  log::ERR "
Helm lint failed

    helm_dir='${helm_dir}'

Helm lint output:

$(helm lint "${helm_dir}" | str::indent 4)
"
}
