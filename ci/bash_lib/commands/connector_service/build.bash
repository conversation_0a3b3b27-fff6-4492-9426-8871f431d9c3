# OTTO_RECONCILE=false
function command::build() {
  local svc acr
  while (( $# > 0 )); do
    case "${1}" in
      --svc) svc="${2:?}"; shift 2 ;;
      --acr) acr="${2:?}"; shift 2 ;;
      *) log::err "Unknown arg | func='${BASH_SOURCE[0]}' arg='${1}'"; exit 1 ;;
    esac
  done

  # Validate args
  arg::_required::type "service" "svc"
  arg::_required "acr"

  # Compute useful values
  local gitcommit
  gitcommit=$(git rev-parse HEAD)

  local image_name
  image_name="$(service::image_name "${svc}" --acr "${acr}")"

  # Build and run the gradle build command
  local gradlew_cmd=(
    "${OTTO_REPO_ROOT}/gradlew"
    ":services:${svc}:printImageName"
    "-Djib.to.image=${image_name}"
  )
  run_cmd "${gradlew_cmd[@]}"
  log::info "Successfully ran build"
}
