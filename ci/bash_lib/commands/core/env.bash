function command::env() {
  command::env::otto
  command::env::jenkins
}

function command::env::otto() {
  log::warn "Most interesting environment variables for build script"
  log::info "Build script env var: OTTO_INVOKER='${OTTO_INVOKER:?}'"
  log::info "Build script env var: OTTO_REPO_ROOT='${OTTO_REPO_ROOT:?}'"
  log::info "Build script env var: OTTO_PROJ='${OTTO_PROJ:?}'"
  log::info "Build script env var: OTTO_BASHLIB='${OTTO_BASHLIB:?}'"
}

function command::env::jenkins() {
  if ! otto::is_jenk; then
    log::debug "Not a jenkins run"
    # return
  fi
  set +u

  log::warn "Most interesting environment variables set by Jenkins system"
  log::info "Jenkins env var: WORKSPACE='${WORKSPACE}'"
  log::info "Jenkins env var: BRANCH_NAME='${BRANCH_NAME}'"
  log::info "Jenkins env var: BUILD_NUMBER='${BUILD_NUMBER}'"
  log::info "Jenkins env var: NODE_NAME='${NODE_NAME}'"
  log::info "Jenkins env var: NODE_LABELS='${NODE_LABELS}'"

  log::warn "Most interesting environment variables set in our Jenkinsfiles"
  log::info "Illumio Jenkins env var: SLACK_CHANNEL_OFFICIAL='${SLACK_CHANNEL_OFFICIAL}'"
  log::info "Illumio Jenkins env var: SLACK_CHANNEL_PRIVATE='${SLACK_CHANNEL_PRIVATE:-}'"

  # Also just dump the env to stdout for good measure
  log::info "All environment variables"
  log::INFO "$(run_cmd env | sort)"
  log::INFO "$(run_cmd id)"
}
