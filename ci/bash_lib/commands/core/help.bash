function command::help::help() {
  cat << EOF
${HU}: help [COMMAND]

If you pass a command to help, then it will show help docs for that specific
command
EOF
}

function command::help() {
  local commands
  mapfile -t commands < <(reflection::list "command")

  case $# in
    0) command::help::all ;;
    *) command::help::one "${1:?}" ;;
  esac
}

function command::help::all() {
  local -r cmd_dir="${OTTO_BASHLIB}/commands"

  local command_groups group commands prefix="* "
  mapfile -t command_groups < <(find "${cmd_dir}" -type d | sort)
  for group in "${command_groups[@]}"; do
    ::_cmd_group_header "${group}"
    mapfile -t commands < <(::_cmd_group_cmds "${group}")
    (( ${#commands[@]} == 0 )) && prefix="|${prefix}" && continue
    printf "%s\n" "${commands[@]}" | str::prepend "${prefix}"
    echo
  done
}

function command::help::one() {
  local cmd="${1:?}"
  local help_fxn="command::${cmd}::help"
  if ! reflection::exists "${help_fxn}"; then
    log::err "No help available for command | cmd='${cmd}'"
    return 1
  fi

  ::_macros
  echo -e "$("${help_fxn}")"
}

function ::_macros() {
  HU="${c_red}Usage${c_rst}"
  HO="${c_red}Options${c_rst}"
  HE="${c_green}Example${c_rst}"
}

function ::_cmd_group_header() {
  echo -e "=== ${c_green}${1#${cmd_dir}/}${c_rst} ==="
}

function ::_cmd_group_cmds() {
  local group="${1:?}"
  find "${group}" -type f -name "*.bash" -maxdepth 1 -exec basename {} .bash \; | sort
}
