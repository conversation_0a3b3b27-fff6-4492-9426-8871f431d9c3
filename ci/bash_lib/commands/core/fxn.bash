function command::fxn::help() {
  cat << EOF
Usful for debugging. Run a 'sh_lib' function. If you ever see this function in
CI/CD then someone is being lazy and they should be stopped.
EOF
}

function command::fxn() {
  local fxn="${1:?}"
  shift

  if ! reflection::exists "$fxn"; then
    log::WARN "Existing functions:"
    log::WARN "$(reflection::list::fxns | str::indent 2)"
    log::err "Unknown function | fxn='$fxn'"
    exit 1
  fi

  lvl=DEV run_cmd "$fxn" "$@"
}
