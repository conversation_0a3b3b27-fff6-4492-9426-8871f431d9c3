readonly DEPLOY_COMMIT_START="[Deployments]"
readonly DEPLOY_COMMIT_END="[End]"

function deploy_commit::extract() {
  local commit_msg_getter="${1:?}"
  shift
  case "${commit_msg_getter}" in
    git::commit_msg::since_last_commit |\
    git::commit_msg::for_commit) ;;
    *) log::err "Unknown commit_msg_getter | commit_msg_getter='${commit_msg_getter}'"; return 1 ;;
  esac

  # This could have been an awk one-liner but it was uglier
  local state="before" whitespace_line line
  while IFS= read -r whitespace_line; do
    line="$(str::trim <<< "${whitespace_line}")"
    [ "${line}" == "$DEPLOY_COMMIT_START" ] && state="between" && continue
    [ "${line}" == "$DEPLOY_COMMIT_END" ] && state="after" && break
    [ "${state}" == "between" ] && echo "${whitespace_line}"
  done < <("${commit_msg_getter}" "$@")

  # Returning 0 for success, or 40 for a happy-path failure
  case "${state}" in
    after) return 0 ;;
    before) return 40 ;;
    between|*) return 41 ;;
  esac
}

function command::deploy_commit::help() {
cat << EOF
Create a deploy_commit, automatically selecting which components to deploy -
based on which files have changed

ARGUMENTS:
  [-n|--dry-run]
  [--bump_type patch|minor|major]
EOF
}


function command::deploy_commit() {
  local dry="false" bump_type="patch"
  while (( $# > 0 )); do
    case "$1" in
      -n|--dry-run) dry="true"; shift;;
      --bump_type) bump_type="${2:?}"; shift 2 ;;
      --svc|--helm|--autolabellib)
        log::ERR "Ignoring arg(s) '${1} ${2:-}'"
        log::ERR "If you want to make a manual deploy commit, use 'manual_deploy_commit'"
        return 1
        ;;
      --help) command::deploy_commit::help; return 0;;
      *) log::err "Unknown argument in ${FUNCNAME[0]}: '$1'"; exit 1;;
    esac
  done

  arg::_required::enum "version_bump" "bump_type"

  local args=()
  args+=(--bump_type "$bump_type")

  local services svc
  mapfile -t services < <(service::reflection::list::changed)
  if (( ${#services[@]} == 0 )); then
    log::warn "No services have changed. Returning immediately"
    return 0
  fi
  for svc in "${services[@]}"; do
    args+=("--svc" "${svc}")
  done

  RUN_CMD="run_cmd"
  if [ "$dry" == "true" ]; then
    log::info "Doing a dry run"
    RUN_CMD="log::warn"
  fi

  "$RUN_CMD" command::manual_deploy_commit "${args[@]}"
}

function _fatal::git() {
  log::ERR "
Critical error when invoking git

We have seen this in the past when trying to access the ref 'HEAD^' while
Jenkins only has a single commit fetched.
"
  exit 1
}
